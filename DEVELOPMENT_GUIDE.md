# Progress Dashboard - Development Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- Python 3.8+
- Git

### Installation & Setup
```bash
# 1. Clone repository
git clone https://github.com/hellozei/progress-dashboard.git
cd progress-dashboard

# 2. Install frontend dependencies
npm install

# 3. Install backend dependencies
cd backend
pip install -r requirements.txt

# 4. Start development servers
# Terminal 1 - Backend
cd backend
python app.py

# Terminal 2 - Frontend  
npm run dev
```

### Access Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5001
- **API Health**: http://localhost:5001/api/health

## 📁 Project Structure

```
progress-dashboard/
├── src/                    # Frontend React application
│   ├── components/         # React components
│   ├── contexts/          # React contexts (global state)
│   ├── services/          # API services
│   ├── types/             # TypeScript definitions
│   └── utils/             # Utility functions
├── backend/               # Python Flask API
│   ├── app.py            # Main Flask application
│   ├── config.py         # Configuration management
│   ├── data/             # CSV data storage
│   ├── optimization_service.py # Performance optimizations
│   └── monitoring.py     # System monitoring
├── public/               # Static assets
└── dist/                # Production build output
```

## 🔧 Core Components

### Frontend Components
- **App.tsx** - Main application with routing
- **Header.tsx** - Navigation header with burger menu
- **Categories.tsx** - Category analysis dashboard
- **Competitors.tsx** - Competitor tracking dashboard  
- **FileManagement.tsx** - File upload and management
- **SystemSettings.tsx** - Application settings
- **Notification*.tsx** - Notification system components

### Backend Modules
- **app.py** - Main Flask API with all endpoints
- **config.py** - Environment and feature configuration
- **optimization_service.py** - Redis caching and performance
- **monitoring.py** - System health monitoring
- **redis_client.py** - Redis connection management

## 🌐 API Endpoints

### Core Data APIs
```
GET  /api/health                    # System health check
GET  /api/categories                # List all categories
GET  /api/categories/{name}/data    # Category data analysis
GET  /api/competitors               # List all competitors
GET  /api/competitors/{name}/data   # Competitor data analysis
```

### File Management APIs
```
GET    /api/files                   # List all files
POST   /api/upload                  # Upload new file
PUT    /api/files/{id}/rename       # Rename file
DELETE /api/files/{id}              # Delete file
```

### Notification APIs
```
GET  /api/notifications             # Get notifications
POST /api/notifications             # Create notification
PUT  /api/notifications/{id}        # Update notification
GET  /api/notifications/settings    # Get notification settings
```

### Monitoring APIs
```
GET /api/monitoring/health          # Detailed health check
GET /api/monitoring/stats           # System statistics
GET /api/monitoring/performance     # Performance metrics
```

## 🎨 UI/UX Guidelines

### Design System
- **Primary Color**: Seashell (#F7F3F0)
- **Accent Colors**: Green (#9CEE69), Blue (#0078d4)
- **Typography**: System fonts with proper hierarchy
- **Layout**: Mobile-first responsive design
- **Effects**: Glass morphism with backdrop blur

### Component Patterns
```tsx
// Standard component structure
interface ComponentProps {
  data: DataType[];
  onAction: (id: string) => void;
}

const Component: React.FC<ComponentProps> = ({ data, onAction }) => {
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    // Load data on mount
  }, []);
  
  return (
    <div className="bg-white/40 backdrop-blur-sm rounded-xl border border-white/30">
      {/* Component content */}
    </div>
  );
};
```

### Responsive Design
```css
/* Mobile-first approach */
.container {
  @apply p-4;
}

@media (min-width: 768px) {
  .container {
    @apply p-6;
  }
}

@media (min-width: 1024px) {
  .container {
    @apply p-8;
  }
}
```

## 🔄 State Management

### React Context Pattern
```typescript
// Global state with Context API
const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);
  
  const contextValue = {
    state,
    actions: {
      updateData: (data) => dispatch({ type: 'UPDATE_DATA', payload: data }),
      setLoading: (loading) => dispatch({ type: 'SET_LOADING', payload: loading })
    }
  };
  
  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
};
```

### Local State Management
```typescript
// Component-level state
const [data, setData] = useState<DataType[]>([]);
const [loading, setLoading] = useState(false);
const [error, setError] = useState<string | null>(null);

// API call with error handling
const loadData = async () => {
  try {
    setLoading(true);
    setError(null);
    const response = await apiService.getData();
    if (response.success) {
      setData(response.data);
    } else {
      setError(response.error);
    }
  } catch (err) {
    setError(err.message);
  } finally {
    setLoading(false);
  }
};
```

## 🔌 API Integration

### API Service Pattern
```typescript
// src/services/api.ts
class ApiService {
  private baseUrl = 'http://localhost:5001/api';
  
  private async fetchWithErrorHandling<T>(url: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${url}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }
      
      return { success: true, data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  async getCategories() {
    return this.fetchWithErrorHandling<CategoriesResponse>('/categories');
  }
}

export const apiService = new ApiService();
```

### Error Handling
```typescript
// Consistent error handling
const handleApiError = (error: string): string => {
  if (error.includes('fetch')) {
    return 'Network error. Please check your connection.';
  }
  if (error.includes('404')) {
    return 'Resource not found.';
  }
  if (error.includes('500')) {
    return 'Server error. Please try again later.';
  }
  return error;
};
```

## ⚡ Performance Optimization

### Frontend Optimizations
```typescript
// React.memo for preventing unnecessary re-renders
const ExpensiveComponent = React.memo<Props>(({ data }) => {
  return <div>{/* Expensive rendering */}</div>;
});

// useCallback for memoizing functions
const handleClick = useCallback((id: string) => {
  // Handle click logic
}, [dependency]);

// useMemo for expensive calculations
const processedData = useMemo(() => {
  return data.map(item => expensiveTransformation(item));
}, [data]);
```

### Backend Optimizations
```python
# Redis caching with circuit breaker
@optimization_service.smart_cache(key_prefix="categories", expiration=3600)
def get_categories_cached():
    return process_categories_data()

# Response compression
from flask_compress import Compress
Compress(app)

# Performance monitoring
@app.before_request
def before_request():
    g.start_time = time.time()

@app.after_request  
def after_request(response):
    duration = time.time() - g.start_time
    response.headers['X-Response-Time'] = f"{duration:.3f}s"
    return response
```

## 🧪 Testing Strategy

### Frontend Testing
```typescript
// Component testing with React Testing Library
import { render, screen, fireEvent } from '@testing-library/react';

test('should render component correctly', () => {
  render(<Component data={mockData} />);
  expect(screen.getByText('Expected Text')).toBeInTheDocument();
});

test('should handle user interactions', () => {
  const mockHandler = jest.fn();
  render(<Component onAction={mockHandler} />);
  
  fireEvent.click(screen.getByRole('button'));
  expect(mockHandler).toHaveBeenCalledWith('expected-value');
});
```

### Backend Testing
```python
# API endpoint testing
def test_categories_endpoint():
    response = client.get('/api/categories')
    assert response.status_code == 200
    assert response.json['success'] == True
    assert 'data' in response.json

# Data processing testing
def test_csv_processing():
    test_data = create_test_csv_data()
    result = process_csv_data(test_data)
    assert len(result) > 0
    assert all('Title' in item for item in result)
```

## 🔒 Security Best Practices

### Input Validation
```python
# File upload validation
def validate_csv_file(file):
    if not file.filename.endswith('.csv'):
        raise ValueError('Only CSV files are allowed')
    
    if file.content_length > MAX_FILE_SIZE:
        raise ValueError('File too large')
    
    return True

# SQL injection prevention
def safe_query(query, params):
    return db.execute(query, params)  # Use parameterized queries
```

### Frontend Security
```typescript
// XSS prevention
const sanitizeInput = (input: string): string => {
  return input.replace(/[<>]/g, '');
};

// CSRF protection
const apiCall = async (url: string, data: any) => {
  const token = getCsrfToken();
  return fetch(url, {
    headers: {
      'X-CSRF-Token': token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
};
```

## 🚀 Deployment

### Production Build
```bash
# Frontend build
npm run build

# Backend setup
pip install -r requirements.txt
export FLASK_ENV=production
export ENABLE_OPTIMIZATIONS=true

# Start production server
python app.py
```

### Environment Configuration
```bash
# Frontend (.env.production)
VITE_API_BASE_URL=https://api.yourdomain.com

# Backend environment variables
FLASK_ENV=production
ENABLE_CACHING=true
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-redis-password
```

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Failed**
   ```typescript
   // Check if backend is running
   const healthCheck = await fetch('http://localhost:5001/api/health');
   ```

2. **File Upload Issues**
   ```python
   # Check file permissions and directory existence
   os.makedirs(UPLOAD_DIR, exist_ok=True)
   ```

3. **Redis Connection Issues**
   ```bash
   # Check Redis status
   redis-cli ping
   ```

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
export ENABLE_DEBUG=true

# Frontend debug
npm run dev -- --debug
```

---

**📚 Additional Resources:**
- [CODING_CONTEXT.md](./CODING_CONTEXT.md) - Detailed coding context
- [README.md](./README.md) - Project overview
- [backend/README.md](./backend/README.md) - Backend specific guide
- [backend/README_OPTIMIZATIONS.md](./backend/README_OPTIMIZATIONS.md) - Performance optimizations
