<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Progress Dashboard Chrome Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .step h3 {
            margin-top: 0;
            color: #007bff;
        }
        .code {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #0056b3; }
        .screenshot {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
            max-width: 100%;
        }
        ol {
            counter-reset: step-counter;
        }
        ol li {
            counter-increment: step-counter;
            margin: 10px 0;
        }
        ol li::marker {
            content: counter(step-counter) ". ";
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Install Progress Dashboard Chrome Extension</h1>
        <p>Panduan lengkap untuk menginstall Chrome Extension yang diperlukan untuk sistem autentikasi OTP.</p>

        <div class="info">
            <strong>📋 Persyaratan:</strong>
            <ul>
                <li>Google Chrome browser (versi 88 atau lebih baru)</li>
                <li>Developer mode enabled di Chrome Extensions</li>
                <li>Akses ke folder chrome-extension di project ini</li>
            </ul>
        </div>

        <div class="step">
            <h3>Step 1: Buka Chrome Extensions Page</h3>
            <p>Buka Chrome browser dan navigasi ke halaman extensions:</p>
            <div class="code">chrome://extensions/</div>
            <p>Atau melalui menu: <strong>Chrome Menu → More Tools → Extensions</strong></p>
            <button onclick="openExtensionsPage()">🔗 Open Extensions Page</button>
        </div>

        <div class="step">
            <h3>Step 2: Enable Developer Mode</h3>
            <p>Di halaman Extensions, aktifkan "Developer mode" dengan toggle di pojok kanan atas.</p>
            <div class="warning">
                <strong>⚠️ Penting:</strong> Developer mode harus diaktifkan untuk dapat menginstall extension dari folder lokal.
            </div>
        </div>

        <div class="step">
            <h3>Step 3: Load Unpacked Extension</h3>
            <p>Setelah Developer mode aktif, klik tombol <strong>"Load unpacked"</strong> yang muncul.</p>
            <p>Pilih folder berikut dari project ini:</p>
            <div class="code">chrome-extension/</div>
            <p>Path lengkap:</p>
            <div class="code">/Users/<USER>/Library/CloudStorage/Dropbox/Assets/Apps/Dashboard/Progress_Dashboard-main/chrome-extension</div>
        </div>

        <div class="step">
            <h3>Step 4: Verify Installation</h3>
            <p>Setelah berhasil diinstall, Anda akan melihat:</p>
            <ul>
                <li>Extension "Progress Dashboard OTP Authenticator" muncul di daftar</li>
                <li>Icon extension muncul di toolbar Chrome</li>
                <li>Status "Enabled" pada extension</li>
            </ul>
            <button onclick="testExtension()">🧪 Test Extension</button>
        </div>

        <div class="step">
            <h3>Step 5: Test Connection</h3>
            <p>Untuk memastikan extension terhubung dengan backend:</p>
            <ol>
                <li>Buka aplikasi frontend di <code>http://localhost:5173</code></li>
                <li>Coba login dengan email apapun</li>
                <li>Extension akan menampilkan notifikasi OTP</li>
                <li>Klik "Login" untuk approve</li>
            </ol>
            <button onclick="openFrontend()">🚀 Open Frontend</button>
            <button onclick="openTestPage()">🔧 Open Test Page</button>
        </div>

        <div class="warning">
            <h3>🔧 Troubleshooting</h3>
            <p><strong>Jika extension tidak muncul:</strong></p>
            <ul>
                <li>Pastikan Developer mode sudah diaktifkan</li>
                <li>Refresh halaman chrome://extensions/</li>
                <li>Cek console untuk error messages</li>
                <li>Pastikan folder chrome-extension dipilih dengan benar</li>
            </ul>
            
            <p><strong>Jika extension error:</strong></p>
            <ul>
                <li>Klik "Reload" pada extension di halaman extensions</li>
                <li>Cek background page untuk error logs</li>
                <li>Pastikan backend berjalan di port 5001</li>
            </ul>
        </div>

        <div class="success">
            <h3>✅ Extension Successfully Installed!</h3>
            <p>Jika semua langkah berhasil, extension siap digunakan untuk autentikasi OTP.</p>
            <p><strong>Next steps:</strong></p>
            <ul>
                <li>Test koneksi dengan backend</li>
                <li>Coba flow login lengkap</li>
                <li>Verifikasi notifikasi OTP berfungsi</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔍 Debug Information</h3>
            <p>Untuk debugging, Anda dapat:</p>
            <ul>
                <li>Klik kanan pada icon extension → "Inspect popup"</li>
                <li>Di halaman extensions, klik "background page" untuk melihat logs</li>
                <li>Buka Developer Tools di frontend untuk melihat komunikasi</li>
            </ul>
            <button onclick="getDebugInfo()">📊 Get Debug Info</button>
            <div id="debug-output"></div>
        </div>
    </div>

    <script>
        function openExtensionsPage() {
            // Chrome extensions page cannot be opened programmatically for security reasons
            alert('Please manually navigate to: chrome://extensions/\n\nOr use Chrome Menu → More Tools → Extensions');
        }

        function openFrontend() {
            window.open('http://localhost:5173', '_blank');
        }

        function openTestPage() {
            window.open('file://' + window.location.pathname.replace('install-extension-guide.html', 'test-extension-backend-connection.html'), '_blank');
        }

        function testExtension() {
            // Check if extension is available
            if (typeof window.progressDashboardExtension !== 'undefined') {
                alert('✅ Extension is installed and available!\n\nYou can now test the OTP authentication flow.');
            } else {
                alert('❌ Extension not detected.\n\nPlease make sure:\n1. Extension is installed\n2. Extension is enabled\n3. Page is refreshed after installation');
            }
        }

        function getDebugInfo() {
            const debugOutput = document.getElementById('debug-output');
            
            const info = {
                extensionAvailable: typeof window.progressDashboardExtension !== 'undefined',
                fallbackAvailable: typeof window.__progressDashboardExtensionBridge__ !== 'undefined',
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: new Date().toISOString()
            };

            if (typeof window.progressDashboardExtension !== 'undefined') {
                try {
                    const bridge = window.progressDashboardExtension;
                    info.bridgeMethods = Object.keys(bridge);
                    
                    if (bridge.getDebugInfo) {
                        info.extensionDebugInfo = bridge.getDebugInfo();
                    }
                } catch (error) {
                    info.bridgeError = error.message;
                }
            }

            debugOutput.innerHTML = `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 15px;">
                    <h4>Debug Information:</h4>
                    <pre style="background: white; padding: 10px; border-radius: 3px; overflow-x: auto;">${JSON.stringify(info, null, 2)}</pre>
                </div>
            `;
        }

        // Auto-check extension on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (typeof window.progressDashboardExtension !== 'undefined') {
                    console.log('✅ Extension detected on page load');
                } else {
                    console.log('❌ Extension not detected on page load');
                }
            }, 1000);
        });
    </script>
</body>
</html>
