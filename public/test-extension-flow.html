<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🧪 Test Chrome Extension Flow</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      min-height: 100vh;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 40px;
      border-radius: 16px;
      box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #059669;
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 20px;
    }
    
    .test-section {
      background: #f0fdf4;
      border: 2px solid #10b981;
      border-radius: 12px;
      padding: 25px;
      margin: 20px 0;
    }
    
    .test-section h3 {
      color: #047857;
      margin-top: 0;
    }
    
    button {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      margin: 10px 5px;
      transition: all 0.3s ease;
    }
    
    button:hover {
      background: linear-gradient(135deg, #059669 0%, #047857 100%);
      transform: translateY(-2px);
    }
    
    button:disabled {
      background: #9ca3af;
      cursor: not-allowed;
      transform: none;
    }
    
    .status {
      padding: 15px;
      border-radius: 8px;
      margin: 15px 0;
      font-weight: 500;
    }
    
    .status.success {
      background: #ecfdf5;
      border: 2px solid #10b981;
      color: #047857;
    }
    
    .status.error {
      background: #fef2f2;
      border: 2px solid #ef4444;
      color: #dc2626;
    }
    
    .status.warning {
      background: #fef3c7;
      border: 2px solid #f59e0b;
      color: #92400e;
    }
    
    .log {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 14px;
      max-height: 300px;
      overflow-y: auto;
      margin: 15px 0;
    }
    
    .progress {
      width: 100%;
      height: 8px;
      background: #e2e8f0;
      border-radius: 4px;
      overflow: hidden;
      margin: 15px 0;
    }
    
    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #10b981, #059669);
      width: 0%;
      transition: width 0.3s ease;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🧪 Extension Flow Test</h1>
    <p style="text-align: center; font-size: 1.2rem; color: #64748b;">
      Test the complete Chrome Extension OTP flow
    </p>

    <!-- Extension Detection -->
    <div class="test-section">
      <h3>🔍 Extension Detection</h3>
      <p>Check if the Chrome Extension is properly installed and available:</p>
      <button onclick="testExtensionDetection()">Test Extension Detection</button>
      <div id="detectionStatus"></div>
    </div>

    <!-- Extension Info -->
    <div class="test-section">
      <h3>ℹ️ Extension Information</h3>
      <p>Get detailed information about the extension:</p>
      <button onclick="getExtensionInfo()">Get Extension Info</button>
      <div id="extensionInfo"></div>
    </div>

    <!-- OTP Generation Test -->
    <div class="test-section">
      <h3>🔐 OTP Generation Test</h3>
      <p>Test OTP generation and extension communication:</p>
      <input type="email" id="testEmail" placeholder="Enter test email" value="<EMAIL>" 
             style="padding: 10px; border: 2px solid #e2e8f0; border-radius: 6px; margin: 10px; width: 250px;">
      <br>
      <button onclick="testOTPGeneration()">Generate Test OTP</button>
      <button onclick="simulateExtensionResponse('APPROVE')">Simulate Approve</button>
      <button onclick="simulateExtensionResponse('REJECT')">Simulate Reject</button>
      <div id="otpTestStatus"></div>
    </div>

    <!-- Complete Flow Test -->
    <div class="test-section">
      <h3>🚀 Complete Flow Test</h3>
      <p>Test the complete authentication flow:</p>
      <button onclick="testCompleteFlow()">Test Complete Flow</button>
      <div class="progress">
        <div id="flowProgress" class="progress-bar"></div>
      </div>
      <div id="flowStatus"></div>
    </div>

    <!-- Debug Console -->
    <div class="test-section">
      <h3>🐛 Debug Console</h3>
      <p>Real-time debug information:</p>
      <button onclick="clearLog()">Clear Log</button>
      <button onclick="enableVerboseLogging()">Enable Verbose Logging</button>
      <div id="debugLog" class="log">Debug log will appear here...</div>
    </div>

    <!-- Quick Actions -->
    <div class="test-section">
      <h3>⚡ Quick Actions</h3>
      <button onclick="window.open('http://localhost:5173/login', '_blank')">Open Login Page</button>
      <button onclick="window.open('chrome://extensions/', '_blank')">Open Extensions</button>
      <button onclick="window.open('http://localhost:5173/chrome-extension-setup.html', '_blank')">Setup Guide</button>
      <button onclick="refreshPage()">Refresh Page</button>
    </div>
  </div>

  <script>
    let verboseLogging = false;
    let testResults = {};

    function log(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      const logElement = document.getElementById('debugLog');
      const colorMap = {
        info: '#e2e8f0',
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b'
      };
      
      logElement.innerHTML += `<div style="color: ${colorMap[type]}">[${timestamp}] ${message}</div>`;
      logElement.scrollTop = logElement.scrollHeight;
      
      if (verboseLogging) {
        console.log(`[Extension Test] ${message}`);
      }
    }

    function clearLog() {
      document.getElementById('debugLog').innerHTML = '';
      log('Debug log cleared', 'info');
    }

    function enableVerboseLogging() {
      verboseLogging = !verboseLogging;
      log(`Verbose logging ${verboseLogging ? 'enabled' : 'disabled'}`, 'info');
    }

    function showStatus(elementId, message, type) {
      const element = document.getElementById(elementId);
      element.innerHTML = `<div class="status ${type}">${message}</div>`;
    }

    async function testExtensionDetection() {
      log('Testing extension detection...', 'info');
      
      try {
        // Check if extension bridge is available
        if (window.progressDashboardExtension) {
          const info = window.progressDashboardExtension.getDebugInfo();
          showStatus('detectionStatus', 
            `✅ Extension detected successfully!<br>
             Version: ${info.version}<br>
             Injection Method: ${info.injectionMethod}<br>
             Available: ${info.isAvailable}`, 'success');
          log('Extension detection successful', 'success');
          testResults.detection = true;
        } else {
          showStatus('detectionStatus', 
            '❌ Extension not detected. Please ensure:<br>
             • Extension is installed<br>
             • Page is refreshed<br>
             • Extension has proper permissions', 'error');
          log('Extension detection failed', 'error');
          testResults.detection = false;
        }
      } catch (error) {
        showStatus('detectionStatus', `❌ Error: ${error.message}`, 'error');
        log(`Extension detection error: ${error.message}`, 'error');
        testResults.detection = false;
      }
    }

    async function getExtensionInfo() {
      log('Getting extension information...', 'info');
      
      try {
        if (!window.progressDashboardExtension) {
          throw new Error('Extension not available');
        }

        const info = await window.progressDashboardExtension.getInfo();
        showStatus('extensionInfo', 
          `📋 Extension Information:<br>
           • Version: ${info.version || 'Unknown'}<br>
           • Status: ${info.isActive ? 'Active' : 'Inactive'}<br>
           • Active Requests: ${info.hasActiveRequest ? 'Yes' : 'No'}`, 'success');
        log('Extension info retrieved successfully', 'success');
        testResults.info = true;
      } catch (error) {
        showStatus('extensionInfo', `❌ Error: ${error.message}`, 'error');
        log(`Extension info error: ${error.message}`, 'error');
        testResults.info = false;
      }
    }

    async function testOTPGeneration() {
      const email = document.getElementById('testEmail').value;
      if (!email) {
        showStatus('otpTestStatus', '❌ Please enter an email address', 'error');
        return;
      }

      log(`Testing OTP generation for ${email}...`, 'info');
      
      try {
        // Call backend API to generate OTP
        const response = await fetch('http://localhost:5001/api/auth/generate-otp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email })
        });

        const result = await response.json();
        
        if (result.success) {
          showStatus('otpTestStatus', 
            `✅ OTP Generated Successfully!<br>
             • Email: ${result.email}<br>
             • OTP Key: ${result.otp_key}<br>
             • Expires In: ${result.expires_in}s<br>
             • OTP Code: ${result.otp_code}`, 'success');
          log('OTP generation successful', 'success');
          testResults.otpGeneration = true;

          // Try to send to extension
          if (window.progressDashboardExtension) {
            try {
              const extensionResponse = await window.progressDashboardExtension.sendMessage({
                type: 'OTP_REQUEST',
                data: {
                  email: result.email,
                  otp_code: result.otp_code,
                  otp_key: result.otp_key,
                  expires_in: result.expires_in,
                  website: window.location.hostname
                }
              });
              log('OTP sent to extension successfully', 'success');
            } catch (extError) {
              log(`Extension communication error: ${extError.message}`, 'warning');
            }
          }
        } else {
          throw new Error(result.error || 'OTP generation failed');
        }
      } catch (error) {
        showStatus('otpTestStatus', `❌ Error: ${error.message}`, 'error');
        log(`OTP generation error: ${error.message}`, 'error');
        testResults.otpGeneration = false;
      }
    }

    function simulateExtensionResponse(action) {
      log(`Simulating extension response: ${action}`, 'info');
      
      // Simulate extension response
      window.postMessage({
        type: 'OTP_RESPONSE',
        data: {
          action: action,
          email: document.getElementById('testEmail').value,
          otp_code: '123456',
          otp_key: 'test-key'
        },
        timestamp: Date.now()
      }, window.location.origin);
      
      log(`Extension response simulated: ${action}`, 'success');
    }

    async function testCompleteFlow() {
      log('Starting complete flow test...', 'info');
      const progressBar = document.getElementById('flowProgress');
      let progress = 0;

      function updateProgress(percent, message) {
        progress = percent;
        progressBar.style.width = `${percent}%`;
        showStatus('flowStatus', message, percent === 100 ? 'success' : 'warning');
        log(message, percent === 100 ? 'success' : 'info');
      }

      try {
        updateProgress(20, '🔍 Checking extension availability...');
        await new Promise(resolve => setTimeout(resolve, 500));
        
        if (!window.progressDashboardExtension) {
          throw new Error('Extension not available');
        }

        updateProgress(40, '🔐 Generating OTP...');
        await testOTPGeneration();
        await new Promise(resolve => setTimeout(resolve, 1000));

        updateProgress(60, '📱 Waiting for extension response...');
        await new Promise(resolve => setTimeout(resolve, 1000));

        updateProgress(80, '✅ Validating response...');
        await new Promise(resolve => setTimeout(resolve, 500));

        updateProgress(100, '🎉 Complete flow test successful!');
        log('Complete flow test completed successfully', 'success');
        testResults.completeFlow = true;

      } catch (error) {
        updateProgress(0, `❌ Flow test failed: ${error.message}`);
        log(`Complete flow test error: ${error.message}`, 'error');
        testResults.completeFlow = false;
      }
    }

    function refreshPage() {
      log('Refreshing page...', 'info');
      window.location.reload();
    }

    // Auto-run extension detection on page load
    window.addEventListener('load', () => {
      log('Page loaded, starting auto-detection...', 'info');
      setTimeout(testExtensionDetection, 1000);
    });

    // Listen for extension messages
    window.addEventListener('message', (event) => {
      if (event.origin !== window.location.origin) return;
      
      if (event.data && event.data.type && event.data.type.startsWith('OTP_')) {
        log(`Received extension message: ${event.data.type}`, 'info');
      }
    });

    // Listen for extension ready event
    window.addEventListener('progressDashboardExtensionReady', (event) => {
      log('Extension ready event received', 'success');
      setTimeout(testExtensionDetection, 500);
    });
  </script>
</body>
</html>
