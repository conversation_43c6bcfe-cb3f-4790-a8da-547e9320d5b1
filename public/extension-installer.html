<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chrome Extension Installer Guide</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: #f8fafc;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #1e293b;
      margin-bottom: 20px;
    }
    
    .step {
      margin: 25px 0;
      padding: 20px;
      background: #f1f5f9;
      border-left: 4px solid #95E565;
      border-radius: 0 8px 8px 0;
    }
    
    .step h3 {
      margin: 0 0 15px 0;
      color: #1e293b;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .step-number {
      background: #95E565;
      color: white;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 14px;
    }
    
    .code-block {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 13px;
      overflow-x: auto;
      margin: 10px 0;
      word-break: break-all;
    }
    
    .warning {
      background: #fef3c7;
      border: 1px solid #fde68a;
      color: #d97706;
      padding: 15px;
      border-radius: 6px;
      margin: 15px 0;
    }
    
    .success {
      background: #dcfce7;
      border: 1px solid #bbf7d0;
      color: #166534;
      padding: 15px;
      border-radius: 6px;
      margin: 15px 0;
    }
    
    .error {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
      padding: 15px;
      border-radius: 6px;
      margin: 15px 0;
    }
    
    button {
      background: #95E565;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin: 10px 5px;
      transition: background 0.2s;
      font-weight: 500;
    }
    
    button:hover {
      background: #608F44;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      margin: 20px 0;
    }
    
    .checklist {
      list-style: none;
      padding: 0;
    }
    
    .checklist li {
      margin: 10px 0;
      padding: 10px;
      background: white;
      border-radius: 6px;
      border-left: 3px solid #e2e8f0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .checklist li.done {
      border-left-color: #22c55e;
      background: #f0fdf4;
    }
    
    .screenshot {
      max-width: 100%;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      margin: 15px 0;
    }
    
    .highlight {
      background: #fef3c7;
      padding: 2px 6px;
      border-radius: 4px;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔧 Chrome Extension Installation Guide</h1>
    <p>Follow these exact steps to install the Progress Dashboard Chrome Extension.</p>
    
    <div class="error">
      <strong>Current Issue:</strong> Extension bridge not found - Extension not properly installed or loaded.
    </div>

    <!-- Step 1 -->
    <div class="step">
      <h3>
        <span class="step-number">1</span>
        Open Chrome Extensions Page
      </h3>
      <p>Open a new tab in Chrome and navigate to the extensions management page:</p>
      <div class="code-block">chrome://extensions/</div>
      <div class="button-group">
        <button onclick="openExtensionsPage()">📂 Open Extensions Page</button>
        <button onclick="copyToClipboard('chrome://extensions/')">📋 Copy URL</button>
      </div>
      <div class="warning">
        <strong>Important:</strong> You must type this URL manually in Chrome's address bar, or use the button above.
      </div>
    </div>

    <!-- Step 2 -->
    <div class="step">
      <h3>
        <span class="step-number">2</span>
        Enable Developer Mode
      </h3>
      <p>In the top-right corner of the Extensions page, find and enable <span class="highlight">"Developer mode"</span> toggle switch.</p>
      <ul class="checklist">
        <li>Look for "Developer mode" toggle in top-right corner</li>
        <li>Click the toggle to enable it (should turn blue/green)</li>
        <li>New buttons will appear: "Load unpacked", "Pack extension", "Update"</li>
      </ul>
    </div>

    <!-- Step 3 -->
    <div class="step">
      <h3>
        <span class="step-number">3</span>
        Load Extension
      </h3>
      <p>Click the <span class="highlight">"Load unpacked"</span> button and select the extension folder:</p>
      <div class="code-block">/Users/<USER>/Library/CloudStorage/Dropbox/Assets/Apps/Dashboard/Progress_Dashboard-main/chrome-extension/</div>
      <div class="button-group">
        <button onclick="copyToClipboard('/Users/<USER>/Library/CloudStorage/Dropbox/Assets/Apps/Dashboard/Progress_Dashboard-main/chrome-extension/')">📋 Copy Folder Path</button>
        <button onclick="openFinder()">📁 Open in Finder</button>
      </div>
      <ul class="checklist">
        <li>Click "Load unpacked" button</li>
        <li>Navigate to the folder path above</li>
        <li>Select the <strong>chrome-extension</strong> folder (not a file inside it)</li>
        <li>Click "Select" or "Open"</li>
      </ul>
    </div>

    <!-- Step 4 -->
    <div class="step">
      <h3>
        <span class="step-number">4</span>
        Verify Installation
      </h3>
      <p>After loading, you should see the extension card with these details:</p>
      <ul class="checklist">
        <li><strong>Name:</strong> "Progress Dashboard - OTP Authenticator"</li>
        <li><strong>Version:</strong> "1.0.0"</li>
        <li><strong>Status:</strong> Enabled (not grayed out)</li>
        <li><strong>Icon:</strong> Green shield icon in Chrome toolbar</li>
        <li><strong>No Errors:</strong> No red error messages</li>
      </ul>
    </div>

    <!-- Step 5 -->
    <div class="step">
      <h3>
        <span class="step-number">5</span>
        Grant Permissions
      </h3>
      <p>Click <span class="highlight">"Details"</span> on the extension card and verify permissions:</p>
      <ul class="checklist">
        <li>✅ <strong>Site access:</strong> "On localhost"</li>
        <li>✅ <strong>Notifications:</strong> "Display notifications"</li>
        <li>✅ <strong>Storage:</strong> "Store data"</li>
      </ul>
      <div class="warning">
        If any permissions are missing, click "Allow" or enable them manually.
      </div>
    </div>

    <!-- Step 6 -->
    <div class="step">
      <h3>
        <span class="step-number">6</span>
        Test Extension
      </h3>
      <p>Return to the test page and verify the extension is working:</p>
      <div class="button-group">
        <button onclick="openTestPage()">🧪 Open Test Page</button>
        <button onclick="reloadCurrentPage()">🔄 Reload This Page</button>
      </div>
      <ul class="checklist">
        <li>Go to test page: <code>http://localhost:5173/debug-extension.html</code></li>
        <li>Click "Run Full Diagnosis"</li>
        <li>Should show: "✅ Extension bridge available"</li>
        <li>Should show: "✅ Extension working correctly"</li>
      </ul>
    </div>

    <!-- Troubleshooting -->
    <div class="step">
      <h3>
        <span class="step-number">⚠️</span>
        If Still Not Working
      </h3>
      <div class="error">
        <strong>Common Issues & Solutions:</strong>
      </div>
      
      <h4>Issue 1: Extension Card Shows Errors</h4>
      <ul>
        <li>Remove extension and reinstall</li>
        <li>Check folder path is correct</li>
        <li>Ensure all files are present</li>
      </ul>

      <h4>Issue 2: No Console Messages</h4>
      <ul>
        <li>Refresh extension (click 🔄 on extension card)</li>
        <li>Refresh test page</li>
        <li>Check browser console for errors</li>
      </ul>

      <h4>Issue 3: Permissions Denied</h4>
      <ul>
        <li>Go to extension Details</li>
        <li>Enable all permissions manually</li>
        <li>Restart Chrome</li>
      </ul>

      <div class="button-group">
        <button onclick="clearCacheInstructions()">🗑️ Clear Cache Instructions</button>
        <button onclick="restartChromeInstructions()">🔄 Restart Chrome Instructions</button>
      </div>
    </div>

    <!-- Success Check -->
    <div class="success" id="successMessage" style="display: none;">
      <strong>✅ Success!</strong> Extension should now be working. Go to the login page to test OTP authentication.
    </div>

    <!-- Quick Actions -->
    <div class="step">
      <h3>
        <span class="step-number">🚀</span>
        Quick Actions
      </h3>
      <div class="button-group">
        <button onclick="openExtensionsPage()">📂 Extensions Page</button>
        <button onclick="openTestPage()">🧪 Test Page</button>
        <button onclick="openLoginPage()">🔐 Login Page</button>
        <button onclick="checkExtensionStatus()">✅ Check Status</button>
      </div>
    </div>
  </div>

  <script>
    // Helper functions
    function openExtensionsPage() {
      window.open('chrome://extensions/', '_blank');
    }

    function openTestPage() {
      window.open('http://localhost:5173/debug-extension.html', '_blank');
    }

    function openLoginPage() {
      window.open('http://localhost:5173/login', '_blank');
    }

    function reloadCurrentPage() {
      window.location.reload();
    }

    function openFinder() {
      alert('To open in Finder:\n1. Open Finder\n2. Press Cmd+Shift+G\n3. Paste the folder path\n4. Press Enter');
    }

    function copyToClipboard(text) {
      navigator.clipboard.writeText(text).then(() => {
        alert('Copied to clipboard: ' + text);
      }).catch(() => {
        prompt('Copy this path:', text);
      });
    }

    function clearCacheInstructions() {
      alert('To clear Chrome cache:\n1. Press Ctrl+Shift+Delete (or Cmd+Shift+Delete on Mac)\n2. Select "Cached images and files"\n3. Click "Clear data"\n4. Refresh test page');
    }

    function restartChromeInstructions() {
      alert('To restart Chrome:\n1. Close all Chrome windows\n2. Wait 5 seconds\n3. Open Chrome again\n4. Go to chrome://extensions/\n5. Verify extension is still enabled');
    }

    function checkExtensionStatus() {
      if (typeof window.progressDashboardExtension !== 'undefined') {
        document.getElementById('successMessage').style.display = 'block';
        alert('✅ Extension is working! Bridge detected.');
      } else {
        alert('❌ Extension bridge still not found. Please follow the installation steps above.');
      }
    }

    // Auto-check extension status
    setTimeout(() => {
      if (typeof window.progressDashboardExtension !== 'undefined') {
        document.getElementById('successMessage').style.display = 'block';
      }
    }, 2000);
  </script>
</body>
</html>
