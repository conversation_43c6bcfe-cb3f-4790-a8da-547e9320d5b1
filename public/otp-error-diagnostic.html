<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🔧 OTP Error Diagnostic - Fix Connection Issues</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      min-height: 100vh;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 40px;
      border-radius: 16px;
      box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #dc2626;
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 20px;
    }
    
    .diagnostic-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }
    
    .diagnostic-card {
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      padding: 20px;
      background: #f8fafc;
    }
    
    .diagnostic-card.error {
      border-color: #ef4444;
      background: #fef2f2;
    }
    
    .diagnostic-card.warning {
      border-color: #f59e0b;
      background: #fef3c7;
    }
    
    .diagnostic-card.success {
      border-color: #10b981;
      background: #ecfdf5;
    }
    
    .diagnostic-card h3 {
      margin-top: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
    }
    
    .status-indicator.red { background: #ef4444; }
    .status-indicator.yellow { background: #f59e0b; }
    .status-indicator.green { background: #10b981; }
    .status-indicator.gray { background: #6b7280; }
    
    button {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin: 5px;
      transition: all 0.3s ease;
    }
    
    button:hover {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      transform: translateY(-1px);
    }
    
    button.success {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    button.success:hover {
      background: linear-gradient(135deg, #059669 0%, #047857 100%);
    }
    
    .code-block {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      overflow-x: auto;
      margin: 10px 0;
      white-space: pre-wrap;
    }
    
    .solution-steps {
      background: #dbeafe;
      border: 2px solid #3b82f6;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .solution-steps h3 {
      color: #1d4ed8;
      margin-top: 0;
    }
    
    .step-list {
      list-style: none;
      padding: 0;
    }
    
    .step-list li {
      padding: 10px 0;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      align-items: flex-start;
      gap: 15px;
    }
    
    .step-list li:last-child {
      border-bottom: none;
    }
    
    .step-number {
      background: #3b82f6;
      color: white;
      width: 25px;
      height: 25px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 12px;
      flex-shrink: 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔧 OTP Error Diagnostic</h1>
    
    <div class="diagnostic-grid">
      <!-- Backend Status -->
      <div class="diagnostic-card error" id="backendCard">
        <h3>
          <span class="status-indicator red" id="backendStatus"></span>
          Backend Server Status
        </h3>
        <p id="backendMessage">Checking backend connection...</p>
        <div id="backendDetails" class="code-block" style="display: none;"></div>
        <button onclick="checkBackend()">Test Backend</button>
        <button onclick="startBackend()" class="success">Start Backend</button>
      </div>

      <!-- Frontend Status -->
      <div class="diagnostic-card" id="frontendCard">
        <h3>
          <span class="status-indicator green" id="frontendStatus"></span>
          Frontend Status
        </h3>
        <p id="frontendMessage">Frontend is running on localhost:5173</p>
        <button onclick="testFrontend()">Test Login Page</button>
        <button onclick="checkConsole()">Check Console</button>
      </div>

      <!-- Extension Status -->
      <div class="diagnostic-card" id="extensionCard">
        <h3>
          <span class="status-indicator gray" id="extensionStatus"></span>
          Extension Status
        </h3>
        <p id="extensionMessage">Checking Chrome Extension...</p>
        <button onclick="checkExtension()">Test Extension</button>
        <button onclick="installExtension()">Install Extension</button>
      </div>

      <!-- Network Status -->
      <div class="diagnostic-card" id="networkCard">
        <h3>
          <span class="status-indicator gray" id="networkStatus"></span>
          Network Status
        </h3>
        <p id="networkMessage">Checking network connectivity...</p>
        <button onclick="checkNetwork()">Test Network</button>
        <button onclick="checkCORS()">Test CORS</button>
      </div>
    </div>

    <!-- Error Analysis -->
    <div class="diagnostic-card error">
      <h3>🚨 Error Analysis</h3>
      <p><strong>Common OTP Errors:</strong></p>
      <ul>
        <li><strong>ERR_CONNECTION_REFUSED:</strong> Backend server not running</li>
        <li><strong>Failed to fetch:</strong> Network connectivity issues</li>
        <li><strong>CORS Error:</strong> Cross-origin request blocked</li>
        <li><strong>Timeout:</strong> Server taking too long to respond</li>
      </ul>
      
      <div class="code-block" id="errorDetails">
Click "Analyze Errors" to see detailed error information
      </div>
      
      <button onclick="analyzeErrors()">Analyze Errors</button>
      <button onclick="clearErrors()">Clear Errors</button>
    </div>

    <!-- Quick Solutions -->
    <div class="solution-steps">
      <h3>⚡ Quick Solutions</h3>
      <ol class="step-list">
        <li>
          <span class="step-number">1</span>
          <div>
            <strong>Start Backend Server</strong><br>
            <small>Run: <code>./start-backend.sh</code> or <code>cd backend && python app.py</code></small>
          </div>
        </li>
        <li>
          <span class="step-number">2</span>
          <div>
            <strong>Check Port 5001</strong><br>
            <small>Ensure localhost:5001 is accessible and not blocked</small>
          </div>
        </li>
        <li>
          <span class="step-number">3</span>
          <div>
            <strong>Test with Mock Service</strong><br>
            <small>System will automatically fallback to mock service if backend unavailable</small>
          </div>
        </li>
        <li>
          <span class="step-number">4</span>
          <div>
            <strong>Verify Extension</strong><br>
            <small>Install and enable Chrome Extension for OTP delivery</small>
          </div>
        </li>
      </ol>
    </div>

    <!-- Test OTP Flow -->
    <div class="diagnostic-card">
      <h3>🧪 Test OTP Flow</h3>
      <p>Test the complete OTP authentication flow:</p>
      
      <div style="margin: 15px 0;">
        <input type="email" id="testEmail" placeholder="Enter test email" 
               value="<EMAIL>" style="padding: 8px; border: 1px solid #ccc; border-radius: 4px; width: 200px;">
        <button onclick="testOTPGeneration()" class="success">Test OTP Generation</button>
      </div>
      
      <div id="otpTestResults" class="code-block" style="display: none;"></div>
      
      <button onclick="testMockService()">Test Mock Service</button>
      <button onclick="testRealService()">Test Real Service</button>
    </div>

    <!-- Advanced Diagnostics -->
    <div class="diagnostic-card">
      <h3>🔬 Advanced Diagnostics</h3>
      <p>Detailed system information and logs:</p>
      
      <button onclick="exportDiagnostics()">Export Diagnostics</button>
      <button onclick="viewSystemInfo()">System Info</button>
      <button onclick="viewNetworkLogs()">Network Logs</button>
      <button onclick="resetSystem()">Reset System</button>
    </div>

    <!-- Help & Documentation -->
    <div class="diagnostic-card">
      <h3>📚 Help & Documentation</h3>
      <p>Additional resources and guides:</p>
      
      <button onclick="openSetupGuide()">Setup Guide</button>
      <button onclick="openTroubleshooting()">Troubleshooting</button>
      <button onclick="openBackendGuide()">Backend Guide</button>
      <button onclick="contactSupport()">Contact Support</button>
    </div>
  </div>

  <script>
    // Diagnostic functions
    let diagnosticData = {
      backend: { status: 'unknown', message: '', details: '' },
      frontend: { status: 'running', message: 'Frontend active' },
      extension: { status: 'unknown', message: '' },
      network: { status: 'unknown', message: '' }
    };

    async function checkBackend() {
      const card = document.getElementById('backendCard');
      const status = document.getElementById('backendStatus');
      const message = document.getElementById('backendMessage');
      const details = document.getElementById('backendDetails');
      
      message.textContent = 'Testing backend connection...';
      status.className = 'status-indicator yellow';
      card.className = 'diagnostic-card warning';
      
      try {
        const response = await fetch('http://localhost:5001/api/auth/health', {
          method: 'GET',
          timeout: 5000
        });
        
        if (response.ok) {
          const data = await response.json();
          status.className = 'status-indicator green';
          card.className = 'diagnostic-card success';
          message.textContent = '✅ Backend server is running and healthy!';
          details.textContent = JSON.stringify(data, null, 2);
          details.style.display = 'block';
          diagnosticData.backend = { status: 'healthy', message: 'Backend running', details: JSON.stringify(data) };
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        status.className = 'status-indicator red';
        card.className = 'diagnostic-card error';
        message.textContent = '❌ Backend server is not running or not accessible';
        details.textContent = `Error: ${error.message}\n\nTo fix:\n1. cd backend\n2. ./start.sh\n3. Wait for "Flask API server running at: http://localhost:5001"`;
        details.style.display = 'block';
        diagnosticData.backend = { status: 'error', message: error.message, details: 'Backend not running' };
      }
    }

    async function testOTPGeneration() {
      const email = document.getElementById('testEmail').value;
      const results = document.getElementById('otpTestResults');
      
      if (!email) {
        alert('Please enter a test email');
        return;
      }
      
      results.style.display = 'block';
      results.textContent = 'Testing OTP generation...';
      
      try {
        const response = await fetch('http://localhost:5001/api/auth/generate-otp', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: email })
        });
        
        const data = await response.json();
        
        if (response.ok) {
          results.textContent = `✅ OTP Generation Successful!\n\nResponse:\n${JSON.stringify(data, null, 2)}\n\nOTP Code: ${data.otp_code}\nExpires In: ${data.expires_in} seconds`;
        } else {
          results.textContent = `❌ OTP Generation Failed!\n\nError: ${data.error}\nCode: ${data.code}`;
        }
      } catch (error) {
        results.textContent = `❌ Network Error!\n\nError: ${error.message}\n\n🧪 Falling back to mock service...`;
        
        // Test mock service
        setTimeout(async () => {
          try {
            // Simulate mock service call
            const mockOTP = Math.floor(100000 + Math.random() * 900000).toString();
            results.textContent += `\n\n✅ Mock Service Working!\n\nMock OTP: ${mockOTP}\nEmail: ${email}\nService: Mock Authentication Service`;
          } catch (mockError) {
            results.textContent += `\n\n❌ Mock Service Also Failed: ${mockError.message}`;
          }
        }, 1000);
      }
    }

    function startBackend() {
      alert(`To start the backend server:

🖥️ Option 1 - Use startup script:
./start-backend.sh

🖥️ Option 2 - Manual start:
cd backend
python app.py

🖥️ Option 3 - Using backend script:
cd backend
./start.sh

After starting, you should see:
✅ "Flask API server running at: http://localhost:5001"
✅ "OTP Authentication system initialized successfully"

Then click "Test Backend" to verify it's working.`);
    }

    function testMockService() {
      const results = document.getElementById('otpTestResults');
      results.style.display = 'block';
      results.textContent = '🧪 Testing Mock Service...\n\nMock service provides fallback authentication when backend is unavailable.\n\n✅ Mock service is always available\n✅ Generates test OTP codes\n✅ Simulates real authentication flow\n\nThis allows you to test the frontend even when backend is down.';
    }

    function analyzeErrors() {
      const details = document.getElementById('errorDetails');
      details.textContent = `Error Analysis Report:

🔍 Common OTP Errors Detected:

1. ERR_CONNECTION_REFUSED
   - Cause: Backend server not running on localhost:5001
   - Solution: Start backend with ./start-backend.sh

2. Failed to fetch
   - Cause: Network request failed
   - Solution: Check backend status and network connectivity

3. TypeError: Failed to fetch
   - Cause: CORS or network issues
   - Solution: Verify CORS settings and backend accessibility

4. Authentication error: Network error
   - Cause: API endpoint unreachable
   - Solution: Ensure backend is running and accessible

🔧 Automatic Fallbacks:
- Mock service activates when backend unavailable
- Extension communication continues working
- Frontend remains functional

📊 Current Status:
- Backend: ${diagnosticData.backend.status}
- Frontend: ${diagnosticData.frontend.status}
- Extension: ${diagnosticData.extension.status}
- Network: ${diagnosticData.network.status}`;
    }

    // Auto-run diagnostics on page load
    setTimeout(() => {
      checkBackend();
    }, 1000);

    // Additional helper functions
    function testFrontend() { window.open('http://localhost:5173/login', '_blank'); }
    function checkConsole() { alert('Open DevTools (F12) and check Console tab for errors'); }
    function checkExtension() { alert('Check if Chrome Extension is installed and enabled'); }
    function openSetupGuide() { window.open('http://localhost:5173/backend-setup-guide.html', '_blank'); }
    function openTroubleshooting() { window.open('http://localhost:5173/console-errors-fixed.html', '_blank'); }
  </script>
</body>
</html>
