<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🔍 Debug Extension Communication</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      min-height: 100vh;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 40px;
      border-radius: 16px;
      box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #dc2626;
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 20px;
    }
    
    .test-section {
      background: #fef2f2;
      border: 2px solid #ef4444;
      border-radius: 12px;
      padding: 25px;
      margin: 20px 0;
    }
    
    .test-section h3 {
      color: #dc2626;
      margin-top: 0;
    }
    
    button {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      margin: 10px 5px;
      transition: all 0.3s ease;
    }
    
    button:hover {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      transform: translateY(-2px);
    }
    
    .status {
      padding: 15px;
      border-radius: 8px;
      margin: 15px 0;
      font-weight: 500;
    }
    
    .status.success {
      background: #ecfdf5;
      border: 2px solid #10b981;
      color: #047857;
    }
    
    .status.error {
      background: #fef2f2;
      border: 2px solid #ef4444;
      color: #dc2626;
    }
    
    .status.warning {
      background: #fef3c7;
      border: 2px solid #f59e0b;
      color: #92400e;
    }
    
    .log {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 14px;
      max-height: 400px;
      overflow-y: auto;
      margin: 15px 0;
      white-space: pre-wrap;
    }
    
    .code-block {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 14px;
      overflow-x: auto;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔍 Debug Extension Communication</h1>
    <p style="text-align: center; font-size: 1.2rem; color: #64748b;">
      Step-by-step debugging untuk Chrome Extension communication
    </p>

    <!-- Step 1: Extension Detection -->
    <div class="test-section">
      <h3>1. 🔍 Extension Detection</h3>
      <p>Check apakah Chrome Extension bridge tersedia di window object:</p>
      <button onclick="testExtensionDetection()">Test Extension Detection</button>
      <div id="detectionResult"></div>
    </div>

    <!-- Step 2: Content Script Injection -->
    <div class="test-section">
      <h3>2. 📝 Content Script Injection</h3>
      <p>Check apakah content script ter-inject dengan benar:</p>
      <button onclick="testContentScriptInjection()">Test Content Script</button>
      <div id="contentScriptResult"></div>
    </div>

    <!-- Step 3: PostMessage Test -->
    <div class="test-section">
      <h3>3. 📨 PostMessage Communication</h3>
      <p>Test basic postMessage communication:</p>
      <button onclick="testPostMessage()">Test PostMessage</button>
      <button onclick="testExtensionMessage()">Test Extension Message</button>
      <div id="postMessageResult"></div>
    </div>

    <!-- Step 4: OTP Request Simulation -->
    <div class="test-section">
      <h3>4. 🔐 OTP Request Simulation</h3>
      <p>Simulate actual OTP request ke extension:</p>
      <input type="email" id="testEmail" placeholder="<EMAIL>" value="<EMAIL>" 
             style="padding: 10px; border: 2px solid #e2e8f0; border-radius: 6px; margin: 10px; width: 250px;">
      <br>
      <button onclick="simulateOTPRequest()">Simulate OTP Request</button>
      <button onclick="testExtensionAvailability()">Test Extension Availability</button>
      <div id="otpRequestResult"></div>
    </div>

    <!-- Step 5: Event Listeners -->
    <div class="test-section">
      <h3>5. 👂 Event Listeners</h3>
      <p>Check event listeners dan message handling:</p>
      <button onclick="listEventListeners()">List Event Listeners</button>
      <button onclick="testMessageListeners()">Test Message Listeners</button>
      <div id="eventListenersResult"></div>
    </div>

    <!-- Debug Console -->
    <div class="test-section">
      <h3>🐛 Debug Console</h3>
      <button onclick="clearDebugLog()">Clear Log</button>
      <button onclick="enableVerboseLogging()">Toggle Verbose</button>
      <button onclick="exportDebugInfo()">Export Debug Info</button>
      <div id="debugLog" class="log">Debug log akan muncul di sini...</div>
    </div>

    <!-- Extension Installation Guide -->
    <div class="test-section">
      <h3>📋 Extension Installation Status</h3>
      <p>Quick check untuk extension installation:</p>
      <div class="code-block">
1. Buka chrome://extensions/
2. Enable "Developer mode"
3. Click "Load unpacked"
4. Select chrome-extension folder
5. Refresh halaman ini
      </div>
      <button onclick="window.open('chrome://extensions/', '_blank')">Open Extensions Page</button>
      <button onclick="window.location.reload()">Refresh Page</button>
    </div>
  </div>

  <script>
    let verboseLogging = false;
    let messageListeners = [];

    function log(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      const debugLog = document.getElementById('debugLog');
      const colors = {
        info: '#e2e8f0',
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b'
      };
      
      debugLog.innerHTML += `<span style="color: ${colors[type]}">[${timestamp}] ${message}</span>\n`;
      debugLog.scrollTop = debugLog.scrollHeight;
      
      if (verboseLogging) {
        console.log(`[Extension Debug] ${message}`);
      }
    }

    function showResult(elementId, message, type) {
      const element = document.getElementById(elementId);
      element.innerHTML = `<div class="status ${type}">${message}</div>`;
    }

    function clearDebugLog() {
      document.getElementById('debugLog').innerHTML = '';
      log('Debug log cleared', 'info');
    }

    function enableVerboseLogging() {
      verboseLogging = !verboseLogging;
      log(`Verbose logging ${verboseLogging ? 'enabled' : 'disabled'}`, 'info');
    }

    // Test 1: Extension Detection
    function testExtensionDetection() {
      log('Testing extension detection...', 'info');
      
      try {
        // Check window object
        const hasExtensionBridge = !!window.progressDashboardExtension;
        const hasExtensionReady = !!window.progressDashboardExtensionReady;
        
        log(`Extension bridge available: ${hasExtensionBridge}`, hasExtensionBridge ? 'success' : 'error');
        log(`Extension ready event: ${hasExtensionReady}`, hasExtensionReady ? 'success' : 'warning');
        
        if (hasExtensionBridge) {
          try {
            const info = window.progressDashboardExtension.getDebugInfo();
            log(`Extension info: ${JSON.stringify(info)}`, 'success');
            
            showResult('detectionResult', 
              `✅ Extension detected!<br>
               • Bridge available: ${hasExtensionBridge}<br>
               • Version: ${info.version || 'Unknown'}<br>
               • Injection method: ${info.injectionMethod || 'Unknown'}<br>
               • Available: ${info.isAvailable}`, 'success');
          } catch (infoError) {
            log(`Extension info error: ${infoError.message}`, 'warning');
            showResult('detectionResult', 
              `⚠️ Extension bridge found but info failed<br>
               Error: ${infoError.message}`, 'warning');
          }
        } else {
          showResult('detectionResult', 
            `❌ Extension not detected<br>
             • No extension bridge in window object<br>
             • Extension may not be installed<br>
             • Content script may not be injected`, 'error');
        }
      } catch (error) {
        log(`Extension detection error: ${error.message}`, 'error');
        showResult('detectionResult', `❌ Detection failed: ${error.message}`, 'error');
      }
    }

    // Test 2: Content Script Injection
    function testContentScriptInjection() {
      log('Testing content script injection...', 'info');
      
      try {
        // Check for content script indicators
        const hasProgressDashboardExtension = !!window.progressDashboardExtension;
        const hasExtensionMarker = document.querySelector('[data-progress-dashboard-extension]');
        const hasContentScriptMarker = !!window.progressDashboardContentScript;
        
        log(`Extension object: ${hasProgressDashboardExtension}`, hasProgressDashboardExtension ? 'success' : 'error');
        log(`DOM marker: ${!!hasExtensionMarker}`, !!hasExtensionMarker ? 'success' : 'warning');
        log(`Content script marker: ${hasContentScriptMarker}`, hasContentScriptMarker ? 'success' : 'warning');
        
        // Check script tags
        const scripts = Array.from(document.scripts);
        const extensionScripts = scripts.filter(script => 
          script.src && script.src.includes('chrome-extension')
        );
        
        log(`Extension scripts found: ${extensionScripts.length}`, extensionScripts.length > 0 ? 'success' : 'warning');
        
        if (hasProgressDashboardExtension) {
          showResult('contentScriptResult', 
            `✅ Content script injected successfully<br>
             • Extension object available<br>
             • Scripts found: ${extensionScripts.length}`, 'success');
        } else {
          showResult('contentScriptResult', 
            `❌ Content script not injected<br>
             • No extension object<br>
             • Extension may not be running<br>
             • Check extension permissions`, 'error');
        }
      } catch (error) {
        log(`Content script test error: ${error.message}`, 'error');
        showResult('contentScriptResult', `❌ Test failed: ${error.message}`, 'error');
      }
    }

    // Test 3: PostMessage Communication
    function testPostMessage() {
      log('Testing basic postMessage...', 'info');
      
      try {
        // Test basic postMessage
        const testMessage = {
          type: 'TEST_MESSAGE',
          data: { test: true },
          timestamp: Date.now()
        };
        
        window.postMessage(testMessage, window.location.origin);
        log('Basic postMessage sent', 'success');
        
        showResult('postMessageResult', 
          `✅ Basic postMessage test completed<br>
           • Message sent to window<br>
           • Check console for any responses`, 'success');
      } catch (error) {
        log(`PostMessage test error: ${error.message}`, 'error');
        showResult('postMessageResult', `❌ PostMessage failed: ${error.message}`, 'error');
      }
    }

    function testExtensionMessage() {
      log('Testing extension-specific message...', 'info');
      
      try {
        const extensionMessage = {
          type: 'EXTENSION_PING',
          data: { ping: true },
          timestamp: Date.now(),
          origin: window.location.origin
        };
        
        window.postMessage(extensionMessage, window.location.origin);
        log('Extension ping message sent', 'success');
        
        // Wait for response
        setTimeout(() => {
          log('Waiting for extension response...', 'warning');
        }, 1000);
        
        showResult('postMessageResult', 
          `✅ Extension message sent<br>
           • Type: EXTENSION_PING<br>
           • Waiting for response...`, 'warning');
      } catch (error) {
        log(`Extension message error: ${error.message}`, 'error');
        showResult('postMessageResult', `❌ Extension message failed: ${error.message}`, 'error');
      }
    }

    // Test 4: OTP Request Simulation
    function simulateOTPRequest() {
      const email = document.getElementById('testEmail').value;
      log(`Simulating OTP request for ${email}...`, 'info');
      
      try {
        const otpRequest = {
          type: 'OTP_REQUEST',
          data: {
            email: email,
            otp_code: '123456',
            otp_key: 'test-key-' + Date.now(),
            expires_in: 300,
            website: window.location.hostname,
            timestamp: Date.now()
          },
          origin: window.location.origin
        };
        
        log(`OTP request data: ${JSON.stringify(otpRequest, null, 2)}`, 'info');
        
        if (window.progressDashboardExtension) {
          log('Sending via extension bridge...', 'info');
          window.progressDashboardExtension.sendMessage(otpRequest)
            .then(response => {
              log(`Extension response: ${JSON.stringify(response)}`, 'success');
              showResult('otpRequestResult', 
                `✅ Extension responded!<br>
                 • Response: ${JSON.stringify(response, null, 2)}`, 'success');
            })
            .catch(error => {
              log(`Extension error: ${error.message}`, 'error');
              showResult('otpRequestResult', 
                `❌ Extension error: ${error.message}`, 'error');
            });
        } else {
          log('No extension bridge, using postMessage...', 'warning');
          window.postMessage(otpRequest, window.location.origin);
          
          showResult('otpRequestResult', 
            `⚠️ Sent via postMessage (no extension bridge)<br>
             • Check if extension receives the message`, 'warning');
        }
      } catch (error) {
        log(`OTP simulation error: ${error.message}`, 'error');
        showResult('otpRequestResult', `❌ Simulation failed: ${error.message}`, 'error');
      }
    }

    function testExtensionAvailability() {
      log('Testing extension availability...', 'info');
      
      try {
        if (window.progressDashboardExtension) {
          const isAvailable = window.progressDashboardExtension.isAvailable();
          const debugInfo = window.progressDashboardExtension.getDebugInfo();
          
          log(`Extension available: ${isAvailable}`, isAvailable ? 'success' : 'error');
          log(`Debug info: ${JSON.stringify(debugInfo)}`, 'info');
          
          showResult('otpRequestResult', 
            `Extension availability: ${isAvailable ? '✅ Available' : '❌ Not available'}<br>
             Debug info: ${JSON.stringify(debugInfo, null, 2)}`, 
            isAvailable ? 'success' : 'error');
        } else {
          showResult('otpRequestResult', 
            `❌ Extension bridge not found<br>
             • Extension not installed or not running<br>
             • Content script not injected`, 'error');
        }
      } catch (error) {
        log(`Availability test error: ${error.message}`, 'error');
        showResult('otpRequestResult', `❌ Test failed: ${error.message}`, 'error');
      }
    }

    // Test 5: Event Listeners
    function listEventListeners() {
      log('Listing event listeners...', 'info');
      
      try {
        // Check window event listeners
        const windowListeners = getEventListeners ? getEventListeners(window) : 'getEventListeners not available';
        log(`Window listeners: ${JSON.stringify(windowListeners)}`, 'info');
        
        // Check message event listeners
        const messageEvents = window.onmessage ? 'onmessage handler exists' : 'No onmessage handler';
        log(`Message handler: ${messageEvents}`, messageEvents.includes('exists') ? 'success' : 'warning');
        
        showResult('eventListenersResult', 
          `Event listeners check completed<br>
           • Window listeners: ${typeof windowListeners}<br>
           • Message handler: ${messageEvents}`, 'info');
      } catch (error) {
        log(`Event listeners error: ${error.message}`, 'error');
        showResult('eventListenersResult', `❌ Failed: ${error.message}`, 'error');
      }
    }

    function testMessageListeners() {
      log('Testing message listeners...', 'info');
      
      try {
        let responseReceived = false;
        
        // Add temporary listener
        const testListener = (event) => {
          if (event.data && event.data.type === 'TEST_RESPONSE') {
            responseReceived = true;
            log('Test response received!', 'success');
            showResult('eventListenersResult', '✅ Message listener working!', 'success');
            window.removeEventListener('message', testListener);
          }
        };
        
        window.addEventListener('message', testListener);
        
        // Send test message
        window.postMessage({
          type: 'TEST_RESPONSE',
          data: { test: true }
        }, window.location.origin);
        
        setTimeout(() => {
          if (!responseReceived) {
            log('No test response received', 'warning');
            showResult('eventListenersResult', '⚠️ Message listener may not be working', 'warning');
            window.removeEventListener('message', testListener);
          }
        }, 1000);
        
      } catch (error) {
        log(`Message listener test error: ${error.message}`, 'error');
        showResult('eventListenersResult', `❌ Test failed: ${error.message}`, 'error');
      }
    }

    function exportDebugInfo() {
      const debugInfo = {
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        extensionBridge: !!window.progressDashboardExtension,
        extensionReady: !!window.progressDashboardExtensionReady,
        debugLog: document.getElementById('debugLog').textContent
      };
      
      const blob = new Blob([JSON.stringify(debugInfo, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'extension-debug-info.json';
      a.click();
      URL.revokeObjectURL(url);
      
      log('Debug info exported', 'success');
    }

    // Auto-run tests on page load
    window.addEventListener('load', () => {
      log('Page loaded, starting auto-detection...', 'info');
      setTimeout(() => {
        testExtensionDetection();
      }, 1000);
    });

    // Listen for all messages
    window.addEventListener('message', (event) => {
      if (event.origin !== window.location.origin) return;
      
      log(`Message received: ${JSON.stringify(event.data)}`, 'info');
    });

    // Listen for extension ready event
    window.addEventListener('progressDashboardExtensionReady', (event) => {
      log('Extension ready event received!', 'success');
      setTimeout(testExtensionDetection, 500);
    });
  </script>
</body>
</html>
