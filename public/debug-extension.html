<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chrome Extension Debug Tool</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
      background: #f8fafc;
    }
    
    .container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #1e293b;
      margin-bottom: 20px;
    }
    
    .debug-section {
      margin: 20px 0;
      padding: 20px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      background: #f8fafc;
    }
    
    .debug-section h3 {
      margin-top: 0;
      color: #475569;
    }
    
    .status {
      padding: 10px;
      border-radius: 6px;
      margin: 10px 0;
      font-weight: 500;
    }
    
    .status.success {
      background: #dcfce7;
      color: #166534;
      border: 1px solid #bbf7d0;
    }
    
    .status.error {
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }
    
    .status.warning {
      background: #fef3c7;
      color: #d97706;
      border: 1px solid #fde68a;
    }
    
    .status.info {
      background: #dbeafe;
      color: #1d4ed8;
      border: 1px solid #bfdbfe;
    }
    
    button {
      background: #95E565;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin: 5px;
      transition: background 0.2s;
    }
    
    button:hover {
      background: #608F44;
    }
    
    .code-block {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      overflow-x: auto;
      margin: 10px 0;
    }
    
    .step {
      margin: 15px 0;
      padding: 15px;
      background: #f1f5f9;
      border-left: 4px solid #95E565;
      border-radius: 0 6px 6px 0;
    }
    
    .step h4 {
      margin: 0 0 10px 0;
      color: #1e293b;
    }
    
    .checklist {
      list-style: none;
      padding: 0;
    }
    
    .checklist li {
      margin: 8px 0;
      padding: 8px;
      background: white;
      border-radius: 4px;
      border-left: 3px solid #e2e8f0;
    }
    
    .checklist li.success {
      border-left-color: #22c55e;
      background: #f0fdf4;
    }
    
    .checklist li.error {
      border-left-color: #ef4444;
      background: #fef2f2;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔧 Chrome Extension Debug Tool</h1>
    <p>Comprehensive debugging tool to diagnose Chrome Extension installation and communication issues.</p>
    
    <!-- Auto Diagnosis -->
    <div class="debug-section">
      <h3>🔍 Auto Diagnosis</h3>
      <div id="autoStatus" class="status info">Running automatic diagnosis...</div>
      <button onclick="runFullDiagnosis()">Run Full Diagnosis</button>
      <button onclick="clearResults()">Clear Results</button>
    </div>

    <!-- Installation Check -->
    <div class="debug-section">
      <h3>📦 Installation Check</h3>
      <div id="installationResults">
        <ul id="installationChecklist" class="checklist">
          <li id="check-chrome">Checking Chrome version...</li>
          <li id="check-devmode">Checking Developer mode...</li>
          <li id="check-extension">Checking extension installation...</li>
          <li id="check-permissions">Checking permissions...</li>
        </ul>
      </div>
    </div>

    <!-- Communication Check -->
    <div class="debug-section">
      <h3>💬 Communication Check</h3>
      <div id="communicationResults">
        <ul id="communicationChecklist" class="checklist">
          <li id="check-bridge">Checking extension bridge...</li>
          <li id="check-content">Checking content script...</li>
          <li id="check-messages">Checking message passing...</li>
          <li id="check-origin">Checking origin permissions...</li>
        </ul>
      </div>
    </div>

    <!-- Manual Installation Guide -->
    <div class="debug-section">
      <h3>📋 Manual Installation Guide</h3>
      
      <div class="step">
        <h4>Step 1: Open Chrome Extensions</h4>
        <p>Navigate to Chrome Extensions page:</p>
        <div class="code-block">chrome://extensions/</div>
        <button onclick="openExtensionsPage()">Open Extensions Page</button>
      </div>

      <div class="step">
        <h4>Step 2: Enable Developer Mode</h4>
        <p>Toggle "Developer mode" switch in the top right corner.</p>
      </div>

      <div class="step">
        <h4>Step 3: Load Extension</h4>
        <p>Click "Load unpacked" and select the extension folder:</p>
        <div class="code-block">/Users/<USER>/Library/CloudStorage/Dropbox/Assets/Apps/Dashboard/Progress_Dashboard-main/chrome-extension/</div>
      </div>

      <div class="step">
        <h4>Step 4: Grant Permissions</h4>
        <p>Ensure all permissions are granted:</p>
        <ul>
          <li>✅ Read and change data on localhost</li>
          <li>✅ Display notifications</li>
          <li>✅ Store data</li>
        </ul>
      </div>

      <div class="step">
        <h4>Step 5: Verify Installation</h4>
        <p>Check that extension appears in toolbar and click "Run Full Diagnosis" above.</p>
      </div>
    </div>

    <!-- Quick Fixes -->
    <div class="debug-section">
      <h3>🛠️ Quick Fixes</h3>
      <button onclick="reloadPage()">Reload Page</button>
      <button onclick="clearCache()">Clear Cache (Manual)</button>
      <button onclick="injectManualBridge()">Inject Manual Bridge</button>
      <button onclick="testManualCommunication()">Test Manual Communication</button>
    </div>

    <!-- Debug Console -->
    <div class="debug-section">
      <h3>📝 Debug Console</h3>
      <div id="debugConsole" class="code-block" style="max-height: 300px; overflow-y: auto;">
        Debug information will appear here...\n
      </div>
    </div>
  </div>

  <script>
    // Debug state
    let debugState = {
      results: [],
      extensionDetected: false,
      bridgeAvailable: false
    };

    // Logging function
    function debugLog(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      const console = document.getElementById('debugConsole');
      const colors = {
        info: '#60a5fa',
        success: '#34d399',
        error: '#f87171',
        warning: '#fbbf24'
      };
      
      console.innerHTML += `<span style="color: ${colors[type]}">[${timestamp}] ${message}</span>\n`;
      console.scrollTop = console.scrollHeight;
      
      debugState.results.push({ timestamp: Date.now(), message, type });
    }

    // Update checklist item
    function updateChecklistItem(id, message, status) {
      const item = document.getElementById(id);
      if (item) {
        item.textContent = message;
        item.className = status;
      }
    }

    // Run full diagnosis
    async function runFullDiagnosis() {
      debugLog('Starting full diagnosis...', 'info');
      document.getElementById('autoStatus').textContent = 'Running diagnosis...';
      document.getElementById('autoStatus').className = 'status info';
      
      // Check Chrome version
      const chromeVersion = navigator.userAgent.match(/Chrome\/(\d+)/);
      if (chromeVersion && parseInt(chromeVersion[1]) >= 88) {
        updateChecklistItem('check-chrome', `✅ Chrome ${chromeVersion[1]} (Compatible)`, 'success');
        debugLog(`Chrome version ${chromeVersion[1]} detected`, 'success');
      } else {
        updateChecklistItem('check-chrome', '❌ Chrome version incompatible or not detected', 'error');
        debugLog('Chrome version check failed', 'error');
      }

      // Check extension bridge
      if (typeof window.progressDashboardExtension !== 'undefined') {
        updateChecklistItem('check-bridge', '✅ Extension bridge available', 'success');
        debugLog('Extension bridge detected', 'success');
        debugState.bridgeAvailable = true;
        
        // Test communication
        try {
          const response = await window.progressDashboardExtension.sendMessage({
            type: 'GET_EXTENSION_STATUS'
          });
          updateChecklistItem('check-messages', '✅ Message communication working', 'success');
          debugLog(`Communication test successful: ${JSON.stringify(response)}`, 'success');
        } catch (error) {
          updateChecklistItem('check-messages', '❌ Message communication failed', 'error');
          debugLog(`Communication test failed: ${error.message}`, 'error');
        }
      } else {
        updateChecklistItem('check-bridge', '❌ Extension bridge not available', 'error');
        debugLog('Extension bridge not found', 'error');
        debugState.bridgeAvailable = false;
      }

      // Check content script
      const contentScriptMessages = performance.getEntriesByType('navigation');
      updateChecklistItem('check-content', '⚠️ Content script check requires manual verification', 'warning');
      debugLog('Content script check: Look for console messages starting with "🔗 Progress Dashboard Extension"', 'warning');

      // Check origin
      const currentOrigin = window.location.origin;
      if (currentOrigin.includes('localhost')) {
        updateChecklistItem('check-origin', `✅ Origin ${currentOrigin} should be allowed`, 'success');
        debugLog(`Current origin ${currentOrigin} matches allowed patterns`, 'success');
      } else {
        updateChecklistItem('check-origin', `⚠️ Origin ${currentOrigin} may not be allowed`, 'warning');
        debugLog(`Current origin ${currentOrigin} may not match extension permissions`, 'warning');
      }

      // Final status
      if (debugState.bridgeAvailable) {
        document.getElementById('autoStatus').textContent = '✅ Extension working correctly';
        document.getElementById('autoStatus').className = 'status success';
        debugLog('Diagnosis complete: Extension working correctly', 'success');
      } else {
        document.getElementById('autoStatus').textContent = '❌ Extension not working - see checklist above';
        document.getElementById('autoStatus').className = 'status error';
        debugLog('Diagnosis complete: Extension not working properly', 'error');
      }
    }

    // Clear results
    function clearResults() {
      document.getElementById('debugConsole').innerHTML = 'Debug information will appear here...\n';
      debugState.results = [];
      debugLog('Debug console cleared', 'info');
    }

    // Open extensions page
    function openExtensionsPage() {
      window.open('chrome://extensions/', '_blank');
      debugLog('Opened Chrome Extensions page', 'info');
    }

    // Reload page
    function reloadPage() {
      debugLog('Reloading page...', 'info');
      window.location.reload();
    }

    // Clear cache instruction
    function clearCache() {
      alert('To clear cache:\n1. Press Ctrl+Shift+Delete (or Cmd+Shift+Delete on Mac)\n2. Select "Cached images and files"\n3. Click "Clear data"\n4. Refresh this page');
      debugLog('Cache clear instructions shown', 'info');
    }

    // Inject manual bridge for testing
    function injectManualBridge() {
      window.progressDashboardExtension = {
        isAvailable: true,
        version: '1.0.0-manual',
        manual: true,
        sendMessage: function(message) {
          debugLog(`Manual bridge received message: ${JSON.stringify(message)}`, 'info');
          return Promise.resolve({ 
            success: true, 
            manual: true, 
            message: 'Manual bridge response',
            timestamp: Date.now()
          });
        }
      };
      debugLog('Manual bridge injected for testing', 'warning');
      runFullDiagnosis();
    }

    // Test manual communication
    async function testManualCommunication() {
      if (window.progressDashboardExtension) {
        try {
          const response = await window.progressDashboardExtension.sendMessage({
            type: 'TEST_MESSAGE',
            data: { test: true }
          });
          debugLog(`Manual communication test successful: ${JSON.stringify(response)}`, 'success');
        } catch (error) {
          debugLog(`Manual communication test failed: ${error.message}`, 'error');
        }
      } else {
        debugLog('No extension bridge available for testing', 'error');
      }
    }

    // Auto-run diagnosis on page load
    document.addEventListener('DOMContentLoaded', () => {
      debugLog('Debug tool loaded', 'info');
      setTimeout(() => {
        runFullDiagnosis();
      }, 1000);
    });

    // Listen for extension messages
    window.addEventListener('message', (event) => {
      if (event.data && event.data.type && event.data.type.startsWith('EXTENSION_')) {
        debugLog(`Received extension message: ${JSON.stringify(event.data)}`, 'info');
      }
    });
  </script>
</body>
</html>
