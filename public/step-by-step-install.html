<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Step-by-Step Extension Installation</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
      background: #f8fafc;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #1e293b;
      margin-bottom: 20px;
    }
    
    .step {
      margin: 30px 0;
      padding: 25px;
      background: #f1f5f9;
      border-left: 5px solid #95E565;
      border-radius: 0 8px 8px 0;
      position: relative;
    }
    
    .step h3 {
      margin: 0 0 15px 0;
      color: #1e293b;
      display: flex;
      align-items: center;
      gap: 15px;
      font-size: 18px;
    }
    
    .step-number {
      background: #95E565;
      color: white;
      width: 35px;
      height: 35px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 16px;
    }
    
    .step-content {
      margin-left: 50px;
    }
    
    .action-item {
      margin: 15px 0;
      padding: 15px;
      background: white;
      border-radius: 6px;
      border-left: 3px solid #95E565;
    }
    
    .action-item h4 {
      margin: 0 0 10px 0;
      color: #1e293b;
      font-size: 14px;
      font-weight: 600;
    }
    
    .code-path {
      background: #1e293b;
      color: #e2e8f0;
      padding: 12px;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      word-break: break-all;
      margin: 10px 0;
    }
    
    .warning {
      background: #fef3c7;
      border: 1px solid #fde68a;
      color: #d97706;
      padding: 15px;
      border-radius: 6px;
      margin: 15px 0;
    }
    
    .success {
      background: #dcfce7;
      border: 1px solid #bbf7d0;
      color: #166534;
      padding: 15px;
      border-radius: 6px;
      margin: 15px 0;
    }
    
    .error {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
      padding: 15px;
      border-radius: 6px;
      margin: 15px 0;
    }
    
    button {
      background: #95E565;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin: 10px 5px;
      transition: background 0.2s;
      font-weight: 500;
    }
    
    button:hover {
      background: #608F44;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      margin: 20px 0;
    }
    
    .checklist {
      list-style: none;
      padding: 0;
    }
    
    .checklist li {
      margin: 8px 0;
      padding: 10px;
      background: white;
      border-radius: 6px;
      border-left: 3px solid #e2e8f0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .checklist li.done {
      border-left-color: #22c55e;
      background: #f0fdf4;
    }
    
    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #e2e8f0;
    }
    
    .status-indicator.success {
      background: #22c55e;
    }
    
    .status-indicator.error {
      background: #ef4444;
    }
    
    .live-status {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
      border-left: 4px solid #95E565;
      min-width: 250px;
    }
    
    .live-status h4 {
      margin: 0 0 10px 0;
      color: #1e293b;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>📋 Step-by-Step Extension Installation</h1>
    <p>Follow these exact steps to install the Chrome Extension successfully.</p>
    
    <!-- Live Status -->
    <div class="live-status">
      <h4>🔴 Live Status</h4>
      <div id="liveStatus">Extension not detected</div>
      <button onclick="checkStatus()" style="margin-top: 10px; padding: 6px 12px; font-size: 12px;">Refresh Status</button>
    </div>

    <!-- Current Issue -->
    <div class="error">
      <h3>❌ Current Issue</h3>
      <p><strong>Extension bridge not found</strong> - Extension tidak ter-install dengan benar atau content script tidak ter-inject.</p>
    </div>

    <!-- Step 1: Open Extensions Page -->
    <div class="step">
      <h3>
        <span class="step-number">1</span>
        Open Chrome Extensions Page
      </h3>
      <div class="step-content">
        <div class="action-item">
          <h4>🎯 Action Required:</h4>
          <p>Open a new tab in Chrome and navigate to extensions management:</p>
          <div class="code-path">chrome://extensions/</div>
          <div class="button-group">
            <button onclick="openExtensionsPage()">📂 Open Extensions Page</button>
            <button onclick="copyToClipboard('chrome://extensions/')">📋 Copy URL</button>
          </div>
        </div>
        
        <div class="warning">
          <strong>Important:</strong> You must type this URL manually in Chrome's address bar or use the button above.
        </div>
      </div>
    </div>

    <!-- Step 2: Enable Developer Mode -->
    <div class="step">
      <h3>
        <span class="step-number">2</span>
        Enable Developer Mode
      </h3>
      <div class="step-content">
        <div class="action-item">
          <h4>🎯 Action Required:</h4>
          <p>In the Extensions page, enable Developer mode:</p>
          <ul class="checklist">
            <li><span class="status-indicator"></span> Look for "Developer mode" toggle in top-right corner</li>
            <li><span class="status-indicator"></span> Click the toggle to enable it (should turn blue/green)</li>
            <li><span class="status-indicator"></span> New buttons appear: "Load unpacked", "Pack extension", "Update"</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Step 3: Remove Existing Extension -->
    <div class="step">
      <h3>
        <span class="step-number">3</span>
        Remove Existing Extension (If Any)
      </h3>
      <div class="step-content">
        <div class="action-item">
          <h4>🎯 Action Required:</h4>
          <p>If you see any "Progress Dashboard" extension, remove it first:</p>
          <ul class="checklist">
            <li><span class="status-indicator"></span> Look for "Progress Dashboard - OTP Authenticator"</li>
            <li><span class="status-indicator"></span> Click "Remove" button on the extension card</li>
            <li><span class="status-indicator"></span> Confirm removal when prompted</li>
            <li><span class="status-indicator"></span> Refresh the extensions page</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Step 4: Load Extension -->
    <div class="step">
      <h3>
        <span class="step-number">4</span>
        Load Extension from Folder
      </h3>
      <div class="step-content">
        <div class="action-item">
          <h4>🎯 Action Required:</h4>
          <p>Click "Load unpacked" and select the extension folder:</p>
          <div class="code-path">/Users/<USER>/Library/CloudStorage/Dropbox/Assets/Apps/Dashboard/Progress_Dashboard-main/chrome-extension/</div>
          <div class="button-group">
            <button onclick="copyToClipboard('/Users/<USER>/Library/CloudStorage/Dropbox/Assets/Apps/Dashboard/Progress_Dashboard-main/chrome-extension/')">📋 Copy Folder Path</button>
            <button onclick="openFinder()">📁 Open in Finder</button>
          </div>
          
          <ul class="checklist">
            <li><span class="status-indicator"></span> Click "Load unpacked" button</li>
            <li><span class="status-indicator"></span> Navigate to the folder path above</li>
            <li><span class="status-indicator"></span> Select the <strong>chrome-extension</strong> folder (not files inside)</li>
            <li><span class="status-indicator"></span> Click "Select" or "Open"</li>
          </ul>
        </div>
        
        <div class="warning">
          <strong>Critical:</strong> Make sure you select the <code>chrome-extension</code> folder itself, not individual files inside it.
        </div>
      </div>
    </div>

    <!-- Step 5: Verify Installation -->
    <div class="step">
      <h3>
        <span class="step-number">5</span>
        Verify Extension Installation
      </h3>
      <div class="step-content">
        <div class="action-item">
          <h4>🎯 Expected Results:</h4>
          <p>After loading, you should see the extension card with:</p>
          <ul class="checklist">
            <li><span class="status-indicator"></span> <strong>Name:</strong> "Progress Dashboard - OTP Authenticator"</li>
            <li><span class="status-indicator"></span> <strong>Version:</strong> "1.0.0"</li>
            <li><span class="status-indicator"></span> <strong>Status:</strong> Enabled (not grayed out)</li>
            <li><span class="status-indicator"></span> <strong>Icon:</strong> Green shield icon visible</li>
            <li><span class="status-indicator"></span> <strong>No Errors:</strong> No red error messages</li>
          </ul>
        </div>
        
        <div class="success" id="installSuccess" style="display: none;">
          <strong>✅ Installation Successful!</strong> Extension card is visible and enabled.
        </div>
        
        <div class="error" id="installError" style="display: none;">
          <strong>❌ Installation Failed!</strong> Extension card not visible or shows errors.
        </div>
      </div>
    </div>

    <!-- Step 6: Grant Permissions -->
    <div class="step">
      <h3>
        <span class="step-number">6</span>
        Grant Required Permissions
      </h3>
      <div class="step-content">
        <div class="action-item">
          <h4>🎯 Action Required:</h4>
          <p>Click "Details" on the extension card and verify permissions:</p>
          <ul class="checklist">
            <li><span class="status-indicator"></span> <strong>Site access:</strong> "On localhost" or "On all sites"</li>
            <li><span class="status-indicator"></span> <strong>Notifications:</strong> "Display notifications"</li>
            <li><span class="status-indicator"></span> <strong>Storage:</strong> "Store data"</li>
          </ul>
        </div>
        
        <div class="warning">
          If any permissions are missing or denied, click "Allow" or enable them manually.
        </div>
      </div>
    </div>

    <!-- Step 7: Test Extension -->
    <div class="step">
      <h3>
        <span class="step-number">7</span>
        Test Extension Communication
      </h3>
      <div class="step-content">
        <div class="action-item">
          <h4>🎯 Action Required:</h4>
          <p>Test that the extension is working properly:</p>
          <div class="button-group">
            <button onclick="testExtension()">🧪 Test Extension</button>
            <button onclick="openDebugTool()">🔧 Open Debug Tool</button>
            <button onclick="refreshPage()">🔄 Refresh Page</button>
          </div>
          
          <div id="testResults" class="error">Click "Test Extension" to verify functionality</div>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div class="success" id="finalSuccess" style="display: none;">
      <h3>🎉 Extension Successfully Installed!</h3>
      <p>The Chrome Extension is now working properly. You can proceed to test the login flow.</p>
      <div class="button-group">
        <button onclick="openLoginPage()">🔐 Test Login Flow</button>
        <button onclick="openDebugTool()">🔧 Debug Tool</button>
      </div>
    </div>
  </div>

  <script>
    // Helper functions
    function openExtensionsPage() {
      window.open('chrome://extensions/', '_blank');
    }

    function openDebugTool() {
      window.open('http://localhost:5173/debug-extension.html', '_blank');
    }

    function openLoginPage() {
      window.open('http://localhost:5173/login', '_blank');
    }

    function refreshPage() {
      window.location.reload();
    }

    function openFinder() {
      alert('To open in Finder:\n1. Open Finder\n2. Press Cmd+Shift+G\n3. Paste the folder path\n4. Press Enter\n5. Select the chrome-extension folder');
    }

    function copyToClipboard(text) {
      navigator.clipboard.writeText(text).then(() => {
        alert('✅ Copied to clipboard:\n' + text);
      }).catch(() => {
        prompt('Copy this path:', text);
      });
    }

    function checkStatus() {
      const statusElement = document.getElementById('liveStatus');
      
      if (typeof window.progressDashboardExtension !== 'undefined') {
        statusElement.innerHTML = '🟢 Extension detected and working';
        statusElement.style.color = '#166534';
        document.getElementById('finalSuccess').style.display = 'block';
      } else {
        statusElement.innerHTML = '🔴 Extension not detected';
        statusElement.style.color = '#dc2626';
      }
    }

    function testExtension() {
      const resultsElement = document.getElementById('testResults');
      
      if (typeof window.progressDashboardExtension !== 'undefined') {
        resultsElement.innerHTML = '✅ Extension bridge detected - Installation successful!';
        resultsElement.className = 'success';
        document.getElementById('finalSuccess').style.display = 'block';
        
        // Test communication
        window.progressDashboardExtension.sendMessage({
          type: 'GET_EXTENSION_STATUS'
        }).then(response => {
          resultsElement.innerHTML += '<br>✅ Communication test successful: ' + JSON.stringify(response);
        }).catch(error => {
          resultsElement.innerHTML += '<br>⚠️ Communication test failed: ' + error.message;
        });
      } else {
        resultsElement.innerHTML = '❌ Extension bridge not found - Please follow installation steps above';
        resultsElement.className = 'error';
      }
    }

    // Auto-check status every 3 seconds
    setInterval(checkStatus, 3000);

    // Initial status check
    setTimeout(checkStatus, 1000);

    // Listen for extension ready event
    window.addEventListener('progressDashboardExtensionReady', (event) => {
      console.log('Extension ready event received:', event.detail);
      checkStatus();
    });
  </script>
</body>
</html>
