<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Advanced Extension Diagnostic Tool</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      background: #f8fafc;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #1e293b;
      margin-bottom: 20px;
    }
    
    .diagnostic-section {
      margin: 25px 0;
      padding: 20px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      background: #f8fafc;
    }
    
    .diagnostic-section h3 {
      margin-top: 0;
      color: #475569;
    }
    
    .status {
      padding: 12px;
      border-radius: 6px;
      margin: 10px 0;
      font-weight: 500;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 13px;
    }
    
    .status.success {
      background: #dcfce7;
      color: #166534;
      border: 1px solid #bbf7d0;
    }
    
    .status.error {
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }
    
    .status.warning {
      background: #fef3c7;
      color: #d97706;
      border: 1px solid #fde68a;
    }
    
    .status.info {
      background: #dbeafe;
      color: #1d4ed8;
      border: 1px solid #bfdbfe;
    }
    
    button {
      background: #95E565;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin: 5px;
      transition: background 0.2s;
    }
    
    button:hover {
      background: #608F44;
    }
    
    .log-container {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      max-height: 400px;
      overflow-y: auto;
      margin: 10px 0;
      white-space: pre-wrap;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      margin: 15px 0;
    }
    
    .live-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
      animation: pulse 2s infinite;
    }
    
    .live-indicator.green {
      background: #22c55e;
    }
    
    .live-indicator.red {
      background: #ef4444;
    }
    
    .live-indicator.yellow {
      background: #eab308;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
    
    .diagnostic-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin: 20px 0;
    }
    
    @media (max-width: 768px) {
      .diagnostic-grid {
        grid-template-columns: 1fr;
      }
    }
    
    .code-block {
      background: #1e293b;
      color: #e2e8f0;
      padding: 12px;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 11px;
      overflow-x: auto;
      margin: 10px 0;
    }
    
    .emergency-fix {
      background: #fef2f2;
      border: 2px solid #ef4444;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
    }
    
    .emergency-fix h3 {
      color: #dc2626;
      margin-top: 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔬 Advanced Extension Diagnostic Tool</h1>
    <p>Comprehensive debugging for Chrome Extension installation and communication issues.</p>
    
    <!-- Live Status -->
    <div class="diagnostic-section">
      <h3><span id="liveIndicator" class="live-indicator red"></span>Live Extension Status</h3>
      <div id="liveStatus" class="status error">Extension not detected</div>
      <div class="button-group">
        <button onclick="runFullDiagnostic()">🔬 Run Full Diagnostic</button>
        <button onclick="forceRedetection()">🔄 Force Re-detection</button>
        <button onclick="clearLogs()">🗑️ Clear Logs</button>
      </div>
    </div>

    <!-- Diagnostic Grid -->
    <div class="diagnostic-grid">
      
      <!-- Extension Detection -->
      <div class="diagnostic-section">
        <h3>🔍 Extension Detection</h3>
        <div id="extensionDetection" class="status info">Running detection...</div>
        <button onclick="testExtensionDetection()">Test Detection</button>
      </div>

      <!-- Content Script Status -->
      <div class="diagnostic-section">
        <h3>📜 Content Script Status</h3>
        <div id="contentScriptStatus" class="status info">Checking content script...</div>
        <button onclick="testContentScript()">Test Content Script</button>
      </div>

      <!-- Communication Test -->
      <div class="diagnostic-section">
        <h3>💬 Communication Test</h3>
        <div id="communicationTest" class="status info">Ready to test...</div>
        <button onclick="testCommunication()">Test Communication</button>
      </div>

      <!-- Permission Check -->
      <div class="diagnostic-section">
        <h3>🔐 Permission Check</h3>
        <div id="permissionCheck" class="status info">Checking permissions...</div>
        <button onclick="testPermissions()">Test Permissions</button>
      </div>

    </div>

    <!-- Console Logs -->
    <div class="diagnostic-section">
      <h3>📝 Extension Console Logs</h3>
      <div id="consoleLogs" class="log-container">Waiting for extension logs...\n</div>
      <div class="button-group">
        <button onclick="captureConsoleLogs()">📋 Capture Console Logs</button>
        <button onclick="exportDiagnostic()">💾 Export Diagnostic</button>
      </div>
    </div>

    <!-- Emergency Fix -->
    <div class="emergency-fix" id="emergencyFix" style="display: none;">
      <h3>🚨 Emergency Fix Required</h3>
      <p>Extension installation has failed multiple times. Try these emergency fixes:</p>
      <div class="button-group">
        <button onclick="emergencyReinstall()">🔧 Emergency Reinstall</button>
        <button onclick="manualBridgeInjection()">💉 Manual Bridge Injection</button>
        <button onclick="alternativeMethod()">🔄 Alternative Method</button>
      </div>
    </div>

    <!-- Detailed Diagnostic Results -->
    <div class="diagnostic-section">
      <h3>📊 Detailed Diagnostic Results</h3>
      <div id="detailedResults" class="log-container">Click "Run Full Diagnostic" to see detailed results...\n</div>
    </div>
  </div>

  <script>
    // Diagnostic state
    let diagnosticState = {
      extensionDetected: false,
      contentScriptActive: false,
      communicationWorking: false,
      permissionsGranted: false,
      logs: [],
      diagnosticCount: 0
    };

    // Logging function
    function log(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
      
      diagnosticState.logs.push(logEntry);
      
      const consoleLogs = document.getElementById('consoleLogs');
      consoleLogs.textContent += logEntry + '\n';
      consoleLogs.scrollTop = consoleLogs.scrollHeight;
    }

    // Update live status
    function updateLiveStatus() {
      const indicator = document.getElementById('liveIndicator');
      const status = document.getElementById('liveStatus');
      
      if (typeof window.progressDashboardExtension !== 'undefined') {
        indicator.className = 'live-indicator green';
        status.textContent = '✅ Extension detected and bridge available';
        status.className = 'status success';
        diagnosticState.extensionDetected = true;
      } else {
        indicator.className = 'live-indicator red';
        status.textContent = '❌ Extension bridge not found';
        status.className = 'status error';
        diagnosticState.extensionDetected = false;
      }
    }

    // Run full diagnostic
    async function runFullDiagnostic() {
      diagnosticState.diagnosticCount++;
      log(`Starting full diagnostic #${diagnosticState.diagnosticCount}`, 'info');
      
      // Clear previous results
      document.getElementById('detailedResults').textContent = '';
      
      // Test 1: Extension Detection
      await testExtensionDetection();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Test 2: Content Script
      await testContentScript();
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Test 3: Communication
      if (diagnosticState.extensionDetected) {
        await testCommunication();
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      // Test 4: Permissions
      await testPermissions();
      
      // Generate summary
      generateDiagnosticSummary();
      
      // Show emergency fix if needed
      if (diagnosticState.diagnosticCount >= 3 && !diagnosticState.extensionDetected) {
        document.getElementById('emergencyFix').style.display = 'block';
      }
    }

    // Test extension detection
    async function testExtensionDetection() {
      log('Testing extension detection...', 'info');
      
      const detectionElement = document.getElementById('extensionDetection');
      
      // Check for extension bridge
      if (typeof window.progressDashboardExtension !== 'undefined') {
        const bridge = window.progressDashboardExtension;
        
        detectionElement.textContent = `✅ Extension detected - Version: ${bridge.version || 'unknown'}`;
        detectionElement.className = 'status success';
        
        log(`Extension bridge found: ${JSON.stringify(bridge.getDebugInfo ? bridge.getDebugInfo() : 'No debug info')}`, 'success');
        
        diagnosticState.extensionDetected = true;
      } else {
        detectionElement.textContent = '❌ Extension bridge not found';
        detectionElement.className = 'status error';
        
        log('Extension bridge not found in window object', 'error');
        
        diagnosticState.extensionDetected = false;
      }
      
      updateLiveStatus();
    }

    // Test content script
    async function testContentScript() {
      log('Testing content script status...', 'info');
      
      const contentElement = document.getElementById('contentScriptStatus');
      
      // Check for content script indicators
      const hasContentScript = document.querySelector('script[src*="content"]') || 
                              window.__progressDashboardExtensionBridge__ ||
                              typeof window.progressDashboardExtension !== 'undefined';
      
      if (hasContentScript) {
        contentElement.textContent = '✅ Content script appears to be active';
        contentElement.className = 'status success';
        log('Content script indicators found', 'success');
        diagnosticState.contentScriptActive = true;
      } else {
        contentElement.textContent = '❌ Content script not detected';
        contentElement.className = 'status error';
        log('No content script indicators found', 'error');
        diagnosticState.contentScriptActive = false;
      }
    }

    // Test communication
    async function testCommunication() {
      log('Testing extension communication...', 'info');
      
      const commElement = document.getElementById('communicationTest');
      
      if (!diagnosticState.extensionDetected) {
        commElement.textContent = '⚠️ Cannot test - extension not detected';
        commElement.className = 'status warning';
        return;
      }
      
      try {
        const startTime = performance.now();
        
        const response = await window.progressDashboardExtension.sendMessage({
          type: 'GET_EXTENSION_STATUS'
        });
        
        const latency = performance.now() - startTime;
        
        commElement.textContent = `✅ Communication successful - ${latency.toFixed(2)}ms`;
        commElement.className = 'status success';
        
        log(`Communication test successful: ${JSON.stringify(response)} (${latency.toFixed(2)}ms)`, 'success');
        
        diagnosticState.communicationWorking = true;
      } catch (error) {
        commElement.textContent = `❌ Communication failed: ${error.message}`;
        commElement.className = 'status error';
        
        log(`Communication test failed: ${error.message}`, 'error');
        
        diagnosticState.communicationWorking = false;
      }
    }

    // Test permissions
    async function testPermissions() {
      log('Testing permissions...', 'info');
      
      const permElement = document.getElementById('permissionCheck');
      
      // Check basic web APIs
      const hasNotifications = 'Notification' in window;
      const hasStorage = 'localStorage' in window;
      const hasPostMessage = 'postMessage' in window;
      
      const permissions = {
        notifications: hasNotifications,
        storage: hasStorage,
        postMessage: hasPostMessage,
        origin: window.location.origin
      };
      
      const allGranted = Object.values(permissions).every(p => p === true);
      
      if (allGranted) {
        permElement.textContent = '✅ All required permissions available';
        permElement.className = 'status success';
        log(`Permissions check passed: ${JSON.stringify(permissions)}`, 'success');
        diagnosticState.permissionsGranted = true;
      } else {
        permElement.textContent = '⚠️ Some permissions may be missing';
        permElement.className = 'status warning';
        log(`Permissions check: ${JSON.stringify(permissions)}`, 'warning');
        diagnosticState.permissionsGranted = false;
      }
    }

    // Generate diagnostic summary
    function generateDiagnosticSummary() {
      const results = document.getElementById('detailedResults');
      
      const summary = `
DIAGNOSTIC SUMMARY #${diagnosticState.diagnosticCount}
=====================================
Extension Detected: ${diagnosticState.extensionDetected ? '✅ YES' : '❌ NO'}
Content Script Active: ${diagnosticState.contentScriptActive ? '✅ YES' : '❌ NO'}
Communication Working: ${diagnosticState.communicationWorking ? '✅ YES' : '❌ NO'}
Permissions Granted: ${diagnosticState.permissionsGranted ? '✅ YES' : '❌ NO'}

Browser Info:
- User Agent: ${navigator.userAgent}
- URL: ${window.location.href}
- Origin: ${window.location.origin}
- Protocol: ${window.location.protocol}

Extension Bridge Info:
${typeof window.progressDashboardExtension !== 'undefined' ? 
  JSON.stringify(window.progressDashboardExtension.getDebugInfo ? window.progressDashboardExtension.getDebugInfo() : window.progressDashboardExtension, null, 2) : 
  'Extension bridge not available'}

Recommendations:
${generateRecommendations()}
`;
      
      results.textContent = summary;
      log('Diagnostic summary generated', 'info');
    }

    // Generate recommendations
    function generateRecommendations() {
      const recommendations = [];
      
      if (!diagnosticState.extensionDetected) {
        recommendations.push('1. Reinstall Chrome Extension from chrome://extensions/');
        recommendations.push('2. Ensure Developer mode is enabled');
        recommendations.push('3. Check extension permissions');
      }
      
      if (!diagnosticState.contentScriptActive) {
        recommendations.push('4. Refresh extension and page');
        recommendations.push('5. Check for CSP violations in console');
      }
      
      if (!diagnosticState.communicationWorking) {
        recommendations.push('6. Verify background script is running');
        recommendations.push('7. Check for message passing errors');
      }
      
      if (recommendations.length === 0) {
        recommendations.push('✅ All systems working correctly!');
      }
      
      return recommendations.join('\n');
    }

    // Emergency functions
    function emergencyReinstall() {
      alert('Emergency Reinstall Steps:\n\n1. Go to chrome://extensions/\n2. Remove "Progress Dashboard" extension\n3. Clear browser cache (Ctrl+Shift+Delete)\n4. Restart Chrome\n5. Reinstall extension\n6. Refresh this page');
      log('Emergency reinstall instructions provided', 'warning');
    }

    function manualBridgeInjection() {
      // Inject manual bridge for testing
      window.progressDashboardExtension = {
        isAvailable: true,
        version: '1.0.0-manual',
        manual: true,
        sendMessage: function(message) {
          log(`Manual bridge received: ${JSON.stringify(message)}`, 'info');
          return Promise.resolve({ success: true, manual: true, timestamp: Date.now() });
        },
        getDebugInfo: function() {
          return { manual: true, injected: Date.now() };
        }
      };
      
      log('Manual bridge injected for testing', 'warning');
      updateLiveStatus();
      runFullDiagnostic();
    }

    function alternativeMethod() {
      window.open('http://localhost:5173/login', '_blank');
      log('Opened login page for alternative testing', 'info');
    }

    // Utility functions
    function forceRedetection() {
      log('Forcing re-detection...', 'info');
      updateLiveStatus();
      runFullDiagnostic();
    }

    function clearLogs() {
      diagnosticState.logs = [];
      document.getElementById('consoleLogs').textContent = 'Logs cleared...\n';
      log('Logs cleared', 'info');
    }

    function captureConsoleLogs() {
      // Capture browser console logs
      const logs = diagnosticState.logs.join('\n');
      navigator.clipboard.writeText(logs).then(() => {
        alert('Console logs copied to clipboard');
      }).catch(() => {
        prompt('Copy these logs:', logs);
      });
    }

    function exportDiagnostic() {
      const diagnosticData = {
        timestamp: new Date().toISOString(),
        diagnosticState: diagnosticState,
        browserInfo: {
          userAgent: navigator.userAgent,
          url: window.location.href,
          origin: window.location.origin
        },
        extensionInfo: typeof window.progressDashboardExtension !== 'undefined' ? 
          (window.progressDashboardExtension.getDebugInfo ? window.progressDashboardExtension.getDebugInfo() : 'Available but no debug info') : 
          'Not available'
      };
      
      const blob = new Blob([JSON.stringify(diagnosticData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `extension-diagnostic-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }

    // Auto-update live status every 2 seconds
    setInterval(updateLiveStatus, 2000);

    // Listen for extension ready event
    window.addEventListener('progressDashboardExtensionReady', (event) => {
      log(`Extension ready event received: ${JSON.stringify(event.detail)}`, 'success');
      updateLiveStatus();
    });

    // Initial diagnostic
    setTimeout(() => {
      log('Advanced diagnostic tool loaded', 'info');
      updateLiveStatus();
      runFullDiagnostic();
    }, 1000);
  </script>
</body>
</html>
