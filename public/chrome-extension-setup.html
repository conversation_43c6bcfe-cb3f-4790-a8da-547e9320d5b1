<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🔐 Chrome Extension Setup - Progress Dashboard</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      min-height: 100vh;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 40px;
      border-radius: 16px;
      box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #1d4ed8;
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 20px;
    }
    
    .step-container {
      background: #f8fafc;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      padding: 25px;
      margin: 20px 0;
    }
    
    .step-header {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 15px;
    }
    
    .step-number {
      background: #3b82f6;
      color: white;
      width: 35px;
      height: 35px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 18px;
    }
    
    .step-title {
      font-size: 1.3rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
    }
    
    .step-content {
      margin-left: 50px;
    }
    
    .code-block {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 14px;
      overflow-x: auto;
      margin: 10px 0;
    }
    
    .warning-box {
      background: #fef3c7;
      border: 2px solid #f59e0b;
      border-radius: 8px;
      padding: 15px;
      margin: 15px 0;
    }
    
    .warning-box h4 {
      color: #92400e;
      margin-top: 0;
    }
    
    .success-box {
      background: #ecfdf5;
      border: 2px solid #10b981;
      border-radius: 8px;
      padding: 15px;
      margin: 15px 0;
    }
    
    .success-box h4 {
      color: #047857;
      margin-top: 0;
    }
    
    button {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      margin: 10px 5px;
      transition: all 0.3s ease;
    }
    
    button:hover {
      background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
      transform: translateY(-2px);
    }
    
    .screenshot {
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      max-width: 100%;
      margin: 15px 0;
    }
    
    .feature-list {
      list-style: none;
      padding: 0;
    }
    
    .feature-list li {
      padding: 8px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .feature-list li::before {
      content: "✅";
      font-size: 16px;
    }
    
    .troubleshooting {
      background: #fef2f2;
      border: 2px solid #ef4444;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .troubleshooting h3 {
      color: #dc2626;
      margin-top: 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔐 Chrome Extension Setup</h1>
    <p style="text-align: center; font-size: 1.2rem; color: #64748b;">
      Install the Progress Dashboard Chrome Extension for secure OTP authentication
    </p>

    <!-- Step 1: Download Extension -->
    <div class="step-container">
      <div class="step-header">
        <div class="step-number">1</div>
        <h3 class="step-title">Download Extension Files</h3>
      </div>
      <div class="step-content">
        <p>First, you need to get the extension files from the project directory:</p>
        <div class="code-block">
# Navigate to the chrome-extension directory
cd chrome-extension

# Verify all files are present
ls -la
        </div>
        <p><strong>Required files:</strong></p>
        <ul>
          <li><code>manifest.json</code> - Extension configuration</li>
          <li><code>background.js</code> - Background script</li>
          <li><code>content-robust.js</code> - Content script</li>
          <li><code>popup/</code> - Popup interface</li>
          <li><code>assets/</code> - Icons and images</li>
        </ul>
      </div>
    </div>

    <!-- Step 2: Open Chrome Extensions -->
    <div class="step-container">
      <div class="step-header">
        <div class="step-number">2</div>
        <h3 class="step-title">Open Chrome Extensions Page</h3>
      </div>
      <div class="step-content">
        <p>Open Chrome and navigate to the extensions management page:</p>
        <div class="code-block">
chrome://extensions/
        </div>
        <p><strong>Alternative methods:</strong></p>
        <ul>
          <li>Chrome menu → More tools → Extensions</li>
          <li>Right-click extension icon → Manage extensions</li>
          <li>Keyboard shortcut: <code>Ctrl+Shift+Delete</code> then click Extensions</li>
        </ul>
        <button onclick="window.open('chrome://extensions/', '_blank')">Open Extensions Page</button>
      </div>
    </div>

    <!-- Step 3: Enable Developer Mode -->
    <div class="step-container">
      <div class="step-header">
        <div class="step-number">3</div>
        <h3 class="step-title">Enable Developer Mode</h3>
      </div>
      <div class="step-content">
        <p>In the top-right corner of the Extensions page, toggle on <strong>"Developer mode"</strong>:</p>
        <div class="warning-box">
          <h4>⚠️ Important</h4>
          <p>Developer mode is required to install unpacked extensions. This is safe for local development.</p>
        </div>
        <p>After enabling Developer mode, you'll see three new buttons:</p>
        <ul>
          <li><strong>Load unpacked</strong> - For installing local extensions</li>
          <li><strong>Pack extension</strong> - For creating .crx files</li>
          <li><strong>Update</strong> - For refreshing extensions</li>
        </ul>
      </div>
    </div>

    <!-- Step 4: Load Extension -->
    <div class="step-container">
      <div class="step-header">
        <div class="step-number">4</div>
        <h3 class="step-title">Load Unpacked Extension</h3>
      </div>
      <div class="step-content">
        <p>Click the <strong>"Load unpacked"</strong> button and select the chrome-extension directory:</p>
        <div class="code-block">
1. Click "Load unpacked"
2. Navigate to your project folder
3. Select the "chrome-extension" directory
4. Click "Select Folder"
        </div>
        <div class="success-box">
          <h4>✅ Success Indicators</h4>
          <p>If successful, you should see:</p>
          <ul>
            <li>Progress Dashboard extension card appears</li>
            <li>Extension icon in Chrome toolbar</li>
            <li>No error messages</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Step 5: Configure Extension -->
    <div class="step-container">
      <div class="step-header">
        <div class="step-number">5</div>
        <h3 class="step-title">Configure Extension</h3>
      </div>
      <div class="step-content">
        <p>Configure the extension for optimal performance:</p>
        
        <h4>📌 Pin Extension to Toolbar</h4>
        <ol>
          <li>Click the puzzle piece icon in Chrome toolbar</li>
          <li>Find "Progress Dashboard" extension</li>
          <li>Click the pin icon to keep it visible</li>
        </ol>

        <h4>🔔 Enable Notifications</h4>
        <ol>
          <li>Click the extension icon</li>
          <li>Go to Settings</li>
          <li>Enable "Notification sounds" if desired</li>
          <li>For development, you can enable "Auto-approve requests"</li>
        </ol>

        <h4>🌐 Verify Permissions</h4>
        <p>The extension needs these permissions:</p>
        <ul class="feature-list">
          <li>Access to localhost:5173 (frontend)</li>
          <li>Access to localhost:5001 (backend)</li>
          <li>Notifications</li>
          <li>Storage</li>
        </ul>
      </div>
    </div>

    <!-- Step 6: Test Extension -->
    <div class="step-container">
      <div class="step-header">
        <div class="step-number">6</div>
        <h3 class="step-title">Test Extension</h3>
      </div>
      <div class="step-content">
        <p>Test the extension to ensure it's working correctly:</p>
        
        <h4>🧪 Basic Test</h4>
        <ol>
          <li>Click the extension icon</li>
          <li>Should show "Ready for Authentication" status</li>
          <li>Click "Test Connection" button</li>
          <li>Should show connection successful</li>
        </ol>

        <h4>🔐 OTP Flow Test</h4>
        <ol>
          <li>Go to <a href="http://localhost:5173/login" target="_blank">Login Page</a></li>
          <li>Enter email: <EMAIL></li>
          <li>Click "Send OTP"</li>
          <li>Extension should show notification</li>
          <li>Click "Approve" in extension popup</li>
          <li>Should complete login automatically</li>
        </ol>

        <button onclick="testExtension()">Test Extension Now</button>
        <button onclick="window.open('http://localhost:5173/login', '_blank')">Open Login Page</button>
      </div>
    </div>

    <!-- Extension Features -->
    <div class="step-container">
      <div class="step-header">
        <div class="step-number">✨</div>
        <h3 class="step-title">Extension Features</h3>
      </div>
      <div class="step-content">
        <h4>🔐 Security Features</h4>
        <ul class="feature-list">
          <li>Origin validation for secure communication</li>
          <li>Encrypted OTP transmission</li>
          <li>Request timeout and expiry</li>
          <li>User approval required for each login</li>
        </ul>

        <h4>🎨 User Interface</h4>
        <ul class="feature-list">
          <li>Beautiful popup with login request details</li>
          <li>Real-time countdown timer</li>
          <li>Visual status indicators</li>
          <li>Settings panel for customization</li>
        </ul>

        <h4>🔔 Notifications</h4>
        <ul class="feature-list">
          <li>Browser notifications for OTP requests</li>
          <li>Sound alerts (configurable)</li>
          <li>Badge counter for pending requests</li>
          <li>Success/failure feedback</li>
        </ul>
      </div>
    </div>

    <!-- Troubleshooting -->
    <div class="troubleshooting">
      <h3>🚨 Troubleshooting</h3>
      
      <h4>Extension Not Loading</h4>
      <ul>
        <li>Ensure Developer mode is enabled</li>
        <li>Check that all required files are present</li>
        <li>Look for error messages in Extensions page</li>
        <li>Try refreshing the extension</li>
        <li><strong>Version Error Fixed:</strong> Manifest now uses valid version format (1.0.0)</li>
        <li><strong>Module Error Fixed:</strong> Background script no longer uses ES modules</li>
      </ul>

      <h4>OTP Requests Not Received</h4>
      <ul>
        <li>Check if extension icon shows in toolbar</li>
        <li>Verify frontend is running on localhost:5173</li>
        <li>Check browser console for errors</li>
        <li>Ensure notifications are enabled</li>
      </ul>

      <h4>Permission Issues</h4>
      <ul>
        <li>Check extension permissions in chrome://extensions/</li>
        <li>Ensure localhost origins are allowed</li>
        <li>Try reloading the extension</li>
        <li>Check for CORS errors in console</li>
      </ul>

      <button onclick="window.open('http://localhost:5173/otp-error-diagnostic.html', '_blank')">
        Open Diagnostic Tool
      </button>
    </div>

    <!-- Next Steps -->
    <div class="success-box">
      <h4>🎉 Extension Installed Successfully!</h4>
      <p>Your Chrome Extension is now ready for secure OTP authentication.</p>
      <p><strong>Next steps:</strong></p>
      <ol>
        <li>Test the complete login flow</li>
        <li>Configure notification preferences</li>
        <li>Bookmark this page for future reference</li>
      </ol>
      
      <button onclick="window.open('http://localhost:5173/login', '_blank')">
        Start Testing Login
      </button>
    </div>
  </div>

  <script>
    function testExtension() {
      // Test extension availability
      if (window.progressDashboardExtension) {
        alert('✅ Extension is available and working!\n\nYou can now test the complete OTP flow.');
      } else {
        alert('❌ Extension not detected.\n\nPlease ensure:\n1. Extension is installed\n2. Page is refreshed\n3. Extension has proper permissions');
      }
    }

    // Auto-detect extension
    setTimeout(() => {
      if (window.progressDashboardExtension) {
        console.log('✅ Progress Dashboard Extension detected');
      } else {
        console.log('❌ Progress Dashboard Extension not detected');
      }
    }, 2000);
  </script>
</body>
</html>
