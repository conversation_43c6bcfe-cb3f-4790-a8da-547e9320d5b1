<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🚀 Install Chrome Extension NOW</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      min-height: 100vh;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 40px;
      border-radius: 16px;
      box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #1d4ed8;
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 20px;
    }
    
    .step {
      background: #f0f9ff;
      border: 2px solid #3b82f6;
      border-radius: 12px;
      padding: 25px;
      margin: 20px 0;
    }
    
    .step h3 {
      color: #1d4ed8;
      margin-top: 0;
      font-size: 1.3rem;
    }
    
    .step-number {
      background: #3b82f6;
      color: white;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 10px;
    }
    
    button {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
      border: none;
      padding: 15px 30px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 18px;
      font-weight: 600;
      margin: 10px 5px;
      transition: all 0.3s ease;
    }
    
    button:hover {
      background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
      transform: translateY(-2px);
    }
    
    .code-block {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 14px;
      margin: 15px 0;
    }
    
    .warning {
      background: #fef3c7;
      border: 2px solid #f59e0b;
      border-radius: 8px;
      padding: 15px;
      margin: 15px 0;
      color: #92400e;
    }
    
    .success {
      background: #ecfdf5;
      border: 2px solid #10b981;
      border-radius: 8px;
      padding: 15px;
      margin: 15px 0;
      color: #047857;
    }
    
    .test-section {
      background: #f8fafc;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      padding: 25px;
      margin: 20px 0;
    }
    
    .status {
      padding: 15px;
      border-radius: 8px;
      margin: 15px 0;
      font-weight: 500;
    }
    
    .status.success {
      background: #ecfdf5;
      border: 2px solid #10b981;
      color: #047857;
    }
    
    .status.error {
      background: #fef2f2;
      border: 2px solid #ef4444;
      color: #dc2626;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🚀 Install Chrome Extension</h1>
    <p style="text-align: center; font-size: 1.2rem; color: #64748b;">
      Follow these exact steps to install the Progress Dashboard Chrome Extension
    </p>

    <div class="warning">
      <h4>⚠️ IMPORTANT</h4>
      <p>Chrome Extension is required for OTP authentication. Without it, you cannot login to the dashboard.</p>
    </div>

    <!-- Step 1 -->
    <div class="step">
      <h3><span class="step-number">1</span>Open Chrome Extensions Page</h3>
      <p>Click the button below to open Chrome Extensions management page:</p>
      <button onclick="openExtensionsPage()">Open chrome://extensions/</button>
      <div class="code-block">
Alternative: Type "chrome://extensions/" in address bar
      </div>
    </div>

    <!-- Step 2 -->
    <div class="step">
      <h3><span class="step-number">2</span>Enable Developer Mode</h3>
      <p>In the Extensions page:</p>
      <ol>
        <li>Look for "Developer mode" toggle in the top-right corner</li>
        <li>Click to enable it (should turn blue/on)</li>
        <li>You'll see new buttons appear: "Load unpacked", "Pack extension", "Update"</li>
      </ol>
    </div>

    <!-- Step 3 -->
    <div class="step">
      <h3><span class="step-number">3</span>Load Extension</h3>
      <p>Click "Load unpacked" button and select the extension folder:</p>
      <ol>
        <li>Click "Load unpacked" button</li>
        <li>Navigate to your project folder</li>
        <li>Select the <strong>"chrome-extension"</strong> folder</li>
        <li>Click "Select Folder"</li>
      </ol>
      <div class="code-block">
Extension folder path:
/Users/<USER>/Library/CloudStorage/Dropbox/Assets/Apps/Dashboard/Progress_Dashboard-main/chrome-extension
      </div>
    </div>

    <!-- Step 4 -->
    <div class="step">
      <h3><span class="step-number">4</span>Verify Installation</h3>
      <p>After successful installation, you should see:</p>
      <ul>
        <li>✅ "Progress Dashboard OTP Authenticator" extension card</li>
        <li>✅ Extension icon in Chrome toolbar</li>
        <li>✅ No error messages</li>
      </ul>
      <div class="success">
        <h4>✅ Success Indicators</h4>
        <p>Extension is working if you see the extension icon in your Chrome toolbar and no error messages in the Extensions page.</p>
      </div>
    </div>

    <!-- Step 5 -->
    <div class="step">
      <h3><span class="step-number">5</span>Test Extension</h3>
      <p>After installation, test if the extension is working:</p>
      <button onclick="testExtension()">Test Extension Now</button>
      <button onclick="refreshAndTest()">Refresh Page & Test</button>
      <div id="testResult"></div>
    </div>

    <!-- Test Section -->
    <div class="test-section">
      <h3>🧪 Extension Communication Test</h3>
      <p>Real-time test untuk check extension communication:</p>
      <button onclick="runFullTest()">Run Full Test</button>
      <button onclick="window.open('http://localhost:5174/debug-extension-communication.html', '_blank')">
        Open Debug Page
      </button>
      <div id="communicationTest"></div>
    </div>

    <!-- Quick Actions -->
    <div class="test-section">
      <h3>⚡ Quick Actions</h3>
      <button onclick="window.open('http://localhost:5174/login', '_blank')">Test Login Page</button>
      <button onclick="window.location.reload()">Refresh This Page</button>
      <button onclick="window.open('http://localhost:5174/chrome-extension-setup.html', '_blank')">
        Full Setup Guide
      </button>
    </div>
  </div>

  <script>
    function openExtensionsPage() {
      // Try to open extensions page
      try {
        window.open('chrome://extensions/', '_blank');
      } catch (error) {
        alert('Please manually open chrome://extensions/ in a new tab');
      }
    }

    function testExtension() {
      const resultDiv = document.getElementById('testResult');
      
      if (window.progressDashboardExtension) {
        try {
          const info = window.progressDashboardExtension.getDebugInfo();
          resultDiv.innerHTML = `
            <div class="status success">
              ✅ Extension detected successfully!<br>
              • Version: ${info.version || 'Unknown'}<br>
              • Injection Method: ${info.injectionMethod || 'Unknown'}<br>
              • Available: ${info.isAvailable}<br>
              • Ready for OTP authentication!
            </div>
          `;
        } catch (error) {
          resultDiv.innerHTML = `
            <div class="status error">
              ⚠️ Extension bridge found but error getting info:<br>
              ${error.message}
            </div>
          `;
        }
      } else {
        resultDiv.innerHTML = `
          <div class="status error">
            ❌ Extension not detected<br>
            • Please ensure extension is installed<br>
            • Refresh this page after installation<br>
            • Check for errors in chrome://extensions/
          </div>
        `;
      }
    }

    function refreshAndTest() {
      window.location.reload();
    }

    function runFullTest() {
      const testDiv = document.getElementById('communicationTest');
      testDiv.innerHTML = '<p>Running communication test...</p>';
      
      setTimeout(() => {
        if (window.progressDashboardExtension) {
          // Test extension communication
          const testMessage = {
            type: 'EXTENSION_PING',
            data: { test: true },
            timestamp: Date.now()
          };
          
          try {
            window.progressDashboardExtension.sendMessage(testMessage)
              .then(response => {
                testDiv.innerHTML = `
                  <div class="status success">
                    ✅ Extension communication working!<br>
                    Response: ${JSON.stringify(response, null, 2)}
                  </div>
                `;
              })
              .catch(error => {
                testDiv.innerHTML = `
                  <div class="status error">
                    ❌ Extension communication failed:<br>
                    ${error.message}
                  </div>
                `;
              });
          } catch (error) {
            testDiv.innerHTML = `
              <div class="status error">
                ❌ Extension method error:<br>
                ${error.message}
              </div>
            `;
          }
        } else {
          testDiv.innerHTML = `
            <div class="status error">
              ❌ Extension not available for communication test<br>
              Please install extension first
            </div>
          `;
        }
      }, 1000);
    }

    // Auto-test on page load
    window.addEventListener('load', () => {
      setTimeout(() => {
        testExtension();
      }, 2000);
    });

    // Listen for extension ready event
    window.addEventListener('progressDashboardExtensionReady', (event) => {
      console.log('Extension ready event received!');
      setTimeout(testExtension, 500);
    });

    // Listen for messages
    window.addEventListener('message', (event) => {
      if (event.origin !== window.location.origin) return;
      console.log('Message received:', event.data);
    });
  </script>
</body>
</html>
