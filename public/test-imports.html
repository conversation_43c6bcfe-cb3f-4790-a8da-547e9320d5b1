<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Import Test - Authentication System</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: #f8fafc;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #1e293b;
      margin-bottom: 20px;
    }
    
    .test-section {
      margin: 25px 0;
      padding: 20px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      background: #f8fafc;
    }
    
    .test-section h3 {
      margin-top: 0;
      color: #475569;
    }
    
    .status {
      padding: 12px;
      border-radius: 6px;
      margin: 10px 0;
      font-weight: 500;
      font-family: 'Monaco', '<PERSON>lo', monospace;
      font-size: 13px;
    }
    
    .status.success {
      background: #dcfce7;
      color: #166534;
      border: 1px solid #bbf7d0;
    }
    
    .status.error {
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }
    
    .status.warning {
      background: #fef3c7;
      color: #d97706;
      border: 1px solid #fde68a;
    }
    
    button {
      background: #95E565;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin: 5px;
      transition: background 0.2s;
    }
    
    button:hover {
      background: #608F44;
    }
    
    .log-container {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
      margin: 10px 0;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🧪 Import Test - Authentication System</h1>
    <p>Testing all authentication imports and dependencies.</p>
    
    <!-- Extension Bridge Test -->
    <div class="test-section">
      <h3>🔗 Extension Bridge Test</h3>
      <div id="extensionTest" class="status warning">Testing extension bridge...</div>
      <button onclick="testExtensionBridge()">Test Extension Bridge</button>
    </div>

    <!-- Auth Context Test -->
    <div class="test-section">
      <h3>🔐 Auth Context Test</h3>
      <div id="authContextTest" class="status warning">Testing auth context availability...</div>
      <button onclick="testAuthContext()">Test Auth Context</button>
    </div>

    <!-- Storage Keys Test -->
    <div class="test-section">
      <h3>🗄️ Storage Keys Test</h3>
      <div id="storageTest" class="status warning">Testing storage keys...</div>
      <button onclick="testStorageKeys()">Test Storage Keys</button>
    </div>

    <!-- API Client Test -->
    <div class="test-section">
      <h3>🌐 API Client Test</h3>
      <div id="apiTest" class="status warning">Testing API client...</div>
      <button onclick="testAPIClient()">Test API Client</button>
    </div>

    <!-- Console Logs -->
    <div class="test-section">
      <h3>📝 Console Logs</h3>
      <div id="consoleLogs" class="log-container">Test logs will appear here...\n</div>
      <button onclick="clearLogs()">Clear Logs</button>
      <button onclick="runAllTests()">Run All Tests</button>
    </div>

    <!-- Fix Instructions -->
    <div class="test-section">
      <h3>🔧 Fix Instructions</h3>
      <div id="fixInstructions" class="status warning">
        Run tests to see specific fix instructions for any failing imports.
      </div>
    </div>
  </div>

  <script>
    // Logging function
    function log(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
      
      const consoleLogs = document.getElementById('consoleLogs');
      consoleLogs.textContent += logEntry + '\n';
      consoleLogs.scrollTop = consoleLogs.scrollHeight;
      
      console.log(logEntry);
    }

    // Test extension bridge
    function testExtensionBridge() {
      log('Testing extension bridge...', 'info');
      
      const testElement = document.getElementById('extensionTest');
      
      if (typeof window.progressDashboardExtension !== 'undefined') {
        const bridge = window.progressDashboardExtension;
        
        testElement.textContent = `✅ Extension bridge available - Version: ${bridge.version || 'unknown'}`;
        testElement.className = 'status success';
        
        log(`Extension bridge found: ${JSON.stringify(bridge.getDebugInfo ? bridge.getDebugInfo() : 'Basic bridge')}`, 'success');
        
        // Test communication
        if (typeof bridge.sendMessage === 'function') {
          bridge.sendMessage({ type: 'TEST_CONNECTION' })
            .then(response => {
              log(`Extension communication test successful: ${JSON.stringify(response)}`, 'success');
            })
            .catch(error => {
              log(`Extension communication test failed: ${error.message}`, 'warning');
            });
        }
      } else {
        testElement.textContent = '❌ Extension bridge not found';
        testElement.className = 'status error';
        log('Extension bridge not found in window object', 'error');
      }
    }

    // Test auth context (simulated)
    function testAuthContext() {
      log('Testing auth context availability...', 'info');
      
      const testElement = document.getElementById('authContextTest');
      
      // Check if we're in React context (we're not, but simulate)
      const hasReactContext = typeof React !== 'undefined';
      
      if (hasReactContext) {
        testElement.textContent = '✅ React context available';
        testElement.className = 'status success';
        log('React context detected', 'success');
      } else {
        testElement.textContent = '⚠️ Not in React context (expected for this test page)';
        testElement.className = 'status warning';
        log('Not in React context - this is expected for standalone test page', 'warning');
      }
    }

    // Test storage keys
    function testStorageKeys() {
      log('Testing storage keys...', 'info');
      
      const testElement = document.getElementById('storageTest');
      
      // Test localStorage availability
      try {
        localStorage.setItem('test_key', 'test_value');
        const testValue = localStorage.getItem('test_key');
        localStorage.removeItem('test_key');
        
        if (testValue === 'test_value') {
          testElement.textContent = '✅ LocalStorage working correctly';
          testElement.className = 'status success';
          log('LocalStorage test successful', 'success');
        } else {
          throw new Error('LocalStorage read/write failed');
        }
      } catch (error) {
        testElement.textContent = `❌ LocalStorage error: ${error.message}`;
        testElement.className = 'status error';
        log(`LocalStorage test failed: ${error.message}`, 'error');
      }
    }

    // Test API client
    function testAPIClient() {
      log('Testing API client...', 'info');
      
      const testElement = document.getElementById('apiTest');
      
      // Test fetch availability
      if (typeof fetch !== 'undefined') {
        testElement.textContent = '✅ Fetch API available';
        testElement.className = 'status success';
        log('Fetch API available', 'success');
        
        // Test actual API call (will likely fail due to CORS, but that's expected)
        fetch('http://localhost:5001/api/health')
          .then(response => {
            log(`API health check response: ${response.status}`, 'info');
          })
          .catch(error => {
            log(`API health check failed (expected): ${error.message}`, 'warning');
          });
      } else {
        testElement.textContent = '❌ Fetch API not available';
        testElement.className = 'status error';
        log('Fetch API not available', 'error');
      }
    }

    // Run all tests
    function runAllTests() {
      log('Running all tests...', 'info');
      
      testExtensionBridge();
      setTimeout(() => testAuthContext(), 500);
      setTimeout(() => testStorageKeys(), 1000);
      setTimeout(() => testAPIClient(), 1500);
      
      setTimeout(() => {
        generateFixInstructions();
      }, 2000);
    }

    // Generate fix instructions
    function generateFixInstructions() {
      const fixElement = document.getElementById('fixInstructions');
      
      const extensionWorking = typeof window.progressDashboardExtension !== 'undefined';
      const storageWorking = typeof localStorage !== 'undefined';
      const fetchWorking = typeof fetch !== 'undefined';
      
      let instructions = [];
      
      if (!extensionWorking) {
        instructions.push('1. Install Chrome Extension from chrome://extensions/');
        instructions.push('2. Ensure extension is enabled and has permissions');
        instructions.push('3. Refresh this page after extension installation');
      }
      
      if (!storageWorking) {
        instructions.push('4. Enable localStorage in browser settings');
        instructions.push('5. Check if running in private/incognito mode');
      }
      
      if (!fetchWorking) {
        instructions.push('6. Update browser to support Fetch API');
        instructions.push('7. Check for browser compatibility issues');
      }
      
      if (instructions.length === 0) {
        fixElement.textContent = '✅ All tests passed! System ready for authentication.';
        fixElement.className = 'status success';
        log('All tests passed successfully', 'success');
      } else {
        fixElement.textContent = 'Fix Instructions:\n' + instructions.join('\n');
        fixElement.className = 'status warning';
        log(`Generated ${instructions.length} fix instructions`, 'warning');
      }
    }

    // Clear logs
    function clearLogs() {
      document.getElementById('consoleLogs').textContent = 'Logs cleared...\n';
    }

    // Auto-run tests on page load
    setTimeout(() => {
      log('Import test page loaded', 'info');
      runAllTests();
    }, 1000);

    // Listen for extension ready event
    window.addEventListener('progressDashboardExtensionReady', (event) => {
      log(`Extension ready event received: ${JSON.stringify(event.detail)}`, 'success');
      testExtensionBridge();
    });
  </script>
</body>
</html>
