<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🚀 Backend Setup Guide - Fix OTP Errors</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 40px;
      border-radius: 16px;
      box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #dc2626;
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 20px;
    }
    
    .error-banner {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
      padding: 20px;
      border-radius: 12px;
      text-align: center;
      margin: 20px 0;
      font-size: 1.2rem;
      font-weight: 600;
    }
    
    .solution-section {
      margin: 25px 0;
      padding: 25px;
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      background: #f8fafc;
    }
    
    .solution-section.urgent {
      border-color: #ef4444;
      background: #fef2f2;
    }
    
    .solution-section.success {
      border-color: #10b981;
      background: #ecfdf5;
    }
    
    .solution-section h3 {
      color: #059669;
      margin-top: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .urgent h3 {
      color: #dc2626;
    }
    
    .code-block {
      background: #1e293b;
      color: #e2e8f0;
      padding: 20px;
      border-radius: 8px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 13px;
      overflow-x: auto;
      margin: 15px 0;
      white-space: pre-wrap;
      border-left: 4px solid #ef4444;
    }
    
    .code-block.success {
      border-left-color: #10b981;
    }
    
    button {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      margin: 8px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }
    
    button:hover {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      transform: translateY(-2px);
    }
    
    button.success {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    button.success:hover {
      background: linear-gradient(135deg, #059669 0%, #047857 100%);
    }
    
    .step-list {
      list-style: none;
      padding: 0;
    }
    
    .step-list li {
      padding: 15px 0;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      align-items: flex-start;
      gap: 15px;
    }
    
    .step-list li:last-child {
      border-bottom: none;
    }
    
    .step-number {
      background: #ef4444;
      color: white;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      flex-shrink: 0;
    }
    
    .status {
      padding: 15px;
      border-radius: 8px;
      margin: 15px 0;
      font-weight: 500;
    }
    
    .status.error {
      background: #fef2f2;
      color: #dc2626;
      border: 2px solid #fecaca;
    }
    
    .status.warning {
      background: #fef3c7;
      color: #d97706;
      border: 2px solid #fde68a;
    }
    
    .status.success {
      background: #dcfce7;
      color: #166534;
      border: 2px solid #bbf7d0;
    }
    
    .terminal-window {
      background: #1e293b;
      border-radius: 8px;
      padding: 20px;
      margin: 15px 0;
    }
    
    .terminal-header {
      color: #94a3b8;
      font-size: 12px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .terminal-content {
      color: #e2e8f0;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 13px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🚨 Backend Server Not Running</h1>
    
    <div class="error-banner">
      ❌ OTP Authentication Failed: Backend server is not running on localhost:5001
    </div>

    <!-- Problem Diagnosis -->
    <div class="solution-section urgent">
      <h3>🔍 Problem Diagnosis</h3>
      <div class="status error">
        <strong>Error Details:</strong><br>
        • ERR_CONNECTION_REFUSED to localhost:5001<br>
        • OTP request failed: TypeError: Failed to fetch<br>
        • Authentication error: Network error
      </div>
      
      <p><strong>Root Cause:</strong> The Flask backend server that handles OTP authentication is not running.</p>
      
      <div class="terminal-window">
        <div class="terminal-header">
          <span>🔴</span>
          <span>Backend Status Check</span>
        </div>
        <div class="terminal-content">$ curl http://localhost:5001/api/auth/health
curl: (7) Failed to connect to localhost port 5001: Connection refused

❌ Backend server is not running</div>
      </div>
    </div>

    <!-- Quick Solution -->
    <div class="solution-section success">
      <h3>⚡ Quick Solution</h3>
      <ol class="step-list">
        <li>
          <span class="step-number">1</span>
          <div>
            <strong>Open Terminal in Backend Directory</strong><br>
            <div class="code-block success">cd backend</div>
          </div>
        </li>
        <li>
          <span class="step-number">2</span>
          <div>
            <strong>Start Backend Server</strong><br>
            <div class="code-block success">./start.sh</div>
            <small>Or manually: <code>python app.py</code></small>
          </div>
        </li>
        <li>
          <span class="step-number">3</span>
          <div>
            <strong>Verify Server Running</strong><br>
            <div class="code-block success">✅ Flask API server running at: http://localhost:5001
🔐 OTP Authentication system initialized successfully</div>
          </div>
        </li>
        <li>
          <span class="step-number">4</span>
          <div>
            <strong>Test OTP Again</strong><br>
            Go back to login page and try sending OTP
          </div>
        </li>
      </ol>
      
      <button onclick="testBackend()" class="success">Test Backend Connection</button>
      <button onclick="openTerminalGuide()">Terminal Setup Guide</button>
    </div>

    <!-- Detailed Setup -->
    <div class="solution-section">
      <h3>🛠️ Detailed Backend Setup</h3>
      
      <p><strong>Prerequisites:</strong></p>
      <ul>
        <li>Python 3.8 or higher</li>
        <li>pip3 package manager</li>
        <li>Redis server (optional, for production)</li>
      </ul>
      
      <div class="terminal-window">
        <div class="terminal-header">
          <span>🟢</span>
          <span>Backend Setup Commands</span>
        </div>
        <div class="terminal-content"># Navigate to backend directory
cd backend

# Make start script executable
chmod +x start.sh

# Start backend server
./start.sh

# Expected output:
🚀 Starting CSV Data Processing API...
📦 Creating virtual environment...
🔧 Activating virtual environment...
📥 Installing dependencies...
✅ Setup complete!
🌐 Starting Flask API server...
📊 API will be available at: http://localhost:5001
🔐 OTP Authentication system initialized successfully</div>
      </div>
    </div>

    <!-- Alternative Methods -->
    <div class="solution-section">
      <h3>🔄 Alternative Start Methods</h3>
      
      <p><strong>Method 1: Direct Python</strong></p>
      <div class="code-block">cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python app.py</div>
      
      <p><strong>Method 2: Using start_optimized.sh</strong></p>
      <div class="code-block">cd backend
./start_optimized.sh</div>
      
      <p><strong>Method 3: Docker (if available)</strong></p>
      <div class="code-block">cd backend
docker build -t progress-dashboard-backend .
docker run -p 5001:5001 progress-dashboard-backend</div>
    </div>

    <!-- Troubleshooting -->
    <div class="solution-section">
      <h3>🔧 Troubleshooting</h3>
      
      <p><strong>Common Issues:</strong></p>
      
      <div class="status warning">
        <strong>Port 5001 Already in Use:</strong><br>
        Kill existing process: <code>lsof -ti:5001 | xargs kill -9</code>
      </div>
      
      <div class="status warning">
        <strong>Python Not Found:</strong><br>
        Install Python 3.8+: <code>brew install python3</code> (macOS) or download from python.org
      </div>
      
      <div class="status warning">
        <strong>Permission Denied:</strong><br>
        Make script executable: <code>chmod +x start.sh</code>
      </div>
      
      <div class="status warning">
        <strong>Dependencies Failed:</strong><br>
        Update pip: <code>pip install --upgrade pip</code><br>
        Install manually: <code>pip install flask flask-cors redis pyotp cryptography</code>
      </div>
    </div>

    <!-- Test Connection -->
    <div class="solution-section">
      <h3>🧪 Test Backend Connection</h3>
      <div id="connectionStatus" class="status warning">Click button below to test backend connection</div>
      
      <button onclick="testConnection()">Test Connection</button>
      <button onclick="testOTPEndpoint()">Test OTP Endpoint</button>
      <button onclick="openBackendLogs()">View Backend Logs</button>
    </div>

    <!-- Next Steps -->
    <div class="solution-section success">
      <h3>🎯 After Backend is Running</h3>
      <ol>
        <li><strong>Verify Connection:</strong> Backend should respond at http://localhost:5001</li>
        <li><strong>Test Login:</strong> Go back to login page and try OTP again</li>
        <li><strong>Check Console:</strong> Should see successful API calls instead of errors</li>
        <li><strong>Extension Communication:</strong> OTP should be sent to Chrome Extension</li>
      </ol>
      
      <button onclick="goToLogin()" class="success">Go to Login Page</button>
      <button onclick="openDiagnostics()">Open Diagnostics</button>
    </div>
  </div>

  <script>
    async function testConnection() {
      const statusElement = document.getElementById('connectionStatus');
      statusElement.textContent = '🔄 Testing backend connection...';
      statusElement.className = 'status warning';
      
      try {
        const response = await fetch('http://localhost:5001/api/auth/health');
        if (response.ok) {
          statusElement.textContent = '✅ Backend server is running and responding!';
          statusElement.className = 'status success';
        } else {
          statusElement.textContent = `❌ Backend responded with error: ${response.status}`;
          statusElement.className = 'status error';
        }
      } catch (error) {
        statusElement.textContent = `❌ Cannot connect to backend: ${error.message}`;
        statusElement.className = 'status error';
      }
    }

    async function testOTPEndpoint() {
      const statusElement = document.getElementById('connectionStatus');
      statusElement.textContent = '🔄 Testing OTP endpoint...';
      statusElement.className = 'status warning';
      
      try {
        const response = await fetch('http://localhost:5001/api/auth/generate-otp', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: '<EMAIL>' })
        });
        
        if (response.ok) {
          statusElement.textContent = '✅ OTP endpoint is working! Backend is ready.';
          statusElement.className = 'status success';
        } else {
          const data = await response.json();
          statusElement.textContent = `⚠️ OTP endpoint responded: ${data.error || 'Unknown error'}`;
          statusElement.className = 'status warning';
        }
      } catch (error) {
        statusElement.textContent = `❌ OTP endpoint test failed: ${error.message}`;
        statusElement.className = 'status error';
      }
    }

    function testBackend() {
      window.open('http://localhost:5001/api/auth/health', '_blank');
    }

    function openTerminalGuide() {
      alert(`Terminal Setup Guide:

🖥️ macOS/Linux:
1. Open Terminal app
2. Navigate: cd /path/to/Progress_Dashboard-main/backend
3. Run: ./start.sh

🪟 Windows:
1. Open Command Prompt or PowerShell
2. Navigate: cd C:\\path\\to\\Progress_Dashboard-main\\backend
3. Run: python app.py

📝 Alternative:
- Use VS Code integrated terminal
- Use any terminal application`);
    }

    function openBackendLogs() {
      alert(`Backend Logs Location:

When you run ./start.sh, logs will appear in the terminal.

Look for:
✅ "Flask API server running at: http://localhost:5001"
✅ "OTP Authentication system initialized successfully"
❌ Any error messages in red

If you see errors, copy them and ask for help.`);
    }

    function goToLogin() {
      window.open('http://localhost:5173/login', '_blank');
    }

    function openDiagnostics() {
      window.open('http://localhost:5173/advanced-diagnostic.html', '_blank');
    }

    // Auto-test connection on page load
    setTimeout(testConnection, 1000);
  </script>
</body>
</html>
