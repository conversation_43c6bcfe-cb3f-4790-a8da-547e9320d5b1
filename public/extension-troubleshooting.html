<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🔧 Extension Troubleshooting</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      min-height: 100vh;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 40px;
      border-radius: 16px;
      box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #d97706;
      text-align: center;
      font-size: 2.5rem;
      margin-bottom: 20px;
    }
    
    .issue-section {
      background: #fef3c7;
      border: 2px solid #f59e0b;
      border-radius: 12px;
      padding: 25px;
      margin: 20px 0;
    }
    
    .issue-section h3 {
      color: #92400e;
      margin-top: 0;
    }
    
    button {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      margin: 10px 5px;
      transition: all 0.3s ease;
    }
    
    button:hover {
      background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
      transform: translateY(-2px);
    }
    
    .status {
      padding: 15px;
      border-radius: 8px;
      margin: 15px 0;
      font-weight: 500;
    }
    
    .status.success {
      background: #ecfdf5;
      border: 2px solid #10b981;
      color: #047857;
    }
    
    .status.error {
      background: #fef2f2;
      border: 2px solid #ef4444;
      color: #dc2626;
    }
    
    .status.warning {
      background: #fef3c7;
      border: 2px solid #f59e0b;
      color: #92400e;
    }
    
    .code-block {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 14px;
      margin: 15px 0;
      white-space: pre-wrap;
    }
    
    .checklist {
      list-style: none;
      padding: 0;
    }
    
    .checklist li {
      padding: 8px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .checklist li::before {
      content: "☐";
      font-size: 18px;
      color: #f59e0b;
    }
    
    .checklist li.checked::before {
      content: "✅";
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔧 Extension Troubleshooting</h1>
    <p style="text-align: center; font-size: 1.2rem; color: #64748b;">
      Diagnose dan fix Chrome Extension installation issues
    </p>

    <!-- Issue 1: Extension Installed but Not Visible -->
    <div class="issue-section">
      <h3>🚨 Issue: Extension Installed but Not Detected</h3>
      <p>You mentioned the extension is installed, but it's not showing up. Let's diagnose this step by step.</p>
      
      <h4>Step 1: Verify Extension is Actually Installed</h4>
      <button onclick="checkExtensionInChrome()">Check Extensions in Chrome</button>
      <div id="extensionCheckResult"></div>
      
      <h4>Step 2: Check for Extension Errors</h4>
      <p>Extensions can be installed but have errors that prevent them from working:</p>
      <ul class="checklist">
        <li>Open chrome://extensions/</li>
        <li>Look for "Progress Dashboard OTP Authenticator"</li>
        <li>Check if there are any red error messages</li>
        <li>Click "Details" if the extension is there</li>
        <li>Check "Inspect views" for errors</li>
      </ul>
      <button onclick="window.open('chrome://extensions/', '_blank')">Open Extensions Page</button>
    </div>

    <!-- Issue 2: Content Script Not Injecting -->
    <div class="issue-section">
      <h3>🔍 Issue: Content Script Not Injecting</h3>
      <p>Even if extension is installed, content script might not be injecting into web pages.</p>
      
      <h4>Possible Causes:</h4>
      <ul>
        <li><strong>Permission Issues</strong>: Extension doesn't have permission for localhost</li>
        <li><strong>Content Script Errors</strong>: Syntax errors in content-robust.js</li>
        <li><strong>Manifest Issues</strong>: Wrong content script configuration</li>
        <li><strong>CSP Issues</strong>: Content Security Policy blocking injection</li>
      </ul>
      
      <button onclick="testContentScriptInjection()">Test Content Script Injection</button>
      <div id="contentScriptResult"></div>
    </div>

    <!-- Issue 3: Manual Extension Installation -->
    <div class="issue-section">
      <h3>🛠️ Manual Extension Installation (Step by Step)</h3>
      <p>Let's try installing the extension again with detailed steps:</p>
      
      <h4>Exact Steps:</h4>
      <ol>
        <li><strong>Open Chrome Extensions</strong>: chrome://extensions/</li>
        <li><strong>Enable Developer Mode</strong>: Toggle in top-right (must be ON)</li>
        <li><strong>Click "Load unpacked"</strong></li>
        <li><strong>Navigate to project folder</strong></li>
        <li><strong>Select "chrome-extension" folder</strong> (not individual files)</li>
        <li><strong>Click "Select Folder"</strong></li>
      </ol>
      
      <div class="code-block">Extension Folder Path:
/Users/<USER>/Library/CloudStorage/Dropbox/Assets/Apps/Dashboard/Progress_Dashboard-main/chrome-extension

⚠️ IMPORTANT: Select the FOLDER "chrome-extension", not the files inside it!</div>
      
      <button onclick="reinstallExtension()">Guide Me Through Reinstallation</button>
      <div id="reinstallResult"></div>
    </div>

    <!-- Issue 4: Extension Permissions -->
    <div class="issue-section">
      <h3>🔐 Issue: Extension Permissions</h3>
      <p>Extension might be installed but not have proper permissions for localhost.</p>
      
      <h4>Check Permissions:</h4>
      <button onclick="checkExtensionPermissions()">Check Extension Permissions</button>
      <div id="permissionsResult"></div>
      
      <h4>Required Permissions:</h4>
      <ul>
        <li>✅ Access to localhost:* (all localhost ports)</li>
        <li>✅ Storage permission</li>
        <li>✅ Notifications permission</li>
        <li>✅ Active tab permission</li>
      </ul>
    </div>

    <!-- Issue 5: Console Errors -->
    <div class="issue-section">
      <h3>🐛 Issue: Check for Console Errors</h3>
      <p>Let's check if there are any JavaScript errors preventing the extension from working.</p>
      
      <button onclick="checkConsoleErrors()">Check Console Errors</button>
      <button onclick="checkExtensionConsole()">Check Extension Console</button>
      <div id="consoleErrorsResult"></div>
      
      <h4>How to Check Extension Console:</h4>
      <ol>
        <li>Go to chrome://extensions/</li>
        <li>Find "Progress Dashboard OTP Authenticator"</li>
        <li>Click "Details"</li>
        <li>Click "Inspect views: background page" (if available)</li>
        <li>Check for errors in the console</li>
      </ol>
    </div>

    <!-- Issue 6: Alternative Testing -->
    <div class="issue-section">
      <h3>🧪 Alternative Testing Methods</h3>
      <p>Let's try different approaches to test extension functionality:</p>
      
      <button onclick="testExtensionAPI()">Test Extension API</button>
      <button onclick="testPostMessageDirect()">Test Direct PostMessage</button>
      <button onclick="injectTestScript()">Inject Test Script</button>
      <div id="alternativeTestResult"></div>
    </div>

    <!-- Debug Console -->
    <div class="issue-section">
      <h3>📋 Debug Information</h3>
      <button onclick="collectDebugInfo()">Collect Debug Info</button>
      <button onclick="exportDebugInfo()">Export Debug Info</button>
      <div id="debugInfo" class="code-block">Debug information will appear here...</div>
    </div>
  </div>

  <script>
    let debugLog = [];

    function log(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      const entry = `[${timestamp}] ${message}`;
      debugLog.push(entry);
      
      const debugDiv = document.getElementById('debugInfo');
      debugDiv.textContent = debugLog.join('\n');
      
      console.log(`[Extension Troubleshooting] ${message}`);
    }

    function showResult(elementId, message, type) {
      const element = document.getElementById(elementId);
      element.innerHTML = `<div class="status ${type}">${message}</div>`;
    }

    function checkExtensionInChrome() {
      log('Checking extension installation in Chrome...');
      
      // Check if extension bridge exists
      const hasExtensionBridge = !!window.progressDashboardExtension;
      const hasExtensionReady = !!window.progressDashboardExtensionReady;
      
      // Check for extension in chrome.runtime
      let chromeExtensionInfo = 'Not available';
      try {
        if (typeof chrome !== 'undefined' && chrome.runtime) {
          chromeExtensionInfo = chrome.runtime.id || 'No ID available';
        }
      } catch (error) {
        chromeExtensionInfo = `Error: ${error.message}`;
      }
      
      log(`Extension bridge: ${hasExtensionBridge}`);
      log(`Extension ready: ${hasExtensionReady}`);
      log(`Chrome runtime: ${chromeExtensionInfo}`);
      
      if (hasExtensionBridge) {
        showResult('extensionCheckResult', 
          `✅ Extension bridge found!<br>
           • Bridge available: ${hasExtensionBridge}<br>
           • Ready event: ${hasExtensionReady}<br>
           • Chrome runtime: ${chromeExtensionInfo}`, 'success');
      } else {
        showResult('extensionCheckResult', 
          `❌ Extension not detected in this page<br>
           • No extension bridge found<br>
           • Chrome runtime: ${chromeExtensionInfo}<br>
           • Extension may not be installed or not working`, 'error');
      }
    }

    function testContentScriptInjection() {
      log('Testing content script injection...');
      
      // Check for various indicators of content script injection
      const indicators = {
        extensionBridge: !!window.progressDashboardExtension,
        extensionMarker: !!document.querySelector('[data-progress-dashboard-extension]'),
        contentScriptMarker: !!window.progressDashboardContentScript,
        extensionScripts: document.scripts.length
      };
      
      log(`Content script indicators: ${JSON.stringify(indicators)}`);
      
      // Try to manually trigger content script
      try {
        const event = new CustomEvent('progressDashboardExtensionCheck', {
          detail: { timestamp: Date.now() }
        });
        window.dispatchEvent(event);
        log('Dispatched extension check event');
      } catch (error) {
        log(`Error dispatching event: ${error.message}`);
      }
      
      if (indicators.extensionBridge) {
        showResult('contentScriptResult', 
          `✅ Content script working!<br>
           • Extension bridge: ${indicators.extensionBridge}<br>
           • DOM marker: ${indicators.extensionMarker}<br>
           • Script marker: ${indicators.contentScriptMarker}`, 'success');
      } else {
        showResult('contentScriptResult', 
          `❌ Content script not injected<br>
           • No extension bridge<br>
           • No DOM markers<br>
           • Extension may not be running<br>
           • Check extension permissions and errors`, 'error');
      }
    }

    function reinstallExtension() {
      log('Starting reinstallation guide...');
      
      showResult('reinstallResult', 
        `🔄 Reinstallation Steps:<br>
         1. Open chrome://extensions/ in new tab<br>
         2. Find "Progress Dashboard OTP Authenticator" (if exists)<br>
         3. Click "Remove" to uninstall<br>
         4. Click "Load unpacked" button<br>
         5. Select chrome-extension FOLDER (not files)<br>
         6. Refresh this page after installation<br><br>
         <strong>⚠️ Common Mistake:</strong> Don't select manifest.json or other files, select the folder!`, 'warning');
      
      // Open extensions page
      setTimeout(() => {
        try {
          window.open('chrome://extensions/', '_blank');
        } catch (error) {
          log(`Error opening extensions page: ${error.message}`);
        }
      }, 1000);
    }

    function checkExtensionPermissions() {
      log('Checking extension permissions...');
      
      // Check current page permissions
      const currentOrigin = window.location.origin;
      const currentHostname = window.location.hostname;
      const currentPort = window.location.port;
      
      log(`Current origin: ${currentOrigin}`);
      log(`Current hostname: ${currentHostname}`);
      log(`Current port: ${currentPort}`);
      
      // Check if current page matches extension permissions
      const isLocalhost = currentHostname === 'localhost';
      const hasPort = currentPort !== '';
      
      if (isLocalhost) {
        showResult('permissionsResult', 
          `✅ Page should be accessible to extension<br>
           • Origin: ${currentOrigin}<br>
           • Hostname: ${currentHostname} (localhost ✅)<br>
           • Port: ${currentPort || 'default'}<br>
           • Should match extension permissions`, 'success');
      } else {
        showResult('permissionsResult', 
          `⚠️ Page may not be accessible to extension<br>
           • Origin: ${currentOrigin}<br>
           • Hostname: ${currentHostname} (not localhost ⚠️)<br>
           • Extension configured for localhost only`, 'warning');
      }
    }

    function checkConsoleErrors() {
      log('Checking console errors...');
      
      // Get console errors (limited in scope)
      const errors = [];
      
      // Override console.error temporarily to catch new errors
      const originalError = console.error;
      console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
      };
      
      // Check for common extension-related errors
      setTimeout(() => {
        console.error = originalError;
        
        if (errors.length > 0) {
          showResult('consoleErrorsResult', 
            `⚠️ Console errors detected:<br>
             ${errors.map(err => `• ${err}`).join('<br>')}<br><br>
             Check browser console (F12) for more details`, 'warning');
        } else {
          showResult('consoleErrorsResult', 
            `✅ No new console errors detected<br>
             • Check browser console (F12) for existing errors<br>
             • Check extension console in chrome://extensions/`, 'success');
        }
      }, 2000);
    }

    function checkExtensionConsole() {
      log('Opening extension console guide...');
      
      showResult('consoleErrorsResult', 
        `📋 How to check extension console:<br>
         1. Open chrome://extensions/<br>
         2. Find "Progress Dashboard OTP Authenticator"<br>
         3. Click "Details"<br>
         4. Look for "Inspect views: background page"<br>
         5. Click it to open extension console<br>
         6. Check for errors in the console<br><br>
         <strong>If no "Inspect views" link:</strong> Extension may have crashed or failed to load`, 'warning');
      
      try {
        window.open('chrome://extensions/', '_blank');
      } catch (error) {
        log(`Error opening extensions page: ${error.message}`);
      }
    }

    function testExtensionAPI() {
      log('Testing extension API...');
      
      // Test various extension-related APIs
      const tests = {
        chromeRuntime: typeof chrome !== 'undefined' && !!chrome.runtime,
        chromeExtension: typeof chrome !== 'undefined' && !!chrome.extension,
        extensionBridge: !!window.progressDashboardExtension,
        postMessage: typeof window.postMessage === 'function'
      };
      
      log(`Extension API tests: ${JSON.stringify(tests)}`);
      
      showResult('alternativeTestResult', 
        `🧪 Extension API Test Results:<br>
         • Chrome Runtime: ${tests.chromeRuntime ? '✅' : '❌'}<br>
         • Chrome Extension: ${tests.chromeExtension ? '✅' : '❌'}<br>
         • Extension Bridge: ${tests.extensionBridge ? '✅' : '❌'}<br>
         • PostMessage: ${tests.postMessage ? '✅' : '❌'}`, 
        tests.extensionBridge ? 'success' : 'error');
    }

    function testPostMessageDirect() {
      log('Testing direct postMessage...');
      
      // Send a test message
      const testMessage = {
        type: 'EXTENSION_PING',
        data: { test: true, timestamp: Date.now() },
        origin: window.location.origin
      };
      
      let responseReceived = false;
      
      // Listen for response
      const messageListener = (event) => {
        if (event.data && event.data.type === 'EXTENSION_PONG') {
          responseReceived = true;
          log('Extension responded to ping!');
          showResult('alternativeTestResult', 
            `✅ Extension responded to direct message!<br>
             Response: ${JSON.stringify(event.data)}`, 'success');
          window.removeEventListener('message', messageListener);
        }
      };
      
      window.addEventListener('message', messageListener);
      
      // Send message
      window.postMessage(testMessage, window.location.origin);
      log('Sent direct postMessage to extension');
      
      // Timeout check
      setTimeout(() => {
        if (!responseReceived) {
          window.removeEventListener('message', messageListener);
          showResult('alternativeTestResult', 
            `❌ No response from extension<br>
             • Message sent but no response<br>
             • Extension may not be listening<br>
             • Check if extension is installed and running`, 'error');
        }
      }, 3000);
    }

    function injectTestScript() {
      log('Injecting test script...');
      
      // Create a test script to check extension presence
      const script = document.createElement('script');
      script.textContent = `
        (function() {
          console.log('Test script injected');
          if (window.progressDashboardExtension) {
            console.log('Extension bridge found in test script!');
            window.postMessage({
              type: 'TEST_SCRIPT_RESULT',
              data: { extensionFound: true }
            }, window.location.origin);
          } else {
            console.log('Extension bridge NOT found in test script');
            window.postMessage({
              type: 'TEST_SCRIPT_RESULT',
              data: { extensionFound: false }
            }, window.location.origin);
          }
        })();
      `;
      
      document.head.appendChild(script);
      document.head.removeChild(script);
      
      log('Test script injected and executed');
    }

    function collectDebugInfo() {
      log('Collecting comprehensive debug information...');
      
      const debugInfo = {
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        extensionBridge: !!window.progressDashboardExtension,
        extensionReady: !!window.progressDashboardExtensionReady,
        chromeRuntime: typeof chrome !== 'undefined' && !!chrome.runtime,
        scripts: Array.from(document.scripts).map(s => s.src).filter(s => s),
        debugLog: debugLog
      };
      
      log(`Debug info collected: ${JSON.stringify(debugInfo, null, 2)}`);
    }

    function exportDebugInfo() {
      const debugInfo = {
        timestamp: new Date().toISOString(),
        debugLog: debugLog,
        extensionStatus: {
          bridge: !!window.progressDashboardExtension,
          ready: !!window.progressDashboardExtensionReady,
          chromeRuntime: typeof chrome !== 'undefined' && !!chrome.runtime
        }
      };
      
      const blob = new Blob([JSON.stringify(debugInfo, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'extension-troubleshooting-debug.json';
      a.click();
      URL.revokeObjectURL(url);
      
      log('Debug info exported to file');
    }

    // Auto-run initial checks
    window.addEventListener('load', () => {
      log('Troubleshooting page loaded');
      setTimeout(() => {
        checkExtensionInChrome();
        testContentScriptInjection();
      }, 1000);
    });

    // Listen for messages
    window.addEventListener('message', (event) => {
      if (event.origin !== window.location.origin) return;
      
      if (event.data && event.data.type === 'TEST_SCRIPT_RESULT') {
        const found = event.data.data.extensionFound;
        log(`Test script result: Extension ${found ? 'found' : 'not found'}`);
        showResult('alternativeTestResult', 
          `🧪 Test Script Result:<br>
           Extension bridge: ${found ? '✅ Found' : '❌ Not found'}`, 
          found ? 'success' : 'error');
      }
    });
  </script>
</body>
</html>
