<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSP Error Fix Guide - Chrome Extension</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: #f8fafc;
      line-height: 1.6;
    }
    
    .container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #1e293b;
      margin-bottom: 20px;
    }
    
    .error-box {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      overflow-x: auto;
    }
    
    .solution {
      background: #dcfce7;
      border: 1px solid #bbf7d0;
      color: #166534;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
    }
    
    .step {
      margin: 25px 0;
      padding: 20px;
      background: #f1f5f9;
      border-left: 4px solid #95E565;
      border-radius: 0 8px 8px 0;
    }
    
    .step h3 {
      margin: 0 0 15px 0;
      color: #1e293b;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .step-number {
      background: #95E565;
      color: white;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 14px;
    }
    
    button {
      background: #95E565;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin: 10px 5px;
      transition: background 0.2s;
      font-weight: 500;
    }
    
    button:hover {
      background: #608F44;
    }
    
    .code-block {
      background: #1e293b;
      color: #e2e8f0;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 13px;
      overflow-x: auto;
      margin: 10px 0;
    }
    
    .warning {
      background: #fef3c7;
      border: 1px solid #fde68a;
      color: #d97706;
      padding: 15px;
      border-radius: 6px;
      margin: 15px 0;
    }
    
    .status {
      padding: 10px;
      border-radius: 6px;
      margin: 10px 0;
      font-weight: 500;
    }
    
    .status.success {
      background: #dcfce7;
      color: #166534;
      border: 1px solid #bbf7d0;
    }
    
    .status.error {
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔧 CSP Error Fix Guide</h1>
    <p>Fixing Content Security Policy violations in Chrome Extension</p>
    
    <!-- Error Description -->
    <div class="error-box">
Refused to execute inline script because it violates the following Content Security Policy directive: "script-src 'self' 'wasm-unsafe-eval' 'inline-speculation-rules' http://localhost:* http://127.0.0.1:* chrome-extension://l0961e7b8-03ba-44b0-9633-cd1a9oad10e/" Either the 'unsafe-inline' keyword, a hash ('sha256-azBNhY30s2+hfMotmCLqX3cBG053f2cMJp7cT15ZgE='), or a nonce ('nonce-...') is required to enable inline execution.
    </div>

    <!-- Solution Overview -->
    <div class="solution">
      <h3>✅ Solution Implemented</h3>
      <p>I've created a <strong>CSP-safe version</strong> of the content script that doesn't use inline script injection. The new version uses direct window object assignment which is CSP-compliant.</p>
    </div>

    <!-- Fix Steps -->
    <div class="step">
      <h3>
        <span class="step-number">1</span>
        Remove Old Extension
      </h3>
      <p>First, remove the current extension that has CSP errors:</p>
      <ol>
        <li>Go to <code>chrome://extensions/</code></li>
        <li>Find "Progress Dashboard - OTP Authenticator"</li>
        <li>Click "Remove" button</li>
        <li>Confirm removal</li>
      </ol>
      <button onclick="openExtensionsPage()">📂 Open Extensions Page</button>
    </div>

    <div class="step">
      <h3>
        <span class="step-number">2</span>
        Install Updated Extension
      </h3>
      <p>Install the extension with the CSP-safe content script:</p>
      <ol>
        <li>Click "Load unpacked" in chrome://extensions/</li>
        <li>Select the same folder:</li>
        <div class="code-block">/Users/<USER>/Library/CloudStorage/Dropbox/Assets/Apps/Dashboard/Progress_Dashboard-main/chrome-extension/</div>
        <li>Extension will load with updated CSP-safe code</li>
      </ol>
      <div class="warning">
        <strong>Important:</strong> The extension now uses <code>content-csp-safe.js</code> instead of <code>content.js</code>
      </div>
    </div>

    <div class="step">
      <h3>
        <span class="step-number">3</span>
        Test Extension
      </h3>
      <p>Verify the extension works without CSP errors:</p>
      <ol>
        <li>Refresh this page</li>
        <li>Open browser console (F12)</li>
        <li>Look for success messages:</li>
        <div class="code-block">🔗 Progress Dashboard Extension content script initializing...
✅ Content script initialized (CSP-safe)
🌉 Progress Dashboard Extension bridge injected (CSP-safe)</div>
        <li>No CSP error messages should appear</li>
      </ol>
      <button onclick="testExtension()">🧪 Test Extension</button>
      <button onclick="openConsole()">📝 Open Console</button>
    </div>

    <div class="step">
      <h3>
        <span class="step-number">4</span>
        Verify Communication
      </h3>
      <p>Test that extension communication works:</p>
      <div id="testResults" class="status error">Click "Test Communication" to verify</div>
      <button onclick="testCommunication()">📡 Test Communication</button>
      <button onclick="openDebugTool()">🔧 Open Debug Tool</button>
    </div>

    <!-- Technical Details -->
    <div class="step">
      <h3>
        <span class="step-number">ℹ️</span>
        What Was Fixed
      </h3>
      <h4>Problem:</h4>
      <ul>
        <li>Original content script used <code>script.textContent</code> injection</li>
        <li>This violates CSP <code>script-src 'self'</code> directive</li>
        <li>Inline script execution was blocked</li>
      </ul>
      
      <h4>Solution:</h4>
      <ul>
        <li>✅ Direct <code>window.progressDashboardExtension</code> assignment</li>
        <li>✅ No inline script injection</li>
        <li>✅ CSP-compliant communication bridge</li>
        <li>✅ Fallback methods for compatibility</li>
      </ul>
      
      <h4>Files Updated:</h4>
      <ul>
        <li><code>content-csp-safe.js</code> - New CSP-safe content script</li>
        <li><code>manifest.json</code> - Updated to use new content script</li>
        <li>Added CSP directive for extension pages</li>
      </ul>
    </div>

    <!-- Success Check -->
    <div class="solution" id="successMessage" style="display: none;">
      <h3>🎉 Extension Working!</h3>
      <p>CSP errors have been resolved. You can now proceed to test the login flow.</p>
      <button onclick="openLoginPage()">🔐 Test Login Flow</button>
    </div>
  </div>

  <script>
    // Helper functions
    function openExtensionsPage() {
      window.open('chrome://extensions/', '_blank');
    }

    function openDebugTool() {
      window.open('http://localhost:5173/debug-extension.html', '_blank');
    }

    function openLoginPage() {
      window.open('http://localhost:5173/login', '_blank');
    }

    function openConsole() {
      alert('To open console:\n1. Press F12 (or Cmd+Option+I on Mac)\n2. Click "Console" tab\n3. Look for extension messages');
    }

    function testExtension() {
      // Check if extension bridge is available
      if (typeof window.progressDashboardExtension !== 'undefined') {
        document.getElementById('testResults').textContent = '✅ Extension bridge detected - CSP fix successful!';
        document.getElementById('testResults').className = 'status success';
        document.getElementById('successMessage').style.display = 'block';
      } else {
        document.getElementById('testResults').textContent = '❌ Extension bridge not found - please reinstall extension';
        document.getElementById('testResults').className = 'status error';
      }
    }

    async function testCommunication() {
      try {
        if (typeof window.progressDashboardExtension === 'undefined') {
          throw new Error('Extension bridge not available');
        }

        const response = await window.progressDashboardExtension.sendMessage({
          type: 'GET_EXTENSION_STATUS'
        });

        document.getElementById('testResults').textContent = '✅ Communication test successful: ' + JSON.stringify(response);
        document.getElementById('testResults').className = 'status success';
        document.getElementById('successMessage').style.display = 'block';
      } catch (error) {
        document.getElementById('testResults').textContent = '❌ Communication test failed: ' + error.message;
        document.getElementById('testResults').className = 'status error';
      }
    }

    // Auto-test extension on page load
    setTimeout(() => {
      testExtension();
    }, 2000);

    // Listen for extension ready event
    window.addEventListener('progressDashboardExtensionReady', (event) => {
      console.log('Extension ready event received:', event.detail);
      document.getElementById('testResults').textContent = '✅ Extension ready event received - CSP fix successful!';
      document.getElementById('testResults').className = 'status success';
      document.getElementById('successMessage').style.display = 'block';
    });
  </script>
</body>
</html>
