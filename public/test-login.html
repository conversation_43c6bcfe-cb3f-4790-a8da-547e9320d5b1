<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Progress Dashboard - Login Test</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', system-ui, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .container {
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 500px;
    }

    .header {
      text-align: center;
      margin-bottom: 40px;
    }

    .logo {
      width: 80px;
      height: 80px;
      background: #95E565;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
      font-size: 32px;
      font-weight: bold;
      color: white;
    }

    h1 {
      color: #1e293b;
      font-size: 28px;
      margin-bottom: 8px;
    }

    .subtitle {
      color: #64748b;
      font-size: 16px;
    }

    .form-group {
      margin-bottom: 24px;
    }

    label {
      display: block;
      color: #374151;
      font-weight: 500;
      margin-bottom: 8px;
    }

    input[type="email"] {
      width: 100%;
      padding: 16px;
      border: 2px solid #e5e7eb;
      border-radius: 12px;
      font-size: 16px;
      transition: border-color 0.2s;
    }

    input[type="email"]:focus {
      outline: none;
      border-color: #95E565;
      box-shadow: 0 0 0 3px rgba(149, 229, 101, 0.1);
    }

    .login-btn {
      width: 100%;
      background: #95E565;
      color: white;
      border: none;
      padding: 16px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: background 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .login-btn:hover {
      background: #608F44;
    }

    .login-btn:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }

    .status {
      margin-top: 20px;
      padding: 16px;
      border-radius: 12px;
      font-weight: 500;
    }

    .status.info {
      background: #dbeafe;
      color: #1e40af;
      border: 1px solid #bfdbfe;
    }

    .status.success {
      background: #dcfce7;
      color: #166534;
      border: 1px solid #bbf7d0;
    }

    .status.error {
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }

    .status.warning {
      background: #fef3c7;
      color: #d97706;
      border: 1px solid #fde68a;
    }

    .extension-status {
      margin-top: 20px;
      padding: 16px;
      background: #f8fafc;
      border-radius: 12px;
      border: 1px solid #e2e8f0;
    }

    .extension-status h3 {
      color: #1e293b;
      margin-bottom: 12px;
      font-size: 16px;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .status-indicator {
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
    }

    .status-indicator.connected {
      background: #dcfce7;
      color: #166534;
    }

    .status-indicator.disconnected {
      background: #fef2f2;
      color: #dc2626;
    }

    .spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #ffffff;
      border-top: 2px solid transparent;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .test-controls {
      margin-top: 30px;
      padding-top: 30px;
      border-top: 1px solid #e5e7eb;
    }

    .test-btn {
      background: #3b82f6;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      font-size: 14px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
    }

    .test-btn:hover {
      background: #2563eb;
    }

    .logs {
      margin-top: 20px;
      background: #1e293b;
      color: #e2e8f0;
      padding: 16px;
      border-radius: 12px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">PD</div>
      <h1>Progress Dashboard</h1>
      <p class="subtitle">Secure OTP Authentication Test</p>
    </div>

    <form id="loginForm">
      <div class="form-group">
        <label for="email">Email Address</label>
        <input 
          type="email" 
          id="email" 
          name="email" 
          placeholder="Enter your email address"
          value="<EMAIL>"
          required
        >
      </div>

      <button type="submit" class="login-btn" id="loginBtn">
        <span id="loginText">🔐 Secure Login with Extension</span>
        <div id="loginSpinner" class="spinner" style="display: none;"></div>
      </button>
    </form>

    <div id="status" class="status" style="display: none;"></div>

    <div class="extension-status">
      <h3>🔌 Extension Status</h3>
      <div class="status-item">
        <span>Extension Available:</span>
        <span id="extensionAvailable" class="status-indicator disconnected">Checking...</span>
      </div>
      <div class="status-item">
        <span>Extension Connected:</span>
        <span id="extensionConnected" class="status-indicator disconnected">Checking...</span>
      </div>
      <div class="status-item">
        <span>Backend Status:</span>
        <span id="backendStatus" class="status-indicator disconnected">Checking...</span>
      </div>
    </div>

    <div class="test-controls">
      <h3>🧪 Test Controls</h3>
      <button class="test-btn" onclick="checkExtension()">Check Extension</button>
      <button class="test-btn" onclick="checkBackend()">Check Backend</button>
      <button class="test-btn" onclick="testRateLimit()">Test Rate Limit</button>
      <button class="test-btn" onclick="debugExtension()">🔍 Debug Extension</button>
      <button class="test-btn" onclick="inspectWindow()">🔍 Inspect Window</button>
      <button class="test-btn" onclick="clearLogs()">Clear Logs</button>
    </div>

    <div id="logs" class="logs"></div>
  </div>

  <script>
    // Configuration
    const BACKEND_URL = 'http://127.0.0.1:5001';
    const API_ENDPOINTS = {
      generateOTP: `${BACKEND_URL}/api/auth/generate-otp`,
      validateOTP: `${BACKEND_URL}/api/auth/validate-otp`,
      health: `${BACKEND_URL}/api/auth/health`
    };

    // State
    let extensionService = null;
    let currentOTPRequest = null;

    // Logging
    function log(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString();
      const logElement = document.getElementById('logs');
      const colorMap = {
        info: '#60a5fa',
        success: '#34d399',
        error: '#f87171',
        warning: '#fbbf24'
      };
      
      logElement.innerHTML += `<div style="color: ${colorMap[type]}">[${timestamp}] ${message}</div>`;
      logElement.scrollTop = logElement.scrollHeight;
    }

    function clearLogs() {
      document.getElementById('logs').innerHTML = '';
    }

    // Debug extension state
    function debugExtension() {
      log('🔍 Starting extension debug...', 'info');

      // Check all possible extension locations
      const checks = {
        'window.progressDashboardExtension': typeof window.progressDashboardExtension,
        'window.__progressDashboardExtensionBridge__': typeof window.__progressDashboardExtensionBridge__,
        'window.progressDashboardContentScript': window.progressDashboardContentScript,
        'window.progressDashboardExtensionReady': window.progressDashboardExtensionReady,
        'DOM attribute': document.documentElement.hasAttribute('data-progress-dashboard-extension'),
        'Chrome runtime': typeof chrome !== 'undefined' && chrome.runtime ? 'available' : 'not available'
      };

      log('Extension state check:', 'info');
      for (const [key, value] of Object.entries(checks)) {
        log(`  ${key}: ${value}`, 'info');
      }

      // Try to get extension info if available
      if (window.progressDashboardExtension) {
        try {
          const info = window.progressDashboardExtension.getDebugInfo();
          log(`Extension debug info: ${JSON.stringify(info)}`, 'success');
        } catch (error) {
          log(`Failed to get extension debug info: ${error.message}`, 'error');
        }
      }

      // Check fallback bridge
      if (window.__progressDashboardExtensionBridge__) {
        try {
          const info = window.__progressDashboardExtensionBridge__.getDebugInfo();
          log(`Fallback bridge debug info: ${JSON.stringify(info)}`, 'success');
        } catch (error) {
          log(`Failed to get fallback bridge debug info: ${error.message}`, 'error');
        }
      }
    }

    // Inspect window object for extension-related properties
    function inspectWindow() {
      log('🔍 Inspecting window object...', 'info');

      const extensionProps = [];
      for (const prop in window) {
        if (prop.toLowerCase().includes('progress') || prop.toLowerCase().includes('dashboard') || prop.toLowerCase().includes('extension')) {
          extensionProps.push(`${prop}: ${typeof window[prop]}`);
        }
      }

      if (extensionProps.length > 0) {
        log('Extension-related properties found:', 'info');
        extensionProps.forEach(prop => log(`  ${prop}`, 'info'));
      } else {
        log('No extension-related properties found in window object', 'warning');
      }

      // Check for content script markers
      const markers = {
        'DOM attribute': document.documentElement.getAttribute('data-progress-dashboard-extension'),
        'Content script variable': window.progressDashboardContentScript,
        'Ready flag': window.progressDashboardExtensionReady,
        'Test extension loaded': window.testExtensionLoaded,
        'Test DOM attribute': document.documentElement.getAttribute('data-test-extension'),
        'Test bridge': typeof window.testExtensionBridge !== 'undefined'
      };

      log('Content script markers:', 'info');
      for (const [key, value] of Object.entries(markers)) {
        log(`  ${key}: ${value}`, 'info');
      }

      // Test simple bridge if available
      if (window.testExtensionBridge) {
        try {
          const testResult = window.testExtensionBridge.test();
          log(`Test bridge result: ${testResult}`, 'success');
        } catch (error) {
          log(`Test bridge error: ${error.message}`, 'error');
        }
      }
    }

    // Status updates
    function updateStatus(message, type = 'info') {
      const statusElement = document.getElementById('status');
      statusElement.textContent = message;
      statusElement.className = `status ${type}`;
      statusElement.style.display = 'block';
      log(`Status: ${message}`, type);
    }

    function updateIndicator(elementId, connected, text = null) {
      const element = document.getElementById(elementId);
      element.className = `status-indicator ${connected ? 'connected' : 'disconnected'}`;
      element.textContent = text || (connected ? 'Connected' : 'Disconnected');
    }

    // Extension detection and communication
    function checkExtension() {
      log('Checking Chrome Extension...', 'info');

      // Multiple detection methods for CSP-safe environments
      let extensionBridge = null;

      // Method 1: Direct window property
      if (typeof window.progressDashboardExtension !== 'undefined') {
        extensionBridge = window.progressDashboardExtension;
        log('Extension bridge found via direct property', 'success');
      }

      // Method 2: Fallback global variable
      else if (typeof window.__progressDashboardExtensionBridge__ !== 'undefined') {
        extensionBridge = window.__progressDashboardExtensionBridge__;
        log('Extension bridge found via fallback variable', 'success');
      }

      // Method 3: Check ready flag
      else if (window.progressDashboardExtensionReady) {
        log('Extension ready flag found, but bridge missing', 'warning');
      }

      if (extensionBridge) {
        updateIndicator('extensionAvailable', true, 'Available');

        // Test extension connection
        try {
          const debugInfo = extensionBridge.getDebugInfo();
          log(`Extension info: ${JSON.stringify(debugInfo)}`, 'info');

          // Test if extension is actually available
          const isAvailable = extensionBridge.isAvailable();
          log(`Extension availability check: ${isAvailable}`, 'info');

          if (isAvailable) {
            updateIndicator('extensionConnected', true, 'Ready');

            // Store bridge reference globally for use in login
            window.progressDashboardExtension = extensionBridge;

            // Test message sending
            extensionBridge.sendMessage({
              type: 'GET_EXTENSION_STATUS',
              requestId: 'test_' + Date.now()
            }).then(response => {
              log(`Extension test message successful: ${JSON.stringify(response)}`, 'success');
            }).catch(error => {
              log(`Extension test message failed: ${error.message}`, 'warning');
            });

            return true;
          } else {
            updateIndicator('extensionConnected', false, 'Not Available');
            return false;
          }
        } catch (error) {
          log(`Extension error: ${error.message}`, 'error');
          updateIndicator('extensionConnected', false, 'Error');
          return false;
        }
      } else {
        updateIndicator('extensionAvailable', false, 'Not Found');
        updateIndicator('extensionConnected', false, 'N/A');
        log('Extension bridge not found - Extension may not be loaded', 'warning');

        // Try to trigger bridge ready event check
        log('Attempting to trigger bridge ready check...', 'info');
        setTimeout(() => {
          if (window.progressDashboardExtension) {
            log('Bridge found after delay - likely CSP-compliant method worked', 'success');
            checkExtension(); // Re-check extension
          }
        }, 500);

        // Check for content script markers
        const hasContentScript = document.documentElement.hasAttribute('data-progress-dashboard-extension');
        const hasContentScriptVar = typeof window.progressDashboardContentScript !== 'undefined';
        const hasReadyFlag = window.progressDashboardExtensionReady;

        log(`Content script markers: DOM attribute=${hasContentScript}, variable=${hasContentScriptVar}, ready=${hasReadyFlag}`, 'info');

        return false;
      }
    }

    // Backend health check
    async function checkBackend() {
      log('Checking backend connection...', 'info');
      
      try {
        const response = await fetch(API_ENDPOINTS.health);
        if (response.ok) {
          const data = await response.json();
          updateIndicator('backendStatus', true, 'Online');
          log(`Backend health: ${JSON.stringify(data)}`, 'success');
          return true;
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        updateIndicator('backendStatus', false, 'Offline');
        log(`Backend error: ${error.message}`, 'error');
        return false;
      }
    }

    // OTP Authentication Flow
    async function handleLogin(email) {
      log(`Starting login flow for: ${email}`, 'info');
      
      try {
        // Step 1: Generate OTP
        updateStatus('Generating OTP...', 'info');
        const otpResponse = await fetch(API_ENDPOINTS.generateOTP, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email })
        });

        if (!otpResponse.ok) {
          throw new Error(`OTP generation failed: ${otpResponse.status}`);
        }

        const otpData = await otpResponse.json();
        log(`OTP generated: ${JSON.stringify(otpData)}`, 'success');

        // Step 2: Send to Extension
        updateStatus('Sending OTP to extension...', 'info');
        
        if (!window.progressDashboardExtension) {
          throw new Error('Chrome Extension not available');
        }

        const extensionResponse = await window.progressDashboardExtension.sendMessage({
          type: 'OTP_REQUEST',
          data: {
            email: otpData.email,
            otp_code: otpData.otp_code,
            otp_key: otpData.otp_key,
            expires_in: otpData.expires_in
          }
        });

        log(`Extension response: ${JSON.stringify(extensionResponse)}`, 'info');

        // Step 3: Handle Extension Response
        if (extensionResponse.data.action === 'APPROVE') {
          updateStatus('OTP approved! Validating...', 'info');
          
          // Step 4: Validate OTP
          const validateResponse = await fetch(API_ENDPOINTS.validateOTP, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: email,
              otp_code: otpData.otp_code,
              otp_key: otpData.otp_key
            })
          });

          if (!validateResponse.ok) {
            throw new Error(`OTP validation failed: ${validateResponse.status}`);
          }

          const validateData = await validateResponse.json();
          log(`OTP validation: ${JSON.stringify(validateData)}`, 'success');

          updateStatus('🎉 Login successful! Session created.', 'success');
          
        } else {
          updateStatus('❌ OTP rejected by user', 'error');
          log('User rejected OTP in extension', 'warning');
        }

      } catch (error) {
        updateStatus(`❌ Login failed: ${error.message}`, 'error');
        log(`Login error: ${error.message}`, 'error');
      }
    }

    // Rate limiting test
    async function testRateLimit() {
      log('Testing rate limiting...', 'info');
      updateStatus('Testing rate limiting...', 'info');
      
      const email = document.getElementById('email').value;
      let successCount = 0;
      let blockedCount = 0;

      for (let i = 1; i <= 7; i++) {
        try {
          log(`Rate limit test ${i}/7`, 'info');
          const response = await fetch(API_ENDPOINTS.generateOTP, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email })
          });

          if (response.ok) {
            successCount++;
            log(`Request ${i}: Success`, 'success');
          } else {
            blockedCount++;
            log(`Request ${i}: Blocked (${response.status})`, 'warning');
          }
        } catch (error) {
          blockedCount++;
          log(`Request ${i}: Error - ${error.message}`, 'error');
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      updateStatus(`Rate limit test complete: ${successCount} success, ${blockedCount} blocked`, 'info');
      log(`Rate limiting test results: ${successCount} allowed, ${blockedCount} blocked`, 'info');
    }

    // Form handling
    document.getElementById('loginForm').addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const email = document.getElementById('email').value;
      const loginBtn = document.getElementById('loginBtn');
      const loginText = document.getElementById('loginText');
      const loginSpinner = document.getElementById('loginSpinner');
      
      // Update button state
      loginBtn.disabled = true;
      loginText.style.display = 'none';
      loginSpinner.style.display = 'block';
      
      try {
        await handleLogin(email);
      } finally {
        // Restore button state
        loginBtn.disabled = false;
        loginText.style.display = 'block';
        loginSpinner.style.display = 'none';
      }
    });

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      log('Test page loaded', 'info');
      
      // Initial checks
      setTimeout(() => {
        checkExtension();
        checkBackend();
      }, 1000);
      
      // Listen for extension ready event
      window.addEventListener('progressDashboardExtensionReady', (event) => {
        log('Extension ready event received', 'success');
        checkExtension();
      });

      // Listen for test extension ready event
      window.addEventListener('testExtensionReady', (event) => {
        log('Test extension ready event received', 'success');
        log(`Test extension detail: ${JSON.stringify(event.detail)}`, 'info');
      });

      // Listen for bridge ready event (CSP-compliant method)
      window.addEventListener('progressDashboardBridgeReady', (event) => {
        log('Bridge ready event received', 'success');
        log(`Bridge detail: ${JSON.stringify(event.detail)}`, 'info');

        // Try to access bridge from event
        if (event.detail && event.detail.bridge) {
          window.progressDashboardExtension = event.detail.bridge;
          log('Bridge assigned from event successfully', 'success');
        }
      });

      // Listen for console messages to detect bridge injection
      const originalConsoleLog = console.log;
      console.log = function(...args) {
        if (args[0] && args[0].includes('[BRIDGE-INJECT]')) {
          log(`Bridge injection: ${args[0]}`, 'info');
        }
        originalConsoleLog.apply(console, args);
      };
    });
  </script>
</body>
</html>
