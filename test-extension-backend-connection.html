<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Extension Backend Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Extension Backend Connection</h1>
        <p>Tool untuk menguji koneksi antara Chrome Extension dan Backend API</p>

        <!-- Backend Health Check -->
        <div class="test-section">
            <h2>1. Backend Health Check</h2>
            <button onclick="testBackendHealth()">Test Backend Health</button>
            <div id="backend-status"></div>
        </div>

        <!-- Extension Availability -->
        <div class="test-section">
            <h2>2. Extension Availability</h2>
            <button onclick="testExtensionAvailability()">Test Extension</button>
            <div id="extension-status"></div>
        </div>

        <!-- Extension Backend Connection -->
        <div class="test-section">
            <h2>3. Extension Backend Connection</h2>
            <button onclick="testExtensionBackendConnection()">Test Extension Connection</button>
            <div id="extension-backend-status"></div>
        </div>

        <!-- Full OTP Flow Test -->
        <div class="test-section">
            <h2>4. Full OTP Flow Test</h2>
            <input type="email" id="test-email" placeholder="Enter test email" value="<EMAIL>">
            <button onclick="testOTPFlow()">Test OTP Flow</button>
            <div id="otp-flow-status"></div>
        </div>

        <!-- Debug Information -->
        <div class="test-section">
            <h2>5. Debug Information</h2>
            <button onclick="getDebugInfo()">Get Debug Info</button>
            <div id="debug-info"></div>
        </div>
    </div>

    <script>
        // Test backend health directly
        async function testBackendHealth() {
            const statusDiv = document.getElementById('backend-status');
            statusDiv.innerHTML = '<div class="info">Testing backend health...</div>';

            try {
                const response = await fetch('http://localhost:5001/api/auth/health');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    statusDiv.innerHTML = `
                        <div class="success">✅ Backend is healthy!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ Backend health check failed</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="error">❌ Cannot connect to backend</div>
                    <pre>Error: ${error.message}</pre>
                `;
            }
        }

        // Test extension availability
        function testExtensionAvailability() {
            const statusDiv = document.getElementById('extension-status');
            
            // Check if extension bridge is available
            if (typeof window.progressDashboardExtension !== 'undefined') {
                const bridge = window.progressDashboardExtension;
                statusDiv.innerHTML = `
                    <div class="success">✅ Extension bridge is available</div>
                    <pre>Bridge methods: ${Object.keys(bridge).join(', ')}</pre>
                `;
            } else if (typeof window.__progressDashboardExtensionBridge__ !== 'undefined') {
                statusDiv.innerHTML = `
                    <div class="warning">⚠️ Extension bridge found in fallback location</div>
                    <pre>Fallback bridge available</pre>
                `;
            } else {
                statusDiv.innerHTML = `
                    <div class="error">❌ Extension bridge not found</div>
                    <p>Please make sure the Chrome Extension is installed and enabled.</p>
                `;
            }
        }

        // Test extension backend connection
        function testExtensionBackendConnection() {
            const statusDiv = document.getElementById('extension-backend-status');
            
            if (typeof window.progressDashboardExtension === 'undefined') {
                statusDiv.innerHTML = '<div class="error">❌ Extension not available</div>';
                return;
            }

            statusDiv.innerHTML = '<div class="info">Testing extension backend connection...</div>';

            try {
                // Send message to extension to check backend connection
                window.progressDashboardExtension.sendMessage({
                    type: 'CHECK_BACKEND_CONNECTION',
                    timestamp: Date.now()
                }).then(response => {
                    if (response && response.success) {
                        statusDiv.innerHTML = `
                            <div class="success">✅ Extension can connect to backend</div>
                            <pre>${JSON.stringify(response.data, null, 2)}</pre>
                        `;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="error">❌ Extension backend connection failed</div>
                            <pre>${JSON.stringify(response, null, 2)}</pre>
                        `;
                    }
                }).catch(error => {
                    statusDiv.innerHTML = `
                        <div class="error">❌ Extension communication error</div>
                        <pre>Error: ${error.message}</pre>
                    `;
                });
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="error">❌ Extension test failed</div>
                    <pre>Error: ${error.message}</pre>
                `;
            }
        }

        // Test full OTP flow
        async function testOTPFlow() {
            const email = document.getElementById('test-email').value;
            const statusDiv = document.getElementById('otp-flow-status');
            
            if (!email) {
                statusDiv.innerHTML = '<div class="error">❌ Please enter an email address</div>';
                return;
            }

            statusDiv.innerHTML = '<div class="info">Testing OTP flow...</div>';

            try {
                // Step 1: Generate OTP
                const generateResponse = await fetch('http://localhost:5001/api/auth/generate-otp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email })
                });

                const generateData = await generateResponse.json();
                
                if (generateResponse.ok && generateData.success) {
                    statusDiv.innerHTML = `
                        <div class="success">✅ OTP generated successfully</div>
                        <pre>${JSON.stringify(generateData, null, 2)}</pre>
                        <div class="info">Check your Chrome Extension for OTP approval...</div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ OTP generation failed</div>
                        <pre>${JSON.stringify(generateData, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="error">❌ OTP flow test failed</div>
                    <pre>Error: ${error.message}</pre>
                `;
            }
        }

        // Get debug information
        function getDebugInfo() {
            const statusDiv = document.getElementById('debug-info');
            
            const debugInfo = {
                userAgent: navigator.userAgent,
                url: window.location.href,
                extensionBridge: typeof window.progressDashboardExtension !== 'undefined',
                fallbackBridge: typeof window.__progressDashboardExtensionBridge__ !== 'undefined',
                timestamp: new Date().toISOString()
            };

            if (typeof window.progressDashboardExtension !== 'undefined') {
                try {
                    const bridge = window.progressDashboardExtension;
                    debugInfo.bridgeMethods = Object.keys(bridge);
                    
                    if (bridge.getDebugInfo) {
                        debugInfo.extensionDebugInfo = bridge.getDebugInfo();
                    }
                } catch (error) {
                    debugInfo.bridgeError = error.message;
                }
            }

            statusDiv.innerHTML = `
                <div class="info">Debug Information:</div>
                <pre>${JSON.stringify(debugInfo, null, 2)}</pre>
            `;
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('🔧 Extension Backend Connection Test Tool loaded');
            testExtensionAvailability();
        });
    </script>
</body>
</html>
