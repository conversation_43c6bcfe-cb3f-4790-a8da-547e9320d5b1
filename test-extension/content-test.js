/**
 * Simple Test Content Script
 */

console.log('🧪 SIMPLE TEST EXTENSION: Content script loaded!');

// Create global marker
window.simpleTestExtension = {
  loaded: true,
  timestamp: Date.now(),
  url: window.location.href
};

// Add DOM marker
document.documentElement.setAttribute('data-simple-test-extension', 'loaded');

// Log to console
console.log('🧪 SIMPLE TEST EXTENSION: Bridge created', window.simpleTestExtension);

// Listen for messages
window.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SIMPLE_TEST_PING') {
    console.log('🧪 SIMPLE TEST EXTENSION: Received ping', event.data);
    
    // Send response
    window.postMessage({
      type: 'SIMPLE_TEST_PONG',
      data: {
        received: event.data,
        timestamp: Date.now(),
        extensionLoaded: true
      }
    }, window.location.origin);
  }
});

// Notify ready
setTimeout(() => {
  window.postMessage({
    type: 'SIMPLE_TEST_READY',
    data: {
      extensionLoaded: true,
      timestamp: Date.now()
    }
  }, window.location.origin);
}, 100);

console.log('🧪 SIMPLE TEST EXTENSION: Setup complete');
