# 🔍 Extension Debug Guide

## 🚨 **CURRENT ISSUE ANALYSIS**

Dari log yang Anda berikan:
- ✅ Extension ready event received (content script berja<PERSON>)
- ✅ DOM attribute=true (content script berhasil set DOM attribute)
- ❌ variable=false (window.progressDashboardContentScript tidak ada)
- ❌ ready=undefined (window.progressDashboardExtensionReady tidak ada)

**Diagnosis**: Content script berjalan tapi bridge creation gagal.

---

## 🔧 **DEBUGGING STEPS**

### **STEP 1: Check Extension Console**

1. **Buka Chrome**: `chrome://extensions/`
2. **Find Extension**: "Progress Dashboard OTP Authenticator"
3. **Click "Details"**
4. **Click "Inspect views: background page"**
5. **Check Console Tab** untuk errors

**Yang harus dicari:**
- Background script errors
- Content script injection errors
- Permission errors
- Manifest errors

### **STEP 2: Check Content Script Console**

1. **Buka test page**: http://localhost:5173/test-login.html
2. **Press F12** (Developer Tools)
3. **Go to Console tab**
4. **Look for messages** yang dimulai dengan `[CONTENT-DEBUG]`

**Expected logs setelah reload extension:**
```
[Time] [CONTENT-DEBUG] Content script initialization started
[Time] [CONTENT-DEBUG] Starting CSP-safe extension bridge creation
[Time] [CONTENT-DEBUG] Attempting direct window assignment
[Time] [CONTENT-DEBUG] ✅ Extension bridge assigned to window object successfully
[Time] [CONTENT-DEBUG] Setting content script marker
[Time] [CONTENT-DEBUG] ✅ Extension ready event dispatched successfully
```

### **STEP 3: Use New Debug Tools**

1. **Reload Extension**: chrome://extensions/ → Reload button
2. **Refresh Test Page**: F5
3. **Click "🔍 Debug Extension"** button
4. **Click "🔍 Inspect Window"** button
5. **Check logs** untuk detailed state

---

## 🎯 **EXPECTED RESULTS AFTER FIX**

### **Console Logs (Content Script):**
```
[Time] [CONTENT-DEBUG] Content script initialization started
[Time] [CONTENT-DEBUG] Starting CSP-safe extension bridge creation
[Time] [CONTENT-DEBUG] Attempting direct window assignment
[Time] [CONTENT-DEBUG] ✅ Extension bridge assigned to window object successfully
[Time] [CONTENT-DEBUG] Setting content script marker
[Time] [CONTENT-DEBUG] Setting ready flag
[Time] [CONTENT-DEBUG] ✅ Extension ready event dispatched successfully
[Time] [CONTENT-DEBUG] Final verification of extension state
```

### **Test Page Logs:**
```
[Time] Extension ready event received
[Time] Checking Chrome Extension...
[Time] Extension bridge found via direct property
[Time] Extension info: {"injectionMethod":"csp-safe-direct"}
[Time] Extension availability check: true
[Time] Extension test message successful
```

### **Debug Extension Output:**
```
Extension state check:
  window.progressDashboardExtension: object
  window.progressDashboardContentScript: true
  window.progressDashboardExtensionReady: true
  DOM attribute: true
```

---

## 🐛 **COMMON ISSUES & SOLUTIONS**

### **Issue 1: Content Script Not Injecting**
**Symptoms**: No [CONTENT-DEBUG] logs
**Solutions**:
- Check manifest.json content_scripts configuration
- Verify host permissions
- Check if page URL matches content script patterns

### **Issue 2: Bridge Creation Fails**
**Symptoms**: [CONTENT-DEBUG] logs but no bridge object
**Solutions**:
- Check for JavaScript errors in content script
- Verify CSP policies
- Check browser compatibility

### **Issue 3: Permission Errors**
**Symptoms**: Chrome runtime errors
**Solutions**:
- Verify extension permissions in manifest
- Check if extension has access to localhost
- Reload extension after permission changes

### **Issue 4: Multiple Content Script Injection**
**Symptoms**: Duplicate logs or conflicting states
**Solutions**:
- Check if extension is loaded multiple times
- Verify content script run_at timing
- Clear browser cache and reload

---

## 🔧 **MANUAL TESTING COMMANDS**

### **Test in Browser Console:**

```javascript
// Check if extension bridge exists
console.log('Extension bridge:', typeof window.progressDashboardExtension);

// Check fallback bridge
console.log('Fallback bridge:', typeof window.__progressDashboardExtensionBridge__);

// Check content script markers
console.log('Content script:', window.progressDashboardContentScript);
console.log('Ready flag:', window.progressDashboardExtensionReady);

// Check DOM attribute
console.log('DOM attribute:', document.documentElement.getAttribute('data-progress-dashboard-extension'));

// Try to get extension info
if (window.progressDashboardExtension) {
  console.log('Extension info:', window.progressDashboardExtension.getDebugInfo());
}
```

### **Test Extension Communication:**

```javascript
// Test message sending (if bridge exists)
if (window.progressDashboardExtension) {
  window.progressDashboardExtension.sendMessage({
    type: 'GET_EXTENSION_STATUS',
    requestId: 'manual_test_' + Date.now()
  }).then(response => {
    console.log('Extension response:', response);
  }).catch(error => {
    console.error('Extension error:', error);
  });
}
```

---

## 📋 **DEBUGGING CHECKLIST**

### **Before Testing:**
- [ ] Extension reloaded in chrome://extensions/
- [ ] Test page refreshed (F5)
- [ ] Browser console cleared
- [ ] Extension console opened

### **During Testing:**
- [ ] Check for [CONTENT-DEBUG] logs in page console
- [ ] Check for errors in extension console
- [ ] Use "🔍 Debug Extension" button
- [ ] Use "🔍 Inspect Window" button
- [ ] Test manual commands in console

### **After Testing:**
- [ ] Report all console logs (both page and extension)
- [ ] Report debug tool outputs
- [ ] Report any error messages
- [ ] Report extension status indicators

---

## 📞 **NEXT STEPS**

1. **Reload Extension**: chrome://extensions/ → Reload
2. **Refresh Test Page**: F5
3. **Check Console Logs**: Look for [CONTENT-DEBUG] messages
4. **Use Debug Tools**: Click debug buttons
5. **Report Results**: Share all console outputs

**🎯 Focus on finding [CONTENT-DEBUG] logs to see exactly where bridge creation fails.**
