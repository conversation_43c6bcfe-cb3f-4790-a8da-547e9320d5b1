# Environment Variables for Progress Dashboard

# React Query DevTools Configuration
# Set to 'true' to enable React Query DevTools by default
# Default: false (hidden by default)
VITE_ENABLE_REACT_QUERY_DEVTOOLS=false

# API Configuration
# Backend API base URL
VITE_API_BASE_URL=http://localhost:5001/api

# Development Settings
# Enable debug logging
VITE_DEBUG_MODE=false

# Performance Monitoring
# Enable performance monitoring in development
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Feature Flags
# Enable experimental features
VITE_ENABLE_EXPERIMENTAL_FEATURES=false
