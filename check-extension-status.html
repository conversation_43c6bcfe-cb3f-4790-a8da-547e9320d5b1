<!DOCTYPE html>
<html>
<head>
    <title>Extension Status Checker</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 Extension Status Checker</h1>
    
    <button onclick="checkExtensions()">Check All Extensions</button>
    <button onclick="openExtensionsPage()">Open chrome://extensions/</button>
    
    <div id="results"></div>
    
    <script>
        function checkExtensions() {
            const results = document.getElementById('results');
            results.innerHTML = '<h3>Checking extensions...</h3>';
            
            // Check if we can access chrome.management
            if (chrome && chrome.management) {
                chrome.management.getAll((extensions) => {
                    let html = '<h3>📋 Installed Extensions:</h3>';
                    
                    const progressExtensions = extensions.filter(ext => 
                        ext.name.toLowerCase().includes('progress') || 
                        ext.name.toLowerCase().includes('dashboard') ||
                        ext.name.toLowerCase().includes('otp')
                    );
                    
                    if (progressExtensions.length > 0) {
                        progressExtensions.forEach(ext => {
                            html += `
                                <div class="status ${ext.enabled ? 'success' : 'error'}">
                                    <strong>${ext.name}</strong><br>
                                    ID: ${ext.id}<br>
                                    Version: ${ext.version}<br>
                                    Enabled: ${ext.enabled ? 'YES' : 'NO'}<br>
                                    Type: ${ext.type}
                                </div>
                            `;
                        });
                    } else {
                        html += '<div class="status error">❌ No Progress Dashboard extensions found!</div>';
                    }
                    
                    html += '<h3>📋 All Extensions:</h3>';
                    extensions.forEach(ext => {
                        html += `
                            <div class="status ${ext.enabled ? 'success' : 'warning'}">
                                <strong>${ext.name}</strong> - ${ext.enabled ? 'Enabled' : 'Disabled'}
                            </div>
                        `;
                    });
                    
                    results.innerHTML = html;
                });
            } else {
                results.innerHTML = '<div class="status error">❌ Cannot access chrome.management API</div>';
            }
        }
        
        function openExtensionsPage() {
            window.open('chrome://extensions/', '_blank');
        }
        
        // Auto-check on load
        setTimeout(checkExtensions, 1000);
    </script>
</body>
</html>
