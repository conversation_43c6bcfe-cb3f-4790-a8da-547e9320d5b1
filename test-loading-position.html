<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Loading Position</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .preview-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            background: white;
        }
        .extension-mockup {
            width: 280px;
            height: 120px;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin: 20px auto;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        .mockup-header {
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            padding: 8px 12px;
            font-size: 12px;
            color: #64748b;
            border-radius: 8px 8px 0 0;
        }
        .mockup-main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .mockup-footer {
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            padding: 6px 12px;
            font-size: 10px;
            color: #64748b;
            text-align: center;
            border-radius: 0 0 8px 8px;
        }
        .connection-badge-mockup {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background: #22c55e;
            border: 2px solid #16a34a;
            border-radius: 50%;
        }
        .loading-spinner-mockup {
            width: 24px;
            height: 24px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #9CEE69;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        .loading-text-mockup {
            margin-top: 16px;
            font-size: 13px;
            font-weight: 500;
            color: #6b7280;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Test Loading Position Fix</h1>
        <p>Verifikasi bahwa loading spinner dan text berada di center, bukan di footer.</p>

        <div class="info">
            <strong>🎯 Yang Diperbaiki:</strong>
            <ul>
                <li>Loading spinner dan text sekarang berada di center area</li>
                <li>Tidak terpengaruh oleh footer positioning</li>
                <li>State container menggunakan flexbox center alignment</li>
                <li>Loading content memiliki proper spacing dan sizing</li>
            </ul>
        </div>

        <!-- Visual Mockup -->
        <div class="test-section">
            <h3>📱 Visual Preview - Loading State</h3>
            <p>Ini adalah mockup bagaimana loading state seharusnya terlihat:</p>
            
            <div class="preview-container">
                <div class="extension-mockup">
                    <div class="mockup-header">Progress Dashboard - OTP Authenticator</div>
                    <div class="connection-badge-mockup"></div>
                    <div class="mockup-main">
                        <div style="display: flex; flex-direction: column; align-items: center;">
                            <div class="loading-spinner-mockup"></div>
                            <div class="loading-text-mockup">Loading...</div>
                        </div>
                    </div>
                    <div class="mockup-footer">v1.0.0 | Built by Hellozai</div>
                </div>
            </div>
            
            <div class="success">
                ✅ <strong>Correct Position:</strong> Loading spinner dan text berada di center area, tidak di footer.
            </div>
        </div>

        <!-- Before vs After -->
        <div class="test-section">
            <h3>🔄 Before vs After</h3>
            
            <div style="display: flex; gap: 20px; justify-content: center;">
                <div>
                    <h4 style="color: #ef4444;">❌ Before (Incorrect)</h4>
                    <div class="extension-mockup" style="height: 140px;">
                        <div class="mockup-header">Extension Header</div>
                        <div class="connection-badge-mockup"></div>
                        <div class="mockup-main" style="align-items: flex-end; padding-bottom: 10px;">
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="width: 20px; height: 20px; border: 2px solid #e2e8f0; border-top: 2px solid #9CEE69; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                                <div style="margin-top: 8px; font-size: 12px; color: #6b7280;">Loading...</div>
                            </div>
                        </div>
                        <div class="mockup-footer">Footer (loading terlalu dekat)</div>
                    </div>
                </div>
                
                <div>
                    <h4 style="color: #22c55e;">✅ After (Correct)</h4>
                    <div class="extension-mockup" style="height: 140px;">
                        <div class="mockup-header">Extension Header</div>
                        <div class="connection-badge-mockup"></div>
                        <div class="mockup-main">
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <div class="loading-spinner-mockup"></div>
                                <div class="loading-text-mockup">Loading...</div>
                            </div>
                        </div>
                        <div class="mockup-footer">Footer (loading di center)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Changes -->
        <div class="test-section">
            <h3>🔧 Technical Changes Made</h3>
            
            <div class="info">
                <strong>CSS Changes:</strong>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px;">
/* State Container - Now uses flexbox center */
.state-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* Loading Content - Enhanced sizing and spacing */
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  width: 100%;
  height: 100%;
  min-height: 80px;
}

/* Loading Spinner - Larger and more prominent */
.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #9CEE69;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}
                </pre>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="test-section">
            <h3>🧪 Testing Instructions</h3>
            
            <div class="warning">
                <strong>⚠️ Untuk memverifikasi fix:</strong>
                <ol>
                    <li>Reload Chrome Extension di <code>chrome://extensions/</code></li>
                    <li>Buka extension popup</li>
                    <li>Trigger loading state (misalnya saat checking connection)</li>
                    <li>Verifikasi bahwa loading spinner dan text berada di center area</li>
                    <li>Pastikan tidak ada overlap dengan footer</li>
                </ol>
            </div>
            
            <button onclick="testExtensionLoading()">Test Extension Loading State</button>
            <div id="test-results"></div>
        </div>

        <!-- States Comparison -->
        <div class="test-section">
            <h3>📊 All States Positioning</h3>
            <p>Verifikasi bahwa semua states memiliki positioning yang benar:</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <!-- Idle State -->
                <div>
                    <h4>Idle State</h4>
                    <div class="extension-mockup" style="height: 120px;">
                        <div class="connection-badge-mockup"></div>
                        <div class="mockup-main">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; margin-bottom: 8px;">🔐</div>
                                <div style="font-size: 12px; font-weight: 600;">Progress Dashboard</div>
                                <div style="font-size: 10px; color: #6b7280;">OTP Authenticator</div>
                                <div style="font-size: 10px; color: #6b7280; margin-top: 8px;">Connected to backend</div>
                            </div>
                        </div>
                        <div class="mockup-footer">Footer</div>
                    </div>
                </div>
                
                <!-- Loading State -->
                <div>
                    <h4>Loading State</h4>
                    <div class="extension-mockup" style="height: 120px;">
                        <div class="connection-badge-mockup"></div>
                        <div class="mockup-main">
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <div class="loading-spinner-mockup"></div>
                                <div class="loading-text-mockup">Loading...</div>
                            </div>
                        </div>
                        <div class="mockup-footer">Footer</div>
                    </div>
                </div>
                
                <!-- Success State -->
                <div>
                    <h4>Success State</h4>
                    <div class="extension-mockup" style="height: 160px;">
                        <div class="connection-badge-mockup" style="background: #22c55e; animation: pulse 1s ease-in-out;"></div>
                        <div class="mockup-main">
                            <div style="text-align: center; padding: 15px; border: 1px solid rgba(156, 238, 105, 0.3); border-radius: 8px; background: linear-gradient(135deg, rgba(156, 238, 105, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%);">
                                <div style="font-size: 24px; margin-bottom: 8px;">✅</div>
                                <div style="font-size: 14px; font-weight: 600;">Login Approved!</div>
                                <div style="font-size: 11px; color: #64748b; margin-top: 8px;">Closing in 3s</div>
                            </div>
                        </div>
                        <div class="mockup-footer">Footer</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testExtensionLoading() {
            const resultsDiv = document.getElementById('test-results');
            
            resultsDiv.innerHTML = `
                <div class="info">🔄 Testing extension loading state...</div>
                <div class="warning">
                    <strong>Manual Verification Required:</strong>
                    <ol>
                        <li>Open Chrome Extension popup</li>
                        <li>Look for loading state (spinner + "Loading..." text)</li>
                        <li>Verify it's centered in the main area, not near footer</li>
                        <li>Check that connection badge is in top-right corner</li>
                    </ol>
                </div>
            `;
            
            // Check if extension is available
            if (typeof window.progressDashboardExtension !== 'undefined') {
                resultsDiv.innerHTML += `
                    <div class="success">✅ Extension is available for testing!</div>
                `;
            } else {
                resultsDiv.innerHTML += `
                    <div class="error">❌ Extension not detected. Please install and reload extension first.</div>
                `;
            }
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('🔄 Loading position test page loaded');
        });
    </script>
</body>
</html>
