#!/bin/bash

# Documentation Maintenance Script
# Helps maintain and validate documentation for Progress Dashboard

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCS_DIR="./docs"
ROOT_DIR="."
BACKUP_DIR="./docs-backup"
LOG_FILE="./docs-maintenance.log"

# Functions
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Documentation Maintenance${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if documentation directory exists
check_docs_structure() {
    log "Checking documentation structure..."
    
    local required_dirs=(
        "$DOCS_DIR"
        "$DOCS_DIR/guides"
        "$DOCS_DIR/api"
        "$DOCS_DIR/components"
        "$DOCS_DIR/templates"
        "$DOCS_DIR/guidelines"
    )
    
    local missing_dirs=()
    
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            missing_dirs+=("$dir")
        fi
    done
    
    if [ ${#missing_dirs[@]} -eq 0 ]; then
        print_success "Documentation structure is complete"
        return 0
    else
        print_warning "Missing directories:"
        for dir in "${missing_dirs[@]}"; do
            echo "  - $dir"
        done
        return 1
    fi
}

# Create missing documentation structure
create_docs_structure() {
    log "Creating missing documentation structure..."
    
    local dirs=(
        "$DOCS_DIR/guides"
        "$DOCS_DIR/api"
        "$DOCS_DIR/components"
        "$DOCS_DIR/architecture"
        "$DOCS_DIR/deployment"
        "$DOCS_DIR/operations"
        "$DOCS_DIR/technical"
        "$DOCS_DIR/design"
        "$DOCS_DIR/performance"
        "$DOCS_DIR/templates"
        "$DOCS_DIR/guidelines"
        "$DOCS_DIR/assets/images"
        "$DOCS_DIR/assets/diagrams"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "Created directory: $dir"
        fi
    done
}

# Check for broken links in markdown files
check_broken_links() {
    log "Checking for broken internal links..."
    
    local broken_links=0
    
    # Find all markdown files
    while IFS= read -r -d '' file; do
        echo "Checking links in: $file"
        
        # Extract markdown links [text](link)
        grep -oP '\[.*?\]\(\K[^)]+' "$file" 2>/dev/null | while read -r link; do
            # Skip external links (http/https)
            if [[ $link =~ ^https?:// ]]; then
                continue
            fi
            
            # Skip anchors
            if [[ $link =~ ^# ]]; then
                continue
            fi
            
            # Convert relative path to absolute
            local dir=$(dirname "$file")
            local target_file="$dir/$link"
            
            # Normalize path
            target_file=$(realpath -m "$target_file" 2>/dev/null || echo "$target_file")
            
            # Check if file exists
            if [ ! -f "$target_file" ]; then
                print_error "Broken link in $file: $link -> $target_file"
                ((broken_links++))
            fi
        done
    done < <(find "$ROOT_DIR" -name "*.md" -print0)
    
    if [ $broken_links -eq 0 ]; then
        print_success "No broken internal links found"
    else
        print_warning "Found $broken_links broken links"
    fi
    
    return $broken_links
}

# Validate markdown syntax
validate_markdown() {
    log "Validating markdown syntax..."
    
    local invalid_files=0
    
    # Check if markdownlint is available
    if ! command -v markdownlint &> /dev/null; then
        print_warning "markdownlint not found. Install with: npm install -g markdownlint-cli"
        return 0
    fi
    
    while IFS= read -r -d '' file; do
        if ! markdownlint "$file" &>/dev/null; then
            print_error "Invalid markdown syntax: $file"
            ((invalid_files++))
        fi
    done < <(find "$ROOT_DIR" -name "*.md" -print0)
    
    if [ $invalid_files -eq 0 ]; then
        print_success "All markdown files have valid syntax"
    else
        print_warning "Found $invalid_files files with syntax issues"
    fi
    
    return $invalid_files
}

# Generate table of contents for markdown files
generate_toc() {
    log "Generating table of contents..."
    
    # Check if markdown-toc is available
    if ! command -v markdown-toc &> /dev/null; then
        print_warning "markdown-toc not found. Install with: npm install -g markdown-toc"
        return 0
    fi
    
    local updated_files=0
    
    while IFS= read -r -d '' file; do
        # Skip files that already have TOC or are templates
        if grep -q "<!-- toc -->" "$file" || [[ $file == *"/templates/"* ]]; then
            continue
        fi
        
        # Check if file is long enough to need TOC (more than 100 lines)
        if [ $(wc -l < "$file") -gt 100 ]; then
            # Add TOC marker if not present
            if ! grep -q "## Table of Contents" "$file"; then
                # Insert TOC after the first heading
                sed -i '1,/^# /a\\n## Table of Contents\n\n<!-- toc -->\n\n<!-- tocstop -->\n' "$file"
                markdown-toc -i "$file"
                print_success "Added TOC to: $file"
                ((updated_files++))
            fi
        fi
    done < <(find "$DOCS_DIR" -name "*.md" -print0)
    
    if [ $updated_files -eq 0 ]; then
        print_success "All files have appropriate TOCs"
    else
        print_success "Updated $updated_files files with TOCs"
    fi
}

# Update last modified dates
update_timestamps() {
    log "Updating last modified timestamps..."
    
    local updated_files=0
    
    while IFS= read -r -d '' file; do
        # Skip template files
        if [[ $file == *"/templates/"* ]]; then
            continue
        fi
        
        local current_date=$(date '+%Y-%m-%d')
        
        # Check if file has a "Last Updated" line
        if grep -q "Last Updated" "$file"; then
            # Update existing timestamp
            sed -i "s/Last Updated.*$/Last Updated**: $current_date/" "$file"
            ((updated_files++))
        elif grep -q "^---$" "$file"; then
            # Add timestamp before closing frontmatter
            sed -i "/^---$/i\\**Last Updated**: $current_date  " "$file"
            ((updated_files++))
        fi
    done < <(find "$DOCS_DIR" -name "*.md" -print0)
    
    if [ $updated_files -gt 0 ]; then
        print_success "Updated timestamps in $updated_files files"
    fi
}

# Create backup of documentation
backup_docs() {
    log "Creating documentation backup..."
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_path="${BACKUP_DIR}/docs_backup_${timestamp}"
    
    mkdir -p "$backup_path"
    cp -r "$DOCS_DIR"/* "$backup_path/" 2>/dev/null || true
    
    # Keep only last 5 backups
    if [ -d "$BACKUP_DIR" ]; then
        ls -t "$BACKUP_DIR" | tail -n +6 | xargs -I {} rm -rf "$BACKUP_DIR/{}"
    fi
    
    print_success "Documentation backed up to: $backup_path"
}

# Generate documentation index
generate_index() {
    log "Generating documentation index..."
    
    local index_file="$DOCS_DIR/generated-index.md"
    
    cat > "$index_file" << 'EOF'
# Generated Documentation Index

This index is automatically generated. Last updated: $(date '+%Y-%m-%d %H:%M:%S')

## All Documentation Files

EOF
    
    # Add all markdown files to index
    find "$DOCS_DIR" -name "*.md" -not -name "generated-index.md" | sort | while read -r file; do
        local relative_path=${file#$DOCS_DIR/}
        local title=$(head -n 1 "$file" | sed 's/^# //')
        echo "- [$title](./$relative_path)" >> "$index_file"
    done
    
    print_success "Generated documentation index: $index_file"
}

# Main function
main() {
    print_header
    
    case "${1:-check}" in
        "check")
            log "Running documentation checks..."
            check_docs_structure
            check_broken_links
            validate_markdown
            ;;
        "fix")
            log "Running documentation fixes..."
            create_docs_structure
            generate_toc
            update_timestamps
            ;;
        "backup")
            backup_docs
            ;;
        "index")
            generate_index
            ;;
        "all")
            log "Running complete documentation maintenance..."
            backup_docs
            create_docs_structure
            check_broken_links
            validate_markdown
            generate_toc
            update_timestamps
            generate_index
            ;;
        "help")
            echo "Usage: $0 [command]"
            echo
            echo "Commands:"
            echo "  check   - Check documentation for issues (default)"
            echo "  fix     - Fix common documentation issues"
            echo "  backup  - Create backup of documentation"
            echo "  index   - Generate documentation index"
            echo "  all     - Run complete maintenance"
            echo "  help    - Show this help message"
            ;;
        *)
            print_error "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
    
    log "Documentation maintenance completed"
    print_success "Done! Check $LOG_FILE for detailed logs."
}

# Run main function with all arguments
main "$@"
