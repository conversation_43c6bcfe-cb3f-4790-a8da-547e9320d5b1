#!/usr/bin/env node

/**
 * Import Verification Script
 *
 * Verifies that all AUTH_STORAGE_KEYS and AUTH_CONFIG imports are correct
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Files to check
const filesToCheck = [
  'src/contexts/AuthContext.tsx',
  'src/components/auth/LoginForm.tsx',
  'src/hooks/useOTP.ts',
  'src/services/extensionService.ts',
  'src/services/authService.ts',
  'src/types/auth.ts'
];

// Patterns to look for
const patterns = {
  authStorageKeysType: /import\s+type\s*{[^}]*AUTH_STORAGE_KEYS[^}]*}\s*from/,
  authStorageKeysValue: /import\s*{[^}]*AUTH_STORAGE_KEYS[^}]*}\s*from/,
  authConfigType: /import\s+type\s*{[^}]*AUTH_CONFIG[^}]*}\s*from/,
  authConfigValue: /import\s*{[^}]*AUTH_CONFIG[^}]*}\s*from/,
  authStorageKeysUsage: /AUTH_STORAGE_KEYS\./,
  authConfigUsage: /AUTH_CONFIG\./
};

function checkFile(filePath) {
  log(`\n📁 Checking: ${filePath}`, 'cyan');
  
  if (!fs.existsSync(filePath)) {
    log(`  ❌ File not found`, 'red');
    return { errors: 1, warnings: 0 };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  let errors = 0;
  let warnings = 0;
  let hasAuthStorageKeysUsage = false;
  let hasAuthConfigUsage = false;
  let hasAuthStorageKeysImport = false;
  let hasAuthConfigImport = false;
  
  // Check each line
  lines.forEach((line, index) => {
    const lineNum = index + 1;
    
    // Check for type imports (should be errors)
    if (patterns.authStorageKeysType.test(line)) {
      log(`  ❌ Line ${lineNum}: AUTH_STORAGE_KEYS imported as type (should be value)`, 'red');
      log(`     ${line.trim()}`, 'yellow');
      errors++;
    }
    
    if (patterns.authConfigType.test(line)) {
      log(`  ❌ Line ${lineNum}: AUTH_CONFIG imported as type (should be value)`, 'red');
      log(`     ${line.trim()}`, 'yellow');
      errors++;
    }
    
    // Check for value imports (should be correct)
    if (patterns.authStorageKeysValue.test(line) && !patterns.authStorageKeysType.test(line)) {
      log(`  ✅ Line ${lineNum}: AUTH_STORAGE_KEYS correctly imported as value`, 'green');
      hasAuthStorageKeysImport = true;
    }
    
    if (patterns.authConfigValue.test(line) && !patterns.authConfigType.test(line)) {
      log(`  ✅ Line ${lineNum}: AUTH_CONFIG correctly imported as value`, 'green');
      hasAuthConfigImport = true;
    }
    
    // Check for usage
    if (patterns.authStorageKeysUsage.test(line)) {
      hasAuthStorageKeysUsage = true;
    }
    
    if (patterns.authConfigUsage.test(line)) {
      hasAuthConfigUsage = true;
    }
  });
  
  // Check for missing imports
  if (hasAuthStorageKeysUsage && !hasAuthStorageKeysImport) {
    log(`  ⚠️  AUTH_STORAGE_KEYS is used but not properly imported`, 'yellow');
    warnings++;
  }
  
  if (hasAuthConfigUsage && !hasAuthConfigImport) {
    log(`  ⚠️  AUTH_CONFIG is used but not properly imported`, 'yellow');
    warnings++;
  }
  
  if (errors === 0 && warnings === 0) {
    log(`  ✅ All imports are correct`, 'green');
  }
  
  return { errors, warnings };
}

function main() {
  log('🔍 AUTH Import Verification Script', 'magenta');
  log('=====================================', 'magenta');
  
  let totalErrors = 0;
  let totalWarnings = 0;
  
  filesToCheck.forEach(filePath => {
    const result = checkFile(filePath);
    totalErrors += result.errors;
    totalWarnings += result.warnings;
  });
  
  log('\n📊 SUMMARY', 'magenta');
  log('===========', 'magenta');
  
  if (totalErrors === 0 && totalWarnings === 0) {
    log('🎉 All imports are correct! No issues found.', 'green');
    log('✅ AUTH_STORAGE_KEYS and AUTH_CONFIG are properly imported as values.', 'green');
  } else {
    if (totalErrors > 0) {
      log(`❌ Found ${totalErrors} error(s) that need to be fixed.`, 'red');
    }
    if (totalWarnings > 0) {
      log(`⚠️  Found ${totalWarnings} warning(s) that should be reviewed.`, 'yellow');
    }
  }
  
  log('\n🔧 FIXES APPLIED:', 'blue');
  log('- AuthContext.tsx: AUTH_STORAGE_KEYS import fixed', 'blue');
  log('- LoginForm.tsx: AUTH_STORAGE_KEYS import fixed', 'blue');
  log('- useOTP.ts: AUTH_CONFIG import fixed', 'blue');
  log('- extensionService.ts: AUTH_CONFIG import fixed', 'blue');
  
  log('\n🎯 NEXT STEPS:', 'cyan');
  log('1. Test login page: http://localhost:5173/login', 'cyan');
  log('2. Check browser console for errors', 'cyan');
  log('3. Verify extension communication works', 'cyan');
  log('4. Test OTP flow end-to-end', 'cyan');
  
  process.exit(totalErrors > 0 ? 1 : 0);
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
