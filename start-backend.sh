#!/bin/bash

# Progress Dashboard Backend Startup Script
# Fixes OTP authentication errors by starting the backend server

echo "🚀 Progress Dashboard Backend Startup"
echo "====================================="

# Check if we're in the right directory
if [ ! -d "backend" ]; then
    echo "❌ Error: backend directory not found"
    echo "Please run this script from the Progress_Dashboard-main directory"
    exit 1
fi

# Navigate to backend directory
cd backend

echo "📁 Current directory: $(pwd)"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed"
    echo "Please install Python 3.8 or higher:"
    echo "  macOS: brew install python3"
    echo "  Ubuntu: sudo apt install python3"
    echo "  Windows: Download from python.org"
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed"
    echo "Please install pip3"
    exit 1
fi

echo "✅ pip3 found: $(pip3 --version)"

# Check if port 5001 is already in use
if lsof -Pi :5001 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  Port 5001 is already in use"
    echo "Killing existing process..."
    lsof -ti:5001 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "❌ Failed to create virtual environment"
        exit 1
    fi
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📥 Installing dependencies..."
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        echo "Trying to install core dependencies manually..."
        pip install flask flask-cors redis pyotp cryptography pandas
    fi
else
    echo "⚠️  requirements.txt not found, installing core dependencies..."
    pip install flask flask-cors redis pyotp cryptography pandas
fi

# Create necessary directories
mkdir -p data
mkdir -p uploads

# Check if Redis is available (optional)
if command -v redis-server &> /dev/null; then
    echo "✅ Redis found - OTP system will use Redis for storage"
else
    echo "⚠️  Redis not found - OTP system will use in-memory storage"
    echo "For production, install Redis: brew install redis (macOS)"
fi

echo ""
echo "✅ Setup complete!"
echo ""
echo "🌐 Starting Flask API server..."
echo "📊 API will be available at: http://localhost:5001"
echo "🔐 OTP Authentication endpoints:"
echo "   • POST /api/auth/generate-otp"
echo "   • POST /api/auth/validate-otp"
echo "   • GET  /api/auth/health"
echo ""
echo "📁 Data directory: $(pwd)/data"
echo "📤 Upload directory: $(pwd)/uploads"
echo ""
echo "🎯 After server starts:"
echo "   1. Go to: http://localhost:5173/login"
echo "   2. Enter email and click 'Send OTP'"
echo "   3. Check Chrome Extension for OTP code"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the Flask application
echo "🚀 Starting server..."
python app.py
