<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        #logs { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>Chrome Extension Test</h1>
    
    <div id="status" class="status info">Testing extension...</div>
    
    <div>
        <button onclick="testExtensionCheck()">Test Extension Check</button>
        <button onclick="testOTPRequest()">Test OTP Request</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <h3>Console Logs:</h3>
    <div id="logs"></div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function testExtensionCheck() {
            log('Starting extension check test...', 'info');

            const responseListener = (event) => {
                if (event.data.type === 'EXTENSION_CHECK_RESPONSE' &&
                    event.origin === window.location.origin) {

                    if (event.data.error || event.data.extensionId === 'context-invalidated') {
                        log('⚠️ Extension context invalidated!', 'error');
                        log('Please reload this page after extension reload', 'error');
                        updateStatus('Extension Context Invalid - Reload Page!', 'error');
                    } else {
                        log('✅ Extension check response received!', 'success');
                        log(`Extension ID: ${event.data.extensionId}`, 'info');
                        log(`Extension Version: ${event.data.version}`, 'info');
                        updateStatus('Extension Available!', 'success');
                    }
                    window.removeEventListener('message', responseListener);
                }
            };

            window.addEventListener('message', responseListener);
            
            // Send extension check
            window.postMessage({ type: 'EXTENSION_CHECK' }, window.location.origin);
            log('Sent EXTENSION_CHECK message', 'info');
            
            // Timeout after 3 seconds
            setTimeout(() => {
                window.removeEventListener('message', responseListener);
                if (document.getElementById('status').textContent === 'Testing extension...') {
                    updateStatus('Extension Not Available', 'error');
                    log('❌ Extension check timeout - no response received', 'error');
                }
            }, 3000);
        }

        function testOTPRequest() {
            log('Starting OTP request test...', 'info');
            
            const responseListener = (event) => {
                if (event.data.type === 'OTP_RESPONSE' && 
                    event.origin === window.location.origin) {
                    log('✅ OTP response received!', 'success');
                    log(`Action: ${event.data.action}`, 'info');
                    if (event.data.otp) {
                        log(`OTP: ${event.data.otp}`, 'info');
                    }
                    window.removeEventListener('message', responseListener);
                }
            };

            window.addEventListener('message', responseListener);
            
            // Send OTP request
            const otpRequest = {
                type: 'OTP_REQUEST',
                email: '<EMAIL>',
                otp: '123456',
                secret: 'test-secret-key'
            };
            
            window.postMessage(otpRequest, window.location.origin);
            log('Sent OTP_REQUEST message', 'info');
            log(`Email: ${otpRequest.email}`, 'info');
            log(`OTP: ${otpRequest.otp}`, 'info');
            
            // Timeout after 30 seconds
            setTimeout(() => {
                window.removeEventListener('message', responseListener);
                log('⏰ OTP request timeout', 'error');
            }, 30000);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        // Auto-test extension on load
        window.addEventListener('load', () => {
            setTimeout(testExtensionCheck, 1000);
        });
    </script>
</body>
</html>
