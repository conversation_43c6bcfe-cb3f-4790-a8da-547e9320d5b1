<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Full OTP Authentication Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            font-size: 12px;
        }
        .test-step {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: #28a745;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Full OTP Authentication Flow</h1>
        <p>Test lengkap untuk memverifikasi bahwa seluruh sistem autentikasi OTP berfungsi dengan baik.</p>

        <div class="success">
            <strong>✅ Extension Status:</strong> Connected to backend (http://localhost:5001)
        </div>

        <!-- Progress Bar -->
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
        </div>
        <div id="progress-text">Ready to start testing...</div>

        <!-- Step 1: Pre-flight Checks -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">1</div>
                <h3>Pre-flight Checks</h3>
            </div>
            <button onclick="runPreflightChecks()">Run Pre-flight Checks</button>
            <div id="preflight-results"></div>
        </div>

        <!-- Step 2: Generate OTP -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">2</div>
                <h3>Generate OTP</h3>
            </div>
            <input type="email" id="test-email" placeholder="Enter test email" value="<EMAIL>">
            <button onclick="generateOTP()" id="generate-btn">Generate OTP</button>
            <div id="generate-results"></div>
        </div>

        <!-- Step 3: Extension Notification -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">3</div>
                <h3>Extension Notification</h3>
            </div>
            <p>Setelah OTP di-generate, extension akan menampilkan notifikasi.</p>
            <button onclick="checkExtensionNotification()" id="check-notification-btn" disabled>Check Extension Notification</button>
            <div id="notification-results"></div>
        </div>

        <!-- Step 4: Approve/Reject OTP -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">4</div>
                <h3>Approve/Reject OTP</h3>
            </div>
            <p>Klik tombol di Chrome Extension untuk approve atau reject OTP.</p>
            <button onclick="waitForUserAction()" id="wait-action-btn" disabled>Wait for User Action</button>
            <div id="action-results"></div>
        </div>

        <!-- Step 5: Validate OTP -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">5</div>
                <h3>Validate OTP & Create Session</h3>
            </div>
            <p>Sistem akan otomatis memvalidasi OTP dan membuat session.</p>
            <div id="validation-results"></div>
        </div>

        <!-- Step 6: Test Session -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">6</div>
                <h3>Test Session</h3>
            </div>
            <button onclick="testSession()" id="test-session-btn" disabled>Test Session</button>
            <div id="session-results"></div>
        </div>

        <!-- Full Auto Test -->
        <div class="test-step" style="background: #e8f5e8; border-color: #28a745;">
            <div class="step-header">
                <div class="step-number" style="background: #28a745;">🚀</div>
                <h3>Full Auto Test</h3>
            </div>
            <p>Jalankan seluruh test secara otomatis (dalam development mode, OTP akan auto-approve).</p>
            <button onclick="runFullAutoTest()" id="auto-test-btn">Run Full Auto Test</button>
            <div id="auto-test-results"></div>
        </div>

        <!-- Debug Panel -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">🔧</div>
                <h3>Debug Information</h3>
            </div>
            <button onclick="getDebugInfo()">Get Debug Info</button>
            <div id="debug-results"></div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let otpData = null;
        let sessionData = null;

        function updateProgress(step, total = 6) {
            const percentage = (step / total) * 100;
            document.getElementById('progress-fill').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = `Step ${step} of ${total} completed`;
        }

        async function runPreflightChecks() {
            const resultsDiv = document.getElementById('preflight-results');
            resultsDiv.innerHTML = '<div class="info">Running pre-flight checks...</div>';

            const checks = {
                backend: false,
                extension: false,
                authEndpoint: false
            };

            try {
                // Check backend health
                const healthResponse = await fetch('http://localhost:5001/api/auth/health');
                checks.backend = healthResponse.ok;

                // Check extension
                checks.extension = typeof window.progressDashboardExtension !== 'undefined';

                // Check auth endpoint
                const authResponse = await fetch('http://localhost:5001/api/auth/health');
                checks.authEndpoint = authResponse.ok;

                let html = '<div class="success">✅ Pre-flight checks completed:</div><ul>';
                html += `<li>Backend Health: ${checks.backend ? '✅' : '❌'}</li>`;
                html += `<li>Extension Available: ${checks.extension ? '✅' : '❌'}</li>`;
                html += `<li>Auth Endpoint: ${checks.authEndpoint ? '✅' : '❌'}</li>`;
                html += '</ul>';

                if (checks.backend && checks.extension && checks.authEndpoint) {
                    html += '<div class="success">🎉 All checks passed! Ready to test OTP flow.</div>';
                    currentStep = 1;
                    updateProgress(1);
                } else {
                    html += '<div class="error">❌ Some checks failed. Please fix issues before proceeding.</div>';
                }

                resultsDiv.innerHTML = html;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Pre-flight checks failed: ${error.message}</div>`;
            }
        }

        async function generateOTP() {
            const email = document.getElementById('test-email').value;
            const resultsDiv = document.getElementById('generate-results');
            
            if (!email) {
                resultsDiv.innerHTML = '<div class="error">❌ Please enter an email address</div>';
                return;
            }

            resultsDiv.innerHTML = '<div class="info">Generating OTP...</div>';

            try {
                const response = await fetch('http://localhost:5001/api/auth/generate-otp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    otpData = data;
                    resultsDiv.innerHTML = `
                        <div class="success">✅ OTP generated successfully!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                        <div class="info">📱 Check your Chrome Extension for OTP notification...</div>
                    `;
                    
                    currentStep = 2;
                    updateProgress(2);
                    document.getElementById('check-notification-btn').disabled = false;
                    
                    // Auto-check notification after 2 seconds
                    setTimeout(checkExtensionNotification, 2000);
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">❌ OTP generation failed</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        function checkExtensionNotification() {
            const resultsDiv = document.getElementById('notification-results');
            
            if (typeof window.progressDashboardExtension === 'undefined') {
                resultsDiv.innerHTML = '<div class="error">❌ Extension not available</div>';
                return;
            }

            try {
                // Get extension state
                const bridge = window.progressDashboardExtension;
                if (bridge.getDebugInfo) {
                    const debugInfo = bridge.getDebugInfo();
                    
                    resultsDiv.innerHTML = `
                        <div class="info">📱 Extension notification status:</div>
                        <pre>${JSON.stringify(debugInfo, null, 2)}</pre>
                        <div class="warning">⏳ Please check your Chrome Extension and click "Login" to approve the OTP request.</div>
                    `;
                    
                    currentStep = 3;
                    updateProgress(3);
                    document.getElementById('wait-action-btn').disabled = false;
                } else {
                    resultsDiv.innerHTML = '<div class="warning">⚠️ Extension debug info not available</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error checking notification: ${error.message}</div>`;
            }
        }

        function waitForUserAction() {
            const resultsDiv = document.getElementById('action-results');
            resultsDiv.innerHTML = `
                <div class="info">⏳ Waiting for user action...</div>
                <div class="warning">Please click "Login" in your Chrome Extension to approve the OTP request.</div>
                <p>In development mode, this will auto-approve after 2 seconds.</p>
            `;
            
            // Poll for completion
            const pollInterval = setInterval(() => {
                // In a real scenario, we'd check if the OTP was approved
                // For now, we'll simulate auto-approval in dev mode
                setTimeout(() => {
                    clearInterval(pollInterval);
                    resultsDiv.innerHTML = `
                        <div class="success">✅ OTP approved! (Development mode auto-approval)</div>
                        <div class="info">Proceeding to validation...</div>
                    `;
                    
                    currentStep = 4;
                    updateProgress(4);
                    
                    // Auto-validate
                    setTimeout(validateOTP, 1000);
                }, 2000);
            }, 1000);
        }

        async function validateOTP() {
            const resultsDiv = document.getElementById('validation-results');
            
            if (!otpData) {
                resultsDiv.innerHTML = '<div class="error">❌ No OTP data available</div>';
                return;
            }

            resultsDiv.innerHTML = '<div class="info">Validating OTP and creating session...</div>';

            try {
                const response = await fetch('http://localhost:5001/api/auth/validate-otp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: otpData.email,
                        otp_code: otpData.otp_code,
                        otp_key: otpData.otp_key
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    sessionData = data;
                    resultsDiv.innerHTML = `
                        <div class="success">✅ OTP validated successfully! Session created.</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    currentStep = 5;
                    updateProgress(5);
                    document.getElementById('test-session-btn').disabled = false;
                    
                    // Auto-test session
                    setTimeout(testSession, 1000);
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">❌ OTP validation failed</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testSession() {
            const resultsDiv = document.getElementById('session-results');
            
            if (!sessionData || !sessionData.session_token) {
                resultsDiv.innerHTML = '<div class="error">❌ No session token available</div>';
                return;
            }

            resultsDiv.innerHTML = '<div class="info">Testing session...</div>';

            try {
                const response = await fetch('http://localhost:5001/api/auth/session-info', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${sessionData.session_token}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ Session is valid!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                        <div class="success">🎉 Full OTP authentication flow completed successfully!</div>
                    `;
                    
                    currentStep = 6;
                    updateProgress(6);
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">❌ Session test failed</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function runFullAutoTest() {
            const resultsDiv = document.getElementById('auto-test-results');
            resultsDiv.innerHTML = '<div class="info">🚀 Running full auto test...</div>';
            
            try {
                await runPreflightChecks();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await generateOTP();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                checkExtensionNotification();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                waitForUserAction();
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                await validateOTP();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testSession();
                
                resultsDiv.innerHTML = '<div class="success">🎉 Full auto test completed successfully!</div>';
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Auto test failed: ${error.message}</div>`;
            }
        }

        function getDebugInfo() {
            const resultsDiv = document.getElementById('debug-results');
            
            const debugInfo = {
                currentStep,
                otpData: otpData ? 'Available' : 'Not available',
                sessionData: sessionData ? 'Available' : 'Not available',
                extensionAvailable: typeof window.progressDashboardExtension !== 'undefined',
                timestamp: new Date().toISOString()
            };

            if (typeof window.progressDashboardExtension !== 'undefined') {
                try {
                    const bridge = window.progressDashboardExtension;
                    if (bridge.getDebugInfo) {
                        debugInfo.extensionDebugInfo = bridge.getDebugInfo();
                    }
                } catch (error) {
                    debugInfo.extensionError = error.message;
                }
            }

            resultsDiv.innerHTML = `
                <div class="info">Debug Information:</div>
                <pre>${JSON.stringify(debugInfo, null, 2)}</pre>
            `;
        }

        // Auto-run preflight checks on page load
        window.addEventListener('load', () => {
            setTimeout(runPreflightChecks, 1000);
        });
    </script>
</body>
</html>
