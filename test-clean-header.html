<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Clean Header Design</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .extension-mockup {
            width: 280px;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin: 20px auto;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        .mockup-main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 8px;
        }
        .mockup-footer {
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            padding: 4px 12px;
            font-size: 10px;
            color: #64748b;
            text-align: center;
            min-height: 20px;
            max-height: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .connection-badge-mockup {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 3px solid;
            transition: all 0.3s ease;
            backdrop-filter: blur(8px);
            box-shadow: 0 2px 8px 0 rgb(0 0 0 / 0.15);
        }
        .badge-connected {
            background: #22c55e;
            border-color: #16a34a;
        }
        .badge-connecting {
            background: #6b7280;
            border-color: #4b5563;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .before-after {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: flex-start;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Clean Header Design</h1>
        <p>Menghilangkan header text dan membuat design yang clean dengan focus pada connection badge.</p>

        <div class="success">
            <strong>✅ Design Changes Applied:</strong>
            <ul>
                <li>Removed "🔐 Progress Dashboard OTP Authenticator" text</li>
                <li>Clean idle state dengan minimal text</li>
                <li>Enhanced connection badge sebagai focal point</li>
                <li>Reduced container height untuk efficiency</li>
                <li>Simplified status messages</li>
            </ul>
        </div>

        <!-- Before vs After Comparison -->
        <div class="test-section">
            <h3>🔄 Before vs After Comparison</h3>
            
            <div class="before-after">
                <div>
                    <h4 style="color: #ef4444;">❌ Before (Cluttered)</h4>
                    <div class="extension-mockup" style="min-height: 110px;">
                        <div class="connection-badge-mockup badge-connected" style="width: 16px; height: 16px; top: 8px; right: 8px; border: 2px solid;"></div>
                        <div class="mockup-main">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; margin-bottom: 8px;">🔐</div>
                                <div style="font-size: 12px; font-weight: 600; margin-bottom: 4px;">Progress Dashboard</div>
                                <div style="font-size: 10px; color: #6b7280; margin-bottom: 12px;">OTP Authenticator</div>
                                <div style="font-size: 10px; color: #6b7280;">Connected to backend</div>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                    <div class="error">Too much text, cluttered</div>
                </div>
                
                <div>
                    <h4 style="color: #22c55e;">✅ After (Clean)</h4>
                    <div class="extension-mockup" style="min-height: 80px;">
                        <div class="connection-badge-mockup badge-connected"></div>
                        <div class="mockup-main">
                            <div style="text-align: center; padding: 20px 16px;">
                                <p style="margin: 0; font-size: 13px; font-weight: 500; color: #6b7280;">Ready for authentication</p>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                    <div class="success">Clean, minimal, focused</div>
                </div>
            </div>
        </div>

        <!-- Connection Badge Enhancement -->
        <div class="test-section">
            <h3>🔗 Enhanced Connection Badge</h3>
            
            <div class="info">
                <strong>🎯 Badge Improvements:</strong>
                <ul>
                    <li><strong>Size:</strong> 16px → 20px (more prominent)</li>
                    <li><strong>Border:</strong> 2px → 3px (stronger presence)</li>
                    <li><strong>Position:</strong> 8px → 12px from edges (better spacing)</li>
                    <li><strong>Shadow:</strong> Enhanced blur and depth</li>
                    <li><strong>Transition:</strong> Smoother 0.3s animation</li>
                </ul>
            </div>

            <div class="comparison-grid">
                <div>
                    <h4>Connected State</h4>
                    <div class="extension-mockup" style="min-height: 80px;">
                        <div class="connection-badge-mockup badge-connected"></div>
                        <div class="mockup-main">
                            <div style="text-align: center; padding: 20px 16px;">
                                <p style="margin: 0; font-size: 13px; font-weight: 500; color: #6b7280;">Ready for authentication</p>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                    <div class="success">✅ Green badge = Ready</div>
                </div>
                
                <div>
                    <h4>Connecting State</h4>
                    <div class="extension-mockup" style="min-height: 80px;">
                        <div class="connection-badge-mockup badge-connecting"></div>
                        <div class="mockup-main">
                            <div style="text-align: center; padding: 20px 16px;">
                                <p style="margin: 0; font-size: 13px; font-weight: 500; color: #6b7280;">Connecting...</p>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                    <div class="warning">⚠️ Gray badge = Connecting</div>
                </div>
            </div>
        </div>

        <!-- All States with Clean Design -->
        <div class="test-section">
            <h3>📱 All States - Clean Design</h3>
            
            <div class="comparison-grid">
                <!-- Idle State -->
                <div>
                    <h4>Idle State (80px)</h4>
                    <div class="extension-mockup" style="min-height: 80px;">
                        <div class="connection-badge-mockup badge-connected"></div>
                        <div class="mockup-main">
                            <div style="text-align: center; padding: 20px 16px;">
                                <p style="margin: 0; font-size: 13px; font-weight: 500; color: #6b7280;">Ready for authentication</p>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                </div>
                
                <!-- Loading State -->
                <div>
                    <h4>Loading State (80px)</h4>
                    <div class="extension-mockup" style="min-height: 80px;">
                        <div class="connection-badge-mockup badge-connecting"></div>
                        <div class="mockup-main">
                            <div style="display: flex; flex-direction: column; align-items: center; padding: 20px 16px;">
                                <div style="width: 24px; height: 24px; border: 3px solid #e2e8f0; border-top: 3px solid #9CEE69; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 16px;"></div>
                                <p style="margin: 0; font-size: 13px; font-weight: 500; color: #6b7280;">Loading...</p>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                </div>
                
                <!-- Active Request State -->
                <div>
                    <h4>Active Request (420px)</h4>
                    <div class="extension-mockup" style="min-height: 420px;">
                        <div class="connection-badge-mockup badge-connected"></div>
                        <div class="mockup-main" style="align-items: flex-start; justify-content: flex-start;">
                            <div style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 16px; margin: 4px; width: calc(100% - 8px);">
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 12px; padding-bottom: 10px; border-bottom: 1px solid #e2e8f0;">
                                    <span style="font-size: 16px;">🔑</span>
                                    <strong style="font-size: 13px;">Authentication Request</strong>
                                </div>
                                <div style="margin-bottom: 16px; font-size: 11px;">
                                    <div style="display: flex; justify-content: space-between; padding: 6px 0; border-bottom: 1px solid #f1f5f9;">
                                        <span style="color: #64748b;">Email:</span>
                                        <span><EMAIL></span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 0; border-bottom: 1px solid #f1f5f9;">
                                        <span style="color: #64748b;">Website:</span>
                                        <span>localhost:5173</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 0;">
                                        <span style="color: #64748b;">Expires in:</span>
                                        <span style="color: #ef4444; font-weight: 600;">4:59</span>
                                    </div>
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <button style="flex: 1; background: #9CEE69; color: #1A1919; border: 1px solid #9CEE69; border-radius: 6px; padding: 10px; font-size: 11px;">
                                        ✓ Approve Login
                                    </button>
                                    <button style="flex: 1; background: #f8fafc; color: #374151; border: 1px solid #e2e8f0; border-radius: 6px; padding: 10px; font-size: 11px;">
                                        ✕ Reject
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Changes -->
        <div class="test-section">
            <h3>🔧 Technical Changes Summary</h3>
            
            <div class="info">
                <strong>HTML Changes:</strong>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px;">
<!-- REMOVED -->
&lt;div class="app-logo"&gt;
  &lt;div class="logo-icon"&gt;🔐&lt;/div&gt;
  &lt;div class="logo-text"&gt;
    &lt;h3&gt;Progress Dashboard&lt;/h3&gt;
    &lt;p&gt;OTP Authenticator&lt;/p&gt;
  &lt;/div&gt;
&lt;/div&gt;

<!-- REPLACED WITH -->
&lt;div class="idle-message"&gt;
  &lt;p id="connectionStatusText"&gt;Ready for authentication&lt;/p&gt;
&lt;/div&gt;
                </pre>
            </div>

            <div class="info">
                <strong>CSS Changes:</strong>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px;">
/* Enhanced Connection Badge */
.connection-badge-mini {
  width: 20px;           /* Was 16px */
  height: 20px;          /* Was 16px */
  border: 3px solid;     /* Was 2px */
  top: 12px;             /* Was 8px */
  right: 12px;           /* Was 8px */
  transition: all 0.3s ease; /* Enhanced */
}

/* Simplified Idle State */
.idle-content {
  padding: 20px 16px;    /* Optimized */
}

/* Reduced Heights */
.app-container.state-idle { min-height: 80px; } /* Was 110px */
.app-container.state-loading { min-height: 80px; } /* Was 110px */
                </pre>
            </div>

            <div class="info">
                <strong>JavaScript Changes:</strong>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px;">
// Updated Status Messages
statusText.textContent = backendConnected ? 
  'Ready for authentication' :    // Was 'Connected to backend'
  'Connecting...';               // Was 'Not connected'

// Updated Heights
const stateMinHeights = {
  'idle': 80,        // Was 110
  'loading': 80,     // Was 110
  // ... other states unchanged
};
                </pre>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="test-section">
            <h3>🧪 Testing Instructions</h3>
            
            <div class="warning">
                <strong>⚠️ Verification Steps:</strong>
                <ol>
                    <li><strong>Reload Extension:</strong> Go to <code>chrome://extensions/</code> → Reload</li>
                    <li><strong>Check Idle State:</strong> Should show only "Ready for authentication" text</li>
                    <li><strong>Verify Badge:</strong> Connection badge should be more prominent in top-right</li>
                    <li><strong>Test States:</strong> All states should maintain clean design</li>
                    <li><strong>Check Sizing:</strong> Idle/Loading states should be more compact (80px)</li>
                </ol>
            </div>
            
            <button onclick="testCleanHeader()">Test Clean Header Design</button>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        function testCleanHeader() {
            const resultsDiv = document.getElementById('test-results');
            
            resultsDiv.innerHTML = `
                <div class="info">🎨 Clean header design applied!</div>
                <div class="success">
                    <strong>✅ Changes Applied:</strong>
                    <ul>
                        <li>Removed logo and branding text</li>
                        <li>Enhanced connection badge prominence</li>
                        <li>Simplified status messages</li>
                        <li>Reduced container heights</li>
                        <li>Clean, minimal design achieved</li>
                    </ul>
                </div>
                <div class="warning">
                    <strong>📱 Manual Verification Required:</strong><br>
                    Please reload the Chrome Extension and verify the clean header design.
                </div>
            `;
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('🎨 Clean header test page loaded');
        });

        // Add spin animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
