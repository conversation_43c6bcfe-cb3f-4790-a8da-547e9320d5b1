# 🔐 Progress Dashboard - OTP Authenticator Chrome Extension

A modern, secure Chrome Extension for OTP-based authentication with Progress Dashboard. Features dynamic UI, smooth animations, and seamless integration.

## ✨ Features

### 🔒 **Security**
- **OTP Authentication**: Secure TOTP-based authentication
- **Origin Validation**: Strict origin checking for security
- **Rate Limiting**: Built-in abuse prevention
- **Encrypted Storage**: Secure data storage with encryption
- **Session Management**: Secure session handling

### 🎨 **Modern UI/UX**
- **Dynamic Animations**: Smooth, modern animations
- **Responsive Design**: Works on all screen sizes
- **Glass Morphism**: Modern glass effects
- **Dark Mode Support**: Automatic dark/light mode
- **Accessibility**: WCAG compliant design

### ⚡ **Performance**
- **Lightweight**: Minimal resource usage
- **Fast Loading**: Optimized for speed
- **Efficient Communication**: Optimized message passing
- **Memory Management**: Proper cleanup and optimization

### 🔧 **Developer Experience**
- **TypeScript Ready**: Full type definitions
- **Modern JavaScript**: ES2021+ features
- **Modular Architecture**: Clean, maintainable code
- **Comprehensive Logging**: Detailed debug information

## 📦 Installation

### For Users

1. **Download from Chrome Web Store** (Coming Soon)
   ```
   https://chrome.google.com/webstore/detail/progress-dashboard-otp
   ```

2. **Manual Installation** (Development)
   - Download or clone this repository
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" (top right toggle)
   - Click "Load unpacked" and select the `chrome-extension` folder
   - The extension icon should appear in your toolbar

### For Developers

1. **Clone Repository**
   ```bash
   git clone https://github.com/progressdashboard/chrome-extension.git
   cd chrome-extension
   ```

2. **Setup Development Environment**
   ```bash
   npm run setup
   ```

3. **Generate Icons**
   - Open `assets/generate-icons.html` in your browser
   - Download all icon sizes (16px, 32px, 48px, 128px)
   - Save them in the `assets/` folder

4. **Load Extension**
   - Open Chrome: `chrome://extensions/`
   - Enable Developer mode
   - Click "Load unpacked"
   - Select the `chrome-extension` folder

## 🚀 Usage

### Basic Authentication Flow

1. **Visit Progress Dashboard**
   ```
   http://localhost:5174  (or your dashboard URL)
   ```

2. **Click "Send OTP"**
   - Enter your email address
   - Click the "Send OTP" button

3. **Approve in Extension**
   - Extension notification will appear
   - Click "Approve Login" to authenticate
   - Or click "Reject" to deny access

4. **Access Dashboard**
   - You'll be automatically logged in
   - Session persists for 24 hours

### Keyboard Shortcuts

- **Enter/Space**: Approve OTP request
- **Escape**: Reject OTP request
- **Ctrl+C**: Close notification

## 🏗️ Architecture

### File Structure
```
chrome-extension/
├── manifest.json              # Extension manifest
├── background.js              # Background service worker
├── content.js                 # Content script
├── popup/                     # Extension popup
│   ├── popup.html
│   ├── popup.js
│   └── popup.css
├── notification/              # OTP notifications
│   ├── notification.html
│   ├── notification.js
│   └── notification.css
├── styles/                    # Shared styles
│   ├── design-tokens.css
│   ├── animations.css
│   └── components.css
├── utils/                     # Utility modules
│   ├── communication.js
│   ├── storage.js
│   └── security.js
└── assets/                    # Icons and images
    ├── icon16.png
    ├── icon48.png
    └── icon128.png
```

### Communication Flow
```
Web Page ↔ Content Script ↔ Background Script ↔ Popup/Notification
```

### Security Model
- **Origin Validation**: Only allowed domains can communicate
- **Message Encryption**: Sensitive data is encrypted
- **Rate Limiting**: Prevents abuse and spam
- **Session Security**: Secure token management

## 🔧 Development

### Prerequisites
- **Node.js**: 16.0.0 or higher
- **Chrome**: 88 or higher
- **Git**: For version control

### Development Commands

```bash
# Setup development environment
npm run setup

# Lint and format code
npm run validate

# Build extension package
npm run build

# Clean build artifacts
npm run clean
```

### Testing

1. **Manual Testing**
   ```bash
   npm test
   ```

2. **Load Test Extension**
   - Make changes to code
   - Go to `chrome://extensions/`
   - Click refresh icon on extension
   - Test functionality

3. **Debug Console**
   - Right-click extension icon → "Inspect popup"
   - Check background script: `chrome://extensions/` → "Inspect views"

### Code Style

- **ESLint**: Automatic linting
- **Prettier**: Code formatting
- **Modern JavaScript**: ES2021+ features
- **Modular Design**: Clean separation of concerns

## 🔒 Security

### Security Features
- **Content Security Policy**: Strict CSP rules
- **Origin Validation**: Whitelist-based origin checking
- **Input Sanitization**: All inputs are sanitized
- **Rate Limiting**: Prevents brute force attacks
- **Encrypted Storage**: Sensitive data encryption

### Permissions
- **storage**: For settings and cache
- **activeTab**: For current tab communication
- **notifications**: For OTP notifications
- **scripting**: For content script injection

### Allowed Origins
- `http://localhost:*` (Development)
- `https://progressdashboard.com`
- `https://app.progressdashboard.com`
- `https://dashboard.progressdashboard.com`

## 🎨 UI/UX Design

### Design System
- **Colors**: Consistent with Progress Dashboard
- **Typography**: Inter font family
- **Spacing**: 8px grid system
- **Animations**: Smooth, purposeful animations
- **Accessibility**: WCAG 2.1 AA compliant

### Components
- **Modern Cards**: Glass morphism effects
- **Dynamic Buttons**: Hover and loading states
- **Progress Indicators**: Real-time progress bars
- **Notifications**: Non-intrusive notifications
- **Responsive Layout**: Mobile-first design

### Animations
- **Entrance**: Slide-in animations
- **Loading**: Smooth loading states
- **Feedback**: Success/error animations
- **Micro-interactions**: Hover effects

## 📱 Browser Support

| Browser | Version | Status |
|---------|---------|--------|
| Chrome  | 88+     | ✅ Full Support |
| Edge    | 88+     | ✅ Full Support |
| Opera   | 74+     | ✅ Full Support |
| Firefox | N/A     | ❌ Not Supported* |

*Firefox uses different extension API (WebExtensions)

## 🚀 Deployment

### Chrome Web Store

1. **Prepare Package**
   ```bash
   npm run build
   ```

2. **Upload to Chrome Web Store**
   - Go to [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole)
   - Upload `progress-dashboard-extension.zip`
   - Fill in store listing details
   - Submit for review

3. **Store Listing**
   - **Name**: Progress Dashboard - OTP Authenticator
   - **Category**: Productivity
   - **Description**: Secure OTP authentication for Progress Dashboard

### Enterprise Deployment

1. **Group Policy**
   - Use Chrome Enterprise policies
   - Force-install extension for organization

2. **Custom Distribution**
   - Host extension on private server
   - Use enterprise installation methods

## 🐛 Troubleshooting

### Common Issues

1. **Extension Not Loading**
   - Check Chrome version (88+ required)
   - Verify all files are present
   - Check console for errors

2. **OTP Not Working**
   - Verify website URL is allowed
   - Check extension permissions
   - Ensure background script is running

3. **Notifications Not Showing**
   - Check notification permissions
   - Verify Chrome notification settings
   - Check popup blocker settings

### Debug Information

1. **Extension Console**
   ```javascript
   // In popup or background console
   chrome.runtime.getManifest()
   chrome.storage.local.get(null)
   ```

2. **Network Issues**
   - Check CORS settings
   - Verify SSL certificates
   - Test with different networks

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📞 Support

- **Documentation**: [https://progressdashboard.com/docs/chrome-extension](https://progressdashboard.com/docs/chrome-extension)
- **Issues**: [GitHub Issues](https://github.com/progressdashboard/chrome-extension/issues)
- **Email**: <EMAIL>
- **Discord**: [Progress Dashboard Community](https://discord.gg/progressdashboard)

## 🎯 Roadmap

- [ ] Firefox WebExtension support
- [ ] Biometric authentication
- [ ] Multi-language support
- [ ] Advanced security features
- [ ] Integration with password managers
- [ ] Mobile companion app

---

**Made with ❤️ by the Progress Dashboard Team**
