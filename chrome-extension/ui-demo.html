<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Progress Dashboard Extension - UI Demo</title>
  
  <!-- Styles -->
  <link rel="stylesheet" href="styles/design-tokens.css">
  <link rel="stylesheet" href="styles/animations.css">
  <link rel="stylesheet" href="styles/components.css">
  <link rel="stylesheet" href="popup/popup.css">
  
  <!-- Preload fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  
  <style>
    body {
      margin: 0;
      padding: 20px;
      background: #f1f5f9;
      font-family: 'Inter', sans-serif;
    }
    
    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .demo-title {
      text-align: center;
      color: #1A1919;
      margin-bottom: 30px;
    }
    
    .demo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 30px;
      margin-bottom: 40px;
    }
    
    .demo-section {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    }
    
    .demo-section h3 {
      margin: 0 0 15px 0;
      color: #1A1919;
      font-size: 18px;
      font-weight: 600;
    }
    
    .button-demo {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    
    .icon-demo {
      display: flex;
      gap: 15px;
      align-items: center;
      flex-wrap: wrap;
    }
    
    .color-demo {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 10px;
    }
    
    .color-swatch {
      height: 60px;
      border-radius: 8px;
      display: flex;
      align-items: end;
      padding: 8px;
      color: white;
      font-size: 12px;
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <h1 class="demo-title">Progress Dashboard Extension - Updated UI</h1>
    <p style="text-align: center; color: #64748b; margin-bottom: 40px;">
      Clean UI design matching Categories.tsx style - No gradients, solid colors, subtle shadows
    </p>
    
    <div class="demo-grid">
      <!-- Color Palette -->
      <div class="demo-section">
        <h3>Color Palette</h3>
        <div class="color-demo">
          <div class="color-swatch" style="background: #9CEE69; color: #1A1919;">#9CEE69<br>Primary</div>
          <div class="color-swatch" style="background: #1A1919;">#1A1919<br>Text</div>
          <div class="color-swatch" style="background: #22c55e;">#22c55e<br>Success</div>
          <div class="color-swatch" style="background: #ef4444;">#ef4444<br>Error</div>
          <div class="color-swatch" style="background: #64748b;">#64748b<br>Secondary</div>
          <div class="color-swatch" style="background: #f8fafc; color: #1A1919; text-shadow: none;">#f8fafc<br>Background</div>
        </div>
      </div>
      
      <!-- Buttons -->
      <div class="demo-section">
        <h3>Button Styles</h3>
        <div class="button-demo">
          <button class="btn btn-primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Primary Button
          </button>
          <button class="btn btn-secondary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
            </svg>
            Secondary Button
          </button>
          <button class="btn btn-ghost">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Ghost Button
          </button>
          <button class="btn btn-danger">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Danger Button
          </button>
        </div>
      </div>
      
      <!-- Icons -->
      <div class="demo-section">
        <h3>Icon Styles</h3>
        <div class="icon-demo">
          <div class="status-icon" style="width: 40px; height: 40px;">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="success-icon" style="width: 40px; height: 40px;">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="error-icon" style="width: 40px; height: 40px;">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Progress Bar -->
      <div class="demo-section">
        <h3>Progress Bar</h3>
        <div class="progress">
          <div class="progress-bar" style="width: 65%;"></div>
        </div>
        <p style="margin-top: 10px; font-size: 14px; color: #64748b;">65% Complete</p>
      </div>
      
      <!-- Cards -->
      <div class="demo-section">
        <h3>Card Component</h3>
        <div class="card">
          <div class="card-header">
            <h4 style="margin: 0; color: #1A1919;">Card Title</h4>
          </div>
          <div class="card-body">
            <p style="margin: 0; color: #64748b; font-size: 14px;">
              Clean card design with subtle shadows and proper spacing.
            </p>
          </div>
        </div>
      </div>
      
      <!-- Extension Preview -->
      <div class="demo-section" style="grid-column: 1 / -1;">
        <h3>Extension Popup Preview</h3>
        <div style="display: flex; justify-content: center; gap: 30px; flex-wrap: wrap; align-items: flex-start;">
          <!-- Idle State -->
          <div class="app-container state-idle">
            <header class="app-header">
              <div class="header-content">
                <div class="brand">
                  <div class="brand-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                      <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                      <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <div class="brand-text">
                    <h1 class="brand-title">Progress Dashboard</h1>
                    <p class="brand-subtitle">OTP Authenticator</p>
                  </div>
                </div>
                <div class="header-actions">
                  <button class="btn btn-ghost btn-sm">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </button>
                </div>
              </div>
            </header>
            
            <main class="app-main">
              <div class="state-container">
                <div class="idle-content">
                  <div class="status-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </div>
                  <h2 class="status-title">Ready for Authentication</h2>
                </div>
              </div>
            </main>
            
            <footer class="app-footer">
              <div class="footer-content">
                <div class="extension-info">
                  <span class="version">v1.0.0</span>
                  <span class="status">
                    <span class="status-dot"></span>
                    Connected
                  </span>
                </div>
                <div class="footer-links">
                  <button class="btn btn-ghost btn-sm">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                      <path d="M9.09 9a3 3 0 015.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M12 17h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    Help
                  </button>
                </div>
              </div>
            </footer>
          </div>

          <!-- Success State Demo -->
          <div class="app-container state-success">
            <header class="app-header">
              <div class="header-content">
                <div class="brand">
                  <div class="brand-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                      <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                      <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <div class="brand-text">
                    <h1 class="brand-title">Progress Dashboard</h1>
                    <p class="brand-subtitle">OTP Authenticator</p>
                  </div>
                </div>
              </div>
            </header>

            <main class="app-main">
              <div class="state-container">
                <div class="success-content">
                  <div class="success-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </div>
                  <h2 class="success-title">Login Approved!</h2>
                  <p class="success-description">
                    You have successfully approved the login request. You can now close this popup.
                  </p>
                </div>
              </div>
            </main>

            <footer class="app-footer">
              <div class="footer-content">
                <div class="extension-info">
                  <span class="version">v1.0.0</span>
                  <span class="status">
                    <span class="status-dot"></span>
                    Connected
                  </span>
                </div>
              </div>
            </footer>
          </div>
        </div>

        <!-- Dynamic Height Demo Controls -->
        <div style="text-align: center; margin-top: 30px;">
          <h4 style="color: #1A1919; margin-bottom: 15px;">Dynamic Height Demo</h4>
          <div style="display: flex; justify-content: center; gap: 10px; flex-wrap: wrap;">
            <button class="btn btn-secondary btn-sm" onclick="demoState('idle')">Idle State</button>
            <button class="btn btn-secondary btn-sm" onclick="demoState('loading')">Loading State</button>
            <button class="btn btn-secondary btn-sm" onclick="demoState('active')">Active State</button>
            <button class="btn btn-secondary btn-sm" onclick="demoState('success')">Success State</button>
            <button class="btn btn-secondary btn-sm" onclick="demoState('error')">Error State</button>
          </div>
        </div>
      </div>
    </div>
    
    <div style="text-align: center; color: #64748b; font-size: 14px;">
      <p><strong>Key Changes:</strong></p>
      <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
        <li>Removed all gradient backgrounds</li>
        <li>Used solid colors matching Categories.tsx (#9CEE69, #1A1919)</li>
        <li>Clean white backgrounds with subtle borders</li>
        <li>Fixed header contrast - light gray background (#f8fafc)</li>
        <li>Simplified idle state - removed long text and test button</li>
        <li>Consistent shadow system (shadow-sm, shadow-lg)</li>
        <li>Proper spacing and typography</li>
        <li>Clean button styles without gradients</li>
        <li>Consistent icon styling</li>
      </ul>
    </div>
  </div>

  <script>
    // Demo function to show different states
    function demoState(state) {
      const container = document.querySelector('.app-container.state-idle');
      if (!container) return;

      // Remove all state classes
      container.classList.remove('state-idle', 'state-loading', 'state-active', 'state-success', 'state-error');

      // Add new state class
      container.classList.add(`state-${state}`);

      // Update content based on state
      const mainContent = container.querySelector('.app-main');

      switch(state) {
        case 'idle':
          mainContent.innerHTML = `
            <div class="state-container">
              <div class="idle-content">
                <div class="status-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <h2 class="status-title">Ready for Authentication</h2>
              </div>
            </div>
          `;
          break;
        case 'loading':
          mainContent.innerHTML = `
            <div class="state-container">
              <div class="loading-content">
                <h2 class="loading-text">Connecting...</h2>
              </div>
            </div>
          `;
          break;
        case 'success':
          mainContent.innerHTML = `
            <div class="state-container">
              <div class="success-content">
                <div class="success-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <h2 class="success-title">Login Approved!</h2>
                <p class="success-description">You have successfully approved the login request.</p>
              </div>
            </div>
          `;
          break;
        case 'error':
          mainContent.innerHTML = `
            <div class="state-container">
              <div class="error-content">
                <div class="error-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <h2 class="error-title">Connection Error</h2>
                <p class="error-description">Unable to connect to Progress Dashboard. Please try again.</p>
              </div>
            </div>
          `;
          break;
        case 'active':
          mainContent.innerHTML = `
            <div class="state-container">
              <div class="request-content">
                <div class="request-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <h2 class="request-title">Authentication Request</h2>
                <div class="request-details">
                  <div class="detail-item">
                    <span class="detail-label">Email:</span>
                    <span class="detail-value"><EMAIL></span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Website:</span>
                    <span class="detail-value">localhost:5174</span>
                  </div>
                </div>
                <div class="request-actions">
                  <button class="btn btn-primary btn-full">Approve</button>
                  <button class="btn btn-danger btn-full">Reject</button>
                </div>
              </div>
            </div>
          `;
          break;
      }
    }
  </script>
</body>
</html>
