<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dynamic Height Demo - Progress Dashboard Extension</title>
  
  <!-- Styles -->
  <link rel="stylesheet" href="styles/design-tokens.css">
  <link rel="stylesheet" href="styles/animations.css">
  <link rel="stylesheet" href="styles/components.css">
  <link rel="stylesheet" href="popup/popup.css">
  
  <!-- Preload fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  
  <style>
    body {
      margin: 0;
      padding: 40px 20px;
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      font-family: 'Inter', sans-serif;
      min-height: 100vh;
    }
    
    .demo-container {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
    }
    
    .demo-title {
      color: #1A1919;
      margin-bottom: 10px;
      font-size: 2rem;
      font-weight: 700;
    }
    
    .demo-subtitle {
      color: #64748b;
      margin-bottom: 40px;
      font-size: 1.1rem;
    }
    
    .controls {
      background: white;
      border-radius: 16px;
      padding: 30px;
      margin-bottom: 40px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    }
    
    .controls h3 {
      margin: 0 0 20px 0;
      color: #1A1919;
      font-size: 1.2rem;
    }
    
    .button-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 12px;
      margin-bottom: 20px;
    }
    
    .demo-extension {
      display: flex;
      justify-content: center;
      align-items: flex-start;
      min-height: 600px;
      position: relative;
    }
    
    .height-indicator {
      position: absolute;
      left: -60px;
      top: 0;
      bottom: 0;
      width: 40px;
      background: rgba(156, 238, 105, 0.2);
      border: 2px solid #9CEE69;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      color: #1A1919;
      writing-mode: vertical-rl;
      text-orientation: mixed;
    }
    
    .current-state {
      background: #f0fdf4;
      border: 1px solid #9CEE69;
      border-radius: 8px;
      padding: 8px 16px;
      color: #1A1919;
      font-weight: 500;
      font-size: 14px;
    }
    
    .animation-info {
      background: #f8fafc;
      border-radius: 12px;
      padding: 20px;
      margin-top: 30px;
      text-align: left;
    }
    
    .animation-info h4 {
      margin: 0 0 10px 0;
      color: #1A1919;
    }
    
    .animation-info ul {
      margin: 0;
      padding-left: 20px;
      color: #64748b;
    }
    
    .animation-info li {
      margin-bottom: 5px;
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <h1 class="demo-title">Dynamic Height Extension</h1>
    <p class="demo-subtitle">
      Watch how the extension smoothly adapts its height based on content state
    </p>
    
    <div class="controls">
      <h3>Extension States</h3>
      <div class="button-grid">
        <button class="btn btn-secondary" onclick="changeState('idle')">
          Idle State
        </button>
        <button class="btn btn-secondary" onclick="changeState('loading')">
          Loading State
        </button>
        <button class="btn btn-primary" onclick="changeState('active')">
          Active Request
        </button>
        <button class="btn" style="background: #22c55e; color: white;" onclick="changeState('success')">
          Success State
        </button>
        <button class="btn btn-danger" onclick="changeState('error')">
          Error State
        </button>
      </div>
      
      <div class="current-state" id="currentState">
        Current State: Idle (200px height)
      </div>
    </div>
    
    <div class="demo-extension">
      <div class="height-indicator" id="heightIndicator">200px</div>
      
      <!-- Extension Container -->
      <div class="app-container state-idle" id="extensionContainer">
        <header class="app-header">
          <div class="header-content">
            <div class="brand">
              <div class="brand-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                  <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                  <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="brand-text">
                <h1 class="brand-title">Progress Dashboard</h1>
                <p class="brand-subtitle">OTP Authenticator</p>
              </div>
            </div>
            <div class="header-actions">
              <button class="btn btn-ghost btn-sm">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
          </div>
        </header>
        
        <main class="app-main" id="mainContent">
          <div class="state-container">
            <div class="idle-content">
              <div class="status-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <h2 class="status-title">Ready for Authentication</h2>
            </div>
          </div>
        </main>
        
        <footer class="app-footer">
          <div class="footer-content">
            <div class="extension-info">
              <span class="version">v1.0.0</span>
              <span class="status">
                <span class="status-dot"></span>
                Connected
              </span>
            </div>
          </div>
        </footer>
      </div>
    </div>
    
    <div class="animation-info">
      <h4>Animation Details</h4>
      <ul>
        <li><strong>Duration:</strong> 0.4 seconds</li>
        <li><strong>Easing:</strong> cubic-bezier(0.4, 0, 0.2, 1) (Material Design)</li>
        <li><strong>Properties:</strong> Height, padding, opacity, transform</li>
        <li><strong>Performance:</strong> GPU-accelerated for smooth 60fps animation</li>
        <li><strong>Responsive:</strong> Adapts content size and spacing automatically</li>
      </ul>
    </div>
  </div>
  
  <script>
    const stateHeights = {
      idle: '200px',
      loading: '250px', 
      active: '480px',
      success: '300px',
      error: '350px'
    };
    
    const stateContents = {
      idle: `
        <div class="state-container">
          <div class="idle-content">
            <div class="status-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h2 class="status-title">Ready for Authentication</h2>
          </div>
        </div>
      `,
      loading: `
        <div class="state-container">
          <div class="loading-content">
            <div class="loading-spinner">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 12a9 9 0 11-6.219-8.56" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <h2 class="loading-text">Connecting to Dashboard...</h2>
          </div>
        </div>
      `,
      active: `
        <div class="state-container">
          <div class="request-content">
            <div class="request-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h2 class="request-title">Authentication Request</h2>
            <div class="request-details">
              <div class="detail-item">
                <span class="detail-label">Email:</span>
                <span class="detail-value"><EMAIL></span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Website:</span>
                <span class="detail-value">localhost:5174</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Time:</span>
                <span class="detail-value">${new Date().toLocaleTimeString()}</span>
              </div>
            </div>
            <div class="request-actions">
              <button class="btn btn-primary btn-full">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Approve Login
              </button>
              <button class="btn btn-danger btn-full">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Reject Request
              </button>
            </div>
          </div>
        </div>
      `,
      success: `
        <div class="state-container">
          <div class="success-content">
            <div class="success-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h2 class="success-title">Login Approved!</h2>
            <p class="success-description">
              You have successfully approved the login request. The user can now access Progress Dashboard.
            </p>
          </div>
        </div>
      `,
      error: `
        <div class="state-container">
          <div class="error-content">
            <div class="error-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h2 class="error-title">Connection Error</h2>
            <p class="error-description">
              Unable to connect to Progress Dashboard. Please check your internet connection and try again.
            </p>
            <div class="error-actions">
              <button class="btn btn-primary btn-full">Try Again</button>
            </div>
          </div>
        </div>
      `
    };
    
    function changeState(state) {
      const container = document.getElementById('extensionContainer');
      const mainContent = document.getElementById('mainContent');
      const heightIndicator = document.getElementById('heightIndicator');
      const currentState = document.getElementById('currentState');
      
      // Remove all state classes
      container.classList.remove('state-idle', 'state-loading', 'state-active', 'state-success', 'state-error');
      
      // Add new state class
      container.classList.add(`state-${state}`);
      
      // Update content
      mainContent.innerHTML = stateContents[state];
      
      // Update indicators
      heightIndicator.textContent = stateHeights[state];
      currentState.textContent = `Current State: ${state.charAt(0).toUpperCase() + state.slice(1)} (${stateHeights[state]} height)`;
      
      // Add animation class
      container.classList.add('animate-height-change');
      setTimeout(() => {
        container.classList.remove('animate-height-change');
      }, 400);
    }
  </script>
</body>
</html>
