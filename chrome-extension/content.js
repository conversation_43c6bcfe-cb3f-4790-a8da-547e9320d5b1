/**
 * Content Script for Progress Dashboard Chrome Extension
 * 
 * Facilitates communication between web pages and the extension
 */

// Content script state
let contentState = {
  isInjected: false,
  messageQueue: [],
  extensionReady: false
};

// Initialize content script
function initializeContentScript() {
  if (contentState.isInjected) return;
  
  console.log('🔗 Progress Dashboard Extension content script initializing...');
  
  // Inject communication bridge
  injectCommunicationBridge();
  
  // Set up message listeners
  setupMessageListeners();
  
  // Notify extension readiness
  notifyExtensionReady();
  
  contentState.isInjected = true;
  console.log('✅ Content script initialized');
}

// Inject communication bridge into page
function injectCommunicationBridge() {
  // Create script element to inject into page context
  const script = document.createElement('script');
  script.textContent = `
    (function() {
      // Page-level communication bridge
      window.progressDashboardExtension = {
        isAvailable: true,
        version: '1.0.0',
        
        // Send message to extension
        sendMessage: function(message) {
          return new Promise((resolve, reject) => {
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            // Add message ID for response tracking
            const messageWithId = {
              ...message,
              messageId: messageId,
              timestamp: Date.now()
            };
            
            // Set up response listener
            const responseListener = (event) => {
              if (event.data && event.data.type === 'EXTENSION_RESPONSE' && event.data.messageId === messageId) {
                window.removeEventListener('message', responseListener);
                
                if (event.data.success) {
                  resolve(event.data.data);
                } else {
                  reject(new Error(event.data.error || 'Extension error'));
                }
              }
            };
            
            window.addEventListener('message', responseListener);
            
            // Send message to content script
            window.postMessage({
              type: 'TO_EXTENSION',
              data: messageWithId
            }, window.location.origin);
            
            // Set timeout
            setTimeout(() => {
              window.removeEventListener('message', responseListener);
              reject(new Error('Extension response timeout'));
            }, 30000); // 30 second timeout
          });
        },
        
        // Listen for extension messages
        onMessage: function(callback) {
          const listener = (event) => {
            if (event.data && event.data.type && event.data.type.startsWith('OTP_')) {
              callback(event.data);
            }
          };
          
          window.addEventListener('message', listener);
          
          // Return cleanup function
          return () => {
            window.removeEventListener('message', listener);
          };
        }
      };
      
      // Notify that extension bridge is ready
      window.dispatchEvent(new CustomEvent('progressDashboardExtensionReady', {
        detail: { version: '1.0.0' }
      }));
      
      console.log('🔗 Progress Dashboard Extension bridge injected');
    })();
  `;
  
  // Inject at document start
  (document.head || document.documentElement).appendChild(script);
  script.remove();
}

// Set up message listeners
function setupMessageListeners() {
  // Listen for messages from page
  window.addEventListener('message', handlePageMessage);
  
  // Listen for messages from extension background
  chrome.runtime.onMessage.addListener(handleExtensionMessage);
}

// Handle messages from page
function handlePageMessage(event) {
  // Only accept messages from same origin
  if (event.origin !== window.location.origin) {
    return;
  }
  
  const message = event.data;
  
  if (message.type === 'TO_EXTENSION') {
    forwardToExtension(message.data);
  }
}

// Handle messages from extension background
function handleExtensionMessage(message, sender, sendResponse) {
  console.log('📨 Content script received from background:', message);
  
  switch (message.type) {
    case 'FORWARD_TO_PAGE':
      forwardToPage(message.data);
      break;
      
    case 'EXTENSION_STATUS_REQUEST':
      sendResponse({
        success: true,
        data: {
          isInjected: contentState.isInjected,
          url: window.location.href,
          ready: contentState.extensionReady
        }
      });
      break;
      
    default:
      console.warn('Unknown message type from background:', message.type);
  }
}

// Forward message to extension background
async function forwardToExtension(message) {
  try {
    console.log('📤 Forwarding to extension:', message);
    
    const response = await chrome.runtime.sendMessage(message);
    
    // Send response back to page
    window.postMessage({
      type: 'EXTENSION_RESPONSE',
      messageId: message.messageId,
      success: response?.success !== false,
      data: response?.data,
      error: response?.error,
      timestamp: Date.now()
    }, window.location.origin);
    
  } catch (error) {
    console.error('Error forwarding to extension:', error);
    
    // Send error response back to page
    window.postMessage({
      type: 'EXTENSION_RESPONSE',
      messageId: message.messageId,
      success: false,
      error: error.message || 'Extension communication error',
      timestamp: Date.now()
    }, window.location.origin);
  }
}

// Forward message to page
function forwardToPage(message) {
  console.log('📤 Forwarding to page:', message);
  
  window.postMessage(message, window.location.origin);
}

// Notify extension readiness
function notifyExtensionReady() {
  // Send ready signal to background
  chrome.runtime.sendMessage({
    type: 'CONTENT_SCRIPT_READY',
    data: {
      url: window.location.href,
      timestamp: Date.now()
    }
  }).catch(error => {
    console.warn('Failed to notify extension readiness:', error);
  });
  
  contentState.extensionReady = true;
}

// Check if we're on a valid page
function isValidPage() {
  const validHosts = [
    'localhost',
    'progressdashboard.com',
    'app.progressdashboard.com',
    'dashboard.progressdashboard.com'
  ];
  
  return validHosts.some(host => 
    window.location.hostname === host || 
    window.location.hostname.endsWith('.' + host)
  );
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    if (isValidPage()) {
      initializeContentScript();
    }
  });
} else {
  if (isValidPage()) {
    initializeContentScript();
  }
}

// Also initialize immediately for dynamic content
if (isValidPage()) {
  initializeContentScript();
}

// Handle page navigation (for SPAs)
let lastUrl = window.location.href;
const observer = new MutationObserver(() => {
  if (window.location.href !== lastUrl) {
    lastUrl = window.location.href;
    
    if (isValidPage() && !contentState.isInjected) {
      initializeContentScript();
    }
  }
});

observer.observe(document.body, {
  childList: true,
  subtree: true
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  observer.disconnect();
  contentState.isInjected = false;
  contentState.extensionReady = false;
});

console.log('🔗 Progress Dashboard Extension content script loaded');
