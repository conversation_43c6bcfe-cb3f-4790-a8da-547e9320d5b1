/**
 * Popup JavaScript for Progress Dashboard Chrome Extension
 * 
 * Handles popup UI interactions and state management
 */

// Popup state
let popupState = {
  currentState: 'loading',
  currentRequest: null,
  timer: null,
  settings: {
    autoApprove: false,
    notificationSound: true
  }
};

// DOM elements
const elements = {
  // States
  loadingState: document.getElementById('loadingState'),
  idleState: document.getElementById('idleState'),
  activeRequestState: document.getElementById('activeRequestState'),
  successState: document.getElementById('successState'),
  rejectState: document.getElementById('rejectState'),
  errorState: document.getElementById('errorState'),

  // Connection badge
  connectionBadge: document.getElementById('connectionBadge'),
  connectionDot: document.getElementById('connectionDot'),

  // Request details
  requestEmail: document.getElementById('requestEmail'),
  requestWebsite: document.getElementById('requestWebsite'),
  requestTimer: document.getElementById('requestTimer'),
  requestProgress: document.getElementById('requestProgress'),

  // Buttons
  approveBtn: document.getElementById('approveBtn'),
  rejectBtn: document.getElementById('rejectBtn'),
  testConnectionBtn: document.getElementById('testConnectionBtn'),
  retryBtn: document.getElementById('retryBtn'),
  settingsBtn: document.getElementById('settingsBtn'),
  helpBtn: document.getElementById('helpBtn'),

  // Settings modal
  settingsModal: document.getElementById('settingsModal'),
  closeSettingsBtn: document.getElementById('closeSettingsBtn'),
  saveSettingsBtn: document.getElementById('saveSettingsBtn'),
  autoApproveToggle: document.getElementById('autoApproveToggle'),
  notificationSoundToggle: document.getElementById('notificationSoundToggle'),

  // Status
  connectionStatus: document.getElementById('connectionStatus'),
  connectionStatusText: document.getElementById('connectionStatusText'),
  errorMessage: document.getElementById('errorMessage'),

  // Timers
  successTimer: document.getElementById('successTimer'),
  rejectTimer: document.getElementById('rejectTimer')
};

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🎨 Popup initializing...');
  
  // Set up event listeners
  setupEventListeners();
  
  // Load settings
  await loadSettings();
  
  // Check extension status
  await checkExtensionStatus();
  
  console.log('✅ Popup initialized');
});

// Set up event listeners
function setupEventListeners() {
  // Action buttons
  elements.approveBtn?.addEventListener('click', handleApprove);
  elements.rejectBtn?.addEventListener('click', handleReject);
  elements.testConnectionBtn?.addEventListener('click', handleTestConnection);
  elements.retryBtn?.addEventListener('click', handleRetry);
  
  // Settings
  elements.settingsBtn?.addEventListener('click', openSettings);
  elements.closeSettingsBtn?.addEventListener('click', closeSettings);
  elements.saveSettingsBtn?.addEventListener('click', saveSettings);
  
  // Help
  elements.helpBtn?.addEventListener('click', openHelp);
  
  // Modal overlay click
  elements.settingsModal?.addEventListener('click', (e) => {
    if (e.target === elements.settingsModal) {
      closeSettings();
    }
  });
  
  // Listen for background messages
  chrome.runtime.onMessage.addListener(handleBackgroundMessage);
}

// Check extension status
async function checkExtensionStatus() {
  try {
    console.log('[POPUP] Checking extension status...');
    showState('loading');

    const response = await chrome.runtime.sendMessage({
      type: 'GET_EXTENSION_STATUS'
    });

    console.log('[POPUP] Extension status response:', response);

    if (response && response.success) {
      const { isActive, hasActiveRequest, currentOTPRequest, popupState, backendConnected } = response.data;

      console.log('[POPUP] Status check result:', {
        isActive,
        hasActiveRequest,
        hasCurrentRequest: !!currentOTPRequest,
        hasPopupState: !!popupState,
        backendConnected
      });

      // Update connection badge with real backend status
      updateConnectionBadge(backendConnected);

      // Check if there's a stored popup state (Manifest V3 compatibility)
      if (popupState && popupState.state === 'active' && popupState.data) {
        console.log('[POPUP] Found stored popup state, applying it');
        window.updateState(popupState.state, popupState.data);
      } else if (hasActiveRequest && currentOTPRequest) {
        console.log('[POPUP] Found active request, switching to active state');
        window.updateState('active', currentOTPRequest);
      } else {
        console.log('[POPUP] No active request, switching to idle state');
        showState('idle');
      }

      updateConnectionStatus(true);
    } else {
      throw new Error((response && response.error) || 'Failed to get extension status');
    }
  } catch (error) {
    console.error('Failed to check extension status:', error);
    showError('Failed to connect to extension background');
    updateConnectionStatus(false);
  }
}

// Get current request details
async function getCurrentRequest() {
  try {
    // This would typically come from background script
    // For now, we'll simulate or get from storage
    const request = popupState.currentRequest;
    
    if (request) {
      showActiveRequest(request);
    } else {
      showState('idle');
    }
  } catch (error) {
    console.error('Failed to get current request:', error);
    showState('idle');
  }
}

// Handle background messages
function handleBackgroundMessage(message, sender, sendResponse) {
  console.log('📨 Popup received message:', message);
  
  switch (message.type) {
    case 'OTP_REQUEST_RECEIVED':
      showActiveRequest(message.data);
      break;
      
    case 'OTP_REQUEST_COMPLETED':
      if (message.data.approved) {
        showState('success');
        setTimeout(() => {
          showState('idle');
        }, 3000);
      } else {
        showState('idle');
      }
      break;
      
    case 'OTP_REQUEST_EXPIRED':
      showState('idle');
      break;
      
    default:
      console.warn('Unknown message type:', message.type);
  }
}

// Update connection badge based on real backend connection
function updateConnectionBadge(backendConnected) {
  const badge = document.getElementById('connectionBadge');
  const dot = document.getElementById('connectionDot');

  if (!badge || !dot) return;

  // Remove all classes
  badge.classList.remove('connected', 'not-connected', 'success');
  dot.classList.remove('connected', 'not-connected', 'success');

  if (backendConnected) {
    badge.classList.add('connected');
    dot.classList.add('connected');
    console.log('[POPUP] Badge updated: Connected to backend');
  } else {
    badge.classList.add('not-connected');
    dot.classList.add('not-connected');
    console.log('[POPUP] Badge updated: Not connected to backend');
  }

  // Update connection status text in idle state
  const statusText = document.getElementById('connectionStatusText');
  if (statusText) {
    statusText.textContent = backendConnected ? 'Ready for authentication' : 'Connecting...';
  }
}

// Check backend connection manually
async function checkBackendConnection() {
  try {
    const response = await chrome.runtime.sendMessage({
      action: 'CHECK_BACKEND_CONNECTION'
    });

    if (response && response.success) {
      updateConnectionBadge(response.data.backendConnected);
      console.log('[POPUP] Backend connection check result:', response.data);
      return response.data.backendConnected;
    }
  } catch (error) {
    console.error('[POPUP] Failed to check backend connection:', error);
    updateConnectionBadge(false);
    return false;
  }
}

// Update popup size based on state
function updatePopupSize(state) {
  const stateMinHeights = {
    'idle': 80,
    'loading': 80,
    'activeRequest': 420,
    'success': 150,
    'reject': 150,
    'error': 170,
    'settings': 200
  };

  const targetMinHeight = stateMinHeights[state] || 80;

  // Use min-height instead of fixed height for flexibility
  const appContainer = document.querySelector('.app-container');
  if (appContainer) {
    // Remove fixed height constraints
    document.body.style.height = 'auto';
    document.documentElement.style.height = 'auto';
    appContainer.style.height = 'auto';

    // Set min-height for proper sizing
    appContainer.style.minHeight = `${targetMinHeight}px`;

    // Remove all state classes
    appContainer.classList.remove('state-idle', 'state-loading', 'state-active', 'state-success', 'state-reject', 'state-error');

    // Add current state class
    if (state === 'activeRequest') {
      appContainer.classList.add('state-active');
    } else {
      appContainer.classList.add(`state-${state}`);
    }
  }

  console.log(`[POPUP] Updated popup min-height to ${targetMinHeight}px for state: ${state}`);
}

// Show different states with dynamic height animation
function showState(state) {
  console.log('[POPUP] showState called with:', state);
  console.log('[POPUP] Current state before change:', popupState.currentState);

  const appContainer = document.querySelector('.app-container');
  const body = document.body;
  const html = document.documentElement;

  // Remove all state classes from app container
  appContainer.classList.remove('state-idle', 'state-loading', 'state-active', 'state-success', 'state-reject', 'state-error', 'state-settings');

  // Add new state class for dynamic height
  const stateClassMap = {
    'idle': 'state-idle',
    'loading': 'state-loading',
    'activeRequest': 'state-active',
    'success': 'state-success',
    'reject': 'state-reject',
    'error': 'state-error',
    'settings': 'state-settings'
  };

  if (stateClassMap[state]) {
    appContainer.classList.add(stateClassMap[state]);

    // Also add state class to body for additional styling control
    body.classList.remove('state-idle', 'state-loading', 'state-active', 'state-success', 'state-reject', 'state-error', 'state-settings');
    body.classList.add(stateClassMap[state]);

    // Force resize after state change
    setTimeout(() => {
      updatePopupSize(state);
    }, 100);
  }

  // Update connection badge based on real backend connection
  // Don't assume connection based on state, check actual backend
  checkBackendConnection();

  // Hide all state containers
  const stateContainers = document.querySelectorAll('.state-container');
  stateContainers.forEach(container => {
    container.classList.add('hidden');
  });

  // Show target state container
  let targetElementId;
  if (state === 'activeRequest') {
    targetElementId = 'activeRequestState';
  } else {
    targetElementId = `${state}State`;
  }

  const targetElement = document.getElementById(targetElementId);
  console.log('[POPUP] Target element for', targetElementId, ':', !!targetElement);

  if (targetElement) {
    // Small delay to ensure smooth transition
    setTimeout(() => {
      targetElement.classList.remove('hidden');
      targetElement.classList.add('animate-fade-in');
      console.log('[POPUP] Showed target element, classes:', targetElement.className);
    }, 50);
  } else {
    console.error('[POPUP] Target element not found for state:', state);
  }

  popupState.currentState = state;
  console.log('[POPUP] Updated current state to:', popupState.currentState);

  // Clear timer if switching away from active request
  if (state !== 'activeRequest' && popupState.timer) {
    clearInterval(popupState.timer);
    popupState.timer = null;
    console.log('[POPUP] Cleared timer');
  }
}

// Show active request
function showActiveRequest(request) {
  console.log('[POPUP] showActiveRequest called with:', request);
  console.log('[POPUP] Elements check:', {
    requestEmail: !!elements.requestEmail,
    requestWebsite: !!elements.requestWebsite,
    activeRequestState: !!elements.activeRequestState
  });

  popupState.currentRequest = request;

  // Update request details
  if (elements.requestEmail) {
    elements.requestEmail.textContent = request.email || 'Unknown';
    console.log('[POPUP] Updated email to:', request.email);
  } else {
    console.log('[POPUP] requestEmail element not found');
  }

  if (elements.requestWebsite) {
    elements.requestWebsite.textContent = request.website || 'Unknown';
    console.log('[POPUP] Updated website to:', request.website);
  } else {
    console.log('[POPUP] requestWebsite element not found');
  }

  // Start timer
  console.log('[POPUP] Starting timer with duration:', request.expires_in || 300);
  startRequestTimer(request.expires_in || 300);

  console.log('[POPUP] Calling showState(activeRequest)');
  showState('activeRequest');
  console.log('[POPUP] showActiveRequest completed');
}

// Start request timer
function startRequestTimer(duration) {
  let timeLeft = duration;
  
  const updateTimer = () => {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    
    if (elements.requestTimer) {
      elements.requestTimer.textContent = timeString;
    }
    
    // Update progress bar
    const progress = ((duration - timeLeft) / duration) * 100;
    if (elements.requestProgress) {
      elements.requestProgress.style.width = `${progress}%`;
    }
    
    timeLeft--;
    
    if (timeLeft < 0) {
      clearInterval(popupState.timer);
      popupState.timer = null;
      showState('idle');
    }
  };
  
  updateTimer();
  popupState.timer = setInterval(updateTimer, 1000);
}

// Handle approve
async function handleApprove() {
  try {
    if (!popupState.currentRequest) return;

    // Add loading state to button
    const approveBtn = document.getElementById('approveBtn');
    if (approveBtn) {
      approveBtn.classList.add('loading');
      approveBtn.disabled = true;
      approveBtn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">Processing...</span>';
    }

    const response = await chrome.runtime.sendMessage({
      type: 'OTP_RESPONSE',
      data: {
        requestId: popupState.currentRequest.id,
        action: 'APPROVE'
      }
    });

    if (response && response.success) {
      // Show success notification with timer
      showSuccessNotification();
    } else {
      throw new Error((response && response.error) || 'Failed to approve request');
    }
  } catch (error) {
    console.error('Failed to approve request:', error);
    showError('Failed to approve request');
  } finally {
    const approveBtn = document.getElementById('approveBtn');
    if (approveBtn) {
      approveBtn.classList.remove('loading');
      approveBtn.disabled = false;
      approveBtn.innerHTML = '<span class="btn-icon">✓</span><span class="btn-text">Approve Login</span>';
    }
  }
}

// Handle reject
async function handleReject() {
  try {
    if (!popupState.currentRequest) return;

    // Add loading state to button
    const rejectBtn = document.getElementById('rejectBtn');
    if (rejectBtn) {
      rejectBtn.classList.add('loading');
      rejectBtn.disabled = true;
      rejectBtn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">Processing...</span>';
    }

    const response = await chrome.runtime.sendMessage({
      type: 'OTP_RESPONSE',
      data: {
        requestId: popupState.currentRequest.id,
        action: 'REJECT'
      }
    });

    if (response && response.success) {
      // Show reject notification with timer
      showRejectNotification();
    } else {
      throw new Error((response && response.error) || 'Failed to reject request');
    }
  } catch (error) {
    console.error('Failed to reject request:', error);
    showError('Failed to reject request');
  } finally {
    const rejectBtn = document.getElementById('rejectBtn');
    if (rejectBtn) {
      rejectBtn.classList.remove('loading');
      rejectBtn.disabled = false;
      rejectBtn.innerHTML = '<span class="btn-icon">✕</span><span class="btn-text">Reject</span>';
    }
  }
}

// Handle test connection
async function handleTestConnection() {
  try {
    console.log('[POPUP] Test connection clicked');
    elements.testConnectionBtn.classList.add('loading');
    elements.testConnectionBtn.disabled = true;

    // First test: Check current background state
    console.log('[POPUP] Testing background state check...');
    const statusResponse = await chrome.runtime.sendMessage({
      type: 'GET_EXTENSION_STATUS'
    });
    console.log('[POPUP] Status response:', statusResponse);

    if (statusResponse && statusResponse.success) {
      const { hasActiveRequest, currentOTPRequest } = statusResponse.data;

      if (hasActiveRequest && currentOTPRequest) {
        console.log('[POPUP] Found active request in background, switching to active state');
        window.updateState('active', currentOTPRequest);
        showNotification('Found active request!', 'success');
      } else {
        console.log('[POPUP] No active request found, testing manual trigger...');

        // Second test: Manually trigger active state
        const testRequest = {
          id: 'test_' + Date.now(),
          email: '<EMAIL>',
          otp: '123456',
          timestamp: Date.now()
        };

        console.log('[POPUP] Calling updateState with test data...');
        window.updateState('active', testRequest);
        showNotification('Manual test triggered - check console', 'success');
      }

      updateConnectionStatus(true);
    } else {
      throw new Error((statusResponse && statusResponse.error) || 'Connection test failed');
    }
  } catch (error) {
    console.error('Connection test failed:', error);
    showNotification('Connection test failed', 'error');
    updateConnectionStatus(false);
  } finally {
    elements.testConnectionBtn.classList.remove('loading');
    elements.testConnectionBtn.disabled = false;
  }
}

// Handle retry
function handleRetry() {
  checkExtensionStatus();
}

// Load settings
async function loadSettings() {
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'GET_SETTINGS'
    });

    if (response && response.success) {
      popupState.settings = response.data;
      updateSettingsUI();
    }
  } catch (error) {
    console.error('Failed to load settings:', error);
  }
}

// Update settings UI
function updateSettingsUI() {
  if (elements.autoApproveToggle) {
    elements.autoApproveToggle.checked = popupState.settings.autoApprove;
  }
  
  if (elements.notificationSoundToggle) {
    elements.notificationSoundToggle.checked = popupState.settings.notificationSound;
  }
}

// Open settings modal
function openSettings() {
  elements.settingsModal?.classList.remove('hidden');
  elements.settingsModal?.classList.add('animate-fade-in');
}

// Close settings modal
function closeSettings() {
  elements.settingsModal?.classList.add('hidden');
  elements.settingsModal?.classList.remove('animate-fade-in');
}

// Save settings
async function saveSettings() {
  try {
    const newSettings = {
      autoApprove: elements.autoApproveToggle?.checked || false,
      notificationSound: elements.notificationSoundToggle?.checked || false
    };
    
    const response = await chrome.runtime.sendMessage({
      type: 'UPDATE_SETTINGS',
      data: newSettings
    });

    if (response && response.success) {
      popupState.settings = response.data;
      showNotification('Settings saved successfully!', 'success');
      closeSettings();
    } else {
      throw new Error((response && response.error) || 'Failed to save settings');
    }
  } catch (error) {
    console.error('Failed to save settings:', error);
    showNotification('Failed to save settings', 'error');
  }
}

// Open help
function openHelp() {
  chrome.tabs.create({
    url: 'https://progressdashboard.com/help/chrome-extension'
  });
}

// Show error
function showError(message) {
  if (elements.errorMessage) {
    elements.errorMessage.textContent = message;
  }
  showState('error');
}

// Update connection status
function updateConnectionStatus(connected) {
  if (elements.connectionStatus) {
    if (connected) {
      elements.connectionStatus.classList.remove('disconnected');
      elements.connectionStatus.innerHTML = `
        <span class="status-dot"></span>
        Connected
      `;
    } else {
      elements.connectionStatus.classList.add('disconnected');
      elements.connectionStatus.innerHTML = `
        <span class="status-dot"></span>
        Disconnected
      `;
    }
  }
}

// Show success notification screen
function showSuccessNotification() {
  showState('success');

  // Update connection badge to success state
  const badge = document.getElementById('connectionBadge');
  const dot = document.getElementById('connectionDot');
  if (badge && dot) {
    badge.classList.remove('connected', 'not-connected');
    badge.classList.add('success');
    dot.classList.remove('connected', 'not-connected');
    dot.classList.add('success');
  }

  // Start countdown timer
  let countdown = 3;
  const timerElement = document.getElementById('successTimer');

  const updateTimer = () => {
    if (timerElement) {
      timerElement.textContent = countdown;
    }
    countdown--;

    if (countdown < 0) {
      window.close();
    } else {
      setTimeout(updateTimer, 1000);
    }
  };

  updateTimer();
}

// Show reject notification screen
function showRejectNotification() {
  showState('reject');

  // Start countdown timer
  let countdown = 3;
  const timerElement = document.getElementById('rejectTimer');

  const updateTimer = () => {
    if (timerElement) {
      timerElement.textContent = countdown;
    }
    countdown--;

    if (countdown < 0) {
      window.close();
    } else {
      setTimeout(updateTimer, 1000);
    }
  };

  updateTimer();
}

// Show notification (simple toast)
function showNotification(message, type = 'info') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification notification-${type} animate-slide-in-right`;
  notification.innerHTML = `
    <div class="p-3">
      <p class="text-sm font-medium">${message}</p>
    </div>
  `;

  // Add to body
  document.body.appendChild(notification);

  // Remove after 3 seconds
  setTimeout(() => {
    notification.classList.add('animate-slide-out-right');
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 300);
  }, 3000);
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
  if (popupState.currentState === 'activeRequest') {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleApprove();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleReject();
    }
  }
  
  if (e.key === 'Escape' && !elements.settingsModal?.classList.contains('hidden')) {
    closeSettings();
  }
});

// Global function to receive state updates from background
window.updateState = function(state, data) {
  console.log('[POPUP] updateState called with:', state, data);
  console.log('[POPUP] Current popup state:', popupState.currentState);
  console.log('[POPUP] Elements available:', !!elements.activeRequestState);

  switch (state) {
    case 'active':
      console.log('[POPUP] Switching to active state');
      if (data) {
        // Store the full request data including ID
        popupState.currentRequest = data;
        console.log('[POPUP] Stored request data:', popupState.currentRequest);

        // Format data for showActiveRequest function
        const requestData = {
          id: data.id,
          email: data.email,
          website: 'localhost:5173',
          expires_in: 300 // 5 minutes
        };
        console.log('[POPUP] Calling showActiveRequest with:', requestData);
        showActiveRequest(requestData);
        console.log('[POPUP] showActiveRequest completed');
      } else {
        console.log('[POPUP] No data provided for active state');
      }
      break;
    case 'success':
      console.log('[POPUP] Switching to success state');
      showState('success');
      break;
    case 'error':
      console.log('[POPUP] Switching to error state');
      showState('error');
      if (elements.errorMessage) {
        elements.errorMessage.textContent = data?.error || 'Unknown error';
      }
      break;
    case 'idle':
      console.log('[POPUP] Switching to idle state');
      showState('idle');
      break;
    default:
      console.log('[POPUP] Unknown state:', state);
  }
};

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('[POPUP] Received runtime message:', message);

  if (message.type === 'UPDATE_POPUP_STATE') {
    console.log('[POPUP] Updating popup state via runtime message:', message.state, message.data);
    window.updateState(message.state, message.data);
    sendResponse({ success: true });
  }

  return true;
});

// Note: Popup initialization is handled above in the first DOMContentLoaded listener

console.log('🎨 Popup script loaded');
