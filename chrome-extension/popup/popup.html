<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Progress Dashboard - OTP Authenticator</title>
  
  <!-- Styles -->
  <link rel="stylesheet" href="popup-minimal.css">
  
  <!-- Preload fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Main Container -->
  <div id="app" class="app-container">

    <!-- Connection Status Badge - Top Right Corner -->
    <div class="connection-badge-container">
      <div id="connectionBadge" class="connection-badge-mini not-connected">
        <span id="connectionDot" class="status-dot not-connected"></span>
      </div>
    </div>

    <!-- Main Content -->
    <main class="app-main">

      <!-- Idle State - Clean UI -->
      <div id="idleState" class="state-container">
        <div class="idle-content">
          <div class="idle-message">
            <p id="connectionStatusText">Ready for authentication</p>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div id="loadingState" class="state-container hidden">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p>Loading...</p>
        </div>
      </div>

      <!-- Active Request State - Detailed Information -->
      <div id="activeRequestState" class="state-container hidden">
        <div class="otp-request-card">
          <div class="request-header">
            <div class="request-icon">🔑</div>
            <h3>Authentication Request</h3>
          </div>

          <div class="request-details">
            <div class="detail-item">
              <span class="detail-label">Email:</span>
              <span id="requestEmail" class="detail-value"><EMAIL></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Website:</span>
              <span id="requestWebsite" class="detail-value">localhost:5173</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Expires in:</span>
              <span id="requestTimer" class="detail-value timer">4:59</span>
            </div>
          </div>

          <div class="request-actions">
            <button id="approveBtn" class="action-btn approve">
              <span class="btn-icon">✓</span>
              <span class="btn-text">Approve Login</span>
            </button>
            <button id="rejectBtn" class="action-btn reject">
              <span class="btn-icon">✕</span>
              <span class="btn-text">Reject</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Success Notification State -->
      <div id="successState" class="state-container hidden">
        <div class="notification-card success">
          <div class="notification-icon">✅</div>
          <h3>Login Berhasil!</h3>
          <p>Anda telah berhasil masuk ke sistem.</p>
          <div class="notification-timer">
            <span>Closing in <span id="successTimer">3</span>s</span>
          </div>
        </div>
      </div>

      <!-- Reject Notification State -->
      <div id="rejectState" class="state-container hidden">
        <div class="notification-card reject">
          <div class="notification-icon">❌</div>
          <h3>Login Ditolak</h3>
          <p>Permintaan login telah ditolak.</p>
          <div class="notification-timer">
            <span>Closing in <span id="rejectTimer">3</span>s</span>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div id="errorState" class="state-container hidden">
        <div class="notification-card error">
          <div class="notification-icon">⚠️</div>
          <h3>Error</h3>
          <p id="errorMessage">Something went wrong. Please try again.</p>
          <button id="retryBtn" class="action-btn retry">
            <span class="btn-text">Retry</span>
          </button>
        </div>
      </div>

    </main>

    <!-- Footer -->
    <footer class="app-footer">
      <div class="footer-content">
        <span class="version">v1.0.0</span>
        <span class="author">Built by Hellozai</span>
      </div>
    </footer>

  </div>

  <!-- Hidden elements for functionality -->
  <div id="settingsModal" class="hidden"></div>
  <button id="settingsBtn" class="hidden"></button>
  <button id="closeSettingsBtn" class="hidden"></button>
  <button id="saveSettingsBtn" class="hidden"></button>
  <input id="autoApproveToggle" class="hidden" type="checkbox">
  <input id="notificationSoundToggle" class="hidden" type="checkbox">
  <button id="helpBtn" class="hidden"></button>
  <span id="connectionStatus" class="hidden"></span>
  <div id="requestProgress" class="hidden"></div>

  <!-- Scripts -->
  <script src="popup.js"></script>
</body>
</html>
