/**
 * Minimal Popup Styles for Progress Dashboard Chrome Extension
 */

/* Base Reset */
html, body {
  margin: 0;
  padding: 0;
  width: 280px;
  height: auto;
  min-height: 120px;
  max-height: 450px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  background: #ffffff;
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: border-box;
}

/* App Container */
.app-container {
  width: 280px;
  height: auto;
  min-height: 120px;
  max-height: 450px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: height 0.3s ease;
}

/* State Heights - Clean design calculations */
.app-container.state-idle { min-height: 80px; }
.app-container.state-loading { min-height: 80px; }
.app-container.state-active { min-height: 420px; }
.app-container.state-success { min-height: 150px; }
.app-container.state-reject { min-height: 150px; }
.app-container.state-error { min-height: 170px; }

/* Main Content */
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  gap: 8px;
  min-height: 60px;
  position: relative;
}

/* State Container */
.state-container {
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.state-container.hidden {
  display: none;
}

/* Simple Message Styles */
.simple-message {
  text-align: center;
  padding: 20px;
  color: #333;
}

.simple-message h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1A1919;
}

.simple-message p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

/* Connection Badge Container - Top Right Corner */
.connection-badge-container {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

/* Connection Badge Mini - Clean indicator */
.connection-badge-mini {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 3px solid;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px 0 rgb(0 0 0 / 0.15);
}

.connection-badge-mini.connected {
  background: #22c55e;
  border-color: #16a34a;
}

.connection-badge-mini.not-connected {
  background: #6b7280;
  border-color: #4b5563;
}

.connection-badge-mini.success {
  background: #22c55e;
  border-color: #16a34a;
  animation: pulse-success 1s ease-in-out;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.connected {
  background: #22c55e;
}

.status-dot.not-connected {
  background: #6b7280;
}

.status-dot.success {
  background: #22c55e;
}

/* Request Info Styling */
.request-info {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
  backdrop-filter: blur(4px);
}

.request-info p {
  margin: 4px 0;
  font-size: 12px;
  text-align: left;
  color: #374151;
}

.request-info strong {
  color: #1A1919;
  font-weight: 500;
}

/* Button Actions - Style from Categories.tsx */
.simple-actions {
  display: flex;
  gap: 8px;
  margin-top: 15px;
  justify-content: center;
}

/* Button Styles matching Categories.tsx */
.simple-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  border: 1px solid;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  min-height: auto;
  height: auto;
  line-height: 1;
}

.simple-btn.approve {
  background: #9CEE69;
  color: #1A1919;
  border-color: #9CEE69;
}

.simple-btn.approve:hover {
  background: rgba(156, 238, 105, 0.8);
  transform: translateY(-1px);
}

.simple-btn.reject {
  background: #f8fafc;
  color: #374151;
  border-color: #e2e8f0;
}

.simple-btn.reject:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.simple-btn.retry {
  background: #9CEE69;
  color: #1A1919;
  border-color: #9CEE69;
}

.simple-btn.retry:hover {
  background: rgba(156, 238, 105, 0.8);
  transform: translateY(-1px);
}

.error-text {
  color: #ef4444;
  font-size: 13px;
  margin: 10px 0;
}

.hidden {
  display: none !important;
}

/* Fade in animation */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Footer */
.app-footer {
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  padding: 4px 12px;
  flex-shrink: 0;
  min-height: 20px;
  max-height: 20px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 10px;
  color: #64748b;
}

.version {
  font-weight: 500;
}

.author {
  font-weight: 400;
}

/* Idle State Styles */
.idle-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px 16px;
  width: 100%;
}

.idle-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.idle-message p {
  margin: 0;
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  text-align: center;
}

/* Loading State */
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  width: 100%;
  min-height: 80px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #9CEE69;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-content p {
  margin: 0;
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  text-align: center;
}

/* Override state container for active request */
#activeRequestState {
  align-items: flex-start;
  justify-content: flex-start;
}

/* OTP Request Card */
.otp-request-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  margin: 4px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  width: calc(100% - 8px);
}

.request-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e2e8f0;
}

.request-icon {
  font-size: 20px;
}

.request-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1A1919;
}

.request-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
}

.detail-value {
  font-size: 12px;
  font-weight: 500;
  color: #1A1919;
}

.detail-value.timer {
  color: #ef4444;
  font-weight: 600;
}

/* Action Buttons */
.request-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 12px;
  border: 1px solid;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.action-btn.approve {
  background: #9CEE69;
  color: #1A1919;
  border-color: #9CEE69;
}

.action-btn.approve:hover {
  background: rgba(156, 238, 105, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(156, 238, 105, 0.3);
}

.action-btn.reject {
  background: #f8fafc;
  color: #374151;
  border-color: #e2e8f0;
}

.action-btn.reject:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.action-btn.retry {
  background: #9CEE69;
  color: #1A1919;
  border-color: #9CEE69;
}

.action-btn.retry:hover {
  background: rgba(156, 238, 105, 0.8);
  transform: translateY(-1px);
}

.btn-icon {
  font-size: 14px;
}

.btn-text {
  font-size: 12px;
}

/* Notification Cards */
.notification-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 16px 12px;
  border-radius: 12px;
  margin: 4px;
  animation: slideInUp 0.3s ease-out;
  width: calc(100% - 8px);
}

.notification-card.success {
  background: linear-gradient(135deg, rgba(156, 238, 105, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%);
  border: 1px solid rgba(156, 238, 105, 0.3);
}

.notification-card.reject {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.notification-card.error {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.notification-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.notification-card h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1A1919;
}

.notification-card p {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
}

.notification-timer {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes pulse-success {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
