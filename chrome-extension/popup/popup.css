/**
 * Minimal Popup Styles for Progress Dashboard Chrome Extension
 */

/* Base Reset */
html, body {
  margin: 0;
  padding: 0;
  width: 280px;
  height: auto;
  min-height: 100px;
  max-height: 350px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  background: #ffffff;
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: border-box;
}

/* App Container */
.app-container {
  width: 280px;
  height: auto;
  min-height: 100px;
  max-height: 350px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  transition: height 0.3s ease;
}

/* State Heights */
.app-container.state-idle { height: 100px; }
.app-container.state-loading { height: 80px; }
.app-container.state-active { height: 280px; }
.app-container.state-success { height: 100px; }
.app-container.state-error { height: 140px; }

/* Main Content */
.app-main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* State Container */
.state-container {
  width: 100%;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.state-container.hidden {
  display: none;
}

/* Simple Message Styles */
.simple-message {
  text-align: center;
  padding: 20px;
  color: #333;
}

.simple-message h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1A1919;
}

.simple-message p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.connected {
  background: #22c55e;
}

.status-dot.success {
  background: #22c55e;
}

.status-dot.error {
  background: #ef4444;
}

.request-info p {
  margin: 5px 0;
  font-size: 13px;
  text-align: left;
}

.simple-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  justify-content: center;
}

.simple-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: #fff;
  color: #333;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.simple-btn:hover {
  background: #f5f5f5;
}

.simple-btn.approve {
  background: #22c55e;
  color: white;
  border-color: #22c55e;
}

.simple-btn.approve:hover {
  background: #16a34a;
}

.simple-btn.reject {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.simple-btn.reject:hover {
  background: #dc2626;
}

.simple-btn.retry {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.simple-btn.retry:hover {
  background: #2563eb;
}

.error-text {
  color: #ef4444;
  font-size: 13px;
  margin: 10px 0;
}

.hidden {
  display: none !important;
}

.app-container.state-loading {
  height: 100px;
}

.app-container.state-active {
  height: 300px;
}

.app-container.state-success {
  height: 120px;
}

.app-container.state-error {
  height: 160px;
}

/* Header */
.app-header {
  position: relative;
  z-index: 1;
  background: #f8fafc;
  color: var(--color-eerie-black);
  padding: var(--spacing-3) var(--spacing-3);
  border-bottom: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-2-5);
  flex: 1;
  min-width: 0;
}

.brand-icon {
  width: 32px;
  height: 32px;
  background: #9CEE69;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.brand-icon svg {
  color: #1A1919;
  width: 16px;
  height: 16px;
}

.brand-text {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.brand-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-sm);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #1A1919;
}

.brand-subtitle {
  font-size: 0.65rem;
  margin: 0;
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #64748b;
}

.header-actions {
  display: flex;
  gap: var(--spacing-1-5);
  flex-shrink: 0;
}

.header-actions .btn {
  color: #64748b;
  border-color: #e2e8f0;
  background: #ffffff;
}

.header-actions .btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: var(--color-eerie-black);
}

/* Main Content */
.app-main {
  flex: 1;
  position: relative;
  z-index: 1;
  padding: var(--spacing-3) var(--spacing-3);
  overflow-y: visible;
  min-height: 100px;
  height: auto;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Adjust main content padding for different states */
.app-container.state-idle .app-main {
  padding: var(--spacing-2) var(--spacing-3);
}

.app-container.state-success .app-main,
.app-container.state-error .app-main {
  padding: var(--spacing-4) var(--spacing-3);
}

/* State Containers */
.state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 100px;
  height: auto;
  padding: var(--spacing-2);
  opacity: 1;
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.state-container.hidden {
  display: none;
  opacity: 0;
  transform: translateY(10px);
}

/* State-specific container adjustments */
.app-container.state-idle .state-container {
  padding: var(--spacing-1) var(--spacing-2);
}

.app-container.state-success .state-container,
.app-container.state-error .state-container {
  padding: var(--spacing-3) var(--spacing-2);
}

/* Loading State */
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding: var(--spacing-4);
}



.loading-text {
  font-size: var(--font-size-base);
  color: var(--color-eerie-black);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Idle State */
.idle-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-3);
  max-width: 280px;
  width: 100%;
  padding: var(--spacing-3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-icon {
  width: 48px;
  height: 48px;
  background: #9CEE69;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-eerie-black);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smaller icon for idle state */
.app-container.state-idle .status-icon {
  width: 40px;
  height: 40px;
}

.status-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-eerie-black);
  margin: 0;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smaller title for idle state */
.app-container.state-idle .status-title {
  font-size: var(--font-size-base);
}



/* Active Request State */
.request-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  width: 100%;
  max-width: 280px;
}

.request-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
}

.request-icon {
  width: 40px;
  height: 40px;
  background: #9CEE69;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-eerie-black);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.request-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--color-eerie-black);
  margin: 0;
}

.request-details {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: var(--radius-lg);
  padding: var(--spacing-3);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: var(--font-size-xs);
  color: var(--color-jet);
  font-weight: var(--font-weight-medium);
}

.detail-value {
  font-size: var(--font-size-xs);
  color: var(--color-eerie-black);
  font-weight: var(--font-weight-semibold);
}

.detail-value.timer {
  color: var(--color-warning-600);
  font-family: var(--font-family-mono);
}

.progress-container {
  margin: var(--spacing-2) 0;
}

.request-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

/* Success State */
.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-3);
  max-width: 240px;
  width: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.success-icon {
  width: 40px;
  height: 40px;
  background: #22c55e;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.success-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: #22c55e;
  margin: 0;
}

.success-description {
  font-size: var(--font-size-xs);
  color: var(--color-jet);
  line-height: var(--line-height-base);
  margin: 0;
}

/* Error State */
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-3);
  max-width: 240px;
  width: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.error-icon {
  width: 40px;
  height: 40px;
  background: #ef4444;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.error-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: #ef4444;
  margin: 0;
}

.error-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-lg);
  margin: 0;
}

.error-actions {
  width: 100%;
  margin-top: var(--spacing-2);
}

/* Footer */
.app-footer {
  position: relative;
  z-index: 1;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
  padding: var(--spacing-2) var(--spacing-3);
  flex-shrink: 0;
  margin-top: auto;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.extension-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.version {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.status {
  display: flex;
  align-items: center;
  gap: var(--spacing-1-5);
  font-size: var(--font-size-xs);
  color: #22c55e;
  font-weight: var(--font-weight-medium);
}

.status-dot {
  width: 6px;
  height: 6px;
  background: #22c55e;
  border-radius: var(--radius-full);
  animation: pulse 2s infinite;
}

.status.disconnected {
  color: #ef4444;
}

.status.disconnected .status-dot {
  background: #ef4444;
}

.footer-links {
  display: flex;
  gap: var(--spacing-2);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal.hidden {
  display: none;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: #ffffff;
  border-radius: var(--radius-xl);
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  width: 90%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 1px solid #e2e8f0;
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.modal-body {
  padding: var(--spacing-4);
  max-height: 300px;
  overflow-y: auto;
}

.modal-footer {
  padding: var(--spacing-4);
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

/* Settings */
.setting-group {
  margin-bottom: var(--spacing-4);
}

.setting-label {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  cursor: pointer;
  padding: var(--spacing-3);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--easing-ease-in-out);
}

.setting-label:hover {
  background: #f8fafc;
}

.setting-checkbox {
  width: 18px;
  height: 18px;
  border: 2px solid #e2e8f0;
  border-radius: var(--radius-sm);
  background: #ffffff;
  cursor: pointer;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
}

.setting-checkbox:checked {
  background: #9CEE69;
  border-color: #9CEE69;
}

.setting-checkbox:checked::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 5px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.setting-text {
  flex: 1;
}

.setting-text strong {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

.setting-text small {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: var(--line-height-sm);
}

/* Responsive adjustments - Extension popup with dynamic height */
@media (max-width: 320px) {
  .app-container {
    width: 320px;
    min-height: 200px;
    max-height: 600px;
    height: auto;
  }

  .brand-title {
    font-size: var(--font-size-xs);
  }

  .brand-subtitle {
    font-size: 0.6rem;
  }

  .status-icon {
    width: 40px;
    height: 40px;
  }

  .request-icon,
  .success-icon,
  .error-icon {
    width: 32px;
    height: 32px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .app-container {
    background: var(--color-dark-800);
  }

  .app-header {
    background: var(--color-dark-600);
    border-bottom: 1px solid var(--color-dark-500);
  }

  .app-footer {
    background: var(--color-dark-700);
    border-top: 1px solid var(--color-dark-600);
  }

  .brand-title {
    color: var(--color-secondary-50);
  }

  .brand-subtitle {
    color: var(--color-secondary-300);
  }
}
