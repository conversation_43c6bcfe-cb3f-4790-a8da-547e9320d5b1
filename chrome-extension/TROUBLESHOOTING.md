# 🔧 Chrome Extension Troubleshooting Guide

## ❌ **Error: "Extension not detected - bridge not available"**

### 🔍 **Diagnosis Steps:**

#### **Step 1: Verify Extension Installation**
```bash
1. Open Chrome: chrome://extensions/
2. Check "Progress Dashboard - OTP Authenticator" is listed
3. Verify status shows "Enabled" (not grayed out)
4. Check for any error messages in red
```

#### **Step 2: Check Extension Permissions**
```bash
1. Click "Details" on extension card
2. Verify permissions granted:
   ✅ Read and change data on localhost
   ✅ Display notifications
   ✅ Store data
3. If missing, click "Allow" for each permission
```

#### **Step 3: Verify Content Script Injection**
```bash
1. Open test page: http://localhost:5173/test-extension.html
2. Press F12 to open DevTools
3. Go to Console tab
4. Look for messages:
   ✅ "🔗 Progress Dashboard Extension content script initializing..."
   ✅ "✅ Content script initialized"
5. If missing, content script not injected
```

#### **Step 4: Check Extension Bridge**
```bash
1. In browser console (F12), type:
   console.log(window.progressDashboardExtension);
2. Expected result: Object with sendMessage function
3. If undefined: Bridge not injected
```

### 🛠️ **Common Fixes:**

#### **Fix 1: Reload Extension**
```bash
1. Go to chrome://extensions/
2. Find "Progress Dashboard - OTP Authenticator"
3. Click refresh icon (🔄) on extension card
4. Refresh test page
```

#### **Fix 2: Reinstall Extension**
```bash
1. Go to chrome://extensions/
2. Click "Remove" on extension
3. Click "Load unpacked" again
4. Select chrome-extension folder
5. Refresh test page
```

#### **Fix 3: Check File Permissions**
```bash
# Verify all files are readable:
ls -la chrome-extension/
ls -la chrome-extension/assets/

# Should show all files with read permissions
```

#### **Fix 4: Clear Browser Cache**
```bash
1. Press Ctrl+Shift+Delete (or Cmd+Shift+Delete on Mac)
2. Select "Cached images and files"
3. Click "Clear data"
4. Refresh test page
```

#### **Fix 5: Check Chrome Version**
```bash
1. Chrome menu → Help → About Google Chrome
2. Verify version 88 or higher
3. Update if necessary
```

### 🔧 **Advanced Troubleshooting:**

#### **Check Extension Console:**
```bash
1. Go to chrome://extensions/
2. Click "Inspect views: service worker" (if available)
3. Or right-click extension icon → "Inspect popup"
4. Check for JavaScript errors
```

#### **Check Content Script Errors:**
```bash
1. Open test page
2. Press F12 → Console tab
3. Look for red error messages
4. Common issues:
   - CSP violations
   - Permission denied
   - Script loading errors
```

#### **Verify Host Permissions:**
```bash
1. Extension should have permission for:
   - http://localhost:*/*
   - https://localhost:*/*
2. Current test URL: http://localhost:5173/
3. Should match permission pattern
```

### 🚀 **Quick Fix Commands:**

#### **Complete Reset:**
```bash
# 1. Remove extension
# 2. Clear browser cache
# 3. Restart Chrome
# 4. Reinstall extension
# 5. Test again
```

#### **Verify Installation:**
```bash
# In browser console:
setTimeout(() => {
  console.log('Extension bridge:', window.progressDashboardExtension);
  console.log('Available:', !!window.progressDashboardExtension);
}, 2000);
```

### 📋 **Installation Checklist:**

- [ ] Chrome version 88+
- [ ] Developer mode enabled
- [ ] Extension loaded from correct folder
- [ ] All permissions granted
- [ ] No error messages in extension list
- [ ] Content script console messages visible
- [ ] Extension bridge object available
- [ ] Test page on correct port (5173)

### 🆘 **If Still Not Working:**

#### **Alternative Testing:**
```bash
# Test on different port:
# Change dev server to port 5174:
npm run dev -- --port 5174

# Or test on simple HTML file:
# Create test.html with basic extension check
```

#### **Manual Bridge Injection:**
```javascript
// Temporary fix - inject bridge manually:
window.progressDashboardExtension = {
  isAvailable: true,
  version: '1.0.0',
  sendMessage: function(message) {
    console.log('Manual bridge - message:', message);
    return Promise.resolve({ success: true, manual: true });
  }
};
```

### 📞 **Support:**

If extension still not working after all steps:
1. Check Chrome DevTools Console for specific errors
2. Verify file permissions and folder structure
3. Try different Chrome profile
4. Test on different machine/browser

---

**Most Common Issue**: Extension not properly loaded or permissions not granted.
**Quick Fix**: Remove and reinstall extension with all permissions.
