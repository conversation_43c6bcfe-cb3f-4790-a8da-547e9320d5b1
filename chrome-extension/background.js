/**
 * Background Script for Progress Dashboard Chrome Extension
 *
 * Handles OTP requests, notifications, and communication with web pages
 */

// Environment detection
// For unpacked extensions, check if we're in development mode
const manifest = chrome.runtime.getManifest();
const isProduction = manifest.update_url !== undefined; // Production extensions have update_url
const isDevelopment = !isProduction;

// Logging configuration
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

// Force DEBUG level for development/testing
const CURRENT_LOG_LEVEL = isDevelopment ? LOG_LEVELS.DEBUG : LOG_LEVELS.INFO;

// Secure logging helper
function log(level, message, data = null) {
  if (level <= CURRENT_LOG_LEVEL) {
    const timestamp = new Date().toISOString();
    const levelName = Object.keys(LOG_LEVELS)[level];

    // In production, avoid logging sensitive data
    const sanitizedData = isProduction && data ? '[REDACTED]' : data;

    console.log(`[${timestamp}] ${levelName} [Background] ${message}`, sanitizedData || '');
  }
}

// Convenience logging functions
const logger = {
  error: (message, data) => log(LOG_LEVELS.ERROR, message, data),
  warn: (message, data) => log(LOG_LEVELS.WARN, message, data),
  info: (message, data) => log(LOG_LEVELS.INFO, message, data),
  debug: (message, data) => log(LOG_LEVELS.DEBUG, message, data)
};

// Extension state
let extensionState = {
  isActive: false,
  currentOTPRequest: null,
  pendingRequests: new Map(),
  rateLimiting: new Map(), // Track request rates per origin
  popupState: null, // Store popup state for Manifest V3 compatibility
  backendConnected: false, // NEW: Real backend connection status
  lastConnectionCheck: null, // NEW: Timestamp of last check
  settings: {
    autoApprove: false,
    notificationSound: true,
    notificationDuration: 30000, // 30 seconds
  }
};

// Rate limiting configuration
const RATE_LIMIT_CONFIG = {
  maxRequestsPerMinute: 5,
  maxRequestsPerHour: 20,
  blockDurationMinutes: 15
};

// Backend connection configuration
const BACKEND_CONFIG = {
  endpoints: [
    'http://localhost:5001',  // Primary backend
    'http://localhost:3000',  // Alternative backend
    'https://your-production-api.com' // Production backend
  ],
  healthCheckPath: '/api/auth/health', // Health check endpoint
  timeout: 5000, // 5 seconds timeout
  checkInterval: 30000 // Check every 30 seconds
};

// Check backend connection
async function checkBackendConnection() {
  console.log('[BACKGROUND] Checking backend connection...');

  for (const endpoint of BACKEND_CONFIG.endpoints) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), BACKEND_CONFIG.timeout);

      const response = await fetch(`${endpoint}${BACKEND_CONFIG.healthCheckPath}`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        }
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        console.log(`[BACKGROUND] Backend connected: ${endpoint}`);
        extensionState.backendConnected = true;
        extensionState.lastConnectionCheck = Date.now();
        return true;
      }
    } catch (error) {
      console.log(`[BACKGROUND] Backend connection failed for ${endpoint}:`, error.message);
    }
  }

  console.log('[BACKGROUND] All backend endpoints failed');
  extensionState.backendConnected = false;
  extensionState.lastConnectionCheck = Date.now();
  return false;
}

// Start periodic backend connection checks
function startBackendConnectionMonitoring() {
  // Initial check
  checkBackendConnection();

  // Periodic checks
  setInterval(checkBackendConnection, BACKEND_CONFIG.checkInterval);

  console.log(`[BACKGROUND] Backend connection monitoring started (every ${BACKEND_CONFIG.checkInterval/1000}s)`);
}

// Initialize extension
chrome.runtime.onInstalled.addListener(async (details) => {
  logger.info('Progress Dashboard OTP Extension installed', { reason: details.reason });

  try {
    // Set default settings
    await chrome.storage.local.set({
      settings: extensionState.settings,
      isFirstRun: details.reason === 'install'
    });

    // Start backend connection monitoring
    startBackendConnectionMonitoring();

    // Show welcome notification on first install
    if (details.reason === 'install') {
      showWelcomeNotification();
    }
  } catch (error) {
    logger.error('Failed to initialize extension', error.message);
  }
});

// Handle extension startup
chrome.runtime.onStartup.addListener(() => {
  logger.info('Progress Dashboard OTP Extension started');
  loadSettings();

  // Start backend connection monitoring on startup
  startBackendConnectionMonitoring();
});

// Load settings from storage
async function loadSettings() {
  try {
    const result = await chrome.storage.local.get(['settings']);
    if (result.settings) {
      extensionState.settings = { ...extensionState.settings, ...result.settings };
      logger.debug('Settings loaded successfully');
    }
  } catch (error) {
    logger.error('Failed to load settings', error.message);
  }
}

// Save settings to storage
async function saveSettings() {
  try {
    await chrome.storage.local.set({ settings: extensionState.settings });
    logger.debug('Settings saved successfully');
  } catch (error) {
    logger.error('Failed to save settings', error.message);
  }
}

// Handle messages from content scripts and web pages
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // Force console log for debugging
  console.log('[BACKGROUND] Message received:', message.type, message);
  logger.info('Background received internal message', { type: message.type });

  try {
    switch (message.type) {
      case 'OTP_REQUEST':
        console.log('[BACKGROUND] Handling OTP_REQUEST');
        handleOTPRequest(message, sender, sendResponse);
        return true; // Keep message channel open for async response

      case 'OTP_RESPONSE':
        console.log('[BACKGROUND] Handling OTP_RESPONSE');
        handleOTPResponse(message, sender, sendResponse);
        return true; // Keep message channel open for async response

      case 'SHOW_OTP_POPUP':
        console.log('[BACKGROUND] Handling SHOW_OTP_POPUP');
        handleShowOTPPopup(message, sender, sendResponse);
        return true; // Keep message channel open for async response

      case 'CHECK_BACKEND_CONNECTION':
        console.log('[BACKGROUND] Manual backend connection check requested');
        checkBackendConnection().then(connected => {
          sendResponse({
            success: true,
            data: {
              backendConnected: connected,
              lastCheck: extensionState.lastConnectionCheck,
              endpoints: BACKEND_CONFIG.endpoints
            }
          });
        });
        return true; // Keep message channel open for async response

      case 'GET_EXTENSION_STATUS':
        console.log('[BACKGROUND] GET_EXTENSION_STATUS request');
        console.log('[BACKGROUND] Current state:', {
          isActive: extensionState.isActive,
          hasActiveRequest: extensionState.currentOTPRequest !== null,
          currentRequest: extensionState.currentOTPRequest,
          popupState: extensionState.popupState,
          backendConnected: extensionState.backendConnected
        });

        const currentPopupState = extensionState.popupState;

        // Clear popup state after sending it (one-time use)
        if (extensionState.popupState) {
          console.log('[BACKGROUND] Clearing popup state after retrieval');
          extensionState.popupState = null;
        }

        sendResponse({
          success: true,
          data: {
            isActive: extensionState.isActive,
            version: chrome.runtime.getManifest().version,
            hasActiveRequest: extensionState.currentOTPRequest !== null,
            currentOTPRequest: extensionState.currentOTPRequest,
            popupState: currentPopupState,
            backendConnected: extensionState.backendConnected, // NEW: Real backend status
            lastConnectionCheck: extensionState.lastConnectionCheck
          }
        });
        break;

      case 'UPDATE_SETTINGS':
        updateSettings(message.data, sendResponse);
        return true;

      case 'GET_SETTINGS':
        sendResponse({
          success: true,
          data: extensionState.settings
        });
        break;

      default:
        logger.warn('Unknown internal message type', { type: message.type });
        sendResponse({ success: false, error: 'Unknown message type' });
    }
  } catch (error) {
    logger.error('Error handling internal message', error.message);
    sendResponse({ success: false, error: 'Internal error' });
  }
});

// Handle external messages from web pages
chrome.runtime.onMessageExternal.addListener((message, sender, sendResponse) => {
  logger.debug('External message received', { type: message.type, origin: sender.origin });

  // Validate origin
  if (!isValidOrigin(sender.origin)) {
    logger.warn('Invalid origin attempted communication', { origin: sender.origin });
    sendResponse({ success: false, error: 'Invalid origin' });
    return;
  }
  
  switch (message.type) {
    case 'OTP_REQUEST':
      handleOTPRequest(message, sender, sendResponse);
      return true;
      
    case 'EXTENSION_READY':
      sendResponse({
        success: true,
        data: {
          version: chrome.runtime.getManifest().version,
          isReady: true
        }
      });
      break;
      
    default:
      sendResponse({ success: false, error: 'Unknown message type' });
  }
});

// Validate origin for security
function isValidOrigin(origin) {
  const allowedOrigins = [
    'http://localhost:5173',  // Vite dev server
    'http://localhost:5174',
    'http://localhost:3000',
    'http://localhost:5001',  // Backend API
    'https://localhost:5173',
    'https://localhost:5174',
    'https://localhost:3000',
    'https://localhost:5001'
  ];
  
  // Add production domains
  const productionDomains = [
    'https://progressdashboard.com',
    'https://app.progressdashboard.com',
    'https://dashboard.progressdashboard.com'
  ];
  
  return allowedOrigins.includes(origin) ||
         productionDomains.some(domain => origin.startsWith(domain));
}

// Rate limiting functions
function checkRateLimit(origin) {
  const now = Date.now();
  const oneMinute = 60 * 1000;
  const oneHour = 60 * 60 * 1000;

  if (!extensionState.rateLimiting.has(origin)) {
    extensionState.rateLimiting.set(origin, {
      requests: [],
      blockedUntil: 0
    });
  }

  const rateLimitData = extensionState.rateLimiting.get(origin);

  // Check if currently blocked
  if (rateLimitData.blockedUntil > now) {
    return {
      allowed: false,
      reason: 'Rate limit exceeded - temporarily blocked',
      blockedUntil: rateLimitData.blockedUntil
    };
  }

  // Clean old requests
  rateLimitData.requests = rateLimitData.requests.filter(time => now - time < oneHour);

  // Check hourly limit
  if (rateLimitData.requests.length >= RATE_LIMIT_CONFIG.maxRequestsPerHour) {
    rateLimitData.blockedUntil = now + (RATE_LIMIT_CONFIG.blockDurationMinutes * 60 * 1000);
    return {
      allowed: false,
      reason: 'Hourly rate limit exceeded',
      blockedUntil: rateLimitData.blockedUntil
    };
  }

  // Check per-minute limit
  const recentRequests = rateLimitData.requests.filter(time => now - time < oneMinute);
  if (recentRequests.length >= RATE_LIMIT_CONFIG.maxRequestsPerMinute) {
    return {
      allowed: false,
      reason: 'Per-minute rate limit exceeded',
      retryAfter: oneMinute - (now - Math.min(...recentRequests))
    };
  }

  // Add current request
  rateLimitData.requests.push(now);

  return { allowed: true };
}

function cleanupRateLimiting() {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;

  for (const [origin, data] of extensionState.rateLimiting.entries()) {
    // Remove old requests
    data.requests = data.requests.filter(time => now - time < oneHour);

    // Remove entries with no recent requests and not blocked
    if (data.requests.length === 0 && data.blockedUntil <= now) {
      extensionState.rateLimiting.delete(origin);
    }
  }
}

// Handle SHOW_OTP_POPUP request from content script
async function handleShowOTPPopup(message, sender, sendResponse) {
  try {
    console.log('[BACKGROUND] Processing SHOW_OTP_POPUP request:', message);
    logger.info('Processing SHOW_OTP_POPUP request', { email: message.email });

    const requestId = generateRequestId();
    const otpRequest = {
      id: requestId,
      email: message.email,
      otp: message.otp,
      secret: message.secret,
      timestamp: Date.now(),
      sender: sender,
      sendResponse: sendResponse
    };

    console.log('[BACKGROUND] Created OTP request:', requestId);

    // Store request
    extensionState.pendingRequests.set(requestId, otpRequest);
    extensionState.currentOTPRequest = otpRequest;
    extensionState.isActive = true;

    console.log('[BACKGROUND] Showing notification...');
    // Show notification
    await showOTPNotification(otpRequest);

    console.log('[BACKGROUND] Notification shown, updating popup...');
    // Update popup if it's open
    updatePopupState('active', otpRequest);

    // Also try sending message to popup directly
    try {
      chrome.runtime.sendMessage({
        type: 'UPDATE_POPUP_STATE',
        state: 'active',
        data: otpRequest
      });
      console.log('[BACKGROUND] Sent UPDATE_POPUP_STATE message');
    } catch (error) {
      console.log('[BACKGROUND] Failed to send UPDATE_POPUP_STATE:', error.message);
    }

    console.log('[BACKGROUND] Notification shown, sending response');
    sendResponse({
      success: true,
      status: 'Popup triggered',
      requestId: requestId
    });

  } catch (error) {
    console.error('[BACKGROUND] Error handling SHOW_OTP_POPUP:', error);
    logger.error('Error handling SHOW_OTP_POPUP', error.message);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// Handle OTP request
async function handleOTPRequest(message, sender, sendResponse) {
  try {
    // Check rate limiting first
    const rateLimitCheck = checkRateLimit(sender.origin);
    if (!rateLimitCheck.allowed) {
      logger.warn('Rate limit exceeded', {
        origin: sender.origin,
        reason: rateLimitCheck.reason
      });
      sendResponse({
        success: false,
        error: rateLimitCheck.reason,
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: rateLimitCheck.retryAfter || rateLimitCheck.blockedUntil
      });
      return;
    }

    logger.info('Processing OTP request', { email: message.data?.email });

    const requestId = generateRequestId();
    const otpRequest = {
      id: requestId,
      ...message.data,
      timestamp: Date.now(),
      sender: sender,
      sendResponse: sendResponse
    };

    // Store request
    extensionState.pendingRequests.set(requestId, otpRequest);
    extensionState.currentOTPRequest = otpRequest;
    extensionState.isActive = true;

    // SECURITY: Remove auto-approve for production
    if (isDevelopment && extensionState.settings.autoApprove) {
      logger.warn('Auto-approving OTP request - DEVELOPMENT MODE ONLY');
      setTimeout(() => {
        approveOTPRequest(requestId);
      }, 2000);
      return;
    }
    
    // Show notification
    await showOTPNotification(otpRequest);
    
    // Update badge
    updateBadge('!');
    
    // Set timeout for request expiry
    setTimeout(() => {
      if (extensionState.pendingRequests.has(requestId)) {
        expireOTPRequest(requestId);
      }
    }, otpRequest.expires_in * 1000);

  } catch (error) {
    logger.error('Error handling OTP request', error.message);
    sendResponse({
      success: false,
      error: 'Failed to process OTP request'
    });
  }
}

// Handle OTP response (approve/reject)
function handleOTPResponse(message, sender, sendResponse) {
  const { requestId, action } = message.data;

  if (!extensionState.pendingRequests.has(requestId)) {
    logger.warn('OTP request not found', { requestId });
    sendResponse({
      success: false,
      error: 'OTP request not found'
    });
    return;
  }

  try {
    if (action === 'APPROVE') {
      approveOTPRequest(requestId);
      sendResponse({
        success: true,
        message: 'OTP request approved successfully'
      });
    } else {
      rejectOTPRequest(requestId);
      sendResponse({
        success: true,
        message: 'OTP request rejected successfully'
      });
    }
  } catch (error) {
    logger.error('Error handling OTP response', error.message);
    sendResponse({
      success: false,
      error: 'Failed to process OTP response'
    });
  }
}

// Approve OTP request
function approveOTPRequest(requestId) {
  const request = extensionState.pendingRequests.get(requestId);
  if (!request) return;

  console.log('[BACKGROUND] Approving OTP request:', requestId);
  logger.info('Approving OTP request', { requestId });

  // Send response to web page via content script
  const response = {
    type: 'OTP_RESPONSE',
    action: 'LOGIN', // Use LOGIN instead of APPROVE for frontend compatibility
    email: request.email,
    otp: request.otp,
    timestamp: Date.now()
  };

  console.log('[BACKGROUND] Sending response to content script:', response);

  // Send to content script to forward to web page
  chrome.tabs.sendMessage(request.sender.tab.id, {
    type: 'FORWARD_TO_PAGE',
    data: response
  }).then(() => {
    console.log('[BACKGROUND] Response sent to content script');
  }).catch(error => {
    console.error('[BACKGROUND] Failed to send response to content script:', error);
  });
  
  // Cleanup
  cleanupRequest(requestId);
  
  // Show success notification
  showNotification({
    type: 'basic',
    iconUrl: 'assets/icon48.png',
    title: 'OTP Approved',
    message: `Login approved for ${request.email}`
  });
}

// Reject OTP request
function rejectOTPRequest(requestId) {
  const request = extensionState.pendingRequests.get(requestId);
  if (!request) return;

  console.log('[BACKGROUND] Rejecting OTP request:', requestId);
  logger.info('Rejecting OTP request', { requestId });

  // Send response to web page via content script
  const response = {
    type: 'OTP_RESPONSE',
    action: 'REJECT',
    email: request.email,
    timestamp: Date.now()
  };

  console.log('[BACKGROUND] Sending reject response to content script:', response);

  // Send to content script to forward to web page
  chrome.tabs.sendMessage(request.sender.tab.id, {
    type: 'FORWARD_TO_PAGE',
    data: response
  }).then(() => {
    console.log('[BACKGROUND] Reject response sent to content script');
  }).catch(error => {
    console.error('[BACKGROUND] Failed to send reject response to content script:', error);
  });
  
  // Send to content script to forward to web page
  sendToWebPage(request.sender.tab.id, response);
  
  // Send response via message channel if available
  if (request.sendResponse) {
    request.sendResponse({
      success: false,
      error: 'User rejected the request'
    });
  }
  
  // Cleanup
  cleanupRequest(requestId);
  
  // Show rejection notification
  showNotification({
    type: 'basic',
    iconUrl: 'assets/icon48.png',
    title: 'OTP Rejected',
    message: `Login rejected for ${request.email}`
  });
}

// Expire OTP request
function expireOTPRequest(requestId) {
  const request = extensionState.pendingRequests.get(requestId);
  if (!request) return;
  
  logger.info('OTP request expired', { requestId });
  
  // Send expiry response
  if (request.sendResponse) {
    request.sendResponse({
      success: false,
      error: 'Request expired'
    });
  }
  
  // Cleanup
  cleanupRequest(requestId);
  
  // Show expiry notification
  showNotification({
    type: 'basic',
    iconUrl: 'assets/icon48.png',
    title: 'OTP Expired',
    message: `Login request expired for ${request.email}`
  });
}

// Cleanup request
function cleanupRequest(requestId) {
  extensionState.pendingRequests.delete(requestId);
  
  if (extensionState.currentOTPRequest?.id === requestId) {
    extensionState.currentOTPRequest = null;
  }
  
  // Update state
  extensionState.isActive = extensionState.pendingRequests.size > 0;
  
  // Update badge
  if (extensionState.pendingRequests.size === 0) {
    updateBadge('');
  } else {
    updateBadge(extensionState.pendingRequests.size.toString());
  }
}

// Send message to web page via content script
async function sendToWebPage(tabId, message) {
  try {
    await chrome.tabs.sendMessage(tabId, {
      type: 'FORWARD_TO_PAGE',
      data: message
    });
  } catch (error) {
    logger.error('Failed to send message to web page', error.message);
  }
}

// Show OTP notification
async function showOTPNotification(request) {
  const notificationId = `otp_${request.id}`;

  console.log('[BACKGROUND] Creating notification:', notificationId);

  try {
    await chrome.notifications.create(notificationId, {
      type: 'basic',
      iconUrl: 'assets/icon48.png',
      title: 'Progress Dashboard - Login Request',
      message: `Login request from ${request.email}\nClick to approve or reject`,
      buttons: [
        { title: 'Approve' },
        { title: 'Reject' }
      ],
      requireInteraction: true
    });

    console.log('[BACKGROUND] Notification created successfully:', notificationId);
  } catch (error) {
    console.error('[BACKGROUND] Failed to create notification:', error);
    throw error;
  }
  
  // Handle notification button clicks
  chrome.notifications.onButtonClicked.addListener((notifId, buttonIndex) => {
    if (notifId === notificationId) {
      if (buttonIndex === 0) {
        approveOTPRequest(request.id);
      } else {
        rejectOTPRequest(request.id);
      }
      chrome.notifications.clear(notifId);
    }
  });
  
  // Handle notification click (open popup)
  chrome.notifications.onClicked.addListener((notifId) => {
    if (notifId === notificationId) {
      chrome.action.openPopup();
      chrome.notifications.clear(notifId);
    }
  });
}

// Show welcome notification
async function showWelcomeNotification() {
  await chrome.notifications.create('welcome', {
    type: 'basic',
    iconUrl: 'assets/icon48.png',
    title: 'Progress Dashboard Extension Installed',
    message: 'Your OTP authenticator is ready! You can now securely login to Progress Dashboard.'
  });
}

// Generic notification helper
async function showNotification(options) {
  try {
    await chrome.notifications.create(options);
  } catch (error) {
    logger.error('Failed to show notification', error.message);
  }
}

// Update extension badge
function updateBadge(text) {
  chrome.action.setBadgeText({ text });
  chrome.action.setBadgeBackgroundColor({ color: '#95E565' });
}

// Update settings
async function updateSettings(newSettings, sendResponse) {
  try {
    extensionState.settings = { ...extensionState.settings, ...newSettings };
    await saveSettings();
    
    sendResponse({
      success: true,
      data: extensionState.settings
    });
  } catch (error) {
    logger.error('Failed to update settings', error.message);
    sendResponse({
      success: false,
      error: 'Failed to update settings'
    });
  }
}

// Generate unique request ID
function generateRequestId() {
  return `otp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Update popup state (Manifest V3 compatible)
async function updatePopupState(state, data = null) {
  try {
    console.log('[BACKGROUND] updatePopupState called with:', state, data);

    // In Manifest V3, we can't directly access popup views
    // Instead, we'll store the state and let popup query it when opened
    extensionState.popupState = {
      state: state,
      data: data,
      timestamp: Date.now()
    };

    console.log('[BACKGROUND] Stored popup state for later retrieval');

    // Try to send message to popup if it's listening
    try {
      chrome.runtime.sendMessage({
        type: 'UPDATE_POPUP_STATE',
        state: state,
        data: data
      });
      console.log('[BACKGROUND] Sent UPDATE_POPUP_STATE message to popup');
    } catch (error) {
      console.log('[BACKGROUND] No popup listening for messages (normal if popup not open)');
    }

  } catch (error) {
    console.error('[BACKGROUND] Failed to update popup state:', error);
  }
}

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // This will open the popup automatically due to default_popup in manifest
  logger.debug('Extension icon clicked');
});

// Cleanup on extension shutdown
chrome.runtime.onSuspend.addListener(() => {
  logger.info('Extension suspending, cleaning up...');

  // Reject all pending requests
  for (const [requestId] of extensionState.pendingRequests) {
    rejectOTPRequest(requestId);
  }
});

// Setup periodic cleanup for rate limiting
setInterval(cleanupRateLimiting, 5 * 60 * 1000); // Every 5 minutes

logger.info('Progress Dashboard OTP Extension background script loaded', {
  environment: isProduction ? 'production' : 'development'
});
