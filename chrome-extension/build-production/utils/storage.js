/**
 * Storage Utilities for Chrome Extension
 * 
 * Handles secure data storage and retrieval
 */

// Storage manager
class StorageManager {
  constructor() {
    this.storageArea = chrome.storage.local;
    this.syncArea = chrome.storage.sync;
    this.encryptionKey = null;
    this.initializeEncryption();
  }

  /**
   * Initialize encryption for sensitive data
   */
  async initializeEncryption() {
    try {
      // Generate or retrieve encryption key
      const result = await this.storageArea.get(['encryptionKey']);
      if (result.encryptionKey) {
        this.encryptionKey = result.encryptionKey;
      } else {
        this.encryptionKey = this.generateEncryptionKey();
        await this.storageArea.set({ encryptionKey: this.encryptionKey });
      }
    } catch (error) {
      console.error('Failed to initialize encryption:', error);
    }
  }

  /**
   * Generate encryption key
   */
  generateEncryptionKey() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Encrypt sensitive data
   */
  async encrypt(data) {
    if (!this.encryptionKey) {
      await this.initializeEncryption();
    }

    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(JSON.stringify(data));
      
      // Simple XOR encryption (for demo purposes)
      // In production, use Web Crypto API
      const keyBuffer = encoder.encode(this.encryptionKey);
      const encrypted = new Uint8Array(dataBuffer.length);
      
      for (let i = 0; i < dataBuffer.length; i++) {
        encrypted[i] = dataBuffer[i] ^ keyBuffer[i % keyBuffer.length];
      }
      
      return Array.from(encrypted, byte => byte.toString(16).padStart(2, '0')).join('');
    } catch (error) {
      console.error('Encryption failed:', error);
      return null;
    }
  }

  /**
   * Decrypt sensitive data
   */
  async decrypt(encryptedData) {
    if (!this.encryptionKey || !encryptedData) {
      return null;
    }

    try {
      const encoder = new TextEncoder();
      const decoder = new TextDecoder();
      
      // Convert hex string back to bytes
      const encrypted = new Uint8Array(
        encryptedData.match(/.{1,2}/g).map(byte => parseInt(byte, 16))
      );
      
      const keyBuffer = encoder.encode(this.encryptionKey);
      const decrypted = new Uint8Array(encrypted.length);
      
      for (let i = 0; i < encrypted.length; i++) {
        decrypted[i] = encrypted[i] ^ keyBuffer[i % keyBuffer.length];
      }
      
      const jsonString = decoder.decode(decrypted);
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('Decryption failed:', error);
      return null;
    }
  }

  /**
   * Store data with optional encryption
   */
  async set(key, value, options = {}) {
    const { encrypt = false, sync = false, ttl = null } = options;
    
    try {
      let dataToStore = value;
      
      if (encrypt) {
        dataToStore = await this.encrypt(value);
        if (!dataToStore) {
          throw new Error('Encryption failed');
        }
      }

      const storageData = {
        value: dataToStore,
        encrypted: encrypt,
        timestamp: Date.now(),
        ttl: ttl
      };

      const storage = sync ? this.syncArea : this.storageArea;
      await storage.set({ [key]: storageData });
      
      return true;
    } catch (error) {
      console.error(`Failed to store ${key}:`, error);
      return false;
    }
  }

  /**
   * Retrieve data with automatic decryption
   */
  async get(key, defaultValue = null) {
    try {
      const storage = this.storageArea;
      const result = await storage.get([key]);
      const storageData = result[key];
      
      if (!storageData) {
        return defaultValue;
      }

      // Check TTL
      if (storageData.ttl && Date.now() > storageData.timestamp + storageData.ttl) {
        await this.remove(key);
        return defaultValue;
      }

      let value = storageData.value;
      
      if (storageData.encrypted) {
        value = await this.decrypt(value);
        if (value === null) {
          console.warn(`Failed to decrypt ${key}, removing corrupted data`);
          await this.remove(key);
          return defaultValue;
        }
      }

      return value;
    } catch (error) {
      console.error(`Failed to retrieve ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * Remove data
   */
  async remove(key) {
    try {
      await this.storageArea.remove([key]);
      return true;
    } catch (error) {
      console.error(`Failed to remove ${key}:`, error);
      return false;
    }
  }

  /**
   * Clear all data
   */
  async clear() {
    try {
      await this.storageArea.clear();
      return true;
    } catch (error) {
      console.error('Failed to clear storage:', error);
      return false;
    }
  }

  /**
   * Get all keys
   */
  async getAllKeys() {
    try {
      const result = await this.storageArea.get(null);
      return Object.keys(result);
    } catch (error) {
      console.error('Failed to get all keys:', error);
      return [];
    }
  }

  /**
   * Get storage usage
   */
  async getUsage() {
    try {
      const usage = await this.storageArea.getBytesInUse();
      return {
        used: usage,
        quota: chrome.storage.local.QUOTA_BYTES,
        percentage: (usage / chrome.storage.local.QUOTA_BYTES) * 100
      };
    } catch (error) {
      console.error('Failed to get storage usage:', error);
      return null;
    }
  }

  /**
   * Cleanup expired data
   */
  async cleanup() {
    try {
      const allData = await this.storageArea.get(null);
      const keysToRemove = [];
      const now = Date.now();

      for (const [key, data] of Object.entries(allData)) {
        if (data && data.ttl && now > data.timestamp + data.ttl) {
          keysToRemove.push(key);
        }
      }

      if (keysToRemove.length > 0) {
        await this.storageArea.remove(keysToRemove);
        console.log(`Cleaned up ${keysToRemove.length} expired items`);
      }

      return keysToRemove.length;
    } catch (error) {
      console.error('Failed to cleanup storage:', error);
      return 0;
    }
  }
}

// Settings manager
class SettingsManager extends StorageManager {
  constructor() {
    super();
    this.defaultSettings = {
      autoApprove: false,
      notificationSound: true,
      notificationDuration: 30000,
      theme: 'auto',
      language: 'en',
      debugMode: false
    };
  }

  /**
   * Get settings with defaults
   */
  async getSettings() {
    const settings = await this.get('settings', {});
    return { ...this.defaultSettings, ...settings };
  }

  /**
   * Update settings
   */
  async updateSettings(newSettings) {
    const currentSettings = await this.getSettings();
    const updatedSettings = { ...currentSettings, ...newSettings };
    
    // Validate settings
    const validatedSettings = this.validateSettings(updatedSettings);
    
    const success = await this.set('settings', validatedSettings, { sync: true });
    return success ? validatedSettings : null;
  }

  /**
   * Reset settings to defaults
   */
  async resetSettings() {
    const success = await this.set('settings', this.defaultSettings, { sync: true });
    return success ? this.defaultSettings : null;
  }

  /**
   * Validate settings
   */
  validateSettings(settings) {
    const validated = { ...settings };

    // Validate boolean settings
    ['autoApprove', 'notificationSound', 'debugMode'].forEach(key => {
      if (typeof validated[key] !== 'boolean') {
        validated[key] = this.defaultSettings[key];
      }
    });

    // Validate numeric settings
    if (typeof validated.notificationDuration !== 'number' || 
        validated.notificationDuration < 5000 || 
        validated.notificationDuration > 300000) {
      validated.notificationDuration = this.defaultSettings.notificationDuration;
    }

    // Validate string settings
    if (!['auto', 'light', 'dark'].includes(validated.theme)) {
      validated.theme = this.defaultSettings.theme;
    }

    if (typeof validated.language !== 'string' || validated.language.length !== 2) {
      validated.language = this.defaultSettings.language;
    }

    return validated;
  }
}

// Request cache manager
class RequestCacheManager extends StorageManager {
  constructor() {
    super();
    this.cachePrefix = 'request_cache_';
    this.maxCacheSize = 100;
    this.defaultTTL = 3600000; // 1 hour
  }

  /**
   * Cache request data
   */
  async cacheRequest(requestId, data) {
    const key = this.cachePrefix + requestId;
    return await this.set(key, data, { 
      encrypt: true, 
      ttl: this.defaultTTL 
    });
  }

  /**
   * Get cached request
   */
  async getCachedRequest(requestId) {
    const key = this.cachePrefix + requestId;
    return await this.get(key);
  }

  /**
   * Remove cached request
   */
  async removeCachedRequest(requestId) {
    const key = this.cachePrefix + requestId;
    return await this.remove(key);
  }

  /**
   * Cleanup old cache entries
   */
  async cleanupCache() {
    const allKeys = await this.getAllKeys();
    const cacheKeys = allKeys.filter(key => key.startsWith(this.cachePrefix));
    
    if (cacheKeys.length > this.maxCacheSize) {
      // Remove oldest entries
      const keysToRemove = cacheKeys.slice(0, cacheKeys.length - this.maxCacheSize);
      for (const key of keysToRemove) {
        await this.remove(key);
      }
    }

    // Also run general cleanup for expired items
    return await this.cleanup();
  }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    StorageManager,
    SettingsManager,
    RequestCacheManager
  };
} else if (typeof window !== 'undefined') {
  window.StorageManager = StorageManager;
  window.SettingsManager = SettingsManager;
  window.RequestCacheManager = RequestCacheManager;
}
