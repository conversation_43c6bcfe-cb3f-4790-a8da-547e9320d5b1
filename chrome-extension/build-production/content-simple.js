/**
 * Production Content Script for Progress Dashboard Chrome Extension
 *
 * Handles secure communication between web page and extension background
 */

// Environment detection
const isProduction = !chrome.runtime.getManifest().key;
const isDevelopment = !isProduction;

// Logging configuration
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

const CURRENT_LOG_LEVEL = isProduction ? LOG_LEVELS.WARN : LOG_LEVELS.DEBUG;

// Secure logging helper
function log(level, message, data = null) {
  if (level <= CURRENT_LOG_LEVEL) {
    const timestamp = new Date().toISOString();
    const levelName = Object.keys(LOG_LEVELS)[level];

    // In production, avoid logging sensitive data
    const sanitizedData = isProduction && data ? '[REDACTED]' : data;

    console.log(`[${timestamp}] ${levelName} [Content] ${message}`, sanitizedData || '');
  }
}

// Convenience logging functions
const logger = {
  error: (message, data) => log(LOG_LEVELS.ERROR, message, data),
  warn: (message, data) => log(LOG_LEVELS.WARN, message, data),
  info: (message, data) => log(LOG_LEVELS.INFO, message, data),
  debug: (message, data) => log(LOG_LEVELS.DEBUG, message, data)
};

logger.info('Progress Dashboard content script loaded', {
  url: window.location.href,
  environment: isProduction ? 'production' : 'development'
});

// Create secure extension bridge
window.progressDashboardExtension = {
  loaded: true,
  timestamp: Date.now(),
  version: '1.0.0-production',

  // Secure message sending to background script
  sendMessage: function(message) {
    logger.debug('Sending message to background script', { type: message.type });

    return new Promise((resolve, reject) => {
      try {
        // Send message to background script
        chrome.runtime.sendMessage(message, (response) => {
          if (chrome.runtime.lastError) {
            logger.error('Extension communication error', chrome.runtime.lastError.message);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          if (response && response.success) {
            logger.debug('Background script response received');
            resolve(response);
          } else {
            const error = response?.error || 'Unknown error';
            logger.warn('Background script returned error', { error });
            reject(new Error(error));
          }
        });
      } catch (error) {
        logger.error('Failed to send message to background script', error.message);
        reject(error);
      }
    });
  },

  // Extension information
  getDebugInfo: function() {
    return {
      version: '1.0.0-production',
      isAvailable: true,
      injectionMethod: 'secure',
      timestamp: this.timestamp,
      environment: isProduction ? 'production' : 'development'
    };
  },

  // Availability check
  isAvailable: function() {
    return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
  }
};

// Add DOM marker
document.documentElement.setAttribute('data-progress-dashboard-extension', 'production-loaded');

// Set content script marker
window.progressDashboardContentScript = true;

// Listen for messages from web page
window.addEventListener('message', (event) => {
  // Validate origin for security
  if (event.origin !== window.location.origin) {
    logger.warn('Message from invalid origin ignored', { origin: event.origin });
    return;
  }

  logger.debug('Message received from web page', { type: event.data?.type });

  if (event.data && event.data.type === 'OTP_REQUEST') {
    logger.info('OTP request received from web page');

    // Forward to background script for user interaction
    chrome.runtime.sendMessage(event.data, (response) => {
      if (chrome.runtime.lastError) {
        logger.error('Failed to forward OTP request', chrome.runtime.lastError.message);
        return;
      }

      logger.debug('OTP request forwarded to background script');
    });
  }
});

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  logger.debug('Message received from background script', { type: message.type });

  if (message.type === 'FORWARD_TO_PAGE') {
    // Forward response back to web page
    window.postMessage(message.data, window.location.origin);
    logger.debug('Message forwarded to web page');
  }

  sendResponse({ success: true });
});

// Notify web page that extension is ready
setTimeout(() => {
  // Dispatch ready event
  const readyEvent = new CustomEvent('progressDashboardExtensionReady', {
    detail: {
      version: '1.0.0-production',
      timestamp: Date.now(),
      environment: isProduction ? 'production' : 'development'
    }
  });
  window.dispatchEvent(readyEvent);

  // Send ready message
  window.postMessage({
    type: 'PROGRESS_DASHBOARD_READY',
    data: {
      extensionLoaded: true,
      version: '1.0.0-production',
      timestamp: Date.now(),
      environment: isProduction ? 'production' : 'development'
    }
  }, window.location.origin);

  logger.info('Extension ready event dispatched to web page');
}, 100);

// Error handling for unhandled errors
window.addEventListener('error', (event) => {
  if (event.filename && event.filename.includes('chrome-extension://')) {
    logger.error('Extension script error', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno
    });
  }
});

logger.info('Production content script setup complete');
