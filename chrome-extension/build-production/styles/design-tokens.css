/**
 * Design Tokens for Chrome Extension
 * 
 * Consistent with Progress Dashboard design system
 * Provides CSS custom properties for all design tokens
 */

:root {
  /* Brand Colors */
  --color-eerie-black: #1A1919;
  --color-light-green: #95E565;
  --color-nav-green: #9CEE69;
  --color-jet: #383838;
  --color-asparagus: #608F44;
  --color-seashell: #FEF5ED;
  
  /* Primary Scale (Green-based) */
  --color-primary-50: #f0fdf4;
  --color-primary-100: #dcfce7;
  --color-primary-200: #bbf7d0;
  --color-primary-300: #86efac;
  --color-primary-400: #4ade80;
  --color-primary-500: #95E565;
  --color-primary-600: #608F44;
  --color-primary-700: #4a7c59;
  --color-primary-800: #365a3d;
  --color-primary-900: #1f2937;
  --color-primary-950: #0f172a;
  
  /* Secondary Scale (Neutral-based) */
  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;
  --color-secondary-950: #020617;
  
  /* Semantic Colors */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  
  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  
  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  
  /* Dark Theme */
  --color-dark-50: #f8fafc;
  --color-dark-500: #383838;
  --color-dark-600: #2d2d2d;
  --color-dark-700: #1f1f1f;
  --color-dark-800: #1A1919;
  --color-dark-900: #0a0a0a;
  --color-dark-950: #000000;
  
  /* Typography */
  --font-family-sans: 'Inter', system-ui, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  --line-height-xs: 1rem;
  --line-height-sm: 1.25rem;
  --line-height-base: 1.5rem;
  --line-height-lg: 1.75rem;
  --line-height-xl: 1.75rem;
  --line-height-2xl: 2rem;
  --line-height-3xl: 2.25rem;
  --line-height-4xl: 2.5rem;
  
  /* Spacing Scale */
  --spacing-0: 0;
  --spacing-0-5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1-5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2-5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3-5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  
  /* Border Radius Scale */
  --radius-none: 0;
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Shadow Scale */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --shadow-glass-lg: 0 16px 64px 0 rgba(31, 38, 135, 0.37);
  
  /* Glass Morphism Effects */
  --glass-light: rgba(254, 245, 237, 0.1);
  --glass-medium: rgba(254, 245, 237, 0.2);
  --glass-heavy: rgba(254, 245, 237, 0.3);
  --glass-dark: rgba(26, 25, 25, 0.1);
  --glass-dark-medium: rgba(26, 25, 25, 0.2);
  --glass-dark-heavy: rgba(26, 25, 25, 0.3);
  --glass-green: rgba(149, 229, 101, 0.1);
  --glass-green-medium: rgba(149, 229, 101, 0.2);
  --glass-green-heavy: rgba(149, 229, 101, 0.3);
  
  /* Animation Durations */
  --duration-fast: 150ms;
  --duration-normal: 200ms;
  --duration-slow: 300ms;
  --duration-slower: 500ms;
  
  /* Animation Easing */
  --easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-Index Scale */
  --z-hide: -1;
  --z-auto: auto;
  --z-base: 0;
  --z-docked: 10;
  --z-dropdown: 1000;
  --z-sticky: 1100;
  --z-banner: 1200;
  --z-overlay: 1300;
  --z-modal: 1400;
  --z-popover: 1500;
  --z-skip-link: 1600;
  --z-toast: 1700;
  --z-tooltip: 1800;
  
  /* Extension Specific */
  --extension-width: 450px;
  --extension-height: 700px;
  --notification-width: 380px;
  --notification-height: 240px;
}

/* Dark mode overrides */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--color-dark-800);
    --color-surface: var(--color-dark-700);
    --color-text-primary: var(--color-secondary-50);
    --color-text-secondary: var(--color-secondary-300);
    --color-border: var(--color-dark-600);
  }
}

/* Light mode (default) */
:root {
  --color-background: var(--color-seashell);
  --color-surface: #ffffff;
  --color-text-primary: var(--color-secondary-900);
  --color-text-secondary: var(--color-secondary-600);
  --color-border: var(--color-secondary-200);
}
