/**
 * Component Styles for Chrome Extension
 * 
 * Consistent component styling matching the main application
 */

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--color-text-primary);
  background: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Button Components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-base);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-normal) var(--easing-ease-in-out);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
  min-height: 44px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.btn:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* Button Variants */
.btn-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--color-surface);
  color: var(--color-text-primary);
  border-color: var(--color-border);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--color-secondary-50);
  border-color: var(--color-secondary-300);
  box-shadow: var(--shadow-md);
}

.btn-ghost {
  background: transparent;
  color: var(--color-text-secondary);
}

.btn-ghost:hover {
  background: var(--color-secondary-100);
  color: var(--color-text-primary);
}

.btn-danger {
  background: linear-gradient(135deg, var(--color-error-500), var(--color-error-600));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
  background: linear-gradient(135deg, var(--color-error-600), var(--color-error-700));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-1-5) var(--spacing-3);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-xs);
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-lg);
  line-height: var(--line-height-lg);
}

.btn-full {
  width: 100%;
}

/* Card Components */
.card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--duration-normal) var(--easing-ease-in-out);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.card-header {
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-2);
  border-bottom: 1px solid var(--color-border);
}

.card-body {
  padding: var(--spacing-4);
}

.card-footer {
  padding: var(--spacing-2) var(--spacing-4) var(--spacing-4);
  border-top: 1px solid var(--color-border);
}

/* Glass Card */
.card-glass {
  background: var(--glass-medium);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-light);
  box-shadow: var(--shadow-glass);
}

/* Input Components */
.input {
  width: 100%;
  padding: var(--spacing-2-5) var(--spacing-3-5);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-sm);
  color: var(--color-text-primary);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--easing-ease-in-out);
}

.input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input-error {
  border-color: var(--color-error-500);
}

.input-error:focus {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px var(--color-error-100);
}

/* Badge Components */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-xs);
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-primary {
  background: var(--color-primary-100);
  color: var(--color-primary-700);
}

.badge-success {
  background: var(--color-success-50);
  color: var(--color-success-700);
}

.badge-warning {
  background: var(--color-warning-50);
  color: var(--color-warning-700);
}

.badge-error {
  background: var(--color-error-50);
  color: var(--color-error-700);
}

.badge-secondary {
  background: var(--color-secondary-100);
  color: var(--color-secondary-700);
}

/* Progress Components */
.progress {
  width: 100%;
  height: var(--spacing-2);
  background: var(--color-secondary-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary-500), var(--color-primary-600));
  border-radius: var(--radius-full);
  transition: width var(--duration-slow) var(--easing-ease-out);
}

.progress-bar-animated {
  background: linear-gradient(90deg, 
    var(--color-primary-500), 
    var(--color-primary-600), 
    var(--color-primary-500));
  background-size: 200% 100%;
  animation: loading 2s infinite;
}

/* Avatar Components */
.avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--spacing-8);
  height: var(--spacing-8);
  border-radius: var(--radius-full);
  background: var(--color-primary-500);
  color: white;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  overflow: hidden;
}

.avatar-sm {
  width: var(--spacing-6);
  height: var(--spacing-6);
  font-size: var(--font-size-xs);
}

.avatar-lg {
  width: var(--spacing-12);
  height: var(--spacing-12);
  font-size: var(--font-size-lg);
}

/* Icon Components */
.icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  fill: currentColor;
  flex-shrink: 0;
}

.icon-sm {
  width: 0.875em;
  height: 0.875em;
}

.icon-lg {
  width: 1.25em;
  height: 1.25em;
}

/* Notification Components */
.notification {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  width: var(--notification-width);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-toast);
  overflow: hidden;
}

.notification-success {
  border-left: 4px solid var(--color-success-500);
}

.notification-warning {
  border-left: 4px solid var(--color-warning-500);
}

.notification-error {
  border-left: 4px solid var(--color-error-500);
}

.notification-info {
  border-left: 4px solid var(--color-primary-500);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: var(--font-weight-bold); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-medium { font-weight: var(--font-weight-medium); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.text-primary { color: var(--color-primary-600); }
.text-secondary { color: var(--color-text-secondary); }
.text-success { color: var(--color-success-600); }
.text-warning { color: var(--color-warning-600); }
.text-error { color: var(--color-error-600); }

.bg-primary { background-color: var(--color-primary-500); }
.bg-secondary { background-color: var(--color-secondary-500); }
.bg-success { background-color: var(--color-success-500); }
.bg-warning { background-color: var(--color-warning-500); }
.bg-error { background-color: var(--color-error-500); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }

.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }

.px-2 { padding-left: var(--spacing-2); padding-right: var(--spacing-2); }
.px-3 { padding-left: var(--spacing-3); padding-right: var(--spacing-3); }
.px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }

.py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
.py-3 { padding-top: var(--spacing-3); padding-bottom: var(--spacing-3); }
.py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }

.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }

.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }

.w-full { width: 100%; }
.h-full { height: 100%; }
