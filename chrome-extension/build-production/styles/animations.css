/**
 * Modern Animations for Chrome Extension
 * 
 * Dynamic and smooth animations for enhanced UX
 */

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutToRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--color-primary-500);
  }
  50% {
    box-shadow: 0 0 20px var(--color-primary-500), 0 0 30px var(--color-primary-500);
  }
}

@keyframes progressBar {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn var(--duration-normal) var(--easing-ease-out) forwards;
}

.animate-fade-out {
  animation: fadeOut var(--duration-normal) var(--easing-ease-in) forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight var(--duration-slow) var(--easing-ease-out) forwards;
}

.animate-slide-out-right {
  animation: slideOutToRight var(--duration-slow) var(--easing-ease-in) forwards;
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--easing-ease-out) forwards;
}

.animate-scale-out {
  animation: scaleOut var(--duration-normal) var(--easing-ease-in) forwards;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-progress {
  animation: progressBar var(--duration-slower) var(--easing-ease-out) forwards;
}

/* Transition Classes */
.transition-all {
  transition: all var(--duration-normal) var(--easing-ease-in-out);
}

.transition-colors {
  transition: color var(--duration-normal) var(--easing-ease-in-out),
              background-color var(--duration-normal) var(--easing-ease-in-out),
              border-color var(--duration-normal) var(--easing-ease-in-out);
}

.transition-transform {
  transition: transform var(--duration-normal) var(--easing-ease-in-out);
}

.transition-opacity {
  transition: opacity var(--duration-normal) var(--easing-ease-in-out);
}

.transition-shadow {
  transition: box-shadow var(--duration-normal) var(--easing-ease-in-out);
}

/* Hover Effects */
.hover-lift {
  transition: transform var(--duration-normal) var(--easing-ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform var(--duration-normal) var(--easing-ease-out);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--duration-normal) var(--easing-ease-out);
}

.hover-glow:hover {
  box-shadow: 0 0 20px var(--color-primary-500);
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, 
    var(--color-secondary-200) 25%, 
    var(--color-secondary-100) 50%, 
    var(--color-secondary-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.loading-dots::after {
  content: '';
  animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Ripple Effect */
.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}

/* Stagger Animation */
.stagger-children > * {
  animation: fadeIn var(--duration-normal) var(--easing-ease-out) forwards;
}

.stagger-children > *:nth-child(1) { animation-delay: 0ms; }
.stagger-children > *:nth-child(2) { animation-delay: 100ms; }
.stagger-children > *:nth-child(3) { animation-delay: 200ms; }
.stagger-children > *:nth-child(4) { animation-delay: 300ms; }
.stagger-children > *:nth-child(5) { animation-delay: 400ms; }

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
