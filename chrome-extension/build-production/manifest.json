{"manifest_version": 3, "name": "Progress Dashboard - OTP Authenticator", "version": "1.0.0", "description": "Secure OTP authentication for Progress Dashboard. Provides seamless two-factor authentication through Chrome Extension integration.", "permissions": ["storage", "activeTab", "notifications", "scripting"], "host_permissions": ["https://progressdashboard.com/*", "https://app.progressdashboard.com/*", "https://dashboard.progressdashboard.com/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://progressdashboard.com/*", "https://app.progressdashboard.com/*", "https://dashboard.progressdashboard.com/*"], "js": ["content-simple.js"], "run_at": "document_start"}], "action": {"default_popup": "popup/popup.html", "default_title": "Progress Dashboard OTP", "default_icon": {"16": "assets/icon16.png", "32": "assets/icon32.png", "48": "assets/icon48.png", "128": "assets/icon128.png"}}, "icons": {"16": "assets/icon16.png", "32": "assets/icon32.png", "48": "assets/icon48.png", "128": "assets/icon128.png"}, "web_accessible_resources": [{"resources": ["assets/*", "styles/*"], "matches": ["https://progressdashboard.com/*", "https://app.progressdashboard.com/*", "https://dashboard.progressdashboard.com/*"]}], "externally_connectable": {"matches": ["https://progressdashboard.com/*", "https://app.progressdashboard.com/*", "https://dashboard.progressdashboard.com/*"]}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "homepage_url": "https://progressdashboard.com", "author": "Progress Dashboard Team"}