/**
 * Popup JavaScript for Progress Dashboard Chrome Extension
 * 
 * Handles popup UI interactions and state management
 */

// Popup state
let popupState = {
  currentState: 'loading',
  currentRequest: null,
  timer: null,
  settings: {
    autoApprove: false,
    notificationSound: true
  }
};

// DOM elements
const elements = {
  // States
  loadingState: document.getElementById('loadingState'),
  idleState: document.getElementById('idleState'),
  activeRequestState: document.getElementById('activeRequestState'),
  successState: document.getElementById('successState'),
  errorState: document.getElementById('errorState'),
  
  // Request details
  requestEmail: document.getElementById('requestEmail'),
  requestWebsite: document.getElementById('requestWebsite'),
  requestTimer: document.getElementById('requestTimer'),
  requestProgress: document.getElementById('requestProgress'),
  
  // Buttons
  approveBtn: document.getElementById('approveBtn'),
  rejectBtn: document.getElementById('rejectBtn'),
  testConnectionBtn: document.getElementById('testConnectionBtn'),
  retryBtn: document.getElementById('retryBtn'),
  settingsBtn: document.getElementById('settingsBtn'),
  helpBtn: document.getElementById('helpBtn'),
  
  // Settings modal
  settingsModal: document.getElementById('settingsModal'),
  closeSettingsBtn: document.getElementById('closeSettingsBtn'),
  saveSettingsBtn: document.getElementById('saveSettingsBtn'),
  autoApproveToggle: document.getElementById('autoApproveToggle'),
  notificationSoundToggle: document.getElementById('notificationSoundToggle'),
  
  // Status
  connectionStatus: document.getElementById('connectionStatus'),
  errorMessage: document.getElementById('errorMessage')
};

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🎨 Popup initializing...');
  
  // Set up event listeners
  setupEventListeners();
  
  // Load settings
  await loadSettings();
  
  // Check extension status
  await checkExtensionStatus();
  
  console.log('✅ Popup initialized');
});

// Set up event listeners
function setupEventListeners() {
  // Action buttons
  elements.approveBtn?.addEventListener('click', handleApprove);
  elements.rejectBtn?.addEventListener('click', handleReject);
  elements.testConnectionBtn?.addEventListener('click', handleTestConnection);
  elements.retryBtn?.addEventListener('click', handleRetry);
  
  // Settings
  elements.settingsBtn?.addEventListener('click', openSettings);
  elements.closeSettingsBtn?.addEventListener('click', closeSettings);
  elements.saveSettingsBtn?.addEventListener('click', saveSettings);
  
  // Help
  elements.helpBtn?.addEventListener('click', openHelp);
  
  // Modal overlay click
  elements.settingsModal?.addEventListener('click', (e) => {
    if (e.target === elements.settingsModal) {
      closeSettings();
    }
  });
  
  // Listen for background messages
  chrome.runtime.onMessage.addListener(handleBackgroundMessage);
}

// Check extension status
async function checkExtensionStatus() {
  try {
    showState('loading');
    
    const response = await chrome.runtime.sendMessage({
      type: 'GET_EXTENSION_STATUS'
    });
    
    if (response.success) {
      const { isActive, hasActiveRequest } = response.data;
      
      if (hasActiveRequest) {
        // Get current request details
        await getCurrentRequest();
      } else {
        showState('idle');
      }
      
      updateConnectionStatus(true);
    } else {
      throw new Error(response.error || 'Failed to get extension status');
    }
  } catch (error) {
    console.error('Failed to check extension status:', error);
    showError('Failed to connect to extension background');
    updateConnectionStatus(false);
  }
}

// Get current request details
async function getCurrentRequest() {
  try {
    // This would typically come from background script
    // For now, we'll simulate or get from storage
    const request = popupState.currentRequest;
    
    if (request) {
      showActiveRequest(request);
    } else {
      showState('idle');
    }
  } catch (error) {
    console.error('Failed to get current request:', error);
    showState('idle');
  }
}

// Handle background messages
function handleBackgroundMessage(message, sender, sendResponse) {
  console.log('📨 Popup received message:', message);
  
  switch (message.type) {
    case 'OTP_REQUEST_RECEIVED':
      showActiveRequest(message.data);
      break;
      
    case 'OTP_REQUEST_COMPLETED':
      if (message.data.approved) {
        showState('success');
        setTimeout(() => {
          showState('idle');
        }, 3000);
      } else {
        showState('idle');
      }
      break;
      
    case 'OTP_REQUEST_EXPIRED':
      showState('idle');
      break;
      
    default:
      console.warn('Unknown message type:', message.type);
  }
}

// Show different states
function showState(state) {
  // Hide all states
  Object.values(elements).forEach(el => {
    if (el && el.classList.contains('state-container')) {
      el.classList.add('hidden');
    }
  });
  
  // Show target state
  const targetElement = elements[`${state}State`];
  if (targetElement) {
    targetElement.classList.remove('hidden');
    targetElement.classList.add('animate-fade-in');
  }
  
  popupState.currentState = state;
  
  // Clear timer if switching away from active request
  if (state !== 'activeRequest' && popupState.timer) {
    clearInterval(popupState.timer);
    popupState.timer = null;
  }
}

// Show active request
function showActiveRequest(request) {
  popupState.currentRequest = request;
  
  // Update request details
  if (elements.requestEmail) {
    elements.requestEmail.textContent = request.email || 'Unknown';
  }
  
  if (elements.requestWebsite) {
    elements.requestWebsite.textContent = request.website || 'Unknown';
  }
  
  // Start timer
  startRequestTimer(request.expires_in || 300);
  
  showState('activeRequest');
}

// Start request timer
function startRequestTimer(duration) {
  let timeLeft = duration;
  
  const updateTimer = () => {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    
    if (elements.requestTimer) {
      elements.requestTimer.textContent = timeString;
    }
    
    // Update progress bar
    const progress = ((duration - timeLeft) / duration) * 100;
    if (elements.requestProgress) {
      elements.requestProgress.style.width = `${progress}%`;
    }
    
    timeLeft--;
    
    if (timeLeft < 0) {
      clearInterval(popupState.timer);
      popupState.timer = null;
      showState('idle');
    }
  };
  
  updateTimer();
  popupState.timer = setInterval(updateTimer, 1000);
}

// Handle approve
async function handleApprove() {
  try {
    if (!popupState.currentRequest) return;
    
    // Add loading state to button
    elements.approveBtn.classList.add('loading');
    elements.approveBtn.disabled = true;
    
    const response = await chrome.runtime.sendMessage({
      type: 'OTP_RESPONSE',
      data: {
        requestId: popupState.currentRequest.id,
        action: 'APPROVE'
      }
    });
    
    if (response.success) {
      showState('success');
      
      // Auto-close after success
      setTimeout(() => {
        window.close();
      }, 2000);
    } else {
      throw new Error(response.error || 'Failed to approve request');
    }
  } catch (error) {
    console.error('Failed to approve request:', error);
    showError('Failed to approve request');
  } finally {
    elements.approveBtn.classList.remove('loading');
    elements.approveBtn.disabled = false;
  }
}

// Handle reject
async function handleReject() {
  try {
    if (!popupState.currentRequest) return;
    
    // Add loading state to button
    elements.rejectBtn.classList.add('loading');
    elements.rejectBtn.disabled = true;
    
    const response = await chrome.runtime.sendMessage({
      type: 'OTP_RESPONSE',
      data: {
        requestId: popupState.currentRequest.id,
        action: 'REJECT'
      }
    });
    
    if (response.success) {
      showState('idle');
    } else {
      throw new Error(response.error || 'Failed to reject request');
    }
  } catch (error) {
    console.error('Failed to reject request:', error);
    showError('Failed to reject request');
  } finally {
    elements.rejectBtn.classList.remove('loading');
    elements.rejectBtn.disabled = false;
  }
}

// Handle test connection
async function handleTestConnection() {
  try {
    elements.testConnectionBtn.classList.add('loading');
    elements.testConnectionBtn.disabled = true;
    
    // Test connection to background script
    const response = await chrome.runtime.sendMessage({
      type: 'GET_EXTENSION_STATUS'
    });
    
    if (response.success) {
      // Show success feedback
      showNotification('Connection test successful!', 'success');
      updateConnectionStatus(true);
    } else {
      throw new Error(response.error || 'Connection test failed');
    }
  } catch (error) {
    console.error('Connection test failed:', error);
    showNotification('Connection test failed', 'error');
    updateConnectionStatus(false);
  } finally {
    elements.testConnectionBtn.classList.remove('loading');
    elements.testConnectionBtn.disabled = false;
  }
}

// Handle retry
function handleRetry() {
  checkExtensionStatus();
}

// Load settings
async function loadSettings() {
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'GET_SETTINGS'
    });
    
    if (response.success) {
      popupState.settings = response.data;
      updateSettingsUI();
    }
  } catch (error) {
    console.error('Failed to load settings:', error);
  }
}

// Update settings UI
function updateSettingsUI() {
  if (elements.autoApproveToggle) {
    elements.autoApproveToggle.checked = popupState.settings.autoApprove;
  }
  
  if (elements.notificationSoundToggle) {
    elements.notificationSoundToggle.checked = popupState.settings.notificationSound;
  }
}

// Open settings modal
function openSettings() {
  elements.settingsModal?.classList.remove('hidden');
  elements.settingsModal?.classList.add('animate-fade-in');
}

// Close settings modal
function closeSettings() {
  elements.settingsModal?.classList.add('hidden');
  elements.settingsModal?.classList.remove('animate-fade-in');
}

// Save settings
async function saveSettings() {
  try {
    const newSettings = {
      autoApprove: elements.autoApproveToggle?.checked || false,
      notificationSound: elements.notificationSoundToggle?.checked || false
    };
    
    const response = await chrome.runtime.sendMessage({
      type: 'UPDATE_SETTINGS',
      data: newSettings
    });
    
    if (response.success) {
      popupState.settings = response.data;
      showNotification('Settings saved successfully!', 'success');
      closeSettings();
    } else {
      throw new Error(response.error || 'Failed to save settings');
    }
  } catch (error) {
    console.error('Failed to save settings:', error);
    showNotification('Failed to save settings', 'error');
  }
}

// Open help
function openHelp() {
  chrome.tabs.create({
    url: 'https://progressdashboard.com/help/chrome-extension'
  });
}

// Show error
function showError(message) {
  if (elements.errorMessage) {
    elements.errorMessage.textContent = message;
  }
  showState('error');
}

// Update connection status
function updateConnectionStatus(connected) {
  if (elements.connectionStatus) {
    if (connected) {
      elements.connectionStatus.classList.remove('disconnected');
      elements.connectionStatus.innerHTML = `
        <span class="status-dot"></span>
        Connected
      `;
    } else {
      elements.connectionStatus.classList.add('disconnected');
      elements.connectionStatus.innerHTML = `
        <span class="status-dot"></span>
        Disconnected
      `;
    }
  }
}

// Show notification (simple toast)
function showNotification(message, type = 'info') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification notification-${type} animate-slide-in-right`;
  notification.innerHTML = `
    <div class="p-3">
      <p class="text-sm font-medium">${message}</p>
    </div>
  `;
  
  // Add to body
  document.body.appendChild(notification);
  
  // Remove after 3 seconds
  setTimeout(() => {
    notification.classList.add('animate-slide-out-right');
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 300);
  }, 3000);
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
  if (popupState.currentState === 'activeRequest') {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleApprove();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleReject();
    }
  }
  
  if (e.key === 'Escape' && !elements.settingsModal?.classList.contains('hidden')) {
    closeSettings();
  }
});

console.log('🎨 Popup script loaded');
