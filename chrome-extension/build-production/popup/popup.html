<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Progress Dashboard - OTP Authenticator</title>
  
  <!-- Styles -->
  <link rel="stylesheet" href="../styles/design-tokens.css">
  <link rel="stylesheet" href="../styles/animations.css">
  <link rel="stylesheet" href="../styles/components.css">
  <link rel="stylesheet" href="popup.css">
  
  <!-- Preload fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Main Container -->
  <div id="app" class="app-container">
    
    <!-- Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="brand">
          <div class="brand-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="brand-text">
            <h1 class="brand-title">Progress Dashboard</h1>
            <p class="brand-subtitle">OTP Authenticator</p>
          </div>
        </div>
        
        <div class="header-actions">
          <button id="settingsBtn" class="btn btn-ghost btn-sm" title="Settings">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="app-main">
      
      <!-- Loading State -->
      <div id="loadingState" class="state-container animate-fade-in">
        <div class="loading-content">
          <div class="loading-spinner animate-spin">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 12a9 9 0 11-6.219-8.56" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
          <p class="loading-text">Initializing extension<span class="loading-dots"></span></p>
        </div>
      </div>

      <!-- Idle State -->
      <div id="idleState" class="state-container hidden">
        <div class="idle-content">
          <div class="status-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <h2 class="status-title">Ready for Authentication</h2>
          <p class="status-description">
            Your OTP authenticator is ready. Visit Progress Dashboard and click "Send OTP" to begin authentication.
          </p>
          
          <div class="quick-actions">
            <button id="testConnectionBtn" class="btn btn-secondary btn-full">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 11.08V12a10 10 0 11-5.93-9.14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M22 4L12 14.01l-3-3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              Test Connection
            </button>
          </div>
        </div>
      </div>

      <!-- Active Request State -->
      <div id="activeRequestState" class="state-container hidden">
        <div class="request-content">
          <div class="request-header">
            <div class="request-icon animate-pulse">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1v6m0 6v6m6-12h-6m-6 0h6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h2 class="request-title">Login Request</h2>
          </div>
          
          <div class="request-details">
            <div class="detail-item">
              <span class="detail-label">Email:</span>
              <span id="requestEmail" class="detail-value"><EMAIL></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Website:</span>
              <span id="requestWebsite" class="detail-value">localhost:5174</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Time remaining:</span>
              <span id="requestTimer" class="detail-value timer">4:59</span>
            </div>
          </div>
          
          <div class="progress-container">
            <div class="progress">
              <div id="requestProgress" class="progress-bar"></div>
            </div>
          </div>
          
          <div class="request-actions">
            <button id="approveBtn" class="btn btn-primary btn-full hover-lift">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              Approve Login
            </button>
            <button id="rejectBtn" class="btn btn-secondary btn-full hover-lift">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              Reject
            </button>
          </div>
        </div>
      </div>

      <!-- Success State -->
      <div id="successState" class="state-container hidden">
        <div class="success-content">
          <div class="success-icon animate-scale-in">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <h2 class="success-title">Login Approved!</h2>
          <p class="success-description">
            You have successfully approved the login request. You can now close this popup.
          </p>
        </div>
      </div>

      <!-- Error State -->
      <div id="errorState" class="state-container hidden">
        <div class="error-content">
          <div class="error-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
              <path d="M15 9l-6 6M9 9l6 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h2 class="error-title">Connection Error</h2>
          <p id="errorMessage" class="error-description">
            Unable to connect to Progress Dashboard. Please check your connection and try again.
          </p>
          
          <div class="error-actions">
            <button id="retryBtn" class="btn btn-primary btn-full">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 4v6h6M23 20v-6h-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20.49 9A9 9 0 005.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 013.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              Retry
            </button>
          </div>
        </div>
      </div>

    </main>

    <!-- Footer -->
    <footer class="app-footer">
      <div class="footer-content">
        <div class="extension-info">
          <span class="version">v1.0.0</span>
          <span class="status" id="connectionStatus">
            <span class="status-dot"></span>
            Connected
          </span>
        </div>
        
        <div class="footer-links">
          <button id="helpBtn" class="btn btn-ghost btn-sm">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
              <path d="M9.09 9a3 3 0 015.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 17h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Help
          </button>
        </div>
      </div>
    </footer>

  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" class="modal hidden">
    <div class="modal-overlay"></div>
    <div class="modal-content animate-scale-in">
      <div class="modal-header">
        <h3 class="modal-title">Extension Settings</h3>
        <button id="closeSettingsBtn" class="btn btn-ghost btn-sm">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
      
      <div class="modal-body">
        <div class="setting-group">
          <label class="setting-label">
            <input type="checkbox" id="autoApproveToggle" class="setting-checkbox">
            <span class="setting-text">
              <strong>Auto-approve requests (Development)</strong>
              <small>Automatically approve OTP requests for testing</small>
            </span>
          </label>
        </div>
        
        <div class="setting-group">
          <label class="setting-label">
            <input type="checkbox" id="notificationSoundToggle" class="setting-checkbox">
            <span class="setting-text">
              <strong>Notification sounds</strong>
              <small>Play sound when receiving OTP requests</small>
            </span>
          </label>
        </div>
      </div>
      
      <div class="modal-footer">
        <button id="saveSettingsBtn" class="btn btn-primary">Save Settings</button>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="popup.js"></script>
</body>
</html>
