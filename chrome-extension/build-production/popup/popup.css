/**
 * Popup Styles for Progress Dashboard Chrome Extension
 * 
 * Modern, dynamic UI with smooth animations
 */

/* App Container */
.app-container {
  width: var(--extension-width);
  min-height: 580px;
  max-height: var(--extension-height);
  background: linear-gradient(135deg, var(--color-seashell) 0%, #ffffff 50%, var(--color-primary-50) 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.app-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  opacity: 0.1;
  z-index: 0;
}

/* Header */
.app-header {
  position: relative;
  z-index: 1;
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  color: white;
  padding: var(--spacing-4);
  box-shadow: var(--shadow-lg);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.brand-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.brand-icon svg {
  color: white;
}

.brand-text {
  flex: 1;
}

.brand-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-lg);
  margin: 0;
}

.brand-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  margin: 0;
  font-weight: var(--font-weight-medium);
}

.header-actions {
  display: flex;
  gap: var(--spacing-2);
}

.header-actions .btn {
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.header-actions .btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Main Content */
.app-main {
  flex: 1;
  position: relative;
  z-index: 1;
  padding: var(--spacing-8) var(--spacing-6);
  overflow-y: auto;
}

/* State Containers */
.state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 380px;
}

.state-container.hidden {
  display: none;
}

/* Loading State */
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
}

.loading-spinner {
  color: var(--color-primary-500);
}

.loading-text {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Idle State */
.idle-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-6);
  max-width: 360px;
}

.status-icon {
  width: 96px;
  height: 96px;
  background: linear-gradient(135deg, var(--color-success-500), var(--color-success-600));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-lg);
  animation: pulse 2s infinite;
}

.status-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
}

.status-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-lg);
  margin: 0;
}

.quick-actions {
  width: 100%;
  margin-top: var(--spacing-2);
}

/* Active Request State */
.request-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
  width: 100%;
  max-width: 380px;
}

.request-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-3);
}

.request-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-lg);
}

.request-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
}

.request-details {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  box-shadow: var(--shadow-sm);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.detail-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-semibold);
}

.detail-value.timer {
  color: var(--color-warning-600);
  font-family: var(--font-family-mono);
}

.progress-container {
  margin: var(--spacing-2) 0;
}

.request-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

/* Success State */
.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
  max-width: 280px;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-success-500), var(--color-success-600));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-lg);
}

.success-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-success-600);
  margin: 0;
}

.success-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-lg);
  margin: 0;
}

/* Error State */
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
  max-width: 280px;
}

.error-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-error-500), var(--color-error-600));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-lg);
}

.error-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-error-600);
  margin: 0;
}

.error-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-lg);
  margin: 0;
}

.error-actions {
  width: 100%;
  margin-top: var(--spacing-2);
}

/* Footer */
.app-footer {
  position: relative;
  z-index: 1;
  background: var(--color-surface);
  border-top: 1px solid var(--color-border);
  padding: var(--spacing-3) var(--spacing-4);
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.extension-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.version {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.status {
  display: flex;
  align-items: center;
  gap: var(--spacing-1-5);
  font-size: var(--font-size-xs);
  color: var(--color-success-600);
  font-weight: var(--font-weight-medium);
}

.status-dot {
  width: 6px;
  height: 6px;
  background: var(--color-success-500);
  border-radius: var(--radius-full);
  animation: pulse 2s infinite;
}

.status.disconnected {
  color: var(--color-error-600);
}

.status.disconnected .status-dot {
  background: var(--color-error-500);
}

.footer-links {
  display: flex;
  gap: var(--spacing-2);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal.hidden {
  display: none;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: var(--color-surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  width: 90%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--color-border);
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.modal-body {
  padding: var(--spacing-4);
  max-height: 300px;
  overflow-y: auto;
}

.modal-footer {
  padding: var(--spacing-4);
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

/* Settings */
.setting-group {
  margin-bottom: var(--spacing-4);
}

.setting-label {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  cursor: pointer;
  padding: var(--spacing-3);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--easing-ease-in-out);
}

.setting-label:hover {
  background: var(--color-secondary-50);
}

.setting-checkbox {
  width: 18px;
  height: 18px;
  border: 2px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-surface);
  cursor: pointer;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
}

.setting-checkbox:checked {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

.setting-checkbox:checked::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 5px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.setting-text {
  flex: 1;
}

.setting-text strong {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

.setting-text small {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  line-height: var(--line-height-sm);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .app-container {
    width: 100vw;
    min-height: 100vh;
  }

  .app-main {
    padding: var(--spacing-6) var(--spacing-4);
  }

  .request-content,
  .idle-content,
  .success-content,
  .error-content {
    max-width: 100%;
    padding: 0 var(--spacing-2);
  }

  .state-container {
    min-height: 60vh;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .app-container {
    background: linear-gradient(135deg, var(--color-dark-800) 0%, var(--color-dark-700) 50%, var(--color-dark-600) 100%);
  }
  
  .app-container::before {
    opacity: 0.2;
  }
}
