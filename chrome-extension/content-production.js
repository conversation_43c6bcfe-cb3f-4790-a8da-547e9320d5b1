/**
 * Production Content Script for Progress Dashboard Chrome Extension
 * Robust communication bridge with comprehensive error handling
 */

// Environment detection
let isProduction = false; // Force development mode for better logging
try {
  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
    const manifest = chrome.runtime.getManifest();
    isProduction = manifest.update_url !== undefined; // Production extensions have update_url
  }
} catch (error) {
  console.warn('[Extension] Environment detection failed:', error.message);
}

// Enhanced logging for debugging
const logger = {
  error: (message, data) => console.error(`[Extension] ${message}`, data || ''),
  warn: (message, data) => console.warn(`[Extension] ${message}`, data || ''),
  info: (message) => console.log(`[Extension] ${message}`),
  debug: (message, data) => {
    if (!isProduction) {
      console.log(`[Extension Debug] ${message}`, data || '');
    }
  }
};

// Content script state
let contentState = {
  isInjected: false,
  messageQueue: [],
  extensionReady: false,
  contextValid: true
};

// Helper function to check if extension context is valid
function isExtensionContextValid() {
  try {
    if (!chrome || !chrome.runtime || !chrome.runtime.id) {
      contentState.contextValid = false;
      return false;
    }
    // Try to access runtime to test if context is valid
    const id = chrome.runtime.id;
    contentState.contextValid = true;
    return true;
  } catch (error) {
    console.warn('[CONTENT] Extension context validation failed:', error.message);
    contentState.contextValid = false;
    return false;
  }
}

// Initialize content script
function initializeContentScript() {
  if (contentState.isInjected) {
    logger.debug('Content script already injected');
    return;
  }

  logger.info('Initializing content script...');

  try {
    // Set DOM marker
    document.documentElement.setAttribute('data-progress-dashboard-extension', 'loaded');
    logger.debug('DOM marker set');

    // Set content script marker
    window.progressDashboardContentScript = true;
    logger.debug('Content script marker set');

    // Create extension bridge with enhanced communication
    window.progressDashboardExtension = {
      loaded: true,
      timestamp: Date.now(),
      version: '1.0.0',

      sendMessage: function(message) {
        return new Promise((resolve, reject) => {
          try {
            // Add message to queue for debugging
            contentState.messageQueue.push({
              message,
              timestamp: Date.now(),
              type: 'outgoing'
            });

            chrome.runtime.sendMessage(message, (response) => {
              if (chrome.runtime.lastError) {
                logger.error('Extension communication error', chrome.runtime.lastError.message);
                reject(new Error(chrome.runtime.lastError.message));
                return;
              }

              if (response && response.success) {
                logger.debug('Message sent successfully', response);
                resolve(response);
              } else {
                const error = response?.error || 'Unknown error';
                logger.warn('Extension returned error', error);
                reject(new Error(error));
              }
            });
          } catch (error) {
            logger.error('Failed to send message to background script', error.message);
            reject(error);
          }
        });
      },

      getDebugInfo: function() {
        return {
          version: '1.0.0',
          isAvailable: true,
          injectionMethod: 'production',
          timestamp: this.timestamp,
          environment: isProduction ? 'production' : 'development',
          messageQueue: contentState.messageQueue.slice(-5), // Last 5 messages
          state: contentState
        };
      },

      isAvailable: function() {
        return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
      }
    };

    // Set ready flag
    window.progressDashboardExtensionReady = true;
    logger.debug('Ready flag set');

    // Dispatch ready event
    const readyEvent = new CustomEvent('progressDashboardExtensionReady', {
      detail: {
        version: '1.0.0',
        timestamp: Date.now(),
        environment: isProduction ? 'production' : 'development'
      }
    });
    window.dispatchEvent(readyEvent);
    logger.debug('Ready event dispatched');

    // Post ready message
    window.postMessage({
      type: 'PROGRESS_DASHBOARD_READY',
      data: {
        extensionLoaded: true,
        version: '1.0.0',
        timestamp: Date.now(),
        environment: isProduction ? 'production' : 'development'
      }
    }, window.location.origin);
    logger.debug('Ready message posted');

    // Set up message listeners
    setupMessageListeners();

    // Mark as injected
    contentState.isInjected = true;
    contentState.extensionReady = true;

    logger.info('Content script initialized successfully');

  } catch (error) {
    logger.error('Failed to initialize content script', error.message);
  }
}

// Set up message listeners
function setupMessageListeners() {
  // Listen for messages from web page
  window.addEventListener('message', handlePageMessage);

  // Listen for messages from background script
  chrome.runtime.onMessage.addListener(handleBackgroundMessage);

  logger.debug('Message listeners set up');
}

// Handle messages from web page
function handlePageMessage(event) {
  // Validate origin
  if (event.origin !== window.location.origin) {
    return;
  }

  // Handle extension check (CRITICAL for detection)
  if (event.data && event.data.type === 'EXTENSION_CHECK') {
    logger.debug('Extension check received from page');

    try {
      // Check if extension context is still valid
      if (!chrome.runtime || !chrome.runtime.id) {
        throw new Error('Extension context invalidated');
      }

      window.postMessage({
        type: 'EXTENSION_CHECK_RESPONSE',
        extensionId: chrome.runtime.id,
        version: chrome.runtime.getManifest().version
      }, window.location.origin);
    } catch (error) {
      console.warn('[CONTENT] Extension context invalid, sending fallback response');
      window.postMessage({
        type: 'EXTENSION_CHECK_RESPONSE',
        extensionId: 'context-invalidated',
        version: 'unknown',
        error: 'Extension context invalidated - please reload page'
      }, window.location.origin);
    }
    return;
  }

  // Handle OTP requests
  if (event.data && event.data.type === 'OTP_REQUEST') {
    console.log('[CONTENT] OTP request received from page:', event.data);
    logger.debug('OTP request received from page', event.data);

    try {
      // Check if extension context is still valid
      if (!chrome.runtime || !chrome.runtime.id) {
        throw new Error('Extension context invalidated');
      }

      const message = {
        type: 'SHOW_OTP_POPUP',
        email: event.data.email,
        otp: event.data.otp,
        secret: event.data.secret,
      };

      console.log('[CONTENT] Sending message to background:', message);

      chrome.runtime.sendMessage(message).then(response => {
        console.log('[CONTENT] Response from background:', response);
        logger.debug('OTP request forwarded to background', response);
      }).catch(error => {
        console.error('[CONTENT] Failed to forward OTP request:', error);
        logger.error('Failed to forward OTP request', error.message);

        // Send error response back to page
        window.postMessage({
          type: 'OTP_RESPONSE',
          action: 'ERROR',
          error: 'Extension communication failed'
        }, window.location.origin);
      });
    } catch (error) {
      console.error('[CONTENT] Extension context invalid:', error);

      // Check if extension context is really invalid
      if (error.message.includes('Extension context invalidated') ||
          error.message.includes('message port closed') ||
          !chrome.runtime?.id) {

        console.log('[CONTENT] Extension context lost, attempting recovery...');

        // Mark content script as needing reload
        contentState.extensionReady = false;
        contentState.isInjected = false;

        // Send error response back to page
        window.postMessage({
          type: 'OTP_RESPONSE',
          action: 'ERROR',
          error: 'Extension disconnected - please reload page or extension'
        }, window.location.origin);

        // Try to reinitialize after a delay
        setTimeout(() => {
          console.log('[CONTENT] Attempting to reinitialize...');
          initializeContentScript();
        }, 1000);

      } else {
        // Other errors
        window.postMessage({
          type: 'OTP_RESPONSE',
          action: 'ERROR',
          error: 'Extension communication failed'
        }, window.location.origin);
      }
    }
    return;
  }

  // Handle legacy TO_EXTENSION messages
  if (event.data && event.data.type === 'TO_EXTENSION') {
    logger.debug('Received message from page', event.data);

    // Check if extension context is still valid
    if (!isExtensionContextValid()) {
      console.log('[CONTENT] Extension context invalid for legacy message');
      window.postMessage({
        type: 'FROM_EXTENSION',
        data: { success: false, error: 'Extension context lost - please reload page' },
        requestId: event.data.requestId
      }, window.location.origin);
      return;
    }

    // Forward to background script
    chrome.runtime.sendMessage(event.data.data)
      .then(response => {
        // Send response back to page
        window.postMessage({
          type: 'FROM_EXTENSION',
          data: response,
          requestId: event.data.requestId
        }, window.location.origin);
      })
      .catch(error => {
        logger.error('Failed to forward message to background', error.message);

        // Send error response back to page
        window.postMessage({
          type: 'FROM_EXTENSION',
          data: { success: false, error: error.message },
          requestId: event.data.requestId
        }, window.location.origin);
      });
  }
}

// Handle messages from background script
function handleBackgroundMessage(message, sender, sendResponse) {
  logger.debug('Received message from background', message);

  switch (message.type) {
    case 'FORWARD_TO_PAGE':
      window.postMessage(message.data, window.location.origin);
      sendResponse({ success: true });
      break;

    case 'EXTENSION_STATUS_REQUEST':
      sendResponse({
        success: true,
        data: {
          isInjected: contentState.isInjected,
          url: window.location.href,
          ready: contentState.extensionReady,
          timestamp: Date.now()
        }
      });
      break;

    default:
      logger.warn('Unknown message type from background:', message.type);
      sendResponse({ success: false, error: 'Unknown message type' });
  }
}

// Check if extension context is valid
function isExtensionContextValid() {
  try {
    // Check if chrome.runtime is available and has an ID
    return !!(chrome.runtime && chrome.runtime.id);
  } catch (error) {
    console.log('[CONTENT] Extension context check failed:', error.message);
    return false;
  }
}

// Check if page is valid for injection
function isValidPage() {
  const url = window.location.href;
  const validPatterns = [
    /^http:\/\/localhost:/,
    /^https:\/\/localhost:/,
    /^https:\/\/.*\.progressdashboard\.com/
  ];

  return validPatterns.some(pattern => pattern.test(url));
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    if (isValidPage() && isExtensionContextValid()) {
      initializeContentScript();
    } else if (isValidPage() && !isExtensionContextValid()) {
      console.warn('[CONTENT] Extension context invalid on DOM ready');
    }
  });
} else {
  // DOM already loaded
  if (isValidPage() && isExtensionContextValid()) {
    initializeContentScript();
  } else if (isValidPage() && !isExtensionContextValid()) {
    console.warn('[CONTENT] Extension context invalid on immediate init');
  }
}

// Also initialize immediately for document_start
if (isValidPage() && isExtensionContextValid()) {
  initializeContentScript();
} else if (isValidPage() && !isExtensionContextValid()) {
  console.warn('[CONTENT] Extension context invalid on immediate start');
}

logger.info('Content script loaded and ready');
