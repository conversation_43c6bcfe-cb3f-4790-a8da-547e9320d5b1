<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Debug Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .step {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Chrome Extension Debug Tool</h1>
        
        <div id="status-container">
            <div class="status info">
                <h3>🔍 Checking Extension Status...</h3>
                <p>Please wait while we analyze your Chrome Extension setup.</p>
            </div>
        </div>

        <div class="step">
            <h3>Quick Actions</h3>
            <button onclick="checkExtension()">🔍 Check Extension</button>
            <button onclick="testCommunication()">📡 Test Communication</button>
            <button onclick="openExtensionsPage()">⚙️ Open Extensions Page</button>
            <button onclick="refreshPage()">🔄 Refresh Page</button>
        </div>

        <div id="debug-info-container"></div>

        <div class="step">
            <h3>📋 Manual Installation Steps</h3>
            <ol>
                <li><strong>Open Chrome Extensions:</strong> <code>chrome://extensions/</code></li>
                <li><strong>Enable Developer Mode:</strong> Toggle switch in top-right</li>
                <li><strong>Click "Load unpacked"</strong></li>
                <li><strong>Select folder:</strong> <code>chrome-extension</code> (NOT build-production)</li>
                <li><strong>Verify extension appears</strong> with toggle ON</li>
                <li><strong>Refresh this page</strong> and test again</li>
            </ol>
        </div>

        <div class="step">
            <h3>🐛 Common Issues & Solutions</h3>
            <div id="troubleshooting"></div>
        </div>
    </div>

    <script>
        let extensionStatus = {
            loaded: false,
            available: false,
            connected: false,
            version: 'unknown'
        };

        function checkExtension() {
            console.log('=== EXTENSION DEBUG CHECK ===');
            
            const results = {
                windowObject: typeof window.progressDashboardExtension !== 'undefined',
                contentScript: window.progressDashboardContentScript === true,
                readyFlag: window.progressDashboardExtensionReady === true,
                domAttribute: document.documentElement.getAttribute('data-progress-dashboard-extension'),
                chromeRuntime: typeof chrome !== 'undefined' && chrome.runtime,
                url: window.location.href
            };

            console.log('Extension check results:', results);

            let debugInfo = '';
            let statusHtml = '';

            if (results.windowObject) {
                statusHtml += '<div class="status success"><h3>✅ Extension Bridge Found</h3><p>Extension communication bridge is available.</p></div>';
                
                const ext = window.progressDashboardExtension;
                if (ext.getDebugInfo) {
                    const info = ext.getDebugInfo();
                    debugInfo += `Extension Debug Info:\n${JSON.stringify(info, null, 2)}\n\n`;
                    
                    extensionStatus = {
                        loaded: true,
                        available: ext.isAvailable ? ext.isAvailable() : true,
                        connected: true,
                        version: info.version || '1.0.0'
                    };
                }
            } else {
                statusHtml += '<div class="status error"><h3>❌ Extension Bridge Missing</h3><p>Extension communication bridge not found.</p></div>';
            }

            if (results.contentScript) {
                statusHtml += '<div class="status success"><h3>✅ Content Script Loaded</h3><p>Content script marker is present.</p></div>';
            } else {
                statusHtml += '<div class="status error"><h3>❌ Content Script Not Loaded</h3><p>Content script marker missing.</p></div>';
            }

            if (results.domAttribute) {
                statusHtml += '<div class="status success"><h3>✅ DOM Marker Present</h3><p>Extension DOM attribute: ' + results.domAttribute + '</p></div>';
            } else {
                statusHtml += '<div class="status error"><h3>❌ DOM Marker Missing</h3><p>Extension DOM attribute not found.</p></div>';
            }

            if (results.chromeRuntime) {
                statusHtml += '<div class="status success"><h3>✅ Chrome Runtime Available</h3><p>Chrome extension APIs are accessible.</p></div>';
            } else {
                statusHtml += '<div class="status error"><h3>❌ Chrome Runtime Missing</h3><p>Chrome extension APIs not available.</p></div>';
            }

            debugInfo += `Full Check Results:\n${JSON.stringify(results, null, 2)}\n\n`;
            debugInfo += `Extension Status:\n${JSON.stringify(extensionStatus, null, 2)}`;

            document.getElementById('status-container').innerHTML = statusHtml;
            document.getElementById('debug-info-container').innerHTML = 
                '<h3>🔍 Debug Information</h3><div class="debug-info">' + debugInfo + '</div>';

            updateTroubleshooting(results);
        }

        function testCommunication() {
            if (!window.progressDashboardExtension) {
                alert('❌ Extension not available for testing');
                return;
            }

            const testMessage = {
                type: 'GET_EXTENSION_STATUS',
                timestamp: Date.now()
            };

            console.log('Testing extension communication...');

            window.progressDashboardExtension.sendMessage(testMessage)
                .then(response => {
                    console.log('✅ Communication test successful:', response);
                    alert('✅ Extension communication successful!\n\n' + JSON.stringify(response, null, 2));
                })
                .catch(error => {
                    console.error('❌ Communication test failed:', error);
                    alert('❌ Extension communication failed:\n\n' + error.message);
                });
        }

        function openExtensionsPage() {
            window.open('chrome://extensions/', '_blank');
        }

        function refreshPage() {
            window.location.reload();
        }

        function updateTroubleshooting(results) {
            let troubleshooting = '';

            if (!results.windowObject && !results.contentScript) {
                troubleshooting += `
                    <div class="status error">
                        <h4>🚨 Extension Not Loaded</h4>
                        <p><strong>Problem:</strong> Extension is not loaded in Chrome</p>
                        <p><strong>Solution:</strong></p>
                        <ol>
                            <li>Go to <code>chrome://extensions/</code></li>
                            <li>Make sure "Progress Dashboard OTP Authenticator" is present and enabled</li>
                            <li>If not present, click "Load unpacked" and select <code>chrome-extension</code> folder</li>
                            <li>Refresh this page</li>
                        </ol>
                    </div>
                `;
            }

            if (results.windowObject && !results.contentScript) {
                troubleshooting += `
                    <div class="status warning">
                        <h4>⚠️ Partial Extension Load</h4>
                        <p><strong>Problem:</strong> Extension bridge exists but content script marker missing</p>
                        <p><strong>Solution:</strong> Reload the extension and refresh this page</p>
                    </div>
                `;
            }

            if (!results.chromeRuntime) {
                troubleshooting += `
                    <div class="status error">
                        <h4>🚨 Chrome Runtime Missing</h4>
                        <p><strong>Problem:</strong> Chrome extension APIs not available</p>
                        <p><strong>Solution:</strong> Make sure you're using Chrome browser and extension is properly loaded</p>
                    </div>
                `;
            }

            if (results.url.includes('localhost') && !results.windowObject) {
                troubleshooting += `
                    <div class="status warning">
                        <h4>⚠️ Localhost Permission Issue</h4>
                        <p><strong>Problem:</strong> Extension might not have localhost permissions</p>
                        <p><strong>Solution:</strong> Make sure you loaded the development version (chrome-extension folder), not production version</p>
                    </div>
                `;
            }

            if (troubleshooting === '') {
                troubleshooting = '<div class="status success"><h4>✅ No Issues Detected</h4><p>Extension appears to be working correctly!</p></div>';
            }

            document.getElementById('troubleshooting').innerHTML = troubleshooting;
        }

        // Auto-check on page load
        window.addEventListener('load', () => {
            setTimeout(checkExtension, 1000);
        });

        // Listen for extension ready event
        window.addEventListener('progressDashboardExtensionReady', (event) => {
            console.log('🎉 Extension ready event received:', event.detail);
            setTimeout(checkExtension, 500);
        });
    </script>
</body>
</html>
