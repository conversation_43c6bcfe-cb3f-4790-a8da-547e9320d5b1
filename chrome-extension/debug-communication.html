<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Communication Debugger</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #005a9e; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .test-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
        }
        
        .log-area {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .origin-test {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 5px 0;
        }
        
        .origin-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
        }
        .origin-allowed { background: #28a745; }
        .origin-blocked { background: #dc3545; }
        .origin-unknown { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Extension Communication Debugger</h1>
        <p>Comprehensive tool untuk mendiagnosis masalah komunikasi Chrome Extension</p>
        
        <div class="status" id="main-status">
            🔄 Initializing debugger...
        </div>
    </div>

    <div class="container">
        <h2>📍 Environment Information</h2>
        <div id="env-info"></div>
    </div>

    <div class="container">
        <h2>🌐 Origin Validation Tests</h2>
        <div id="origin-tests"></div>
        <button onclick="testOriginValidation()">Test All Origins</button>
    </div>

    <div class="container">
        <h2>🔌 Extension Detection</h2>
        <div id="extension-detection"></div>
        <button onclick="testExtensionDetection()">Detect Extension</button>
    </div>

    <div class="container">
        <h2>📨 Message Communication Tests</h2>
        <div class="test-grid">
            <div class="test-card">
                <h3>Content Script Injection</h3>
                <div id="content-script-test"></div>
                <button onclick="testContentScript()">Test Content Script</button>
            </div>
            
            <div class="test-card">
                <h3>Background Communication</h3>
                <div id="background-test"></div>
                <button onclick="testBackgroundComm()">Test Background</button>
            </div>
            
            <div class="test-card">
                <h3>Tab Query Test</h3>
                <div id="tab-query-test"></div>
                <button onclick="testTabQuery()">Test Tab Query</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🚀 OTP Flow Test</h2>
        <div id="otp-test"></div>
        <button onclick="testOTPFlow()">Test Complete OTP Flow</button>
    </div>

    <div class="container">
        <h2>📋 Debug Log</h2>
        <div class="log-area" id="debug-log"></div>
        <button onclick="clearLog()">Clear Log</button>
        <button onclick="exportLog()">Export Log</button>
    </div>

    <script>
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            debugLog.push(logEntry);
            
            const logArea = document.getElementById('debug-log');
            logArea.textContent = debugLog.join('\n');
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(logEntry);
        }
        
        function clearLog() {
            debugLog = [];
            document.getElementById('debug-log').textContent = '';
        }
        
        function exportLog() {
            const blob = new Blob([debugLog.join('\n')], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `extension-debug-${Date.now()}.log`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = `status ${type}`;
                element.textContent = message;
            }
        }
        
        // Initialize environment info
        function initEnvironmentInfo() {
            const info = {
                'Current URL': window.location.href,
                'Origin': window.location.origin,
                'Protocol': window.location.protocol,
                'Host': window.location.host,
                'User Agent': navigator.userAgent,
                'Secure Context': window.isSecureContext,
                'Chrome Extension API': typeof chrome !== 'undefined',
                'Chrome Runtime': typeof chrome !== 'undefined' && !!chrome.runtime
            };
            
            let html = '<div class="test-grid">';
            for (const [key, value] of Object.entries(info)) {
                const status = value === true ? 'success' : value === false ? 'error' : 'info';
                html += `<div class="status ${status}">${key}: ${value}</div>`;
            }
            html += '</div>';
            
            document.getElementById('env-info').innerHTML = html;
            log('Environment information initialized');
        }
        
        // Test origin validation
        function testOriginValidation() {
            const origins = [
                'http://localhost:5173',  // Should be allowed (FIXED)
                'http://localhost:5174',  // Should be allowed
                'http://localhost:3000',  // Should be allowed
                'http://localhost:5001',  // Should be allowed
                'https://localhost:5173', // Should be allowed (FIXED)
                'https://progressdashboard.com', // Should be allowed
                'http://malicious-site.com', // Should be blocked
                'https://fake-dashboard.com' // Should be blocked
            ];
            
            let html = '<h3>Origin Validation Results:</h3>';
            
            origins.forEach(origin => {
                const isCurrentOrigin = origin === window.location.origin;
                const shouldBeAllowed = origin.includes('localhost') || origin.includes('progressdashboard.com');
                
                let statusClass = 'origin-unknown';
                let statusText = 'Unknown';
                
                if (shouldBeAllowed) {
                    statusClass = 'origin-allowed';
                    statusText = 'Should be allowed';
                } else {
                    statusClass = 'origin-blocked';
                    statusText = 'Should be blocked';
                }
                
                if (isCurrentOrigin) {
                    statusText += ' (CURRENT)';
                }
                
                html += `
                    <div class="origin-test">
                        <span class="origin-status ${statusClass}"></span>
                        <strong>${origin}</strong> - ${statusText}
                    </div>
                `;
            });
            
            document.getElementById('origin-tests').innerHTML = html;
            log('Origin validation test completed');
        }
        
        // Test extension detection
        function testExtensionDetection() {
            const tests = {
                'Chrome API Available': typeof chrome !== 'undefined',
                'Chrome Runtime': typeof chrome !== 'undefined' && !!chrome.runtime,
                'Extension Bridge': typeof window.progressDashboardExtension !== 'undefined',
                'Content Script Marker': window.progressDashboardContentScript === true,
                'Extension Ready Flag': window.progressDashboardExtensionReady === true,
                'DOM Attribute': document.documentElement.getAttribute('data-progress-dashboard-extension') !== null
            };
            
            let html = '<div class="test-grid">';
            for (const [test, result] of Object.entries(tests)) {
                const status = result ? 'success' : 'error';
                html += `<div class="status ${status}">${test}: ${result ? '✅ Pass' : '❌ Fail'}</div>`;
            }
            html += '</div>';
            
            document.getElementById('extension-detection').innerHTML = html;
            log('Extension detection test completed');
        }
        
        // Test content script
        function testContentScript() {
            updateStatus('content-script-test', '🔄 Testing content script injection...', 'info');
            
            // Test if content script is injected
            const hasContentScript = window.progressDashboardContentScript === true;
            const hasBridge = typeof window.progressDashboardExtension !== 'undefined';
            
            if (hasContentScript && hasBridge) {
                updateStatus('content-script-test', '✅ Content script properly injected', 'success');
                log('Content script test: PASS');
            } else {
                updateStatus('content-script-test', '❌ Content script not detected', 'error');
                log('Content script test: FAIL - Missing injection');
            }
        }
        
        // Test background communication
        function testBackgroundComm() {
            updateStatus('background-test', '🔄 Testing background communication...', 'info');
            
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                updateStatus('background-test', '❌ Chrome runtime not available', 'error');
                log('Background communication test: FAIL - No Chrome runtime');
                return;
            }
            
            try {
                chrome.runtime.sendMessage({ type: 'GET_EXTENSION_STATUS' }, (response) => {
                    if (chrome.runtime.lastError) {
                        updateStatus('background-test', `❌ Error: ${chrome.runtime.lastError.message}`, 'error');
                        log(`Background communication test: FAIL - ${chrome.runtime.lastError.message}`);
                    } else if (response && response.success) {
                        updateStatus('background-test', '✅ Background communication working', 'success');
                        log('Background communication test: PASS');
                    } else {
                        updateStatus('background-test', '❌ Invalid response from background', 'error');
                        log('Background communication test: FAIL - Invalid response');
                    }
                });
            } catch (error) {
                updateStatus('background-test', `❌ Exception: ${error.message}`, 'error');
                log(`Background communication test: FAIL - Exception: ${error.message}`);
            }
        }
        
        // Test tab query
        function testTabQuery() {
            updateStatus('tab-query-test', '🔄 Testing tab query...', 'info');
            
            if (typeof chrome === 'undefined' || !chrome.tabs) {
                updateStatus('tab-query-test', '❌ Chrome tabs API not available', 'error');
                log('Tab query test: FAIL - No Chrome tabs API');
                return;
            }
            
            try {
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (chrome.runtime.lastError) {
                        updateStatus('tab-query-test', `❌ Error: ${chrome.runtime.lastError.message}`, 'error');
                        log(`Tab query test: FAIL - ${chrome.runtime.lastError.message}`);
                    } else if (tabs && tabs.length > 0) {
                        updateStatus('tab-query-test', `✅ Found ${tabs.length} active tab(s)`, 'success');
                        log(`Tab query test: PASS - Found ${tabs.length} active tab(s)`);
                    } else {
                        updateStatus('tab-query-test', '❌ No active tabs found', 'error');
                        log('Tab query test: FAIL - No active tabs found');
                    }
                });
            } catch (error) {
                updateStatus('tab-query-test', `❌ Exception: ${error.message}`, 'error');
                log(`Tab query test: FAIL - Exception: ${error.message}`);
            }
        }
        
        // Test complete OTP flow
        function testOTPFlow() {
            updateStatus('otp-test', '🔄 Testing complete OTP flow...', 'info');
            
            const testData = {
                type: 'OTP_REQUEST',
                data: {
                    email: '<EMAIL>',
                    otp_code: '123456',
                    otp_key: 'test-key',
                    expires_in: 300,
                    website: window.location.hostname
                },
                timestamp: Date.now(),
                origin: window.location.origin
            };
            
            if (typeof window.progressDashboardExtension !== 'undefined') {
                try {
                    window.progressDashboardExtension.sendMessage(testData)
                        .then(response => {
                            updateStatus('otp-test', '✅ OTP flow test successful', 'success');
                            log('OTP flow test: PASS');
                        })
                        .catch(error => {
                            updateStatus('otp-test', `❌ OTP flow failed: ${error.message}`, 'error');
                            log(`OTP flow test: FAIL - ${error.message}`);
                        });
                } catch (error) {
                    updateStatus('otp-test', `❌ Exception: ${error.message}`, 'error');
                    log(`OTP flow test: FAIL - Exception: ${error.message}`);
                }
            } else {
                updateStatus('otp-test', '❌ Extension bridge not available', 'error');
                log('OTP flow test: FAIL - Extension bridge not available');
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            log('Communication debugger initialized');
            updateStatus('main-status', '✅ Debugger ready - Run tests to diagnose issues', 'success');
            
            initEnvironmentInfo();
            testOriginValidation();
            testExtensionDetection();
        });
    </script>
</body>
</html>
