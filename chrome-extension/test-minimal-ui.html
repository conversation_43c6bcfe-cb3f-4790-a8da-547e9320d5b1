<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Minimal UI Test - Progress Dashboard Extension</title>
  
  <!-- Minimal Styles -->
  <link rel="stylesheet" href="popup/popup-minimal.css">
  
  <style>
    body {
      background: #f1f5f9;
      padding: 20px;
      font-family: 'Inter', sans-serif;
    }
    
    .test-container {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
    }
    
    .test-title {
      color: #1A1919;
      margin-bottom: 30px;
    }
    
    .extension-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 30px;
      margin-bottom: 30px;
    }
    
    .extension-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .state-label {
      background: #22c55e;
      color: white;
      padding: 8px 16px;
      border-radius: 8px;
      font-weight: 600;
      margin-bottom: 15px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1 class="test-title">Minimal UI Extension</h1>
    <p style="color: #64748b; margin-bottom: 30px;">
      Simplified extension UI showing only connection status and basic functionality
    </p>
    
    <div class="extension-grid">
      <!-- Idle State -->
      <div class="extension-wrapper">
        <div class="state-label">Connected State</div>
        <div class="app-container state-idle">
          <main class="app-main">
            <div class="state-container">
              <div class="simple-message">
                <div class="connection-status connected">
                  <span class="status-dot connected"></span>
                  <span>Connected to Progress Dashboard</span>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
      
      <!-- Loading State -->
      <div class="extension-wrapper">
        <div class="state-label">Loading State</div>
        <div class="app-container state-loading">
          <main class="app-main">
            <div class="state-container">
              <div class="simple-message">
                <p>Connecting...</p>
              </div>
            </div>
          </main>
        </div>
      </div>
      
      <!-- Active Request State -->
      <div class="extension-wrapper">
        <div class="state-label">Authentication Request</div>
        <div class="app-container state-active">
          <main class="app-main">
            <div class="state-container">
              <div class="simple-message">
                <h3>Authentication Request</h3>
                <div class="request-info">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Website:</strong> localhost:5174</p>
                  <p><strong>Time:</strong> 4:59</p>
                </div>
                <div class="simple-actions">
                  <button class="simple-btn approve">Approve</button>
                  <button class="simple-btn reject">Reject</button>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
      
      <!-- Success State -->
      <div class="extension-wrapper">
        <div class="state-label">Success State</div>
        <div class="app-container state-success">
          <main class="app-main">
            <div class="state-container">
              <div class="simple-message">
                <div class="connection-status success">
                  <span class="status-dot success"></span>
                  <span>Login Approved!</span>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
      
      <!-- Error State -->
      <div class="extension-wrapper">
        <div class="state-label">Error State</div>
        <div class="app-container state-error">
          <main class="app-main">
            <div class="state-container">
              <div class="simple-message">
                <div class="connection-status error">
                  <span class="status-dot error"></span>
                  <span>Connection Error</span>
                </div>
                <p class="error-text">Unable to connect to Progress Dashboard.</p>
                <button class="simple-btn retry">Retry</button>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
    
    <div style="background: #f8fafc; border-radius: 8px; padding: 20px; text-align: left; max-width: 600px; margin: 0 auto;">
      <h4 style="margin: 0 0 10px 0; color: #1A1919;">Updated UI Features (Categories.tsx Style):</h4>
      <ul style="margin: 0; color: #64748b; font-size: 14px;">
        <li><strong>Badge Design</strong> - Connection status using Categories.tsx badge style</li>
        <li><strong>Button Styling</strong> - Approve/reject buttons matching Categories.tsx design</li>
        <li><strong>Color Scheme</strong> - #9CEE69 primary, #1A1919 text, consistent with dashboard</li>
        <li><strong>Backdrop Blur</strong> - Subtle blur effects like Categories.tsx badges</li>
        <li><strong>Hover Effects</strong> - Transform and color transitions matching dashboard</li>
        <li><strong>Request Info</strong> - Styled info box with proper spacing and colors</li>
        <li><strong>Preserved Functionality</strong> - All backend features remain intact</li>
      </ul>
    </div>
  </div>
</body>
</html>
