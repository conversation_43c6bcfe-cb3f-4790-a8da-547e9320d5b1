<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Style Comparison - Categories.tsx vs Extension</title>
  
  <!-- Extension Styles -->
  <link rel="stylesheet" href="popup/popup-minimal.css">
  
  <style>
    body {
      background: #f1f5f9;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .comparison-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .comparison-title {
      text-align: center;
      color: #1A1919;
      margin-bottom: 30px;
    }
    
    .comparison-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin-bottom: 30px;
    }
    
    .comparison-section {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    }
    
    .section-title {
      margin: 0 0 20px 0;
      color: #1A1919;
      font-size: 18px;
      font-weight: 600;
      text-align: center;
    }
    
    /* Categories.tsx Style Recreation */
    .categories-badge {
      display: inline-flex;
      align-items: center;
      padding: 4px 8px;
      border-radius: 9999px;
      font-size: 12px;
      font-weight: 500;
      border: 1px solid;
      transition: all 0.2s ease;
      backdrop-filter: blur(4px);
      margin: 5px;
    }
    
    .categories-badge.connected {
      background: rgba(156, 238, 105, 0.8);
      color: #1A1919;
      border-color: rgba(156, 238, 105, 0.3);
    }
    
    .categories-button {
      display: inline-flex;
      align-items: center;
      padding: 4px 8px;
      border-radius: 9999px;
      font-size: 12px;
      font-weight: 500;
      border: 1px solid;
      backdrop-filter: blur(4px);
      transition: all 0.2s ease;
      cursor: pointer;
      margin: 5px;
    }
    
    .categories-button.view {
      background: #f9fafb;
      color: #374151;
      border-color: #e5e7eb;
    }
    
    .categories-button.view:hover {
      background: #9CEE69;
      color: #1A1919;
      border-color: #9CEE69;
    }
    
    .categories-button.primary {
      background: #9CEE69;
      color: #1A1919;
      border-color: #9CEE69;
      padding: 8px 16px;
      border-radius: 8px;
    }
    
    .categories-button.primary:hover {
      background: rgba(156, 238, 105, 0.8);
    }
    
    .demo-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 15px;
    }
    
    .demo-item {
      padding: 15px;
      background: #f8fafc;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
    }
    
    .demo-label {
      font-size: 14px;
      font-weight: 600;
      color: #1A1919;
      margin-bottom: 10px;
    }
    
    .extension-preview {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="comparison-container">
    <h1 class="comparison-title">Style Comparison: Categories.tsx vs Extension</h1>
    
    <div class="comparison-grid">
      <!-- Categories.tsx Style -->
      <div class="comparison-section">
        <h2 class="section-title">Categories.tsx Original Style</h2>
        
        <div class="demo-grid">
          <div class="demo-item">
            <div class="demo-label">Author Badge Style:</div>
            <span class="categories-badge connected">
              <span style="width: 6px; height: 6px; background: #22c55e; border-radius: 50%; margin-right: 6px;"></span>
              Connected Author
            </span>
          </div>
          
          <div class="demo-item">
            <div class="demo-label">View Button Style:</div>
            <button class="categories-button view">
              <svg width="10" height="10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right: 4px;">
                <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M15 3h6v6M10 14L21 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              View
            </button>
          </div>
          
          <div class="demo-item">
            <div class="demo-label">Primary Button Style:</div>
            <button class="categories-button primary">
              Coba Lagi
            </button>
          </div>
        </div>
      </div>
      
      <!-- Extension Style -->
      <div class="comparison-section">
        <h2 class="section-title">Extension Updated Style</h2>
        
        <div class="demo-grid">
          <div class="demo-item">
            <div class="demo-label">Connection Status Badge:</div>
            <div class="connection-status connected">
              <span class="status-dot connected"></span>
              <span>Connected to Progress Dashboard</span>
            </div>
          </div>
          
          <div class="demo-item">
            <div class="demo-label">Action Buttons:</div>
            <div style="display: flex; gap: 8px;">
              <button class="simple-btn approve">Approve</button>
              <button class="simple-btn reject">Reject</button>
            </div>
          </div>
          
          <div class="demo-item">
            <div class="demo-label">Retry Button:</div>
            <button class="simple-btn retry">Retry</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Extension Preview -->
    <div class="comparison-section">
      <h2 class="section-title">Extension Preview with New Style</h2>
      
      <div class="extension-preview">
        <div class="app-container state-active">
          <main class="app-main">
            <div class="state-container">
              <div class="simple-message">
                <h3>Authentication Request</h3>
                <div class="request-info">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Website:</strong> localhost:5174</p>
                  <p><strong>Time:</strong> 4:59</p>
                </div>
                <div class="simple-actions">
                  <button class="simple-btn approve">Approve</button>
                  <button class="simple-btn reject">Reject</button>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
    
    <!-- Style Details -->
    <div class="comparison-section">
      <h2 class="section-title">Style Implementation Details</h2>
      
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <div>
          <h4 style="color: #1A1919; margin-bottom: 10px;">Colors Used:</h4>
          <ul style="color: #64748b; font-size: 14px; margin: 0;">
            <li><strong>#9CEE69</strong> - Primary green (nav-green)</li>
            <li><strong>#1A1919</strong> - Primary text (eerie-black)</li>
            <li><strong>#374151</strong> - Secondary text</li>
            <li><strong>#f8fafc</strong> - Light background</li>
            <li><strong>#e2e8f0</strong> - Border color</li>
          </ul>
        </div>
        
        <div>
          <h4 style="color: #1A1919; margin-bottom: 10px;">Design Features:</h4>
          <ul style="color: #64748b; font-size: 14px; margin: 0;">
            <li>Rounded corners (border-radius: 6px-9999px)</li>
            <li>Backdrop blur effects</li>
            <li>Subtle hover transformations</li>
            <li>Consistent padding and spacing</li>
            <li>Smooth transitions (0.2s ease)</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
