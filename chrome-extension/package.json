{"name": "progress-dashboard-otp-extension", "version": "1.0.0", "description": "Secure OTP authentication Chrome Extension for Progress Dashboard with modern UI and seamless integration", "main": "background.js", "scripts": {"build": "npm run build:icons && npm run build:zip", "build:icons": "echo 'Open assets/generate-icons.html in browser to generate PNG icons'", "build:zip": "zip -r progress-dashboard-extension.zip . -x '*.git*' 'node_modules/*' 'package*.json' '*.md' 'assets/generate-icons.html' 'assets/icon.svg'", "dev": "npm run watch", "watch": "echo 'Load extension in Chrome Developer Mode from this directory'", "lint": "eslint *.js **/*.js --fix", "format": "prettier --write '**/*.{js,css,html,json}'", "validate": "npm run lint && npm run format", "test": "echo 'Manual testing: Load extension in Chrome and test OTP flow'", "clean": "rm -f *.zip && echo 'Cleaned build artifacts'", "install-dev": "npm install --save-dev eslint prettier", "setup": "npm run install-dev && echo 'Development environment setup complete'"}, "keywords": ["chrome-extension", "otp", "authentication", "security", "progress-dashboard", "two-factor-auth", "totp"], "author": {"name": "Progress Dashboard Team", "email": "<EMAIL>", "url": "https://progressdashboard.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/progressdashboard/chrome-extension.git"}, "bugs": {"url": "https://github.com/progressdashboard/chrome-extension/issues"}, "homepage": "https://progressdashboard.com/chrome-extension", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {"eslint": "^8.50.0", "prettier": "^3.0.0"}, "eslintConfig": {"env": {"browser": true, "es2021": true, "webextensions": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "globals": {"chrome": "readonly"}, "rules": {"no-console": "warn", "no-unused-vars": "warn", "prefer-const": "error", "no-var": "error"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false}, "manifest": {"version": "1.0.0", "manifest_version": 3, "minimum_chrome_version": "88"}, "permissions": {"required": ["storage", "activeTab", "notifications", "scripting"], "optional": ["background"]}, "browser_compatibility": {"chrome": ">=88", "edge": ">=88", "opera": ">=74"}, "security": {"content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "host_permissions": ["http://localhost:*/*", "https://localhost:*/*", "https://*.progressdashboard.com/*"]}, "features": {"otp_authentication": {"description": "Secure OTP-based authentication", "version": "1.0.0"}, "real_time_notifications": {"description": "Real-time OTP request notifications", "version": "1.0.0"}, "modern_ui": {"description": "Modern, responsive UI with animations", "version": "1.0.0"}, "security_validation": {"description": "Comprehensive security validation", "version": "1.0.0"}, "cross_origin_communication": {"description": "Secure cross-origin communication", "version": "1.0.0"}}, "development": {"reload_on_change": true, "debug_mode": true, "test_urls": ["http://localhost:5174", "http://localhost:3000", "http://localhost:5001"]}, "build": {"output": "progress-dashboard-extension.zip", "exclude": ["node_modules", "package*.json", "*.md", ".git*", "assets/generate-icons.html", "assets/icon.svg"]}, "chrome_web_store": {"category": "productivity", "rating": "everyone", "languages": ["en"], "regions": ["US", "GB", "CA", "AU", "DE", "FR", "ES", "IT", "NL", "SE", "NO", "DK", "FI"]}}