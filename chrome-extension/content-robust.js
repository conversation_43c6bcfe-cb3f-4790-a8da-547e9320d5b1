/**
 * Robust Content Script for Progress Dashboard Chrome Extension
 *
 * Multiple injection methods with extensive debugging
 */

// Environment configuration
const DEBUG_ENABLED = window.location.hostname === 'localhost';

// Debug logging
function debugLog(message, data = null) {
  if (DEBUG_ENABLED) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 🔗 Progress Dashboard Extension: ${message}`, data || '');
  }
}

// Content script state
let contentState = {
  isInjected: false,
  injectionAttempts: 0,
  maxAttempts: 5,
  retryDelay: 1000,
  bridgeInjected: false
};

console.log('🔐 Progress Dashboard Extension: Content script loaded!', {
  url: window.location.href,
  timestamp: Date.now(),
  hostname: window.location.hostname
});
debugLog('Content script loaded');

// Initialize content script with multiple attempts
function initializeContentScript() {
  debugLog('Initializing content script', { 
    attempt: contentState.injectionAttempts + 1,
    isInjected: contentState.isInjected,
    url: window.location.href
  });
  
  if (contentState.isInjected) {
    debugLog('Content script already initialized');
    return;
  }
  
  contentState.injectionAttempts++;
  
  // Try multiple injection methods
  const success = tryAllInjectionMethods();
  
  if (success) {
    contentState.isInjected = true;
    setupMessageListeners();
    notifyExtensionReady();
    debugLog('✅ Content script initialized successfully');
  } else if (contentState.injectionAttempts < contentState.maxAttempts) {
    debugLog(`❌ Injection failed, retrying in ${contentState.retryDelay}ms (attempt ${contentState.injectionAttempts}/${contentState.maxAttempts})`);
    setTimeout(initializeContentScript, contentState.retryDelay);
    contentState.retryDelay *= 1.5; // Exponential backoff
  } else {
    debugLog('❌ All injection attempts failed');
  }
}

// Try all injection methods
function tryAllInjectionMethods() {
  const methods = [
    { name: 'Direct Window Assignment', func: injectDirectAssignment },
    { name: 'DefineProperty Method', func: injectDefineProperty },
    { name: 'Proxy Method', func: injectProxyMethod },
    { name: 'Event-based Method', func: injectEventBased },
    { name: 'Fallback Method', func: injectFallback }
  ];
  
  for (const method of methods) {
    debugLog(`Trying injection method: ${method.name}`);
    
    try {
      const success = method.func();
      if (success && window.progressDashboardExtension) {
        debugLog(`✅ Success with method: ${method.name}`);
        contentState.bridgeInjected = true;
        return true;
      }
    } catch (error) {
      debugLog(`❌ Method ${method.name} failed:`, error.message);
    }
  }
  
  return false;
}

// Method 1: Direct window assignment
function injectDirectAssignment() {
  try {
    window.progressDashboardExtension = createBridgeObject();
    
    // Verify injection
    if (window.progressDashboardExtension && typeof window.progressDashboardExtension.sendMessage === 'function') {
      debugLog('✅ Direct assignment successful');
      dispatchReadyEvent();
      return true;
    }
    
    return false;
  } catch (error) {
    debugLog('❌ Direct assignment failed:', error.message);
    return false;
  }
}

// Method 2: Object.defineProperty
function injectDefineProperty() {
  try {
    Object.defineProperty(window, 'progressDashboardExtension', {
      value: createBridgeObject(),
      writable: false,
      configurable: true,
      enumerable: true
    });
    
    if (window.progressDashboardExtension) {
      debugLog('✅ DefineProperty successful');
      dispatchReadyEvent();
      return true;
    }
    
    return false;
  } catch (error) {
    debugLog('❌ DefineProperty failed:', error.message);
    return false;
  }
}

// Method 3: Proxy method
function injectProxyMethod() {
  try {
    const bridgeObject = createBridgeObject();
    const proxy = new Proxy(bridgeObject, {
      get(target, prop) {
        debugLog(`Bridge property accessed: ${prop}`);
        return target[prop];
      }
    });
    
    window.progressDashboardExtension = proxy;
    
    if (window.progressDashboardExtension) {
      debugLog('✅ Proxy method successful');
      dispatchReadyEvent();
      return true;
    }
    
    return false;
  } catch (error) {
    debugLog('❌ Proxy method failed:', error.message);
    return false;
  }
}

// Method 4: Event-based injection
function injectEventBased() {
  try {
    const bridgeObject = createBridgeObject();
    
    // Store in a global variable with unique name
    window.__progressDashboardExtensionBridge__ = bridgeObject;
    
    // Create getter that returns the bridge
    Object.defineProperty(window, 'progressDashboardExtension', {
      get() {
        return window.__progressDashboardExtensionBridge__;
      },
      configurable: true,
      enumerable: true
    });
    
    if (window.progressDashboardExtension) {
      debugLog('✅ Event-based method successful');
      dispatchReadyEvent();
      return true;
    }
    
    return false;
  } catch (error) {
    debugLog('❌ Event-based method failed:', error.message);
    return false;
  }
}

// Method 5: Fallback method
function injectFallback() {
  try {
    // Use setTimeout to ensure DOM is ready
    setTimeout(() => {
      if (!window.progressDashboardExtension) {
        window.progressDashboardExtension = createBridgeObject();
        debugLog('✅ Fallback method applied');
        dispatchReadyEvent();
      }
    }, 100);
    
    return true;
  } catch (error) {
    debugLog('❌ Fallback method failed:', error.message);
    return false;
  }
}

// Create bridge object
function createBridgeObject() {
  return {
    isAvailable: true,
    version: '1.0.0-robust',
    injectionMethod: 'robust-multi-method',
    
    // Send message to extension
    sendMessage: function(message) {
      debugLog('Bridge sendMessage called', message);
      
      return new Promise((resolve, reject) => {
        const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        const messageWithId = {
          ...message,
          messageId: messageId,
          timestamp: Date.now()
        };
        
        const responseListener = (event) => {
          if (event.data && event.data.type === 'EXTENSION_RESPONSE' && event.data.messageId === messageId) {
            window.removeEventListener('message', responseListener);
            
            if (event.data.success) {
              resolve(event.data.data);
            } else {
              reject(new Error(event.data.error || 'Extension error'));
            }
          }
        };
        
        window.addEventListener('message', responseListener);
        
        // Send message to content script
        window.postMessage({
          type: 'TO_EXTENSION',
          data: messageWithId
        }, window.location.origin);
        
        // Set timeout
        setTimeout(() => {
          window.removeEventListener('message', responseListener);
          reject(new Error('Extension response timeout'));
        }, 10000);
      });
    },
    
    // Listen for extension messages
    onMessage: function(callback) {
      const listener = (event) => {
        if (event.data && event.data.type && event.data.type.startsWith('OTP_')) {
          callback(event.data);
        }
      };
      
      window.addEventListener('message', listener);
      
      return () => {
        window.removeEventListener('message', listener);
      };
    },
    
    // Get extension info
    getInfo: function() {
      return this.sendMessage({ type: 'GET_EXTENSION_STATUS' });
    },
    
    // Check if extension is ready
    isReady: function() {
      return this.isAvailable;
    },
    
    // Debug info
    getDebugInfo: function() {
      return {
        isAvailable: this.isAvailable,
        version: this.version,
        injectionMethod: this.injectionMethod,
        contentState: contentState,
        timestamp: Date.now()
      };
    }
  };
}

// Dispatch ready event
function dispatchReadyEvent() {
  try {
    window.dispatchEvent(new CustomEvent('progressDashboardExtensionReady', {
      detail: { 
        version: '1.0.0-robust', 
        timestamp: Date.now(),
        injectionMethod: 'robust-multi-method'
      }
    }));
    debugLog('✅ Ready event dispatched');
  } catch (error) {
    debugLog('❌ Failed to dispatch ready event:', error.message);
  }
}

// Set up message listeners
function setupMessageListeners() {
  debugLog('Setting up message listeners');
  
  // Listen for messages from page
  window.addEventListener('message', handlePageMessage);
  
  // Listen for messages from background script
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener(handleBackgroundMessage);
  }
}

// Handle messages from page
function handlePageMessage(event) {
  if (event.origin !== window.location.origin) {
    return;
  }
  
  if (event.data && event.data.type === 'TO_EXTENSION') {
    debugLog('Forwarding message to background', event.data.data);
    forwardToBackground(event.data.data);
  }
}

// Handle messages from background script
function handleBackgroundMessage(message, sender, sendResponse) {
  debugLog('Received message from background', message);
  
  if (message.type === 'OTP_REQUEST') {
    window.postMessage({
      type: 'OTP_REQUEST',
      data: message.data,
      timestamp: Date.now()
    }, window.location.origin);
    
    sendResponse({ success: true });
  } else if (message.type === 'OTP_RESPONSE') {
    window.postMessage({
      type: 'OTP_RESPONSE',
      data: message.data,
      timestamp: Date.now()
    }, window.location.origin);
    
    sendResponse({ success: true });
  }
  
  return true;
}

// Forward message to background script
async function forwardToBackground(message) {
  try {
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      throw new Error('Chrome runtime not available');
    }
    
    const response = await chrome.runtime.sendMessage({
      type: 'FROM_CONTENT',
      data: message,
      origin: window.location.origin,
      timestamp: Date.now()
    });
    
    window.postMessage({
      type: 'EXTENSION_RESPONSE',
      messageId: message.messageId,
      success: true,
      data: response,
      timestamp: Date.now()
    }, window.location.origin);
    
  } catch (error) {
    debugLog('Failed to forward message to background:', error.message);
    
    window.postMessage({
      type: 'EXTENSION_RESPONSE',
      messageId: message.messageId,
      success: false,
      error: error.message,
      timestamp: Date.now()
    }, window.location.origin);
  }
}

// Notify extension readiness
function notifyExtensionReady() {
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.sendMessage({
      type: 'CONTENT_SCRIPT_READY',
      url: window.location.href,
      timestamp: Date.now()
    }).catch(error => {
      debugLog('Failed to notify extension readiness:', error.message);
    });
  }
}

// Initialize based on document state
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  // Document already loaded
  setTimeout(initializeContentScript, 100);
}

// Also try immediate initialization
initializeContentScript();

// Periodic retry for robustness
setInterval(() => {
  if (!contentState.isInjected || !window.progressDashboardExtension) {
    debugLog('Periodic check: Extension not detected, retrying...');
    contentState.isInjected = false;
    contentState.injectionAttempts = 0;
    initializeContentScript();
  }
}, 5000);

debugLog('🔗 Robust content script loaded and initialized');
