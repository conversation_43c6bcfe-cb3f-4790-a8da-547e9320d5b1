<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Reload Extension</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Consolas', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Force Reload Chrome Extension</h1>
        
        <div class="step error">
            <h3>❌ Problem Detected</h3>
            <p>Extension is still loading test files even after removal from manifest.json</p>
            <p><strong>Error:</strong> <code>content-test.js</code> still executing</p>
        </div>

        <div class="step warning">
            <h3>⚠️ Cache Issue</h3>
            <p>Chrome is using cached version of the extension. Need to force reload.</p>
        </div>

        <h2>🚀 Solution Steps</h2>

        <div class="step">
            <h3>Step 1: Open Chrome Extensions</h3>
            <button onclick="openExtensions()">Open chrome://extensions/</button>
            <p>Or manually navigate to: <code>chrome://extensions/</code></p>
        </div>

        <div class="step">
            <h3>Step 2: Enable Developer Mode</h3>
            <p>Toggle the "Developer mode" switch in the top-right corner</p>
        </div>

        <div class="step">
            <h3>Step 3: Remove Extension Completely</h3>
            <p>Find "Progress Dashboard OTP Authenticator" and click <strong>Remove</strong></p>
            <button class="danger" onclick="alert('Click REMOVE button on the extension card')">⚠️ Remove Extension</button>
        </div>

        <div class="step">
            <h3>Step 4: Clear Chrome Cache</h3>
            <button onclick="clearCache()">Clear Cache & Reload</button>
            <p>Or press: <code>Ctrl+Shift+Delete</code> (Windows) / <code>Cmd+Shift+Delete</code> (Mac)</p>
        </div>

        <div class="step">
            <h3>Step 5: Reload Extension Fresh</h3>
            <p>Click "Load unpacked" and select the chrome-extension folder</p>
            <button onclick="alert('Select the chrome-extension folder from your project')">📁 Load Unpacked</button>
        </div>

        <div class="step success">
            <h3>Step 6: Verify Clean Load</h3>
            <p>Check console - should NOT see:</p>
            <ul>
                <li><code>[CONTENT-TEST]</code> messages</li>
                <li><code>content-test.js</code> references</li>
                <li>Excessive debug logging</li>
            </ul>
        </div>

        <h2>🧪 Test After Reload</h2>
        
        <div class="step">
            <button onclick="testLogin()">Test Login Page</button>
            <button onclick="testConsole()">Check Console</button>
        </div>

        <div class="step success">
            <h3>✅ Expected Clean Console Output</h3>
            <pre>
✅ "Content script setup complete"
✅ "Extension communication successful"
❌ NO "[CONTENT-TEST]" messages
❌ NO "[CONTENT-DEBUG]" spam
            </pre>
        </div>
    </div>

    <script>
        function openExtensions() {
            window.open('chrome://extensions/', '_blank');
        }

        function clearCache() {
            // Open Chrome settings for clearing cache
            window.open('chrome://settings/clearBrowserData', '_blank');
        }

        function testLogin() {
            window.open('http://localhost:5173/login', '_blank');
        }

        function testConsole() {
            alert('Open Developer Tools (F12) and check Console tab for clean output');
        }

        // Auto-open extensions page
        setTimeout(() => {
            if (confirm('Auto-open Chrome Extensions page?')) {
                openExtensions();
            }
        }, 1000);
    </script>
</body>
</html>
