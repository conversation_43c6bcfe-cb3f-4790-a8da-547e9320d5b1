/**
 * CSP-Safe Content Script for Progress Dashboard Chrome Extension
 * 
 * Uses CSP-compliant methods to inject communication bridge
 */

// Content script state
let contentState = {
  isInjected: false,
  messageQueue: [],
  extensionReady: false
};

// Initialize content script
function initializeContentScript() {
  if (contentState.isInjected) return;
  
  console.log('🔗 Progress Dashboard Extension content script initializing...');
  
  // Use CSP-safe bridge injection
  injectCSPSafeBridge();
  
  // Set up message listeners
  setupMessageListeners();
  
  // Notify extension readiness
  notifyExtensionReady();
  
  contentState.isInjected = true;
  console.log('✅ Content script initialized (CSP-safe)');
}

// CSP-Safe bridge injection using direct window assignment
function injectCSPSafeBridge() {
  try {
    // Direct window object assignment (CSP-safe)
    const bridgeObject = {
      isAvailable: true,
      version: '1.0.0',
      
      // Send message to extension
      sendMessage: function(message) {
        return new Promise((resolve, reject) => {
          const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
          
          // Add message ID for response tracking
          const messageWithId = {
            ...message,
            messageId: messageId,
            timestamp: Date.now()
          };
          
          // Set up response listener
          const responseListener = (event) => {
            if (event.data && event.data.type === 'EXTENSION_RESPONSE' && event.data.messageId === messageId) {
              window.removeEventListener('message', responseListener);
              
              if (event.data.success) {
                resolve(event.data.data);
              } else {
                reject(new Error(event.data.error || 'Extension error'));
              }
            }
          };
          
          window.addEventListener('message', responseListener);
          
          // Send message to content script
          window.postMessage({
            type: 'TO_EXTENSION',
            data: messageWithId
          }, window.location.origin);
          
          // Set timeout
          setTimeout(() => {
            window.removeEventListener('message', responseListener);
            reject(new Error('Extension response timeout'));
          }, 10000);
        });
      },
      
      // Listen for extension messages
      onMessage: function(callback) {
        const listener = (event) => {
          if (event.data && event.data.type && event.data.type.startsWith('OTP_')) {
            callback(event.data);
          }
        };
        
        window.addEventListener('message', listener);
        
        // Return cleanup function
        return () => {
          window.removeEventListener('message', listener);
        };
      },
      
      // Get extension info
      getInfo: function() {
        return this.sendMessage({ type: 'GET_EXTENSION_STATUS' });
      },
      
      // Check if extension is ready
      isReady: function() {
        return this.isAvailable;
      }
    };
    
    // Assign to window object
    window.progressDashboardExtension = bridgeObject;
    
    // Notify that bridge is ready
    window.dispatchEvent(new CustomEvent('progressDashboardExtensionReady', {
      detail: { version: '1.0.0', timestamp: Date.now() }
    }));
    
    console.log('🌉 Progress Dashboard Extension bridge injected (CSP-safe)');
    
  } catch (error) {
    console.error('Failed to inject CSP-safe bridge:', error);
    
    // Fallback: Try alternative method
    injectFallbackBridge();
  }
}

// Fallback bridge injection method
function injectFallbackBridge() {
  try {
    // Use defineProperty for more robust assignment
    Object.defineProperty(window, 'progressDashboardExtension', {
      value: {
        isAvailable: true,
        version: '1.0.0-fallback',
        
        sendMessage: function(message) {
          return new Promise((resolve, reject) => {
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            const messageWithId = {
              ...message,
              messageId: messageId,
              timestamp: Date.now()
            };
            
            const responseListener = (event) => {
              if (event.data && event.data.type === 'EXTENSION_RESPONSE' && event.data.messageId === messageId) {
                window.removeEventListener('message', responseListener);
                
                if (event.data.success) {
                  resolve(event.data.data);
                } else {
                  reject(new Error(event.data.error || 'Extension error'));
                }
              }
            };
            
            window.addEventListener('message', responseListener);
            
            window.postMessage({
              type: 'TO_EXTENSION',
              data: messageWithId
            }, window.location.origin);
            
            setTimeout(() => {
              window.removeEventListener('message', responseListener);
              reject(new Error('Extension response timeout'));
            }, 10000);
          });
        },
        
        onMessage: function(callback) {
          const listener = (event) => {
            if (event.data && event.data.type && event.data.type.startsWith('OTP_')) {
              callback(event.data);
            }
          };
          
          window.addEventListener('message', listener);
          
          return () => {
            window.removeEventListener('message', listener);
          };
        },
        
        getInfo: function() {
          return this.sendMessage({ type: 'GET_EXTENSION_STATUS' });
        },
        
        isReady: function() {
          return this.isAvailable;
        }
      },
      writable: false,
      configurable: false
    });
    
    console.log('🌉 Progress Dashboard Extension bridge injected (fallback method)');
    
  } catch (error) {
    console.error('All bridge injection methods failed:', error);
  }
}

// Set up message listeners
function setupMessageListeners() {
  // Listen for messages from page
  window.addEventListener('message', handlePageMessage);
  
  // Listen for messages from background script
  chrome.runtime.onMessage.addListener(handleBackgroundMessage);
  
  console.log('📡 Message listeners set up');
}

// Handle messages from page
function handlePageMessage(event) {
  // Validate origin
  if (event.origin !== window.location.origin) {
    return;
  }
  
  // Handle extension messages
  if (event.data && event.data.type === 'TO_EXTENSION') {
    forwardToBackground(event.data.data);
  }
}

// Handle messages from background script
function handleBackgroundMessage(message, sender, sendResponse) {
  if (message.type === 'OTP_REQUEST') {
    // Forward OTP request to page
    window.postMessage({
      type: 'OTP_REQUEST',
      data: message.data,
      timestamp: Date.now()
    }, window.location.origin);
    
    sendResponse({ success: true });
  } else if (message.type === 'OTP_RESPONSE') {
    // Forward OTP response to page
    window.postMessage({
      type: 'OTP_RESPONSE',
      data: message.data,
      timestamp: Date.now()
    }, window.location.origin);
    
    sendResponse({ success: true });
  }
  
  return true; // Keep message channel open
}

// Forward message to background script
async function forwardToBackground(message) {
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'FROM_CONTENT',
      data: message,
      origin: window.location.origin,
      timestamp: Date.now()
    });
    
    // Send response back to page
    window.postMessage({
      type: 'EXTENSION_RESPONSE',
      messageId: message.messageId,
      success: true,
      data: response,
      timestamp: Date.now()
    }, window.location.origin);
    
  } catch (error) {
    console.error('Failed to forward message to background:', error);
    
    // Send error response back to page
    window.postMessage({
      type: 'EXTENSION_RESPONSE',
      messageId: message.messageId,
      success: false,
      error: error.message,
      timestamp: Date.now()
    }, window.location.origin);
  }
}

// Notify extension readiness
function notifyExtensionReady() {
  chrome.runtime.sendMessage({
    type: 'CONTENT_SCRIPT_READY',
    url: window.location.href,
    timestamp: Date.now()
  }).catch(error => {
    console.warn('Failed to notify extension readiness:', error);
  });
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  initializeContentScript();
}

// Also initialize immediately for early injection
initializeContentScript();

console.log('🔗 Progress Dashboard Extension content script loaded (CSP-safe version)');
