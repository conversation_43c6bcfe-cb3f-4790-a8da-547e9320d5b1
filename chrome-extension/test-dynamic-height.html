<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Dynamic Height - Progress Dashboard Extension</title>
  
  <!-- Styles -->
  <link rel="stylesheet" href="styles/design-tokens.css">
  <link rel="stylesheet" href="styles/animations.css">
  <link rel="stylesheet" href="styles/components.css">
  <link rel="stylesheet" href="popup/popup.css">
  
  <style>
    body {
      background: #f1f5f9;
      padding: 20px;
      font-family: 'Inter', sans-serif;
    }
    
    .test-container {
      max-width: 800px;
      margin: 0 auto;
    }
    
    .test-title {
      text-align: center;
      color: #1A1919;
      margin-bottom: 30px;
    }
    
    .controls {
      background: white;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 30px;
      box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    }
    
    .button-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 10px;
      margin-bottom: 20px;
    }
    
    .extension-wrapper {
      display: flex;
      justify-content: center;
      align-items: flex-start;
      min-height: 600px;
      position: relative;
    }
    
    .height-indicator {
      position: absolute;
      left: -80px;
      top: 0;
      width: 60px;
      background: rgba(156, 238, 105, 0.1);
      border: 2px solid #9CEE69;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      color: #1A1919;
      writing-mode: vertical-rl;
      text-orientation: mixed;
      transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .current-state-info {
      background: #f0fdf4;
      border: 1px solid #9CEE69;
      border-radius: 8px;
      padding: 10px;
      text-align: center;
      color: #1A1919;
      font-weight: 500;
    }
    
    .debug-info {
      background: #f8fafc;
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
      font-family: monospace;
      font-size: 12px;
      color: #64748b;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1 class="test-title">Dynamic Height Test</h1>
    
    <div class="controls">
      <h3>Test Extension States</h3>
      <div class="button-grid">
        <button class="btn btn-secondary" onclick="testState('idle')">Idle</button>
        <button class="btn btn-secondary" onclick="testState('loading')">Loading</button>
        <button class="btn btn-primary" onclick="testState('activeRequest')">Active</button>
        <button class="btn" style="background: #22c55e; color: white;" onclick="testState('success')">Success</button>
        <button class="btn btn-danger" onclick="testState('error')">Error</button>
      </div>
      
      <div class="current-state-info" id="stateInfo">
        Current State: idle (200px height)
      </div>
    </div>
    
    <div class="extension-wrapper">
      <div class="height-indicator" id="heightIndicator">200px</div>
      
      <!-- Extension Container -->
      <div class="app-container state-idle" id="testExtension">
        <header class="app-header">
          <div class="header-content">
            <div class="brand">
              <div class="brand-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="brand-text">
                <h1 class="brand-title">Progress Dashboard</h1>
                <p class="brand-subtitle">OTP Authenticator</p>
              </div>
            </div>
          </div>
        </header>
        
        <main class="app-main" id="testMain">
          <div class="state-container">
            <div class="idle-content">
              <div class="status-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
              <h2 class="status-title">Ready for Authentication</h2>
            </div>
          </div>
        </main>
        
        <footer class="app-footer">
          <div class="footer-content">
            <div class="extension-info">
              <span class="version">v1.0.0</span>
              <span class="status">
                <span class="status-dot"></span>
                Connected
              </span>
            </div>
          </div>
        </footer>
      </div>
    </div>
    
    <div class="debug-info" id="debugInfo">
      Debug Info: Extension initialized in idle state
    </div>
  </div>
  
  <script>
    const stateHeights = {
      idle: 200,
      loading: 250,
      activeRequest: 480,
      success: 300,
      error: 350
    };
    
    const stateContents = {
      idle: `
        <div class="state-container">
          <div class="idle-content">
            <div class="status-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h2 class="status-title">Ready for Authentication</h2>
          </div>
        </div>
      `,
      loading: `
        <div class="state-container">
          <div class="loading-content">
            <h2 class="loading-text">Connecting...</h2>
          </div>
        </div>
      `,
      activeRequest: `
        <div class="state-container">
          <div class="request-content">
            <div class="request-header">
              <div class="request-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 1v6m0 6v6m6-12h-6m-6 0h6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
              </div>
              <h2 class="request-title">Login Request</h2>
            </div>
            <div class="request-details">
              <div class="detail-item">
                <span class="detail-label">Email:</span>
                <span class="detail-value"><EMAIL></span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Website:</span>
                <span class="detail-value">localhost:5174</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Time remaining:</span>
                <span class="detail-value timer">4:59</span>
              </div>
            </div>
            <div class="request-actions">
              <button class="btn btn-primary btn-full">Approve Login</button>
              <button class="btn btn-secondary btn-full">Reject</button>
            </div>
          </div>
        </div>
      `,
      success: `
        <div class="state-container">
          <div class="success-content">
            <div class="success-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h2 class="success-title">Login Approved!</h2>
            <p class="success-description">You have successfully approved the login request.</p>
          </div>
        </div>
      `,
      error: `
        <div class="state-container">
          <div class="error-content">
            <div class="error-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                <path d="M15 9l-6 6M9 9l6 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h2 class="error-title">Connection Error</h2>
            <p class="error-description">Unable to connect to Progress Dashboard.</p>
            <div class="error-actions">
              <button class="btn btn-primary btn-full">Retry</button>
            </div>
          </div>
        </div>
      `
    };
    
    function testState(state) {
      const container = document.getElementById('testExtension');
      const mainContent = document.getElementById('testMain');
      const heightIndicator = document.getElementById('heightIndicator');
      const stateInfo = document.getElementById('stateInfo');
      const debugInfo = document.getElementById('debugInfo');
      
      // Remove all state classes
      container.classList.remove('state-idle', 'state-loading', 'state-active', 'state-success', 'state-error');
      
      // Add new state class
      const stateClass = state === 'activeRequest' ? 'state-active' : `state-${state}`;
      container.classList.add(stateClass);
      
      // Update content
      mainContent.innerHTML = stateContents[state];
      
      // Update indicators
      const height = stateHeights[state];
      heightIndicator.textContent = `${height}px`;
      heightIndicator.style.height = `${height}px`;
      stateInfo.textContent = `Current State: ${state} (${height}px height)`;
      
      // Update debug info
      debugInfo.innerHTML = `
        Debug Info:<br>
        - State: ${state}<br>
        - Target Height: ${height}px<br>
        - Container Classes: ${container.className}<br>
        - Timestamp: ${new Date().toLocaleTimeString()}
      `;
      
      console.log(`[TEST] Changed to state: ${state}, height: ${height}px`);
    }
    
    // Initialize
    console.log('[TEST] Dynamic height test initialized');
  </script>
</body>
</html>
