/**
 * Production Content Script for Progress Dashboard Chrome Extension
 *
 * Handles secure communication between web page and extension background
 */

// Global error handler for content script
window.addEventListener('error', (event) => {
  console.log('[CONTENT-DEBUG] ❌ Content script error:', event.error?.message || event.message);
});

// Immediate startup log
console.log('[CONTENT-DEBUG] 🚀 Content script starting...');

// Environment detection with error handling
let isProduction = true; // Default to production for safety
let isDevelopment = false;

try {
  // Safely check if chrome.runtime is available
  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
    const manifest = chrome.runtime.getManifest();
    // In development, extensions have a 'key' field
    isProduction = !manifest.key;
    isDevelopment = !isProduction;
  }
} catch (error) {
  console.log('[CONTENT-DEBUG] Environment detection error, defaulting to production:', error.message);
  isProduction = true;
  isDevelopment = false;
}

// Logging configuration
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

const CURRENT_LOG_LEVEL = isProduction ? LOG_LEVELS.WARN : LOG_LEVELS.DEBUG;

// Secure logging helper
function log(level, message, data = null) {
  if (level <= CURRENT_LOG_LEVEL) {
    const timestamp = new Date().toISOString();
    const levelName = Object.keys(LOG_LEVELS)[level];

    // In production, avoid logging sensitive data
    const sanitizedData = isProduction && data ? '[REDACTED]' : data;

    console.log(`[${timestamp}] ${levelName} [Content] ${message}`, sanitizedData || '');
  }
}

// Convenience logging functions
const logger = {
  error: (message, data) => log(LOG_LEVELS.ERROR, message, data),
  warn: (message, data) => log(LOG_LEVELS.WARN, message, data),
  info: (message, data) => log(LOG_LEVELS.INFO, message, data),
  debug: (message, data) => log(LOG_LEVELS.DEBUG, message, data)
};

// Immediate debug logging
console.log('[CONTENT-DEBUG] Content script file loaded');
console.log('[CONTENT-DEBUG] Environment detection completed', { isProduction, isDevelopment });

logger.info('Progress Dashboard content script loaded', {
  url: window.location.href,
  environment: isProduction ? 'production' : 'development'
});

// Debug function for detailed logging
function debugLog(message, data = null) {
  try {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] [CONTENT-DEBUG] ${message}`, data || '');
  } catch (error) {
    console.error('[CONTENT-DEBUG] Logging error:', error);
  }
}

debugLog('Content script initialization started');

// Error handler for uncaught errors
window.addEventListener('error', (event) => {
  if (event.filename && event.filename.includes('content-simple.js')) {
    debugLog('❌ Content script error detected', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    });
  }
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  debugLog('❌ Unhandled promise rejection in content script', {
    reason: event.reason
  });
});

// CSP-Compliant Bridge Creation via DOM Events
function createCSPCompliantBridge() {
  debugLog('Starting CSP-compliant bridge creation');

  try {
    // Create bridge object in content script context
    const bridgeObject = {
      loaded: true,
      timestamp: Date.now(),
      version: '1.0.0-production',

      sendMessage: function(message) {
        debugLog('Bridge sendMessage called', { type: message.type });

        return new Promise((resolve, reject) => {
          try {
            // Add request ID
            const requestId = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            message.requestId = requestId;

            // Send message to background script directly
            chrome.runtime.sendMessage(message, (response) => {
              if (chrome.runtime.lastError) {
                debugLog('❌ Extension communication error', chrome.runtime.lastError.message);
                reject(new Error(chrome.runtime.lastError.message));
                return;
              }

              if (response && response.success) {
                debugLog('✅ Background script response received');
                resolve(response);
              } else {
                const error = response?.error || 'Unknown error';
                debugLog('❌ Background script returned error', { error });
                reject(new Error(error));
              }
            });
          } catch (error) {
            debugLog('❌ Failed to send message to background script', error.message);
            reject(error);
          }
        });
      },

      getDebugInfo: function() {
        return {
          version: '1.0.0-production',
          isAvailable: true,
          injectionMethod: 'csp-compliant-direct',
          timestamp: this.timestamp,
          environment: isProduction ? 'production' : 'development'
        };
      },

      isAvailable: function() {
        return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
      }
    };

    // Use multiple assignment methods
    debugLog('Attempting multiple bridge assignment methods');

    // Method 1: Direct assignment
    try {
      window.progressDashboardExtension = bridgeObject;
      debugLog('✅ Direct assignment successful');
    } catch (error) {
      debugLog('❌ Direct assignment failed', error.message);
    }

    // Method 2: defineProperty
    try {
      Object.defineProperty(window, 'progressDashboardExtension', {
        value: bridgeObject,
        writable: true,
        enumerable: true,
        configurable: true
      });
      debugLog('✅ defineProperty assignment successful');
    } catch (error) {
      debugLog('❌ defineProperty assignment failed', error.message);
    }

    // Method 3: Fallback global
    try {
      window.__progressDashboardExtensionBridge__ = bridgeObject;
      debugLog('✅ Fallback global assignment successful');
    } catch (error) {
      debugLog('❌ Fallback global assignment failed', error.message);
    }

    // Method 4: Custom event with bridge data
    try {
      const bridgeEvent = new CustomEvent('progressDashboardBridgeReady', {
        detail: {
          bridge: bridgeObject,
          timestamp: Date.now()
        }
      });
      window.dispatchEvent(bridgeEvent);
      debugLog('✅ Bridge event dispatched successfully');
    } catch (error) {
      debugLog('❌ Bridge event dispatch failed', error.message);
    }

    // Set content script markers
    try {
      window.progressDashboardContentScript = true;
      window.progressDashboardExtensionReady = true;
      debugLog('✅ Content script markers set successfully');
    } catch (error) {
      debugLog('❌ Failed to set content script markers', error.message);
    }

    return bridgeObject;

  } catch (error) {
    debugLog('❌ CSP-compliant bridge creation failed', error.message);
    return null;
  }
}

// CSP-Safe Extension Bridge Creation (fallback)
function createCSPSafeBridge() {
  debugLog('Starting CSP-safe extension bridge creation (fallback)');
  logger.info('Creating CSP-safe extension bridge');

  try {
    // Create bridge object directly in content script context
    const bridgeObject = {
    loaded: true,
    timestamp: Date.now(),
    version: '1.0.0-production',

    // Secure message sending to background script
    sendMessage: function(message) {
      logger.debug('Bridge sendMessage called', { type: message.type });

      return new Promise((resolve, reject) => {
        try {
          // Add request ID for response matching
          const requestId = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
          message.requestId = requestId;

          // Send message to background script directly
          chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
              logger.error('Extension communication error', chrome.runtime.lastError.message);
              reject(new Error(chrome.runtime.lastError.message));
              return;
            }

            if (response && response.success) {
              logger.debug('Background script response received');
              resolve(response);
            } else {
              const error = response?.error || 'Unknown error';
              logger.warn('Background script returned error', { error });
              reject(new Error(error));
            }
          });
        } catch (error) {
          logger.error('Failed to send message to background script', error.message);
          reject(error);
        }
      });
    },

    // Extension information
    getDebugInfo: function() {
      return {
        version: '1.0.0-production',
        isAvailable: true,
        injectionMethod: 'csp-safe-direct',
        timestamp: this.timestamp,
        environment: isProduction ? 'production' : 'development'
      };
    },

    // Availability check
    isAvailable: function() {
      return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
    }
  };

    // Try to assign to window object (may fail due to CSP)
    debugLog('Attempting direct window assignment');
    try {
      window.progressDashboardExtension = bridgeObject;
      debugLog('✅ Extension bridge assigned to window object successfully');
      logger.info('Extension bridge assigned to window object');
    } catch (error) {
      debugLog('❌ Failed to assign bridge to window object', error.message);
      logger.warn('Failed to assign bridge to window object', error.message);
    }

    // Use defineProperty as fallback
    debugLog('Attempting defineProperty fallback');
    try {
      Object.defineProperty(window, 'progressDashboardExtension', {
        value: bridgeObject,
        writable: false,
        enumerable: true,
        configurable: true
      });
      debugLog('✅ Extension bridge defined using defineProperty successfully');
      logger.info('Extension bridge defined using defineProperty');
    } catch (error) {
      debugLog('❌ Failed to define bridge using defineProperty', error.message);
      logger.warn('Failed to define bridge using defineProperty', error.message);
    }

    // Store in global variable as last resort
    debugLog('Setting fallback global variable');
    try {
      window.__progressDashboardExtensionBridge__ = bridgeObject;
      debugLog('✅ Fallback global variable set successfully');
    } catch (error) {
      debugLog('❌ Failed to set fallback global variable', error.message);
    }

    // Verify what was actually set
    debugLog('Verifying bridge assignment results', {
      directAssignment: typeof window.progressDashboardExtension !== 'undefined',
      fallbackVariable: typeof window.__progressDashboardExtensionBridge__ !== 'undefined',
      bridgeObjectValid: bridgeObject && typeof bridgeObject.sendMessage === 'function'
    });

    return bridgeObject;

  } catch (error) {
    debugLog('❌ Critical error in bridge creation', error.message);
    logger.error('Critical error in bridge creation', error.message);
    return null;
  }
}

// Main execution wrapper with comprehensive error handling
try {
  debugLog('🚀 Starting main content script execution');

  // Create bridge immediately with error handling
  debugLog('Creating CSP-compliant bridge');
  let extensionBridge = null;

  try {
    // Use CSP-compliant method (no script injection needed)
    extensionBridge = createCSPCompliantBridge();
    debugLog('CSP-compliant bridge creation completed', {
      bridgeCreated: extensionBridge !== null,
      bridgeType: typeof extensionBridge
    });
  } catch (error) {
    debugLog('❌ Critical error in CSP-compliant bridge creation', error.message);
    console.error('[CONTENT-DEBUG] Bridge creation failed:', error);

    // Fallback to original CSP-safe method
    debugLog('Falling back to original CSP-safe method');
    try {
      extensionBridge = createCSPSafeBridge();
      debugLog('Original fallback bridge creation completed', {
        bridgeCreated: extensionBridge !== null,
        bridgeType: typeof extensionBridge
      });
    } catch (fallbackError) {
      debugLog('❌ Critical error in original fallback bridge creation', fallbackError.message);
      console.error('[CONTENT-DEBUG] All bridge creation methods failed:', fallbackError);
    }
  }

  // Set DOM marker immediately
  try {
    document.documentElement.setAttribute('data-progress-dashboard-extension', 'production-loaded');
    debugLog('✅ DOM attribute set successfully');
  } catch (error) {
    debugLog('❌ Failed to set DOM attribute', error.message);
  }

  // Set content script marker immediately
  try {
    window.progressDashboardContentScript = true;
    debugLog('✅ Content script marker set successfully');
  } catch (error) {
    debugLog('❌ Failed to set content script marker', error.message);
  }

  // Legacy bridge for backward compatibility (will be overridden by injected bridge)
  window.progressDashboardExtension = {
    loaded: true,
    timestamp: Date.now(),
    version: '1.0.0-production',

    // Secure message sending to background script
    sendMessage: function(message) {
      logger.debug('Sending message to background script', { type: message.type });

      return new Promise((resolve, reject) => {
        try {
          // Send message to background script
          chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
              logger.error('Extension communication error', chrome.runtime.lastError.message);
              reject(new Error(chrome.runtime.lastError.message));
              return;
            }

            if (response && response.success) {
              logger.debug('Background script response received');
              resolve(response);
            } else {
              const error = response?.error || 'Unknown error';
              logger.warn('Background script returned error', { error });
              reject(new Error(error));
            }
          });
        } catch (error) {
          logger.error('Failed to send message to background script', error.message);
          reject(error);
        }
      });
    },

    // Extension information
    getDebugInfo: function() {
      return {
        version: '1.0.0-production',
        isAvailable: true,
        injectionMethod: 'secure',
        timestamp: this.timestamp,
        environment: isProduction ? 'production' : 'development'
      };
    },

    // Availability check
    isAvailable: function() {
      return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
    }
  };

  // Additional DOM marker (redundant but safe)
  try {
    document.documentElement.setAttribute('data-progress-dashboard-extension', 'production-loaded');
    debugLog('✅ Additional DOM attribute confirmed');
  } catch (error) {
    debugLog('❌ Failed to set additional DOM attribute', error.message);
  }

  // Additional content script marker (redundant but safe)
  try {
    window.progressDashboardContentScript = true;
    debugLog('✅ Additional content script marker confirmed');
  } catch (error) {
    debugLog('❌ Failed to set additional content script marker', error.message);
  }

  // Listen for messages from web page (including bridge messages)
  window.addEventListener('message', (event) => {
    // Validate origin for security
    if (event.origin !== window.location.origin) {
      logger.warn('Message from invalid origin ignored', { origin: event.origin });
      return;
    }

    logger.debug('Message received from web page', { type: event.data?.type });

    // Handle bridge communication
    if (event.data && event.data.type === 'EXTENSION_BRIDGE_MESSAGE') {
      logger.info('Bridge message received from web page');

      const originalMessage = event.data.data;
      const requestId = originalMessage.requestId;

      // Forward to background script
      chrome.runtime.sendMessage(originalMessage, (response) => {
        if (chrome.runtime.lastError) {
          logger.error('Failed to forward bridge message', chrome.runtime.lastError.message);

          // Send error response back to page
          window.postMessage({
            type: 'EXTENSION_BRIDGE_RESPONSE',
            requestId: requestId,
            success: false,
            error: chrome.runtime.lastError.message
          }, window.location.origin);
          return;
        }

        logger.debug('Bridge message forwarded to background script');

        // Send response back to page
        window.postMessage({
          type: 'EXTENSION_BRIDGE_RESPONSE',
          requestId: requestId,
          success: true,
          data: response
        }, window.location.origin);
      });
      return;
    }

    // Handle legacy OTP requests
    if (event.data && event.data.type === 'OTP_REQUEST') {
      logger.info('Legacy OTP request received from web page');

      // Forward to background script for user interaction
      chrome.runtime.sendMessage(event.data, (response) => {
        if (chrome.runtime.lastError) {
          logger.error('Failed to forward OTP request', chrome.runtime.lastError.message);
          return;
        }

        logger.debug('OTP request forwarded to background script');
      });
    }
  });

  // Listen for messages from background script
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    logger.debug('Message received from background script', { type: message.type });

    if (message.type === 'FORWARD_TO_PAGE') {
      // Forward response back to web page
      window.postMessage(message.data, window.location.origin);
      logger.debug('Message forwarded to web page');
    }

    sendResponse({ success: true });
  });

  // Notify web page that extension is ready (CSP-safe)
  function notifyExtensionReady() {
    debugLog('Starting extension ready notification');

    try {
      // Set content script marker
      debugLog('Setting content script marker');
      window.progressDashboardContentScript = true;

      // Set ready flag
      debugLog('Setting ready flag');
      window.progressDashboardExtensionReady = true;

      // Dispatch ready event
      debugLog('Dispatching ready event');
      const readyEvent = new CustomEvent('progressDashboardExtensionReady', {
        detail: {
          version: '1.0.0-production',
          timestamp: Date.now(),
          environment: isProduction ? 'production' : 'development',
          bridge: extensionBridge,
          bridgeAvailable: typeof window.progressDashboardExtension !== 'undefined'
        }
      });
      window.dispatchEvent(readyEvent);
      debugLog('✅ Extension ready event dispatched successfully');
      logger.info('Extension ready event dispatched');

      // Send ready message
      debugLog('Posting ready message');
      window.postMessage({
        type: 'PROGRESS_DASHBOARD_READY',
        data: {
          extensionLoaded: true,
          version: '1.0.0-production',
          timestamp: Date.now(),
          environment: isProduction ? 'production' : 'development',
        bridgeAvailable: typeof window.progressDashboardExtension !== 'undefined'
        }
      }, window.location.origin);
      debugLog('✅ Extension ready message posted successfully');
      logger.info('Extension ready message posted');

      // Final verification
      debugLog('Final verification of extension state', {
        progressDashboardExtension: typeof window.progressDashboardExtension !== 'undefined',
        progressDashboardContentScript: window.progressDashboardContentScript,
        progressDashboardExtensionReady: window.progressDashboardExtensionReady,
        fallbackBridge: typeof window.__progressDashboardExtensionBridge__ !== 'undefined'
      });

    } catch (error) {
      debugLog('❌ Failed to notify extension ready', error.message);
      logger.error('Failed to notify extension ready', error.message);
    }
  }

  // Notify readiness after a short delay with error handling
  debugLog('Setting up extension ready notification');
  try {
    setTimeout(() => {
      try {
        notifyExtensionReady();
      } catch (error) {
        debugLog('❌ Error in notifyExtensionReady', error.message);
        console.error('[CONTENT-DEBUG] notifyExtensionReady failed:', error);
      }
    }, 100);
    debugLog('✅ Notification timeout set successfully');
  } catch (error) {
    debugLog('❌ Failed to set notification timeout', error.message);
    // Try immediate notification as fallback
    try {
      notifyExtensionReady();
    } catch (fallbackError) {
      debugLog('❌ Fallback notification also failed', fallbackError.message);
    }
  }

  debugLog('✅ Content script setup completed successfully');

} catch (mainError) {
  console.log('[CONTENT-DEBUG] ❌ Critical error in main execution:', mainError.message);
  console.error('[CONTENT-DEBUG] Main execution failed:', mainError);
}

// Error handling for unhandled errors
window.addEventListener('error', (event) => {
  if (event.filename && event.filename.includes('chrome-extension://')) {
    logger.error('Extension script error', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno
    });
  }
});

logger.info('Production content script setup complete');
debugLog('🎉 Content script setup completed successfully');

// Final state check
try {
  debugLog('Final content script state check', {
    progressDashboardExtension: typeof window.progressDashboardExtension !== 'undefined',
    progressDashboardContentScript: window.progressDashboardContentScript,
    progressDashboardExtensionReady: window.progressDashboardExtensionReady,
    fallbackBridge: typeof window.__progressDashboardExtensionBridge__ !== 'undefined',
    domAttribute: document.documentElement.hasAttribute('data-progress-dashboard-extension')
  });
} catch (error) {
  debugLog('❌ Failed final state check', error.message);
}
