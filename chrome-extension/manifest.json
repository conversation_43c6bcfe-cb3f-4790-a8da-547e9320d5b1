{"manifest_version": 3, "name": "Progress Dashboard OTP Authenticator", "version": "1.0.0", "description": "Secure OTP authentication for Progress Dashboard", "permissions": ["storage", "activeTab", "notifications", "scripting"], "host_permissions": ["http://localhost:*/*", "https://localhost:*/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["http://localhost:*/*", "https://localhost:*/*", "https://*.progressdashboard.com/*"], "js": ["content-production.js"], "run_at": "document_idle"}], "action": {"default_popup": "popup/popup.html", "default_title": "Progress Dashboard OTP", "default_icon": {"16": "assets/icon16.png", "32": "assets/icon32.png", "48": "assets/icon48.png", "128": "assets/icon128.png"}}, "icons": {"16": "assets/icon16.png", "32": "assets/icon32.png", "48": "assets/icon48.png", "128": "assets/icon128.png"}, "web_accessible_resources": [{"resources": ["notification/*", "assets/*", "styles/*"], "matches": ["http://localhost:*/*", "https://localhost:*/*", "https://*.progressdashboard.com/*"]}], "externally_connectable": {"matches": ["http://localhost:*/*", "https://localhost:*/*", "https://*.progressdashboard.com/*"]}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}}