<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Icon Generator - Progress Dashboard Extension</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      padding: 20px;
      background: #f8fafc;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #1e293b;
      margin-bottom: 20px;
    }
    
    .icon-preview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
      margin: 30px 0;
    }
    
    .icon-item {
      text-align: center;
      padding: 20px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      background: #f8fafc;
    }
    
    .icon-item h3 {
      margin: 10px 0 5px;
      color: #475569;
      font-size: 14px;
    }
    
    .icon-item p {
      margin: 0;
      color: #64748b;
      font-size: 12px;
    }
    
    canvas {
      border: 1px solid #e2e8f0;
      border-radius: 4px;
      background: white;
    }
    
    .download-btn {
      background: #95E565;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      margin-top: 10px;
      transition: background 0.2s;
    }
    
    .download-btn:hover {
      background: #608F44;
    }
    
    .instructions {
      background: #f1f5f9;
      padding: 20px;
      border-radius: 8px;
      margin-top: 30px;
      border-left: 4px solid #95E565;
    }
    
    .instructions h3 {
      margin-top: 0;
      color: #1e293b;
    }
    
    .instructions ol {
      color: #475569;
      line-height: 1.6;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎨 Progress Dashboard Extension - Icon Generator</h1>
    <p>Generate PNG icons from SVG for Chrome Extension manifest</p>
    
    <div class="icon-preview">
      <div class="icon-item">
        <canvas id="icon16" width="16" height="16"></canvas>
        <h3>icon16.png</h3>
        <p>16×16 pixels</p>
        <button class="download-btn" onclick="downloadIcon('icon16', 16)">Download</button>
      </div>
      
      <div class="icon-item">
        <canvas id="icon32" width="32" height="32"></canvas>
        <h3>icon32.png</h3>
        <p>32×32 pixels</p>
        <button class="download-btn" onclick="downloadIcon('icon32', 32)">Download</button>
      </div>
      
      <div class="icon-item">
        <canvas id="icon48" width="48" height="48"></canvas>
        <h3>icon48.png</h3>
        <p>48×48 pixels</p>
        <button class="download-btn" onclick="downloadIcon('icon48', 48)">Download</button>
      </div>
      
      <div class="icon-item">
        <canvas id="icon128" width="128" height="128"></canvas>
        <h3>icon128.png</h3>
        <p>128×128 pixels</p>
        <button class="download-btn" onclick="downloadIcon('icon128', 128)">Download</button>
      </div>
    </div>
    
    <div class="instructions">
      <h3>📋 Instructions</h3>
      <ol>
        <li>Click "Download" for each icon size you need</li>
        <li>Save the downloaded PNG files to the <code>chrome-extension/assets/</code> folder</li>
        <li>Make sure the filenames match exactly: <code>icon16.png</code>, <code>icon32.png</code>, <code>icon48.png</code>, <code>icon128.png</code></li>
        <li>The manifest.json file is already configured to use these icons</li>
        <li>Load the extension in Chrome to test the icons</li>
      </ol>
    </div>
  </div>

  <script>
    // SVG content (embedded for generation)
    const svgContent = `
      <svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="64" cy="64" r="60" fill="url(#gradient1)" stroke="url(#gradient2)" stroke-width="4"/>
        <g transform="translate(32, 32)">
          <circle cx="32" cy="32" r="28" fill="none" stroke="white" stroke-width="3" opacity="0.3"/>
          <path d="M 32 4 A 28 28 0 0 1 56.284 20.716" fill="none" stroke="white" stroke-width="4" stroke-linecap="round"/>
          <path d="M 56.284 20.716 A 28 28 0 0 1 60 32" fill="none" stroke="#95E565" stroke-width="4" stroke-linecap="round"/>
          <path d="M 60 32 A 28 28 0 0 1 56.284 43.284" fill="none" stroke="#95E565" stroke-width="4" stroke-linecap="round"/>
          <circle cx="32" cy="32" r="12" fill="white" opacity="0.9"/>
          <path d="M 32 20 L 26 24 L 26 36 C 26 40 32 44 32 44 C 32 44 38 40 38 36 L 38 24 L 32 20 Z" fill="#95E565"/>
          <path d="M 29 32 L 31 34 L 35 28" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </g>
        <g transform="translate(20, 100)">
          <circle cx="0" cy="0" r="3" fill="white" opacity="0.8"/>
          <circle cx="12" cy="0" r="3" fill="#95E565" opacity="0.9"/>
          <circle cx="24" cy="0" r="3" fill="white" opacity="0.8"/>
          <circle cx="36" cy="0" r="3" fill="white" opacity="0.8"/>
          <circle cx="48" cy="0" r="3" fill="white" opacity="0.8"/>
          <circle cx="60" cy="0" r="3" fill="white" opacity="0.8"/>
        </g>
        <defs>
          <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#95E565;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#608F44;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#4a7c59;stop-opacity:1" />
          </linearGradient>
          <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
            <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
          </linearGradient>
        </defs>
      </svg>
    `;

    // Generate icons when page loads
    document.addEventListener('DOMContentLoaded', () => {
      generateIcon('icon16', 16);
      generateIcon('icon32', 32);
      generateIcon('icon48', 48);
      generateIcon('icon128', 128);
    });

    function generateIcon(canvasId, size) {
      const canvas = document.getElementById(canvasId);
      const ctx = canvas.getContext('2d');
      
      // Create image from SVG
      const img = new Image();
      const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(svgBlob);
      
      img.onload = function() {
        // Clear canvas
        ctx.clearRect(0, 0, size, size);
        
        // Draw image scaled to canvas size
        ctx.drawImage(img, 0, 0, size, size);
        
        // Clean up
        URL.revokeObjectURL(url);
      };
      
      img.src = url;
    }

    function downloadIcon(canvasId, size) {
      const canvas = document.getElementById(canvasId);
      
      // Create download link
      const link = document.createElement('a');
      link.download = `icon${size}.png`;
      link.href = canvas.toDataURL('image/png');
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      console.log(`Downloaded icon${size}.png`);
    }
  </script>
</body>
</html>
