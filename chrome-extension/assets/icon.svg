<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="64" cy="64" r="60" fill="url(#gradient1)" stroke="url(#gradient2)" stroke-width="4"/>
  
  <!-- Main Icon - Dashboard/Progress Symbol -->
  <g transform="translate(32, 32)">
    <!-- Outer Ring -->
    <circle cx="32" cy="32" r="28" fill="none" stroke="white" stroke-width="3" opacity="0.3"/>
    
    <!-- Progress Arc -->
    <path d="M 32 4 A 28 28 0 0 1 56.284 20.716" fill="none" stroke="white" stroke-width="4" stroke-linecap="round"/>
    <path d="M 56.284 20.716 A 28 28 0 0 1 60 32" fill="none" stroke="#95E565" stroke-width="4" stroke-linecap="round"/>
    <path d="M 60 32 A 28 28 0 0 1 56.284 43.284" fill="none" stroke="#95E565" stroke-width="4" stroke-linecap="round"/>
    
    <!-- Center Elements -->
    <circle cx="32" cy="32" r="12" fill="white" opacity="0.9"/>
    
    <!-- Shield/Security Symbol -->
    <path d="M 32 20 L 26 24 L 26 36 C 26 40 32 44 32 44 C 32 44 38 40 38 36 L 38 24 L 32 20 Z" fill="#95E565"/>
    
    <!-- Check Mark -->
    <path d="M 29 32 L 31 34 L 35 28" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- OTP Indicator Dots -->
  <g transform="translate(20, 100)">
    <circle cx="0" cy="0" r="3" fill="white" opacity="0.8"/>
    <circle cx="12" cy="0" r="3" fill="#95E565" opacity="0.9"/>
    <circle cx="24" cy="0" r="3" fill="white" opacity="0.8"/>
    <circle cx="36" cy="0" r="3" fill="white" opacity="0.8"/>
    <circle cx="48" cy="0" r="3" fill="white" opacity="0.8"/>
    <circle cx="60" cy="0" r="3" fill="white" opacity="0.8"/>
  </g>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#95E565;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#608F44;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4a7c59;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
    </linearGradient>
    
    <!-- Glow Effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Apply glow to main elements -->
  <style>
    circle, path {
      filter: url(#glow);
    }
  </style>
</svg>
