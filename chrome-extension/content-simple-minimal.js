/**
 * Minimal Production Content Script for Progress Dashboard Chrome Extension
 */

// Environment detection with error handling
let isProduction = true;
let isDevelopment = false;

try {
  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
    const manifest = chrome.runtime.getManifest();
    isProduction = !manifest.key;
    isDevelopment = !isProduction;
  }
} catch (error) {
  // Silent error handling for production
}

// Logging configuration - Production mode
const LOG_LEVELS = { ERROR: 0, WARN: 1, INFO: 2, DEBUG: 3 };
const CURRENT_LOG_LEVEL = LOG_LEVELS.ERROR; // Force production logging

function log(level, message, data = null) {
  if (level <= CURRENT_LOG_LEVEL) {
    const timestamp = new Date().toISOString();
    const levelName = Object.keys(LOG_LEVELS)[level];
    const sanitizedData = isProduction && data ? '[REDACTED]' : data;
    console.log(`[${timestamp}] ${levelName} [Content] ${message}`, sanitizedData || '');
  }
}

const logger = {
  error: (message, data) => log(LOG_LEVELS.ERROR, message, data),
  warn: (message, data) => log(LOG_LEVELS.WARN, message, data),
  info: (message, data) => log(LOG_LEVELS.INFO, message, data),
  debug: (message, data) => log(LOG_LEVELS.DEBUG, message, data)
};

function debugLog(message, data = null) {
  // Debug logging disabled for production
}

logger.info('Progress Dashboard content script loaded');

// Main execution wrapper
try {
  debugLog('🚀 Starting main content script execution');
  
  // Set DOM marker
  try {
    document.documentElement.setAttribute('data-progress-dashboard-extension', 'loaded');
    debugLog('✅ DOM attribute set successfully');
  } catch (error) {
    logger.error('Failed to set DOM attribute', error.message);
  }

  // Set content script marker
  try {
    window.progressDashboardContentScript = true;
    debugLog('✅ Content script marker set successfully');
  } catch (error) {
    logger.error('Failed to set content script marker', error.message);
  }

  // Create simple bridge
  try {
    window.progressDashboardExtension = {
      loaded: true,
      timestamp: Date.now(),
      version: '1.0.0',

      sendMessage: function(message) {
        return new Promise((resolve, reject) => {
          try {
            chrome.runtime.sendMessage(message, (response) => {
              if (chrome.runtime.lastError) {
                logger.error('Extension communication error', chrome.runtime.lastError.message);
                reject(new Error(chrome.runtime.lastError.message));
                return;
              }

              if (response && response.success) {
                resolve(response);
              } else {
                const error = response?.error || 'Unknown error';
                logger.warn('Background script returned error', error);
                reject(new Error(error));
              }
            });
          } catch (error) {
            logger.error('Failed to send message to background script', error.message);
            reject(error);
          }
        });
      },

      getDebugInfo: function() {
        return {
          version: '1.0.0',
          isAvailable: true,
          injectionMethod: 'minimal',
          timestamp: this.timestamp,
          environment: isProduction ? 'production' : 'development'
        };
      },

      isAvailable: function() {
        return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
      }
    };
    debugLog('✅ Simple bridge created successfully');
  } catch (error) {
    logger.error('Failed to create simple bridge', error.message);
  }

  // Set ready flag
  try {
    window.progressDashboardExtensionReady = true;
    debugLog('✅ Ready flag set successfully');
  } catch (error) {
    logger.error('Failed to set ready flag', error.message);
  }

  // Dispatch ready event
  try {
    const readyEvent = new CustomEvent('progressDashboardExtensionReady', {
      detail: {
        version: '1.0.0',
        timestamp: Date.now(),
        environment: isProduction ? 'production' : 'development'
      }
    });
    window.dispatchEvent(readyEvent);
    debugLog('✅ Extension ready event dispatched successfully');
  } catch (error) {
    logger.error('Failed to dispatch ready event', error.message);
  }

  // Post ready message
  try {
    window.postMessage({
      type: 'PROGRESS_DASHBOARD_READY',
      data: {
        extensionLoaded: true,
        version: '1.0.0',
        timestamp: Date.now(),
        environment: isProduction ? 'production' : 'development'
      }
    }, window.location.origin);
    debugLog('✅ Extension ready message posted successfully');
  } catch (error) {
    logger.error('Failed to post ready message', error.message);
  }

  debugLog('✅ Main execution completed successfully');

} catch (mainError) {
  logger.error('Critical error in main execution:', mainError.message);
}

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'FORWARD_TO_PAGE') {
    window.postMessage(message.data, window.location.origin);
  }

  sendResponse({ success: true });
});

logger.info('Content script setup complete');
debugLog('🎉 Content script setup completed successfully');

// Final state check
debugLog('Content script initialization complete');
