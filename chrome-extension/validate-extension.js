/**
 * Extension Validation Script
 * Run this to validate all required files are present
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Chrome Extension...\n');

// Required files
const requiredFiles = [
  'manifest.json',
  'background.js',
  'content-robust.js',
  'popup/popup.html',
  'popup/popup.js',
  'popup/popup.css',
  'assets/icon16.png',
  'assets/icon32.png',
  'assets/icon48.png',
  'assets/icon128.png'
];

let allValid = true;

// Check each required file
requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allValid = false;
  }
});

// Validate manifest.json
try {
  const manifestPath = path.join(__dirname, 'manifest.json');
  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
  
  console.log('\n📋 Manifest Validation:');
  console.log(`✅ Name: ${manifest.name}`);
  console.log(`✅ Version: ${manifest.version}`);
  console.log(`✅ Manifest Version: ${manifest.manifest_version}`);
  console.log(`✅ Permissions: ${manifest.permissions.length} items`);
  console.log(`✅ Host Permissions: ${manifest.host_permissions.length} items`);
  
  // Validate version format
  const versionRegex = /^\d+\.\d+\.\d+(\.\d+)?$/;
  if (!versionRegex.test(manifest.version)) {
    console.log(`❌ Invalid version format: ${manifest.version}`);
    allValid = false;
  }
  
} catch (error) {
  console.log(`❌ Manifest validation failed: ${error.message}`);
  allValid = false;
}

// Final result
console.log('\n' + '='.repeat(50));
if (allValid) {
  console.log('🎉 Extension validation PASSED!');
  console.log('✅ Ready for installation in Chrome');
} else {
  console.log('❌ Extension validation FAILED!');
  console.log('🔧 Please fix the missing files/issues above');
}
console.log('='.repeat(50));
