<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Final UI Test - Progress Dashboard Extension</title>
  
  <!-- Final Styles -->
  <link rel="stylesheet" href="popup/popup-minimal.css">
  
  <style>
    body {
      background: #f1f5f9;
      padding: 20px;
      font-family: 'Inter', sans-serif;
    }
    
    .test-container {
      max-width: 1000px;
      margin: 0 auto;
      text-align: center;
    }
    
    .test-title {
      color: #1A1919;
      margin-bottom: 30px;
    }
    
    .extension-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 30px;
      margin-bottom: 30px;
    }
    
    .extension-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .state-label {
      background: #1A1919;
      color: white;
      padding: 8px 16px;
      border-radius: 8px;
      font-weight: 600;
      margin-bottom: 15px;
      font-size: 14px;
    }
    
    .description {
      background: #f8fafc;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      text-align: left;
      border: 1px solid #e2e8f0;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1 class="test-title">Final Extension UI - Badge Centered with Footer</h1>
    <p style="color: #64748b; margin-bottom: 30px;">
      Simple badge-centered design with footer information
    </p>
    
    <div class="extension-grid">
      <!-- Not Connected State -->
      <div class="extension-wrapper">
        <div class="state-label">Not Connected State</div>
        <div class="app-container state-loading">
          <main class="app-main">
            <div class="state-container">
              <div class="badge-container">
                <div class="connection-badge not-connected">
                  <span class="status-dot not-connected"></span>
                  <span>Not Connected</span>
                </div>
              </div>
            </div>
          </main>
          <footer class="app-footer">
            <div class="footer-content">
              <span class="version">v1.0.0</span>
              <span class="author">Built by Hellozai</span>
            </div>
          </footer>
        </div>
      </div>
      
      <!-- Connected State -->
      <div class="extension-wrapper">
        <div class="state-label">Connected State</div>
        <div class="app-container state-idle">
          <main class="app-main">
            <div class="state-container">
              <div class="badge-container">
                <div class="connection-badge connected">
                  <span class="status-dot connected"></span>
                  <span>Connected</span>
                </div>
              </div>
            </div>
          </main>
          <footer class="app-footer">
            <div class="footer-content">
              <span class="version">v1.0.0</span>
              <span class="author">Built by Hellozai</span>
            </div>
          </footer>
        </div>
      </div>
      
      <!-- Authentication Request -->
      <div class="extension-wrapper">
        <div class="state-label">Authentication Request</div>
        <div class="app-container state-active">
          <main class="app-main">
            <div class="state-container">
              <div class="simple-message">
                <h3>Authentication Request</h3>
                <div class="request-info">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Website:</strong> localhost:5174</p>
                  <p><strong>Time:</strong> 4:59</p>
                </div>
                <div class="simple-actions">
                  <button class="simple-btn approve">Approve</button>
                  <button class="simple-btn reject">Reject</button>
                </div>
              </div>
            </div>
          </main>
          <footer class="app-footer">
            <div class="footer-content">
              <span class="version">v1.0.0</span>
              <span class="author">Built by Hellozai</span>
            </div>
          </footer>
        </div>
      </div>
      
      <!-- Success State -->
      <div class="extension-wrapper">
        <div class="state-label">Success State</div>
        <div class="app-container state-success">
          <main class="app-main">
            <div class="state-container">
              <div class="badge-container">
                <div class="connection-badge success">
                  <span class="status-dot success"></span>
                  <span>Login Approved!</span>
                </div>
              </div>
            </div>
          </main>
          <footer class="app-footer">
            <div class="footer-content">
              <span class="version">v1.0.0</span>
              <span class="author">Built by Hellozai</span>
            </div>
          </footer>
        </div>
      </div>
      
      <!-- Error State (Not Connected) -->
      <div class="extension-wrapper">
        <div class="state-label">Error State</div>
        <div class="app-container state-error">
          <main class="app-main">
            <div class="state-container">
              <div class="badge-container">
                <div class="connection-badge not-connected">
                  <span class="status-dot not-connected"></span>
                  <span>Not Connected</span>
                </div>
              </div>
            </div>
          </main>
          <footer class="app-footer">
            <div class="footer-content">
              <span class="version">v1.0.0</span>
              <span class="author">Built by Hellozai</span>
            </div>
          </footer>
        </div>
      </div>
    </div>
    
    <div class="description">
      <h4 style="margin: 0 0 15px 0; color: #1A1919;">Final UI Design Features:</h4>
      
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <div>
          <h5 style="color: #1A1919; margin-bottom: 10px;">Badge Design:</h5>
          <ul style="color: #64748b; font-size: 14px; margin: 0; padding-left: 20px;">
            <li>Centered in extension popup</li>
            <li>Categories.tsx styling with backdrop blur</li>
            <li>Dynamic status indicators</li>
            <li>Smooth color transitions</li>
            <li>Pulsing animation for connecting state</li>
          </ul>
        </div>
        
        <div>
          <h5 style="color: #1A1919; margin-bottom: 10px;">Footer Information:</h5>
          <ul style="color: #64748b; font-size: 14px; margin: 0; padding-left: 20px;">
            <li>Version number (v1.0.0)</li>
            <li>Built by Hellozai attribution</li>
            <li>Subtle styling with light background</li>
            <li>Consistent across all states</li>
            <li>Compact 10px font size</li>
          </ul>
        </div>
      </div>
      
      <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e2e8f0;">
        <h5 style="color: #1A1919; margin-bottom: 10px;">State Variations:</h5>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
          <div>
            <strong style="color: #2563eb;">Connecting:</strong>
            <span style="color: #64748b; font-size: 13px;">Blue badge with pulsing dot</span>
          </div>
          <div>
            <strong style="color: #22c55e;">Connected:</strong>
            <span style="color: #64748b; font-size: 13px;">Green badge with solid dot</span>
          </div>
          <div>
            <strong style="color: #22c55e;">Success:</strong>
            <span style="color: #64748b; font-size: 13px;">Bright green success badge</span>
          </div>
          <div>
            <strong style="color: #ef4444;">Error:</strong>
            <span style="color: #64748b; font-size: 13px;">Red badge for connection issues</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
