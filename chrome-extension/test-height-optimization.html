<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Height Optimization Test - Progress Dashboard Extension</title>
  
  <!-- Final Styles -->
  <link rel="stylesheet" href="popup/popup-minimal.css">
  
  <style>
    body {
      background: #f1f5f9;
      padding: 20px;
      font-family: 'Inter', sans-serif;
    }
    
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
      text-align: center;
    }
    
    .test-title {
      color: #1A1919;
      margin-bottom: 30px;
    }
    
    .comparison-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
      margin-bottom: 30px;
    }
    
    .comparison-item {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    }
    
    .state-label {
      background: #1A1919;
      color: white;
      padding: 8px 16px;
      border-radius: 8px;
      font-weight: 600;
      margin-bottom: 15px;
      font-size: 14px;
      display: inline-block;
    }
    
    .height-indicator {
      position: absolute;
      left: -60px;
      top: 0;
      bottom: 0;
      width: 50px;
      background: rgba(156, 238, 105, 0.1);
      border: 2px solid #9CEE69;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      font-weight: 600;
      color: #1A1919;
      writing-mode: vertical-rl;
      text-orientation: mixed;
    }
    
    .extension-wrapper {
      position: relative;
      display: flex;
      justify-content: center;
      margin: 20px 0;
    }
    
    .measurements {
      background: #f8fafc;
      border-radius: 8px;
      padding: 15px;
      margin-top: 15px;
      text-align: left;
      font-size: 12px;
      color: #64748b;
    }
    
    .measurements strong {
      color: #1A1919;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1 class="test-title">Height Optimization Test</h1>
    <p style="color: #64748b; margin-bottom: 30px;">
      Testing optimal heights for badge-only and authentication request states
    </p>
    
    <div class="comparison-grid">
      <!-- Badge Only State -->
      <div class="comparison-item">
        <div class="state-label">Badge Only (Connected)</div>
        <div class="extension-wrapper">
          <div class="height-indicator">100px</div>
          <div class="app-container state-idle">
            <main class="app-main">
              <div class="badge-container">
                <div class="connection-badge connected">
                  <span class="status-dot connected"></span>
                  <span>Connected</span>
                </div>
              </div>
            </main>
            <footer class="app-footer">
              <div class="footer-content">
                <span class="version">v1.0.0</span>
                <span class="author">Built by Hellozai</span>
              </div>
            </footer>
          </div>
        </div>
        <div class="measurements">
          <strong>Measurements:</strong><br>
          • Total Height: 100px<br>
          • Badge: ~30px<br>
          • Main Content: ~60px<br>
          • Footer: ~28px<br>
          • Padding: 12px
        </div>
      </div>
      
      <!-- Badge Only State (Not Connected) -->
      <div class="comparison-item">
        <div class="state-label">Badge Only (Not Connected)</div>
        <div class="extension-wrapper">
          <div class="height-indicator">100px</div>
          <div class="app-container state-loading">
            <main class="app-main">
              <div class="badge-container">
                <div class="connection-badge not-connected">
                  <span class="status-dot not-connected"></span>
                  <span>Not Connected</span>
                </div>
              </div>
            </main>
            <footer class="app-footer">
              <div class="footer-content">
                <span class="version">v1.0.0</span>
                <span class="author">Built by Hellozai</span>
              </div>
            </footer>
          </div>
        </div>
        <div class="measurements">
          <strong>Measurements:</strong><br>
          • Total Height: 100px<br>
          • Badge: ~30px<br>
          • Main Content: ~60px<br>
          • Footer: ~28px<br>
          • Padding: 12px
        </div>
      </div>
      
      <!-- Authentication Request State -->
      <div class="comparison-item">
        <div class="state-label">Authentication Request</div>
        <div class="extension-wrapper">
          <div class="height-indicator">320px</div>
          <div class="app-container state-active">
            <main class="app-main">
              <div class="badge-container">
                <div class="connection-badge connected">
                  <span class="status-dot connected"></span>
                  <span>Connected</span>
                </div>
              </div>
              <div class="state-container">
                <div class="simple-message">
                  <h3>Authentication Request</h3>
                  <div class="request-info">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Website:</strong> localhost:5174</p>
                    <p><strong>Time:</strong> 4:59</p>
                  </div>
                  <div class="simple-actions">
                    <button class="simple-btn approve">Approve</button>
                    <button class="simple-btn reject">Reject</button>
                  </div>
                </div>
              </div>
            </main>
            <footer class="app-footer">
              <div class="footer-content">
                <span class="version">v1.0.0</span>
                <span class="author">Built by Hellozai</span>
              </div>
            </footer>
          </div>
        </div>
        <div class="measurements">
          <strong>Measurements:</strong><br>
          • Total Height: 320px<br>
          • Badge: ~30px<br>
          • Auth Content: ~220px<br>
          • Main Content: ~280px<br>
          • Footer: ~28px<br>
          • Gap: 12px
        </div>
      </div>
    </div>
    
    <!-- Dynamic Height Demo -->
    <div style="background: white; border-radius: 12px; padding: 30px; box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);">
      <h3 style="margin-bottom: 20px; color: #1A1919;">Dynamic Height Demo</h3>
      <p style="color: #64748b; margin-bottom: 20px;">
        Click buttons to see how extension height changes dynamically
      </p>
      
      <div style="display: flex; gap: 10px; justify-content: center; margin-bottom: 20px;">
        <button onclick="showDemoState('badge')" style="padding: 8px 16px; border: 1px solid #ddd; border-radius: 6px; background: #fff; cursor: pointer;">Badge Only</button>
        <button onclick="showDemoState('auth')" style="padding: 8px 16px; border: 1px solid #ddd; border-radius: 6px; background: #fff; cursor: pointer;">Auth Request</button>
      </div>
      
      <div style="display: flex; justify-content: center;">
        <div id="dynamicDemo" class="app-container state-idle" style="transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1);">
          <main class="app-main">
            <div class="badge-container">
              <div class="connection-badge connected">
                <span class="status-dot connected"></span>
                <span>Connected</span>
              </div>
            </div>
            <div id="authContent" class="state-container hidden">
              <div class="simple-message">
                <h3>Authentication Request</h3>
                <div class="request-info">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Website:</strong> localhost:5174</p>
                  <p><strong>Time:</strong> 4:59</p>
                </div>
                <div class="simple-actions">
                  <button class="simple-btn approve">Approve</button>
                  <button class="simple-btn reject">Reject</button>
                </div>
              </div>
            </div>
          </main>
          <footer class="app-footer">
            <div class="footer-content">
              <span class="version">v1.0.0</span>
              <span class="author">Built by Hellozai</span>
            </div>
          </footer>
        </div>
      </div>
      
      <div style="text-align: center; margin-top: 15px; font-size: 14px; color: #64748b;">
        Current Height: <span id="currentHeight" style="font-weight: 600; color: #1A1919;">100px</span>
      </div>
    </div>
    
    <!-- Optimization Summary -->
    <div style="background: #f8fafc; border-radius: 12px; padding: 30px; margin-top: 30px; text-align: left;">
      <h3 style="margin-bottom: 20px; color: #1A1919; text-align: center;">Height Optimization Summary</h3>
      
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
        <div>
          <h4 style="color: #1A1919; margin-bottom: 15px;">✅ Optimizations Applied</h4>
          <ul style="color: #64748b; font-size: 14px; line-height: 1.6; margin: 0;">
            <li>Reduced badge-only height to 100px (was 120px)</li>
            <li>Increased auth request height to 320px (was 280px)</li>
            <li>Optimized padding and spacing</li>
            <li>Compact footer design (6px padding)</li>
            <li>Smaller badge size for better fit</li>
          </ul>
        </div>
        
        <div>
          <h4 style="color: #1A1919; margin-bottom: 15px;">📏 Height Breakdown</h4>
          <ul style="color: #64748b; font-size: 14px; line-height: 1.6; margin: 0;">
            <li><strong>Badge Only:</strong> 100px total (minimal, fits content)</li>
            <li><strong>Auth Request:</strong> 320px total (comfortable for buttons)</li>
            <li><strong>Dynamic Transition:</strong> 0.4s smooth animation</li>
            <li><strong>Footer:</strong> 28px consistent across states</li>
            <li><strong>Badge:</strong> 30px compact design</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    function showDemoState(state) {
      const demo = document.getElementById('dynamicDemo');
      const authContent = document.getElementById('authContent');
      const heightDisplay = document.getElementById('currentHeight');
      
      demo.classList.remove('state-idle', 'state-active');
      
      if (state === 'badge') {
        demo.classList.add('state-idle');
        authContent.classList.add('hidden');
        heightDisplay.textContent = '100px';
      } else if (state === 'auth') {
        demo.classList.add('state-active');
        authContent.classList.remove('hidden');
        heightDisplay.textContent = '320px';
      }
    }
  </script>
</body>
</html>
