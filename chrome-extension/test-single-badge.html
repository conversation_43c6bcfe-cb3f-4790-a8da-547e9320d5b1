<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Single Badge Test - Progress Dashboard Extension</title>
  
  <!-- Final Styles -->
  <link rel="stylesheet" href="popup/popup-minimal.css">
  
  <style>
    body {
      background: #f1f5f9;
      padding: 20px;
      font-family: 'Inter', sans-serif;
    }
    
    .test-container {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
    }
    
    .test-title {
      color: #1A1919;
      margin-bottom: 30px;
    }
    
    .demo-section {
      background: white;
      border-radius: 12px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    }
    
    .demo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 30px;
      margin-bottom: 30px;
    }
    
    .demo-item {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .demo-label {
      background: #1A1919;
      color: white;
      padding: 8px 16px;
      border-radius: 8px;
      font-weight: 600;
      margin-bottom: 15px;
      font-size: 14px;
    }
    
    .controls {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin: 20px 0;
    }
    
    .control-btn {
      padding: 8px 16px;
      border: 1px solid #ddd;
      border-radius: 6px;
      background: #fff;
      color: #333;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .control-btn:hover {
      background: #f5f5f5;
    }
    
    .control-btn.active {
      background: #9CEE69;
      color: #1A1919;
      border-color: #9CEE69;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1 class="test-title">Single Badge UI - Final Design</h1>
    <p style="color: #64748b; margin-bottom: 30px;">
      Simplified extension with one consistent connection badge
    </p>
    
    <!-- Interactive Demo -->
    <div class="demo-section">
      <h3 style="margin-bottom: 20px; color: #1A1919;">Interactive Demo</h3>
      
      <div class="controls">
        <button class="control-btn active" onclick="showDemo('connected')">Connected</button>
        <button class="control-btn" onclick="showDemo('not-connected')">Not Connected</button>
        <button class="control-btn" onclick="showDemo('auth-request')">Auth Request</button>
      </div>
      
      <div id="demoExtension" class="app-container state-idle" style="transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1);">
        <main class="app-main">
          <!-- Single Badge - Always Visible -->
          <div class="badge-container">
            <div id="demoBadge" class="connection-badge connected">
              <span id="demoDot" class="status-dot connected"></span>
              <span id="demoText">Connected</span>
            </div>
          </div>

          <!-- Auth Request Content - Hidden by default -->
          <div id="authContent" class="state-container hidden">
            <div class="simple-message">
              <h3>Authentication Request</h3>
              <div class="request-info">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Website:</strong> localhost:5174</p>
                <p><strong>Time:</strong> 4:59</p>
              </div>
              <div class="simple-actions">
                <button class="simple-btn approve">Approve</button>
                <button class="simple-btn reject">Reject</button>
              </div>
            </div>
          </div>
        </main>

        <footer class="app-footer">
          <div class="footer-content">
            <span class="version">v1.0.0</span>
            <span class="author">Built by Hellozai</span>
          </div>
        </footer>
      </div>

      <div style="text-align: center; margin-top: 15px; font-size: 14px; color: #64748b;">
        Current Height: <span id="heightDisplay" style="font-weight: 600; color: #1A1919;">100px</span>
      </div>
    </div>
    
    <!-- Static Examples -->
    <div class="demo-section">
      <h3 style="margin-bottom: 20px; color: #1A1919;">All States</h3>
      
      <div class="demo-grid">
        <!-- Connected State -->
        <div class="demo-item">
          <div class="demo-label">Connected State</div>
          <div class="app-container state-idle">
            <main class="app-main">
              <div class="badge-container">
                <div class="connection-badge connected">
                  <span class="status-dot connected"></span>
                  <span>Connected</span>
                </div>
              </div>
            </main>
            <footer class="app-footer">
              <div class="footer-content">
                <span class="version">v1.0.0</span>
                <span class="author">Built by Hellozai</span>
              </div>
            </footer>
          </div>
        </div>
        
        <!-- Not Connected State -->
        <div class="demo-item">
          <div class="demo-label">Not Connected State</div>
          <div class="app-container state-loading">
            <main class="app-main">
              <div class="badge-container">
                <div class="connection-badge not-connected">
                  <span class="status-dot not-connected"></span>
                  <span>Not Connected</span>
                </div>
              </div>
            </main>
            <footer class="app-footer">
              <div class="footer-content">
                <span class="version">v1.0.0</span>
                <span class="author">Built by Hellozai</span>
              </div>
            </footer>
          </div>
        </div>
        
        <!-- Authentication Request -->
        <div class="demo-item">
          <div class="demo-label">Authentication Request</div>
          <div class="app-container state-active">
            <main class="app-main">
              <div class="badge-container">
                <div class="connection-badge connected">
                  <span class="status-dot connected"></span>
                  <span>Connected</span>
                </div>
              </div>
              <div class="state-container">
                <div class="simple-message">
                  <h3>Authentication Request</h3>
                  <div class="request-info">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Website:</strong> localhost:5174</p>
                    <p><strong>Time:</strong> 4:59</p>
                  </div>
                  <div class="simple-actions">
                    <button class="simple-btn approve">Approve</button>
                    <button class="simple-btn reject">Reject</button>
                  </div>
                </div>
              </div>
            </main>
            <footer class="app-footer">
              <div class="footer-content">
                <span class="version">v1.0.0</span>
                <span class="author">Built by Hellozai</span>
              </div>
            </footer>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Benefits -->
    <div class="demo-section" style="text-align: left;">
      <h3 style="margin-bottom: 20px; color: #1A1919; text-align: center;">Benefits of Single Badge Design</h3>
      
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
        <div>
          <h4 style="color: #1A1919; margin-bottom: 15px;">✅ User Experience</h4>
          <ul style="color: #64748b; font-size: 14px; line-height: 1.6;">
            <li>Consistent badge position across all states</li>
            <li>Clear connection status at all times</li>
            <li>No confusing multiple badges</li>
            <li>Simple "Connected" vs "Not Connected" messaging</li>
          </ul>
        </div>
        
        <div>
          <h4 style="color: #1A1919; margin-bottom: 15px;">🔧 Technical Benefits</h4>
          <ul style="color: #64748b; font-size: 14px; line-height: 1.6;">
            <li>Single badge element to manage</li>
            <li>Simplified state management</li>
            <li>Consistent styling and behavior</li>
            <li>All backend functionality preserved</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    function showDemo(type) {
      const badge = document.getElementById('demoBadge');
      const dot = document.getElementById('demoDot');
      const text = document.getElementById('demoText');
      const authContent = document.getElementById('authContent');
      const container = document.getElementById('demoExtension');
      const heightDisplay = document.getElementById('heightDisplay');
      const buttons = document.querySelectorAll('.control-btn');

      // Update button states
      buttons.forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      // Reset classes
      badge.classList.remove('connected', 'not-connected');
      dot.classList.remove('connected', 'not-connected');
      container.classList.remove('state-idle', 'state-loading', 'state-active');
      authContent.classList.add('hidden');

      switch(type) {
        case 'connected':
          badge.classList.add('connected');
          dot.classList.add('connected');
          text.textContent = 'Connected';
          container.classList.add('state-idle');
          heightDisplay.textContent = '100px';
          break;
        case 'not-connected':
          badge.classList.add('not-connected');
          dot.classList.add('not-connected');
          text.textContent = 'Not Connected';
          container.classList.add('state-loading');
          heightDisplay.textContent = '100px';
          break;
        case 'auth-request':
          badge.classList.add('connected');
          dot.classList.add('connected');
          text.textContent = 'Connected';
          container.classList.add('state-active');
          authContent.classList.remove('hidden');
          heightDisplay.textContent = '320px';
          break;
      }
    }
  </script>
</body>
</html>
