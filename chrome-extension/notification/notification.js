/**
 * Notification JavaScript for Chrome Extension
 * 
 * Handles notification UI and user interactions
 */

// Notification state
let notificationState = {
  requestData: null,
  timer: null,
  timeRemaining: 0,
  isProcessing: false
};

// DOM elements
const elements = {
  container: document.getElementById('notificationContainer'),
  successState: document.getElementById('successState'),
  rejectedState: document.getElementById('rejectedState'),
  
  // Request details
  requestEmail: document.getElementById('requestEmail'),
  requestWebsite: document.getElementById('requestWebsite'),
  requestTimer: document.getElementById('requestTimer'),
  progressBar: document.getElementById('progressBar'),
  progressText: document.getElementById('progressText'),
  
  // Buttons
  approveBtn: document.getElementById('approveBtn'),
  rejectBtn: document.getElementById('rejectBtn'),
  closeBtn: document.getElementById('closeBtn')
};

// Initialize notification
document.addEventListener('DOMContentLoaded', () => {
  console.log('🔔 Notification initializing...');
  
  // Get request data from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const requestData = {
    email: urlParams.get('email') || '<EMAIL>',
    website: urlParams.get('website') || 'localhost:5174',
    expires_in: parseInt(urlParams.get('expires_in')) || 300,
    otp_code: urlParams.get('otp_code') || '123456',
    otp_key: urlParams.get('otp_key') || 'test-key',
    request_id: urlParams.get('request_id') || 'test-request'
  };
  
  // Initialize with request data
  initializeNotification(requestData);
  
  // Set up event listeners
  setupEventListeners();
  
  console.log('✅ Notification initialized');
});

// Initialize notification with request data
function initializeNotification(requestData) {
  notificationState.requestData = requestData;
  notificationState.timeRemaining = requestData.expires_in;
  
  // Update UI with request data
  updateRequestInfo(requestData);
  
  // Start timer
  startTimer();
  
  // Add entrance animation
  elements.container?.classList.add('animate-slide-in-right');
  
  // Play notification sound (if enabled)
  playNotificationSound();
}

// Update request information in UI
function updateRequestInfo(data) {
  if (elements.requestEmail) {
    elements.requestEmail.textContent = data.email;
  }
  
  if (elements.requestWebsite) {
    elements.requestWebsite.textContent = data.website;
  }
  
  // Update progress text
  if (elements.progressText) {
    elements.progressText.textContent = `Waiting for your response...`;
  }
}

// Set up event listeners
function setupEventListeners() {
  // Action buttons
  elements.approveBtn?.addEventListener('click', handleApprove);
  elements.rejectBtn?.addEventListener('click', handleReject);
  elements.closeBtn?.addEventListener('click', handleClose);
  
  // Keyboard shortcuts
  document.addEventListener('keydown', handleKeydown);
  
  // Window focus/blur
  window.addEventListener('focus', handleWindowFocus);
  window.addEventListener('blur', handleWindowBlur);
  
  // Auto-close on click outside (optional)
  document.addEventListener('click', (e) => {
    if (e.target === document.body) {
      handleClose();
    }
  });
}

// Start countdown timer
function startTimer() {
  const totalTime = notificationState.requestData.expires_in;
  
  const updateTimer = () => {
    const minutes = Math.floor(notificationState.timeRemaining / 60);
    const seconds = notificationState.timeRemaining % 60;
    const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    
    // Update timer display
    if (elements.requestTimer) {
      elements.requestTimer.textContent = timeString;
    }
    
    // Update progress bar
    const progress = ((totalTime - notificationState.timeRemaining) / totalTime) * 100;
    if (elements.progressBar) {
      elements.progressBar.style.width = `${progress}%`;
    }
    
    // Update progress text based on time remaining
    if (elements.progressText) {
      if (notificationState.timeRemaining <= 30) {
        elements.progressText.textContent = 'Request expiring soon!';
        elements.progressText.style.color = 'var(--color-error-600)';
      } else if (notificationState.timeRemaining <= 60) {
        elements.progressText.textContent = 'Please respond soon...';
        elements.progressText.style.color = 'var(--color-warning-600)';
      }
    }
    
    // Add urgency animations when time is running out
    if (notificationState.timeRemaining <= 30) {
      elements.container?.classList.add('animate-pulse');
    }
    
    notificationState.timeRemaining--;
    
    // Handle expiry
    if (notificationState.timeRemaining < 0) {
      clearInterval(notificationState.timer);
      handleExpiry();
    }
  };
  
  updateTimer();
  notificationState.timer = setInterval(updateTimer, 1000);
}

// Handle approve action
async function handleApprove() {
  if (notificationState.isProcessing) return;
  
  try {
    notificationState.isProcessing = true;
    
    // Update button state
    elements.approveBtn.classList.add('loading');
    elements.approveBtn.disabled = true;
    elements.rejectBtn.disabled = true;
    
    // Send approval to background script
    const response = await chrome.runtime.sendMessage({
      type: 'OTP_RESPONSE',
      data: {
        requestId: notificationState.requestData.request_id,
        action: 'APPROVE',
        email: notificationState.requestData.email,
        otp_code: notificationState.requestData.otp_code,
        otp_key: notificationState.requestData.otp_key
      }
    });
    
    if (response && response.success) {
      showSuccessState();
    } else {
      throw new Error(response?.error || 'Failed to approve request');
    }
    
  } catch (error) {
    console.error('Failed to approve request:', error);
    showError('Failed to approve request');
    
    // Reset button states
    elements.approveBtn.classList.remove('loading');
    elements.approveBtn.disabled = false;
    elements.rejectBtn.disabled = false;
    notificationState.isProcessing = false;
  }
}

// Handle reject action
async function handleReject() {
  if (notificationState.isProcessing) return;
  
  try {
    notificationState.isProcessing = true;
    
    // Update button state
    elements.rejectBtn.classList.add('loading');
    elements.rejectBtn.disabled = true;
    elements.approveBtn.disabled = true;
    
    // Send rejection to background script
    const response = await chrome.runtime.sendMessage({
      type: 'OTP_RESPONSE',
      data: {
        requestId: notificationState.requestData.request_id,
        action: 'REJECT',
        email: notificationState.requestData.email
      }
    });
    
    if (response && response.success !== false) {
      showRejectedState();
    } else {
      throw new Error(response?.error || 'Failed to reject request');
    }
    
  } catch (error) {
    console.error('Failed to reject request:', error);
    showError('Failed to reject request');
    
    // Reset button states
    elements.rejectBtn.classList.remove('loading');
    elements.rejectBtn.disabled = false;
    elements.approveBtn.disabled = false;
    notificationState.isProcessing = false;
  }
}

// Handle close action
function handleClose() {
  // Add exit animation
  elements.container?.classList.add('animate-slide-out-right');
  
  // Close window after animation
  setTimeout(() => {
    window.close();
  }, 300);
}

// Handle keyboard shortcuts
function handleKeydown(e) {
  if (notificationState.isProcessing) return;
  
  switch (e.key) {
    case 'Enter':
    case ' ':
      e.preventDefault();
      handleApprove();
      break;
    case 'Escape':
      e.preventDefault();
      handleReject();
      break;
    case 'c':
    case 'C':
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
        handleClose();
      }
      break;
  }
}

// Handle window focus
function handleWindowFocus() {
  // Remove any urgency animations when user focuses
  elements.container?.classList.remove('animate-pulse');
  
  // Update title
  document.title = 'Progress Dashboard - OTP Request';
}

// Handle window blur
function handleWindowBlur() {
  // Add urgency animation if time is running out
  if (notificationState.timeRemaining <= 30) {
    elements.container?.classList.add('animate-pulse');
  }
  
  // Update title with urgency
  if (notificationState.timeRemaining <= 60) {
    document.title = `⚠️ OTP Request - ${Math.floor(notificationState.timeRemaining / 60)}:${(notificationState.timeRemaining % 60).toString().padStart(2, '0')}`;
  }
}

// Handle request expiry
function handleExpiry() {
  console.log('🕐 Request expired');
  
  // Clear timer
  if (notificationState.timer) {
    clearInterval(notificationState.timer);
    notificationState.timer = null;
  }
  
  // Show expiry message
  if (elements.progressText) {
    elements.progressText.textContent = 'Request expired';
    elements.progressText.style.color = 'var(--color-error-600)';
  }
  
  // Disable buttons
  elements.approveBtn.disabled = true;
  elements.rejectBtn.disabled = true;
  
  // Add shake animation
  elements.container?.classList.add('animate-shake');
  
  // Auto-close after 3 seconds
  setTimeout(() => {
    handleClose();
  }, 3000);
}

// Show success state
function showSuccessState() {
  console.log('✅ Request approved');
  
  // Clear timer
  if (notificationState.timer) {
    clearInterval(notificationState.timer);
    notificationState.timer = null;
  }
  
  // Hide main container
  elements.container?.classList.add('hidden');
  
  // Show success state
  elements.successState?.classList.remove('hidden');
  elements.successState?.classList.add('animate-scale-in');
  
  // Play success sound
  playSuccessSound();
  
  // Auto-close after 2 seconds
  setTimeout(() => {
    window.close();
  }, 2000);
}

// Show rejected state
function showRejectedState() {
  console.log('❌ Request rejected');
  
  // Clear timer
  if (notificationState.timer) {
    clearInterval(notificationState.timer);
    notificationState.timer = null;
  }
  
  // Hide main container
  elements.container?.classList.add('hidden');
  
  // Show rejected state
  elements.rejectedState?.classList.remove('hidden');
  elements.rejectedState?.classList.add('animate-scale-in');
  
  // Auto-close after 2 seconds
  setTimeout(() => {
    window.close();
  }, 2000);
}

// Show error message
function showError(message) {
  // Create error toast
  const errorToast = document.createElement('div');
  errorToast.className = 'notification notification-error animate-slide-in-right';
  errorToast.innerHTML = `
    <div class="p-3">
      <p class="text-sm font-medium text-error-700">${message}</p>
    </div>
  `;
  
  document.body.appendChild(errorToast);
  
  // Remove after 3 seconds
  setTimeout(() => {
    errorToast.classList.add('animate-slide-out-right');
    setTimeout(() => {
      document.body.removeChild(errorToast);
    }, 300);
  }, 3000);
}

// Play notification sound
function playNotificationSound() {
  try {
    // Create audio context for notification sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
    
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.2);
  } catch (error) {
    console.warn('Failed to play notification sound:', error);
  }
}

// Play success sound
function playSuccessSound() {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    // Success melody
    oscillator.frequency.setValueAtTime(523, audioContext.currentTime); // C
    oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1); // E
    oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2); // G
    
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);
  } catch (error) {
    console.warn('Failed to play success sound:', error);
  }
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (notificationState.timer) {
    clearInterval(notificationState.timer);
  }
});

console.log('🔔 Notification script loaded');
