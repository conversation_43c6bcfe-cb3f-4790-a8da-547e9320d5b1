<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Progress Dashboard - OTP Request</title>
  
  <!-- Styles -->
  <link rel="stylesheet" href="../styles/design-tokens.css">
  <link rel="stylesheet" href="../styles/animations.css">
  <link rel="stylesheet" href="../styles/components.css">
  <link rel="stylesheet" href="notification.css">
  
  <!-- Preload fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Notification Container -->
  <div id="notificationContainer" class="notification-container animate-slide-in-right">
    
    <!-- Header -->
    <div class="notification-header">
      <div class="header-icon animate-pulse">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="header-text">
        <h1 class="header-title">Progress Dashboard</h1>
        <p class="header-subtitle">Login Request</p>
      </div>
      <button id="closeBtn" class="close-btn btn btn-ghost btn-sm" title="Close">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>

    <!-- Content -->
    <div class="notification-content">
      
      <!-- Request Info -->
      <div class="request-info">
        <div class="info-item">
          <div class="info-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="info-content">
            <span class="info-label">Email:</span>
            <span id="requestEmail" class="info-value"><EMAIL></span>
          </div>
        </div>
        
        <div class="info-item">
          <div class="info-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
              <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="info-content">
            <span class="info-label">Expires in:</span>
            <span id="requestTimer" class="info-value timer">4:59</span>
          </div>
        </div>
        
        <div class="info-item">
          <div class="info-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              <path d="M12 1v6m0 6v6m6-12h-6m-6 0h6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
          <div class="info-content">
            <span class="info-label">Website:</span>
            <span id="requestWebsite" class="info-value">localhost:5174</span>
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="progress-section">
        <div class="progress">
          <div id="progressBar" class="progress-bar animate-progress"></div>
        </div>
        <p class="progress-text">
          <span id="progressText">Waiting for your response...</span>
        </p>
      </div>

      <!-- Security Notice -->
      <div class="security-notice">
        <div class="notice-icon">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <p class="notice-text">
          Only approve if you initiated this login request
        </p>
      </div>

    </div>

    <!-- Actions -->
    <div class="notification-actions">
      <button id="approveBtn" class="btn btn-primary btn-full hover-lift ripple-effect">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        Approve Login
      </button>
      
      <button id="rejectBtn" class="btn btn-secondary btn-full hover-lift">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        Reject
      </button>
    </div>

    <!-- Footer -->
    <div class="notification-footer">
      <div class="footer-info">
        <span class="extension-version">Extension v1.0.0</span>
        <span class="security-badge">
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Secure
        </span>
      </div>
    </div>

  </div>

  <!-- Success State -->
  <div id="successState" class="success-container hidden animate-scale-in">
    <div class="success-content">
      <div class="success-icon animate-bounce">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
      <h2 class="success-title">Login Approved!</h2>
      <p class="success-message">You can now continue to Progress Dashboard</p>
    </div>
  </div>

  <!-- Rejected State -->
  <div id="rejectedState" class="rejected-container hidden animate-scale-in">
    <div class="rejected-content">
      <div class="rejected-icon animate-shake">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
          <path d="M15 9l-6 6M9 9l6 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <h2 class="rejected-title">Login Rejected</h2>
      <p class="rejected-message">The login request has been rejected</p>
    </div>
  </div>

  <!-- Scripts -->
  <script src="../utils/communication.js"></script>
  <script src="../utils/security.js"></script>
  <script src="notification.js"></script>
</body>
</html>
