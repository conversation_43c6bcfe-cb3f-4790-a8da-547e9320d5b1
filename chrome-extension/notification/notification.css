/**
 * Notification Styles for Chrome Extension
 * 
 * Modern, dynamic notification UI with smooth animations
 */

/* Base Styles */
body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-sans);
  background: transparent;
  overflow: hidden;
}

/* Notification Container */
.notification-container {
  width: var(--notification-width);
  min-height: 280px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(20px);
}

.notification-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #9CEE69;
  z-index: 1;
}

/* Header */
.notification-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: #ffffff;
  color: var(--color-eerie-black);
  border-bottom: 1px solid #e2e8f0;
  position: relative;
  z-index: 2;
}

.header-icon {
  width: 40px;
  height: 40px;
  background: #9CEE69;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  margin: 0;
  line-height: var(--line-height-sm);
}

.header-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  margin: 0;
  font-weight: var(--font-weight-medium);
}

.close-btn {
  color: #64748b;
  border-color: #e2e8f0;
  flex-shrink: 0;
}

.close-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: var(--color-eerie-black);
}

/* Content */
.notification-content {
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-5);
}

/* Request Info */
.request-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--easing-ease-in-out);
}

.info-item:hover {
  background: #f8fafc;
}

.info-icon {
  width: 32px;
  height: 32px;
  background: #f0fdf4;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #608F44;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.info-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-semibold);
}

.info-value.timer {
  color: var(--color-warning-600);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
}

/* Progress Section */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.progress {
  height: 6px;
  background: #e2e8f0;
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: #9CEE69;
  border-radius: var(--radius-full);
  transition: width var(--duration-slow) var(--easing-ease-out);
  position: relative;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  text-align: center;
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  font-weight: var(--font-weight-medium);
}

/* Security Notice */
.security-notice {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  background: var(--color-warning-50);
  border: 1px solid var(--color-warning-200);
  border-radius: var(--radius-lg);
}

.notice-icon {
  color: var(--color-warning-600);
  flex-shrink: 0;
}

.notice-text {
  font-size: var(--font-size-xs);
  color: var(--color-warning-700);
  margin: 0;
  font-weight: var(--font-weight-medium);
}

/* Actions */
.notification-actions {
  padding: 0 var(--spacing-4) var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.notification-actions .btn {
  justify-content: center;
  font-weight: var(--font-weight-semibold);
}

/* Footer */
.notification-footer {
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--color-secondary-50);
  border-top: 1px solid var(--color-border);
}

.footer-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.extension-version {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.security-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--color-success-600);
  font-weight: var(--font-weight-medium);
}

/* Success State */
.success-container {
  width: var(--notification-width);
  background: var(--color-surface);
  border: 1px solid var(--color-success-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  position: relative;
}

.success-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #22c55e;
}

.success-content {
  padding: var(--spacing-6);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
}

.success-icon {
  width: 80px;
  height: 80px;
  background: #22c55e;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.success-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: #22c55e;
  margin: 0;
}

.success-message {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-lg);
}

/* Rejected State */
.rejected-container {
  width: var(--notification-width);
  background: var(--color-surface);
  border: 1px solid var(--color-error-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  overflow: hidden;
  position: relative;
}

.rejected-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #ef4444;
}

.rejected-content {
  padding: var(--spacing-6);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
}

.rejected-icon {
  width: 80px;
  height: 80px;
  background: #ef4444;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.rejected-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: #ef4444;
  margin: 0;
}

.rejected-message {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-lg);
}

/* Hidden state */
.hidden {
  display: none !important;
}

/* Responsive adjustments */
@media (max-width: 360px) {
  .notification-container,
  .success-container,
  .rejected-container {
    width: 100vw;
    border-radius: 0;
  }
  
  .notification-content {
    padding: var(--spacing-3);
  }
  
  .notification-actions {
    padding: 0 var(--spacing-3) var(--spacing-3);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .notification-container,
  .success-container,
  .rejected-container {
    background: var(--color-dark-700);
    border-color: var(--color-dark-600);
  }
  
  .info-item:hover {
    background: var(--color-dark-600);
  }
  
  .security-notice {
    background: var(--color-dark-600);
    border-color: var(--color-warning-700);
  }
  
  .notification-footer {
    background: var(--color-dark-600);
    border-color: var(--color-dark-500);
  }
}
