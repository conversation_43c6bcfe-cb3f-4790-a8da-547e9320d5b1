<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Footer Position - Progress Dashboard Extension</title>
  
  <!-- Styles -->
  <link rel="stylesheet" href="styles/design-tokens.css">
  <link rel="stylesheet" href="styles/animations.css">
  <link rel="stylesheet" href="styles/components.css">
  <link rel="stylesheet" href="popup/popup.css">
  
  <style>
    body {
      background: #f1f5f9;
      padding: 20px;
      font-family: 'Inter', sans-serif;
    }
    
    .test-container {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
    }
    
    .test-title {
      color: #1A1919;
      margin-bottom: 30px;
    }
    
    .extension-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 30px;
      margin-bottom: 30px;
    }
    
    .extension-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .state-label {
      background: #9CEE69;
      color: #1A1919;
      padding: 8px 16px;
      border-radius: 8px;
      font-weight: 600;
      margin-bottom: 15px;
      font-size: 14px;
    }
    
    .footer-highlight {
      position: relative;
    }
    
    .footer-highlight::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: -5px;
      right: -5px;
      height: 5px;
      background: #ef4444;
      border-radius: 0 0 8px 8px;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1 class="test-title">Footer Position Test</h1>
    <p style="color: #64748b; margin-bottom: 30px;">
      Testing footer positioning across different states. Red line shows footer position.
    </p>
    
    <div class="extension-grid">
      <!-- Idle State -->
      <div class="extension-wrapper">
        <div class="state-label">Idle State (200px)</div>
        <div class="app-container state-idle footer-highlight">
          <header class="app-header">
            <div class="header-content">
              <div class="brand">
                <div class="brand-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="brand-text">
                  <h1 class="brand-title">Progress Dashboard</h1>
                  <p class="brand-subtitle">OTP Authenticator</p>
                </div>
              </div>
            </div>
          </header>
          
          <main class="app-main">
            <div class="state-container">
              <div class="idle-content">
                <div class="status-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <h2 class="status-title">Ready for Authentication</h2>
              </div>
            </div>
          </main>
          
          <footer class="app-footer">
            <div class="footer-content">
              <div class="extension-info">
                <span class="version">v1.0.0</span>
                <span class="status">
                  <span class="status-dot"></span>
                  Connected
                </span>
              </div>
            </div>
          </footer>
        </div>
      </div>
      
      <!-- Loading State -->
      <div class="extension-wrapper">
        <div class="state-label">Loading State (250px)</div>
        <div class="app-container state-loading footer-highlight">
          <header class="app-header">
            <div class="header-content">
              <div class="brand">
                <div class="brand-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="brand-text">
                  <h1 class="brand-title">Progress Dashboard</h1>
                  <p class="brand-subtitle">OTP Authenticator</p>
                </div>
              </div>
            </div>
          </header>
          
          <main class="app-main">
            <div class="state-container">
              <div class="loading-content">
                <p class="loading-text">Initializing extension<span class="loading-dots"></span></p>
              </div>
            </div>
          </main>
          
          <footer class="app-footer">
            <div class="footer-content">
              <div class="extension-info">
                <span class="version">v1.0.0</span>
                <span class="status">
                  <span class="status-dot"></span>
                  Connected
                </span>
              </div>
            </div>
          </footer>
        </div>
      </div>
      
      <!-- Success State -->
      <div class="extension-wrapper">
        <div class="state-label">Success State (300px)</div>
        <div class="app-container state-success footer-highlight">
          <header class="app-header">
            <div class="header-content">
              <div class="brand">
                <div class="brand-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div class="brand-text">
                  <h1 class="brand-title">Progress Dashboard</h1>
                  <p class="brand-subtitle">OTP Authenticator</p>
                </div>
              </div>
            </div>
          </header>
          
          <main class="app-main">
            <div class="state-container">
              <div class="success-content">
                <div class="success-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <h2 class="success-title">Login Approved!</h2>
                <p class="success-description">You have successfully approved the login request.</p>
              </div>
            </div>
          </main>
          
          <footer class="app-footer">
            <div class="footer-content">
              <div class="extension-info">
                <span class="version">v1.0.0</span>
                <span class="status">
                  <span class="status-dot"></span>
                  Connected
                </span>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </div>
    
    <div style="background: #f8fafc; border-radius: 8px; padding: 20px; text-align: left; max-width: 600px; margin: 0 auto;">
      <h4 style="margin: 0 0 10px 0; color: #1A1919;">Footer Position Fixes Applied:</h4>
      <ul style="margin: 0; color: #64748b; font-size: 14px;">
        <li><strong>margin-top: auto</strong> - Pushes footer to bottom of container</li>
        <li><strong>flex-shrink: 0</strong> - Prevents footer from shrinking</li>
        <li><strong>Main content centering</strong> - Centers content vertically in available space</li>
        <li><strong>Removed loading spinner</strong> - Simplified loading state</li>
      </ul>
    </div>
  </div>
</body>
</html>
