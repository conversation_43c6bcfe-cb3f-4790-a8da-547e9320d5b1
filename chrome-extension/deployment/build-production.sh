#!/bin/bash

# 🚀 Production Build Script for Progress Dashboard Chrome Extension
# This script creates an optimized production build ready for Chrome Web Store

set -e  # Exit on any error

echo "🚀 Starting Production Build Process..."
echo "========================================"

# Configuration
BUILD_DIR="build-production"
PACKAGE_NAME="progress-dashboard-extension-v1.0.0"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Clean previous builds
log_info "Cleaning previous builds..."
rm -rf "$BUILD_DIR"
rm -f *.zip
mkdir -p "$BUILD_DIR"

# Step 2: Copy core files
log_info "Copying core extension files..."
cp manifest.json "$BUILD_DIR/"
cp background.js "$BUILD_DIR/"
cp content-simple.js "$BUILD_DIR/"

# Step 3: Copy directories
log_info "Copying extension directories..."
cp -r popup "$BUILD_DIR/"
cp -r assets "$BUILD_DIR/"
cp -r styles "$BUILD_DIR/"
cp -r utils "$BUILD_DIR/"

# Step 4: Copy documentation
log_info "Copying documentation..."
cp README.md "$BUILD_DIR/"
cp TROUBLESHOOTING.md "$BUILD_DIR/"

# Step 5: Create production manifest
log_info "Optimizing manifest for production..."
cat > "$BUILD_DIR/manifest.json" << 'EOF'
{
  "manifest_version": 3,
  "name": "Progress Dashboard - OTP Authenticator",
  "version": "1.0.0",
  "description": "Secure OTP authentication for Progress Dashboard. Provides seamless two-factor authentication through Chrome Extension integration.",

  "permissions": [
    "storage",
    "activeTab",
    "notifications",
    "scripting"
  ],

  "host_permissions": [
    "https://progressdashboard.com/*",
    "https://app.progressdashboard.com/*",
    "https://dashboard.progressdashboard.com/*"
  ],

  "background": {
    "service_worker": "background.js"
  },
  
  "content_scripts": [
    {
      "matches": [
        "https://progressdashboard.com/*",
        "https://app.progressdashboard.com/*",
        "https://dashboard.progressdashboard.com/*"
      ],
      "js": ["content-simple.js"],
      "run_at": "document_start"
    }
  ],
  
  "action": {
    "default_popup": "popup/popup.html",
    "default_title": "Progress Dashboard OTP",
    "default_icon": {
      "16": "assets/icon16.png",
      "32": "assets/icon32.png",
      "48": "assets/icon48.png",
      "128": "assets/icon128.png"
    }
  },
  
  "icons": {
    "16": "assets/icon16.png",
    "32": "assets/icon32.png",
    "48": "assets/icon48.png",
    "128": "assets/icon128.png"
  },
  
  "web_accessible_resources": [
    {
      "resources": [
        "assets/*",
        "styles/*"
      ],
      "matches": [
        "https://progressdashboard.com/*",
        "https://app.progressdashboard.com/*",
        "https://dashboard.progressdashboard.com/*"
      ]
    }
  ],
  
  "externally_connectable": {
    "matches": [
      "https://progressdashboard.com/*",
      "https://app.progressdashboard.com/*",
      "https://dashboard.progressdashboard.com/*"
    ]
  },

  "content_security_policy": {
    "extension_pages": "script-src 'self'; object-src 'self'"
  },

  "homepage_url": "https://progressdashboard.com",
  "author": "Progress Dashboard Team"
}
EOF

# Step 6: Validate icons
log_info "Validating required icons..."
REQUIRED_ICONS=("icon16.png" "icon32.png" "icon48.png" "icon128.png")
for icon in "${REQUIRED_ICONS[@]}"; do
    if [ ! -f "$BUILD_DIR/assets/$icon" ]; then
        log_error "Missing required icon: $icon"
        exit 1
    fi
done
log_success "All required icons present"

# Step 7: Validate manifest
log_info "Validating manifest.json..."
if ! python3 -c "import json; json.load(open('$BUILD_DIR/manifest.json'))" 2>/dev/null; then
    log_error "Invalid manifest.json format"
    exit 1
fi
log_success "Manifest validation passed"

# Step 8: Remove development files
log_info "Removing development files..."
rm -f "$BUILD_DIR/test-integration.html"
rm -f "$BUILD_DIR/security-validation-test.html"
rm -f "$BUILD_DIR/validate-extension.js"
rm -rf "$BUILD_DIR/deployment"

# Step 9: Optimize files
log_info "Optimizing files for production..."

# Remove development comments from JavaScript files
sed -i.bak '/\/\/ DEV:/d' "$BUILD_DIR/background.js" 2>/dev/null || true
sed -i.bak '/\/\/ DEBUG:/d' "$BUILD_DIR/background.js" 2>/dev/null || true
sed -i.bak '/console\.log.*DEV/d' "$BUILD_DIR/background.js" 2>/dev/null || true

# Remove development mode blocks (more precise)
sed -i.bak '/DEVELOPMENT MODE ONLY/d' "$BUILD_DIR/background.js" 2>/dev/null || true
sed -i.bak '/Auto-approving OTP request/d' "$BUILD_DIR/background.js" 2>/dev/null || true

# Clean up backup files
rm -f "$BUILD_DIR"/*.bak

# Step 10: Create package
log_info "Creating Chrome Web Store package..."
cd "$BUILD_DIR"
zip -r "../${PACKAGE_NAME}.zip" . -x "*.DS_Store" "*.git*" "node_modules/*"
cd ..

# Step 11: Generate checksums
log_info "Generating checksums..."
if command -v sha256sum >/dev/null 2>&1; then
    sha256sum "${PACKAGE_NAME}.zip" > "${PACKAGE_NAME}.sha256"
elif command -v shasum >/dev/null 2>&1; then
    shasum -a 256 "${PACKAGE_NAME}.zip" > "${PACKAGE_NAME}.sha256"
fi

# Step 12: Create deployment info
log_info "Creating deployment information..."
cat > "deployment-info.json" << EOF
{
  "package_name": "${PACKAGE_NAME}.zip",
  "version": "1.0.0",
  "build_timestamp": "${TIMESTAMP}",
  "build_type": "production",
  "security_level": "enterprise-grade",
  "target_platform": "chrome-web-store",
  "manifest_version": 3,
  "permissions": [
    "storage",
    "activeTab", 
    "notifications",
    "scripting"
  ],
  "supported_domains": [
    "https://progressdashboard.com",
    "https://app.progressdashboard.com", 
    "https://dashboard.progressdashboard.com"
  ],
  "security_features": [
    "environment_detection",
    "rate_limiting",
    "input_validation",
    "secure_error_handling",
    "origin_validation",
    "no_auto_approve"
  ]
}
EOF

# Step 13: Final validation
log_info "Running final validation..."
PACKAGE_SIZE=$(du -h "${PACKAGE_NAME}.zip" | cut -f1)
FILE_COUNT=$(unzip -l "${PACKAGE_NAME}.zip" | tail -1 | awk '{print $2}')

echo ""
echo "🎉 Production Build Complete!"
echo "==============================="
echo "📦 Package: ${PACKAGE_NAME}.zip"
echo "📏 Size: $PACKAGE_SIZE"
echo "📁 Files: $FILE_COUNT"
echo "🔐 Security: Enterprise-grade"
echo "🎯 Target: Chrome Web Store"
echo ""

# Step 14: Security check
log_info "Running security check..."
if grep -r "DEBUG_ENABLED.*true" "$BUILD_DIR" >/dev/null 2>&1; then
    log_error "Security issue: DEBUG_ENABLED found in production build"
    exit 1
fi

if grep -r "auto.*approve" "$BUILD_DIR" | grep -v "// SECURITY:" | grep -v "Remove auto-approve" >/dev/null 2>&1; then
    log_error "Security issue: Auto-approve code found in production build"
    exit 1
fi

if grep -r "localhost" "$BUILD_DIR/manifest.json" >/dev/null 2>&1; then
    log_error "Security issue: localhost found in production manifest"
    exit 1
fi

log_success "Security check passed"

echo ""
log_success "🚀 Production build ready for Chrome Web Store submission!"
echo ""
echo "Next steps:"
echo "1. Upload ${PACKAGE_NAME}.zip to Chrome Web Store Developer Dashboard"
echo "2. Fill in store listing details"
echo "3. Submit for review"
echo ""
echo "Build artifacts:"
echo "- ${PACKAGE_NAME}.zip (Chrome Web Store package)"
echo "- ${PACKAGE_NAME}.sha256 (checksum)"
echo "- deployment-info.json (deployment metadata)"
echo "- $BUILD_DIR/ (build directory)"
echo ""
