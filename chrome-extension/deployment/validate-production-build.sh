#!/bin/bash

# 🔍 Production Build Validation Script
# Comprehensive validation of production build for security and compliance

set -e

echo "🔍 Starting Production Build Validation..."
echo "========================================"

# Configuration
BUILD_DIR="build-production"
PACKAGE_NAME="progress-dashboard-extension-v1.0.0.zip"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Check function
check() {
    local description="$1"
    local command="$2"
    local expected_result="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_info "Checking: $description"
    
    if eval "$command"; then
        if [ "$expected_result" = "fail" ]; then
            log_error "FAIL: $description (expected to fail but passed)"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            return 1
        else
            log_success "PASS: $description"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            return 0
        fi
    else
        if [ "$expected_result" = "fail" ]; then
            log_success "PASS: $description (correctly failed)"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            return 0
        else
            log_error "FAIL: $description"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            return 1
        fi
    fi
}

# Warning check function
check_warning() {
    local description="$1"
    local command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_info "Checking: $description"
    
    if eval "$command"; then
        log_success "PASS: $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        log_warning "WARNING: $description"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
}

echo ""
echo "🔐 SECURITY VALIDATION"
echo "======================"

# Check 1: No debug mode enabled
check "No debug mode in production" \
    "! grep -r 'DEBUG_ENABLED.*true' $BUILD_DIR" \
    "pass"

# Check 2: No auto-approve code
check "No auto-approve mechanisms" \
    "! grep -r 'auto.*approve' $BUILD_DIR | grep -v '// SECURITY:' | grep -v 'Remove auto-approve' | grep -v 'no auto-approve' | grep -v 'Real user interaction'" \
    "pass"

# Check 3: No development bypasses
check "No development bypasses" \
    "! grep -r 'enableDevMode\|simulateExtensionResponse\|DEV.*MODE' $BUILD_DIR" \
    "pass"

# Check 4: No localhost in manifest
check "No localhost in production manifest" \
    "! grep -r 'localhost' $BUILD_DIR/manifest.json" \
    "pass"

# Check 5: No console.log with sensitive data
check "No sensitive console logs" \
    "! grep -r 'console\.log.*password\|console\.log.*token\|console\.log.*secret' $BUILD_DIR" \
    "pass"

# Check 6: Rate limiting implemented
check "Rate limiting code present" \
    "grep -r 'checkRateLimit\|RATE_LIMIT_CONFIG' $BUILD_DIR" \
    "pass"

# Check 7: Environment detection present
check "Environment detection implemented" \
    "grep -r 'isProduction.*chrome\.runtime\.getManifest' $BUILD_DIR" \
    "pass"

echo ""
echo "📋 MANIFEST VALIDATION"
echo "======================"

# Check 8: Valid manifest.json
check "Valid manifest.json format" \
    "python3 -c 'import json; json.load(open(\"$BUILD_DIR/manifest.json\"))'" \
    "pass"

# Check 9: Manifest version 3
check "Manifest version 3" \
    "grep -q '\"manifest_version\": 3' $BUILD_DIR/manifest.json" \
    "pass"

# Check 10: Required permissions only
check "Minimal permissions" \
    "python3 -c 'import json; m=json.load(open(\"$BUILD_DIR/manifest.json\")); exit(0 if len(m.get(\"permissions\", [])) <= 4 else 1)'" \
    "pass"

# Check 11: Production domains only
check "Production domains in manifest" \
    "grep -q 'progressdashboard.com' $BUILD_DIR/manifest.json && ! grep -q 'localhost' $BUILD_DIR/manifest.json" \
    "pass"

# Check 12: CSP policy present
check "Content Security Policy present" \
    "grep -q 'content_security_policy' $BUILD_DIR/manifest.json" \
    "pass"

echo ""
echo "📁 FILE STRUCTURE VALIDATION"
echo "============================"

# Check 13: Required files present
REQUIRED_FILES=("manifest.json" "background.js" "content-simple.js" "popup/popup.html" "popup/popup.js" "popup/popup.css")
for file in "${REQUIRED_FILES[@]}"; do
    check "Required file: $file" \
        "[ -f '$BUILD_DIR/$file' ]" \
        "pass"
done

# Check 14: Required icons present
REQUIRED_ICONS=("assets/icon16.png" "assets/icon32.png" "assets/icon48.png" "assets/icon128.png")
for icon in "${REQUIRED_ICONS[@]}"; do
    check "Required icon: $icon" \
        "[ -f '$BUILD_DIR/$icon' ]" \
        "pass"
done

# Check 15: No development files
DEV_FILES=("test-integration.html" "security-validation-test.html" "validate-extension.js" ".git" "node_modules")
for dev_file in "${DEV_FILES[@]}"; do
    check "No development file: $dev_file" \
        "[ ! -e '$BUILD_DIR/$dev_file' ]" \
        "pass"
done

echo ""
echo "📦 PACKAGE VALIDATION"
echo "====================="

# Check 16: Package exists
check "Package file exists" \
    "[ -f '$PACKAGE_NAME' ]" \
    "pass"

# Check 17: Package size reasonable
check_warning "Package size under 1MB" \
    "[ $(stat -f%z '$PACKAGE_NAME' 2>/dev/null || stat -c%s '$PACKAGE_NAME' 2>/dev/null || echo 0) -lt 1048576 ]"

# Check 18: Package contains all files
check "Package contains manifest" \
    "unzip -l '$PACKAGE_NAME' | grep -q manifest.json" \
    "pass"

# Check 19: Checksum file exists
check_warning "Checksum file exists" \
    "[ -f '${PACKAGE_NAME}.sha256' ]"

echo ""
echo "🔧 FUNCTIONALITY VALIDATION"
echo "==========================="

# Check 20: Background script syntax
check "Background script syntax valid" \
    "node -c '$BUILD_DIR/background.js'" \
    "pass"

# Check 21: Content script syntax
check "Content script syntax valid" \
    "node -c '$BUILD_DIR/content-simple.js'" \
    "pass"

# Check 22: Popup HTML valid
check_warning "Popup HTML structure valid" \
    "grep -q '<html\|<head\|<body' '$BUILD_DIR/popup/popup.html'"

# Check 23: No broken references
check_warning "No broken asset references" \
    "! grep -r 'src=\"[^\"]*\"' $BUILD_DIR | grep -v 'http' | while read line; do file=\$(echo \$line | sed 's/.*src=\"\\([^\"]*\\)\".*/\\1/'); [ -f \"\$BUILD_DIR/\$file\" ] || exit 1; done"

echo ""
echo "🛡️ PRIVACY VALIDATION"
echo "====================="

# Check 24: No data collection code
check "No analytics/tracking code" \
    "! grep -r 'google-analytics\|gtag\|facebook\|tracking' $BUILD_DIR" \
    "pass"

# Check 25: No external requests
check "No unauthorized external requests" \
    "! grep -r 'fetch.*http\|XMLHttpRequest.*http' $BUILD_DIR | grep -v 'progressdashboard.com'" \
    "pass"

# Check 26: No personal data storage
check "No personal data storage" \
    "! grep -r 'localStorage.*password\|localStorage.*email\|localStorage.*personal' $BUILD_DIR" \
    "pass"

echo ""
echo "⚡ PERFORMANCE VALIDATION"
echo "========================"

# Check 27: No large files
check_warning "No files larger than 100KB" \
    "! find '$BUILD_DIR' -type f -size +100k | grep -v '.png$'"

# Check 28: Optimized images
check_warning "Icon files reasonable size" \
    "[ $(find '$BUILD_DIR/assets' -name '*.png' -exec stat -f%z {} + 2>/dev/null | awk '{sum+=$1} END {print sum}' || echo 0) -lt 51200 ]"

echo ""
echo "📊 VALIDATION SUMMARY"
echo "===================="

# Calculate percentages
PASS_PERCENTAGE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
FAIL_PERCENTAGE=$((FAILED_CHECKS * 100 / TOTAL_CHECKS))
WARNING_PERCENTAGE=$((WARNING_CHECKS * 100 / TOTAL_CHECKS))

echo "Total Checks: $TOTAL_CHECKS"
echo -e "${GREEN}Passed: $PASSED_CHECKS ($PASS_PERCENTAGE%)${NC}"
echo -e "${RED}Failed: $FAILED_CHECKS ($FAIL_PERCENTAGE%)${NC}"
echo -e "${YELLOW}Warnings: $WARNING_CHECKS ($WARNING_PERCENTAGE%)${NC}"

echo ""

# Determine overall status
if [ $FAILED_CHECKS -eq 0 ]; then
    if [ $WARNING_CHECKS -eq 0 ]; then
        echo -e "${GREEN}🎉 VALIDATION RESULT: PERFECT - Ready for production deployment!${NC}"
        exit 0
    else
        echo -e "${YELLOW}⚠️ VALIDATION RESULT: GOOD - Ready for production with minor warnings${NC}"
        exit 0
    fi
else
    echo -e "${RED}❌ VALIDATION RESULT: FAILED - Critical issues must be resolved before deployment${NC}"
    echo ""
    echo "Please fix the failed checks and run validation again."
    exit 1
fi
