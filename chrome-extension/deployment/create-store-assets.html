<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chrome Web Store Assets Generator</title>
  <style>
    body {
      font-family: 'Inter', system-ui, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f8fafc;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    }
    
    h1 {
      color: #1e293b;
      margin-bottom: 20px;
    }
    
    .asset-section {
      margin: 30px 0;
      padding: 20px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      background: #f8fafc;
    }
    
    .canvas-container {
      margin: 20px 0;
      text-align: center;
    }
    
    canvas {
      border: 1px solid #d1d5db;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgb(0 0 0 / 0.1);
    }
    
    button {
      background: #95E565;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      margin: 5px;
      transition: background 0.2s;
    }
    
    button:hover {
      background: #608F44;
    }
    
    .download-link {
      display: inline-block;
      margin: 10px;
      padding: 8px 16px;
      background: #3b82f6;
      color: white;
      text-decoration: none;
      border-radius: 6px;
      font-size: 14px;
    }
    
    .download-link:hover {
      background: #2563eb;
    }
    
    .asset-info {
      background: #dbeafe;
      padding: 15px;
      border-radius: 6px;
      margin: 10px 0;
      border-left: 4px solid #3b82f6;
    }
    
    .feature-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 10px;
      margin: 20px 0;
    }
    
    .feature-item {
      background: white;
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #e2e8f0;
      text-align: center;
    }
    
    .feature-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎨 Chrome Web Store Assets Generator</h1>
    <p>Generate promotional images and assets for Chrome Web Store submission.</p>
    
    <!-- Small Promo Tile -->
    <div class="asset-section">
      <h3>📱 Small Promo Tile (440x280px)</h3>
      <div class="asset-info">
        <strong>Purpose:</strong> Small promotional tile for Chrome Web Store listing
      </div>
      <div class="canvas-container">
        <canvas id="smallPromo" width="440" height="280"></canvas>
      </div>
      <button onclick="generateSmallPromo()">Generate Small Promo</button>
      <a id="downloadSmallPromo" class="download-link" style="display: none;">Download Small Promo</a>
    </div>

    <!-- Large Promo Tile -->
    <div class="asset-section">
      <h3>🖼️ Large Promo Tile (920x680px)</h3>
      <div class="asset-info">
        <strong>Purpose:</strong> Large promotional tile showcasing key features
      </div>
      <div class="canvas-container">
        <canvas id="largePromo" width="920" height="680"></canvas>
      </div>
      <button onclick="generateLargePromo()">Generate Large Promo</button>
      <a id="downloadLargePromo" class="download-link" style="display: none;">Download Large Promo</a>
    </div>

    <!-- Marquee Promo Tile -->
    <div class="asset-section">
      <h3>🎪 Marquee Promo Tile (1400x560px)</h3>
      <div class="asset-info">
        <strong>Purpose:</strong> Featured promotional banner for Chrome Web Store
      </div>
      <div class="canvas-container">
        <canvas id="marqueePromo" width="1400" height="560"></canvas>
      </div>
      <button onclick="generateMarqueePromo()">Generate Marquee Promo</button>
      <a id="downloadMarqueePromo" class="download-link" style="display: none;">Download Marquee Promo</a>
    </div>

    <!-- Screenshot Templates -->
    <div class="asset-section">
      <h3>📸 Screenshot Templates (1280x800px)</h3>
      <div class="asset-info">
        <strong>Purpose:</strong> Screenshots showing extension functionality
      </div>
      
      <div class="feature-list">
        <div class="feature-item">
          <div class="feature-icon">🔐</div>
          <strong>Security Interface</strong>
          <p>OTP approval interface</p>
          <button onclick="generateScreenshot('security')">Generate</button>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">🔔</div>
          <strong>Notifications</strong>
          <p>Real-time notifications</p>
          <button onclick="generateScreenshot('notifications')">Generate</button>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">⚡</div>
          <strong>Dashboard Integration</strong>
          <p>Seamless integration</p>
          <button onclick="generateScreenshot('integration')">Generate</button>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">📊</div>
          <strong>Settings Panel</strong>
          <p>Extension settings</p>
          <button onclick="generateScreenshot('settings')">Generate</button>
        </div>
      </div>
      
      <div class="canvas-container">
        <canvas id="screenshot" width="1280" height="800"></canvas>
      </div>
      <a id="downloadScreenshot" class="download-link" style="display: none;">Download Screenshot</a>
    </div>

    <!-- Generate All -->
    <div class="asset-section">
      <h3>🚀 Generate All Assets</h3>
      <div class="asset-info">
        <strong>Generate all promotional assets at once for Chrome Web Store submission.</strong>
      </div>
      <button onclick="generateAllAssets()" style="background: #dc2626; font-size: 16px; padding: 15px 30px;">
        🎨 Generate All Store Assets
      </button>
    </div>
  </div>

  <script>
    // Brand colors
    const colors = {
      primary: '#95E565',
      primaryDark: '#608F44',
      secondary: '#3b82f6',
      dark: '#1e293b',
      light: '#f8fafc',
      white: '#ffffff',
      gray: '#64748b'
    };

    // Helper function to draw rounded rectangle
    function roundRect(ctx, x, y, width, height, radius) {
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
    }

    // Generate small promo tile
    function generateSmallPromo() {
      const canvas = document.getElementById('smallPromo');
      const ctx = canvas.getContext('2d');
      
      // Background gradient
      const gradient = ctx.createLinearGradient(0, 0, 440, 280);
      gradient.addColorStop(0, colors.primary);
      gradient.addColorStop(1, colors.primaryDark);
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, 440, 280);
      
      // Main title
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 28px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('Progress Dashboard', 220, 80);
      
      // Subtitle
      ctx.font = 'bold 20px Inter, sans-serif';
      ctx.fillText('OTP Authenticator', 220, 110);
      
      // Security badge
      ctx.fillStyle = colors.white;
      roundRect(ctx, 160, 140, 120, 40, 20);
      ctx.fill();
      
      ctx.fillStyle = colors.dark;
      ctx.font = 'bold 14px Inter, sans-serif';
      ctx.fillText('🔐 Enterprise Security', 220, 165);
      
      // Features
      ctx.fillStyle = colors.white;
      ctx.font = '12px Inter, sans-serif';
      ctx.fillText('✓ Passwordless Authentication', 220, 200);
      ctx.fillText('✓ Real-time Notifications', 220, 220);
      ctx.fillText('✓ Zero Data Collection', 220, 240);
      
      // Download link
      setupDownload('downloadSmallPromo', canvas, 'progress-dashboard-small-promo.png');
    }

    // Generate large promo tile
    function generateLargePromo() {
      const canvas = document.getElementById('largePromo');
      const ctx = canvas.getContext('2d');
      
      // Background
      ctx.fillStyle = colors.light;
      ctx.fillRect(0, 0, 920, 680);
      
      // Header section
      const headerGradient = ctx.createLinearGradient(0, 0, 920, 200);
      headerGradient.addColorStop(0, colors.primary);
      headerGradient.addColorStop(1, colors.primaryDark);
      ctx.fillStyle = headerGradient;
      ctx.fillRect(0, 0, 920, 200);
      
      // Main title
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 36px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('Progress Dashboard OTP Authenticator', 460, 80);
      
      // Subtitle
      ctx.font = '18px Inter, sans-serif';
      ctx.fillText('Enterprise-Grade Security for Your Dashboard', 460, 120);
      
      // Feature boxes
      const features = [
        { icon: '🔐', title: 'Secure Authentication', desc: 'Enterprise-grade OTP security' },
        { icon: '⚡', title: 'Lightning Fast', desc: 'Instant authentication approval' },
        { icon: '🛡️', title: 'Privacy First', desc: 'Zero data collection policy' },
        { icon: '🔔', title: 'Real-time Alerts', desc: 'Instant notification system' }
      ];
      
      features.forEach((feature, index) => {
        const x = 80 + (index % 2) * 380;
        const y = 280 + Math.floor(index / 2) * 160;
        
        // Feature box
        ctx.fillStyle = colors.white;
        roundRect(ctx, x, y, 340, 120, 12);
        ctx.fill();
        
        // Shadow
        ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        roundRect(ctx, x + 2, y + 2, 340, 120, 12);
        ctx.fill();
        
        // Feature box (on top)
        ctx.fillStyle = colors.white;
        roundRect(ctx, x, y, 340, 120, 12);
        ctx.fill();
        
        // Icon
        ctx.font = '32px Inter, sans-serif';
        ctx.textAlign = 'left';
        ctx.fillText(feature.icon, x + 20, y + 50);
        
        // Title
        ctx.fillStyle = colors.dark;
        ctx.font = 'bold 18px Inter, sans-serif';
        ctx.fillText(feature.title, x + 80, y + 40);
        
        // Description
        ctx.fillStyle = colors.gray;
        ctx.font = '14px Inter, sans-serif';
        ctx.fillText(feature.desc, x + 80, y + 65);
      });
      
      // Call to action
      ctx.fillStyle = colors.secondary;
      roundRect(ctx, 360, 600, 200, 50, 25);
      ctx.fill();
      
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 16px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('Install Now - Free', 460, 630);
      
      setupDownload('downloadLargePromo', canvas, 'progress-dashboard-large-promo.png');
    }

    // Generate marquee promo tile
    function generateMarqueePromo() {
      const canvas = document.getElementById('marqueePromo');
      const ctx = canvas.getContext('2d');
      
      // Background gradient
      const gradient = ctx.createLinearGradient(0, 0, 1400, 560);
      gradient.addColorStop(0, colors.primary);
      gradient.addColorStop(0.5, colors.primaryDark);
      gradient.addColorStop(1, colors.secondary);
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, 1400, 560);
      
      // Overlay pattern
      ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
      for (let i = 0; i < 1400; i += 100) {
        for (let j = 0; j < 560; j += 100) {
          ctx.fillRect(i, j, 2, 2);
        }
      }
      
      // Main content area
      ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
      roundRect(ctx, 100, 80, 1200, 400, 20);
      ctx.fill();
      
      // Logo area
      ctx.fillStyle = colors.primary;
      ctx.fillRect(150, 130, 80, 80);
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 24px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('PD', 190, 180);
      
      // Main title
      ctx.fillStyle = colors.dark;
      ctx.font = 'bold 48px Inter, sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText('Secure. Fast. Reliable.', 280, 180);
      
      // Subtitle
      ctx.fillStyle = colors.gray;
      ctx.font = '24px Inter, sans-serif';
      ctx.fillText('Enterprise-grade OTP authentication for Progress Dashboard', 280, 220);
      
      // Feature highlights
      const highlights = [
        '🔐 Bank-level security standards',
        '⚡ Lightning-fast authentication',
        '🛡️ Complete privacy protection',
        '🎯 Seamless dashboard integration'
      ];
      
      highlights.forEach((highlight, index) => {
        ctx.fillStyle = colors.dark;
        ctx.font = '18px Inter, sans-serif';
        ctx.fillText(highlight, 280 + (index % 2) * 400, 280 + Math.floor(index / 2) * 40);
      });
      
      // Call to action
      ctx.fillStyle = colors.primary;
      roundRect(ctx, 280, 360, 250, 60, 30);
      ctx.fill();
      
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 20px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('Add to Chrome - Free', 405, 395);
      
      // Trust indicators
      ctx.fillStyle = colors.gray;
      ctx.font = '14px Inter, sans-serif';
      ctx.textAlign = 'right';
      ctx.fillText('⭐⭐⭐⭐⭐ Trusted by 10,000+ users', 1250, 450);
      
      setupDownload('downloadMarqueePromo', canvas, 'progress-dashboard-marquee-promo.png');
    }

    // Generate screenshots
    function generateScreenshot(type) {
      const canvas = document.getElementById('screenshot');
      const ctx = canvas.getContext('2d');
      
      // Clear canvas
      ctx.fillStyle = colors.light;
      ctx.fillRect(0, 0, 1280, 800);
      
      // Browser chrome
      ctx.fillStyle = colors.white;
      roundRect(ctx, 40, 40, 1200, 720, 12);
      ctx.fill();
      
      // Browser header
      ctx.fillStyle = '#f3f4f6';
      roundRect(ctx, 40, 40, 1200, 60, 12);
      ctx.fill();
      ctx.fillRect(40, 88, 1200, 12);
      
      // Browser buttons
      ctx.fillStyle = '#ef4444';
      ctx.beginPath();
      ctx.arc(70, 70, 8, 0, 2 * Math.PI);
      ctx.fill();
      
      ctx.fillStyle = '#f59e0b';
      ctx.beginPath();
      ctx.arc(100, 70, 8, 0, 2 * Math.PI);
      ctx.fill();
      
      ctx.fillStyle = '#10b981';
      ctx.beginPath();
      ctx.arc(130, 70, 8, 0, 2 * Math.PI);
      ctx.fill();
      
      // URL bar
      ctx.fillStyle = colors.white;
      roundRect(ctx, 200, 55, 400, 30, 15);
      ctx.fill();
      
      ctx.fillStyle = colors.gray;
      ctx.font = '12px Inter, sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText('https://app.progressdashboard.com', 210, 75);
      
      // Extension icon in toolbar
      ctx.fillStyle = colors.primary;
      roundRect(ctx, 1180, 55, 30, 30, 6);
      ctx.fill();
      
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 12px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('PD', 1195, 75);
      
      // Content based on type
      switch (type) {
        case 'security':
          generateSecurityScreenshot(ctx);
          break;
        case 'notifications':
          generateNotificationScreenshot(ctx);
          break;
        case 'integration':
          generateIntegrationScreenshot(ctx);
          break;
        case 'settings':
          generateSettingsScreenshot(ctx);
          break;
      }
      
      setupDownload('downloadScreenshot', canvas, `progress-dashboard-screenshot-${type}.png`);
    }

    function generateSecurityScreenshot(ctx) {
      // OTP approval popup
      ctx.fillStyle = colors.white;
      roundRect(ctx, 400, 200, 480, 400, 12);
      ctx.fill();
      
      // Shadow
      ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
      roundRect(ctx, 405, 205, 480, 400, 12);
      ctx.fill();
      
      // Popup content
      ctx.fillStyle = colors.white;
      roundRect(ctx, 400, 200, 480, 400, 12);
      ctx.fill();
      
      // Header
      ctx.fillStyle = colors.primary;
      roundRect(ctx, 400, 200, 480, 80, 12);
      ctx.fill();
      ctx.fillRect(400, 268, 480, 12);
      
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 24px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('🔐 OTP Authentication', 640, 250);
      
      // Content
      ctx.fillStyle = colors.dark;
      ctx.font = '18px Inter, sans-serif';
      ctx.fillText('Approve login request?', 640, 340);
      
      ctx.fillStyle = colors.gray;
      ctx.font = '14px Inter, sans-serif';
      ctx.fillText('Email: <EMAIL>', 640, 370);
      ctx.fillText('Time: Just now', 640, 390);
      ctx.fillText('Location: Progress Dashboard', 640, 410);
      
      // Buttons
      ctx.fillStyle = colors.primary;
      roundRect(ctx, 450, 480, 120, 40, 20);
      ctx.fill();
      
      ctx.fillStyle = '#ef4444';
      roundRect(ctx, 610, 480, 120, 40, 20);
      ctx.fill();
      
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 14px Inter, sans-serif';
      ctx.fillText('✓ Approve', 510, 505);
      ctx.fillText('✗ Reject', 670, 505);
    }

    function generateNotificationScreenshot(ctx) {
      // Chrome notification
      ctx.fillStyle = colors.white;
      roundRect(ctx, 900, 150, 320, 120, 8);
      ctx.fill();
      
      // Shadow
      ctx.fillStyle = 'rgba(0, 0, 0, 0.15)';
      roundRect(ctx, 905, 155, 320, 120, 8);
      ctx.fill();
      
      // Notification content
      ctx.fillStyle = colors.white;
      roundRect(ctx, 900, 150, 320, 120, 8);
      ctx.fill();
      
      // Icon
      ctx.fillStyle = colors.primary;
      roundRect(ctx, 920, 170, 40, 40, 6);
      ctx.fill();
      
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 16px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('PD', 940, 195);
      
      // Text
      ctx.fillStyle = colors.dark;
      ctx.font = 'bold 14px Inter, sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText('Progress Dashboard', 980, 180);
      
      ctx.fillStyle = colors.gray;
      ctx.font = '12px Inter, sans-serif';
      ctx.fillText('OTP authentication request', 980, 200);
      ctx.fillText('Click to approve or reject', 980, 215);
      
      // Action buttons
      ctx.fillStyle = colors.primary;
      roundRect(ctx, 980, 230, 60, 25, 12);
      ctx.fill();
      
      ctx.fillStyle = '#ef4444';
      roundRect(ctx, 1050, 230, 60, 25, 12);
      ctx.fill();
      
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 10px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('Approve', 1010, 245);
      ctx.fillText('Reject', 1080, 245);
    }

    function generateIntegrationScreenshot(ctx) {
      // Dashboard interface
      ctx.fillStyle = '#f8fafc';
      ctx.fillRect(60, 120, 1160, 620);
      
      // Login form
      ctx.fillStyle = colors.white;
      roundRect(ctx, 440, 250, 400, 300, 12);
      ctx.fill();
      
      // Form header
      ctx.fillStyle = colors.dark;
      ctx.font = 'bold 24px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('Welcome Back', 640, 290);
      
      // Email input
      ctx.fillStyle = '#f3f4f6';
      roundRect(ctx, 480, 320, 320, 40, 6);
      ctx.fill();
      
      ctx.fillStyle = colors.gray;
      ctx.font = '14px Inter, sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText('Email address', 490, 345);
      
      // Login button
      ctx.fillStyle = colors.primary;
      roundRect(ctx, 480, 380, 320, 45, 22);
      ctx.fill();
      
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 16px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('🔐 Secure Login with Extension', 640, 407);
      
      // Security badge
      ctx.fillStyle = colors.secondary;
      roundRect(ctx, 520, 450, 240, 30, 15);
      ctx.fill();
      
      ctx.fillStyle = colors.white;
      ctx.font = '12px Inter, sans-serif';
      ctx.fillText('🛡️ Protected by OTP Authentication', 640, 470);
    }

    function generateSettingsScreenshot(ctx) {
      // Extension popup
      ctx.fillStyle = colors.white;
      roundRect(ctx, 500, 180, 280, 440, 12);
      ctx.fill();
      
      // Header
      ctx.fillStyle = colors.primary;
      roundRect(ctx, 500, 180, 280, 60, 12);
      ctx.fill();
      ctx.fillRect(500, 228, 280, 12);
      
      ctx.fillStyle = colors.white;
      ctx.font = 'bold 16px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('Progress Dashboard OTP', 640, 215);
      
      // Status
      ctx.fillStyle = colors.dark;
      ctx.font = 'bold 14px Inter, sans-serif';
      ctx.fillText('Status', 520, 270);
      
      ctx.fillStyle = '#10b981';
      ctx.beginPath();
      ctx.arc(530, 290, 6, 0, 2 * Math.PI);
      ctx.fill();
      
      ctx.fillStyle = colors.gray;
      ctx.font = '12px Inter, sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText('Connected and Ready', 550, 295);
      
      // Settings options
      const settings = [
        'Enable Notifications',
        'Auto-approve (Dev only)',
        'Sound Alerts',
        'Show Badge Count'
      ];
      
      settings.forEach((setting, index) => {
        const y = 330 + index * 40;
        
        ctx.fillStyle = colors.dark;
        ctx.font = '12px Inter, sans-serif';
        ctx.fillText(setting, 520, y);
        
        // Toggle switch
        ctx.fillStyle = index < 2 ? colors.primary : '#d1d5db';
        roundRect(ctx, 720, y - 15, 40, 20, 10);
        ctx.fill();
        
        ctx.fillStyle = colors.white;
        ctx.beginPath();
        ctx.arc(index < 2 ? 750 : 730, y - 5, 8, 0, 2 * Math.PI);
        ctx.fill();
      });
      
      // Version info
      ctx.fillStyle = colors.gray;
      ctx.font = '10px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('Version 1.0.0 - Production', 640, 600);
    }

    // Setup download link
    function setupDownload(linkId, canvas, filename) {
      const link = document.getElementById(linkId);
      canvas.toBlob(blob => {
        const url = URL.createObjectURL(blob);
        link.href = url;
        link.download = filename;
        link.style.display = 'inline-block';
        link.textContent = `Download ${filename}`;
      });
    }

    // Generate all assets
    function generateAllAssets() {
      generateSmallPromo();
      setTimeout(() => generateLargePromo(), 500);
      setTimeout(() => generateMarqueePromo(), 1000);
      setTimeout(() => generateScreenshot('security'), 1500);
      setTimeout(() => generateScreenshot('notifications'), 2000);
      setTimeout(() => generateScreenshot('integration'), 2500);
      setTimeout(() => generateScreenshot('settings'), 3000);
      
      alert('🎨 All store assets generated! Download each asset using the download links.');
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Chrome Web Store Assets Generator loaded');
    });
  </script>
</body>
</html>
