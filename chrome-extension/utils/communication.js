/**
 * Communication Utilities for Chrome Extension
 * 
 * Handles secure communication between extension components and web pages
 */

// Communication manager
class CommunicationManager {
  constructor() {
    this.messageQueue = new Map();
    this.responseCallbacks = new Map();
    this.messageTimeout = 30000; // 30 seconds
  }

  /**
   * Send message with response handling
   */
  async sendMessage(target, message, options = {}) {
    const messageId = this.generateMessageId();
    const { timeout = this.messageTimeout, retries = 1 } = options;

    const messageWithId = {
      ...message,
      messageId,
      timestamp: Date.now()
    };

    return new Promise((resolve, reject) => {
      let attempt = 0;
      
      const attemptSend = async () => {
        try {
          attempt++;
          
          // Set up response handler
          const timeoutId = setTimeout(() => {
            this.responseCallbacks.delete(messageId);
            reject(new Error(`Message timeout after ${timeout}ms`));
          }, timeout);

          this.responseCallbacks.set(messageId, (response) => {
            clearTimeout(timeoutId);
            this.responseCallbacks.delete(messageId);
            
            if (response.success) {
              resolve(response.data);
            } else {
              reject(new Error(response.error || 'Unknown error'));
            }
          });

          // Send message based on target
          let result;
          switch (target) {
            case 'background':
              result = await chrome.runtime.sendMessage(messageWithId);
              break;
            case 'content':
              const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
              if (tabs[0]) {
                result = await chrome.tabs.sendMessage(tabs[0].id, messageWithId);
              } else {
                throw new Error('No active tab found');
              }
              break;
            case 'popup':
              result = await chrome.runtime.sendMessage(messageWithId);
              break;
            default:
              throw new Error(`Unknown target: ${target}`);
          }

          // Handle immediate response
          if (result) {
            const callback = this.responseCallbacks.get(messageId);
            if (callback) {
              callback(result);
            }
          }

        } catch (error) {
          if (attempt < retries) {
            console.warn(`Message send attempt ${attempt} failed, retrying...`, error);
            setTimeout(attemptSend, 1000 * attempt); // Exponential backoff
          } else {
            this.responseCallbacks.delete(messageId);
            reject(error);
          }
        }
      };

      attemptSend();
    });
  }

  /**
   * Handle incoming message response
   */
  handleResponse(messageId, response) {
    const callback = this.responseCallbacks.get(messageId);
    if (callback) {
      callback(response);
    }
  }

  /**
   * Broadcast message to all extension contexts
   */
  async broadcast(message, excludeTarget = null) {
    const targets = ['background', 'content', 'popup'].filter(t => t !== excludeTarget);
    const promises = targets.map(target => 
      this.sendMessage(target, message).catch(error => {
        console.warn(`Failed to broadcast to ${target}:`, error);
        return null;
      })
    );

    return Promise.allSettled(promises);
  }

  /**
   * Generate unique message ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Validate message structure
   */
  validateMessage(message) {
    if (!message || typeof message !== 'object') {
      return false;
    }

    if (!message.type || typeof message.type !== 'string') {
      return false;
    }

    if (message.timestamp && (Date.now() - message.timestamp > 60000)) {
      return false; // Message older than 1 minute
    }

    return true;
  }

  /**
   * Sanitize message data
   */
  sanitizeMessage(message) {
    // Remove potentially dangerous properties
    const sanitized = { ...message };
    delete sanitized.__proto__;
    delete sanitized.constructor;
    
    // Ensure timestamp
    if (!sanitized.timestamp) {
      sanitized.timestamp = Date.now();
    }

    return sanitized;
  }
}

// Web page communication bridge
class WebPageBridge {
  constructor() {
    this.allowedOrigins = [
      'http://localhost:5173',  // Vite dev server (FIXED)
      'http://localhost:5174',
      'http://localhost:3000',
      'http://localhost:5001',
      'https://localhost:5173', // Secure Vite dev server (FIXED)
      'https://localhost:5174',
      'https://localhost:3000',
      'https://localhost:5001',
      'https://progressdashboard.com',
      'https://app.progressdashboard.com',
      'https://dashboard.progressdashboard.com'
    ];
    
    this.messageHandlers = new Map();
    this.setupListeners();
  }

  /**
   * Setup message listeners
   */
  setupListeners() {
    // Listen for messages from web pages
    window.addEventListener('message', (event) => {
      this.handleWebPageMessage(event);
    });

    // Listen for messages from extension
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleExtensionMessage(message, sender, sendResponse);
      });
    }
  }

  /**
   * Handle messages from web pages
   */
  handleWebPageMessage(event) {
    // Validate origin
    if (!this.isValidOrigin(event.origin)) {
      console.warn('Invalid origin:', event.origin);
      return;
    }

    const message = event.data;
    if (!this.validateWebMessage(message)) {
      console.warn('Invalid message structure:', message);
      return;
    }

    // Handle different message types
    switch (message.type) {
      case 'OTP_REQUEST':
        this.handleOTPRequest(message, event.origin);
        break;
      case 'EXTENSION_READY':
        this.handleExtensionReady(message, event.origin);
        break;
      default:
        console.warn('Unknown web message type:', message.type);
    }
  }

  /**
   * Handle messages from extension
   */
  handleExtensionMessage(message, sender, sendResponse) {
    switch (message.type) {
      case 'FORWARD_TO_PAGE':
        this.forwardToPage(message.data);
        break;
      default:
        console.warn('Unknown extension message type:', message.type);
    }
  }

  /**
   * Handle OTP request from web page
   */
  async handleOTPRequest(message, origin) {
    try {
      // Forward to background script
      const response = await chrome.runtime.sendMessage({
        type: 'OTP_REQUEST',
        data: {
          ...message.data,
          origin: origin,
          timestamp: Date.now()
        }
      });

      // Send response back to web page
      this.sendToWebPage({
        type: 'OTP_RESPONSE',
        messageId: message.messageId,
        success: response.success,
        data: response.data,
        error: response.error
      }, origin);

    } catch (error) {
      console.error('Failed to handle OTP request:', error);
      
      this.sendToWebPage({
        type: 'OTP_RESPONSE',
        messageId: message.messageId,
        success: false,
        error: error.message
      }, origin);
    }
  }

  /**
   * Handle extension ready signal
   */
  handleExtensionReady(message, origin) {
    // Send back extension info
    this.sendToWebPage({
      type: 'EXTENSION_READY',
      data: {
        version: chrome.runtime.getManifest().version,
        isReady: true
      }
    }, origin);
  }

  /**
   * Forward message to web page
   */
  forwardToPage(message) {
    this.sendToWebPage(message, window.location.origin);
  }

  /**
   * Send message to web page
   */
  sendToWebPage(message, targetOrigin = '*') {
    window.postMessage(message, targetOrigin);
  }

  /**
   * Validate origin
   */
  isValidOrigin(origin) {
    return this.allowedOrigins.includes(origin) ||
           this.allowedOrigins.some(allowed => origin.startsWith(allowed));
  }

  /**
   * Validate web message
   */
  validateWebMessage(message) {
    if (!message || typeof message !== 'object') {
      return false;
    }

    if (!message.type || typeof message.type !== 'string') {
      return false;
    }

    return true;
  }

  /**
   * Add message handler
   */
  addMessageHandler(type, handler) {
    this.messageHandlers.set(type, handler);
  }

  /**
   * Remove message handler
   */
  removeMessageHandler(type) {
    this.messageHandlers.delete(type);
  }
}

// Message types constants
const MESSAGE_TYPES = {
  // OTP Flow
  OTP_REQUEST: 'OTP_REQUEST',
  OTP_RESPONSE: 'OTP_RESPONSE',
  OTP_REJECTED: 'OTP_REJECTED',
  
  // Extension Status
  EXTENSION_READY: 'EXTENSION_READY',
  EXTENSION_ERROR: 'EXTENSION_ERROR',
  GET_EXTENSION_STATUS: 'GET_EXTENSION_STATUS',
  
  // Settings
  GET_SETTINGS: 'GET_SETTINGS',
  UPDATE_SETTINGS: 'UPDATE_SETTINGS',
  
  // Internal
  FORWARD_TO_PAGE: 'FORWARD_TO_PAGE',
  CONTENT_SCRIPT_READY: 'CONTENT_SCRIPT_READY'
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    CommunicationManager,
    WebPageBridge,
    MESSAGE_TYPES
  };
} else if (typeof window !== 'undefined') {
  window.CommunicationManager = CommunicationManager;
  window.WebPageBridge = WebPageBridge;
  window.MESSAGE_TYPES = MESSAGE_TYPES;
}
