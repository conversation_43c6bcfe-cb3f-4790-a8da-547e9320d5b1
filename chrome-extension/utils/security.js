/**
 * Security Utilities for Chrome Extension
 * 
 * Handles validation, sanitization, and security measures
 */

// Security manager
class SecurityManager {
  constructor() {
    this.allowedOrigins = [
      'http://localhost:5173',  // Vite dev server (FIXED)
      'http://localhost:5174',
      'http://localhost:3000',
      'http://localhost:5001',
      'https://localhost:5173', // Secure Vite dev server (FIXED)
      'https://localhost:5174',
      'https://localhost:3000',
      'https://localhost:5001',
      'https://progressdashboard.com',
      'https://app.progressdashboard.com',
      'https://dashboard.progressdashboard.com'
    ];
    
    this.rateLimits = new Map();
    this.blockedOrigins = new Set();
    this.suspiciousActivity = new Map();
  }

  /**
   * Validate origin against allowed list
   */
  validateOrigin(origin) {
    if (!origin) return false;
    
    // Check if origin is blocked
    if (this.blockedOrigins.has(origin)) {
      console.warn('Blocked origin attempted access:', origin);
      return false;
    }
    
    // Check against allowed origins
    return this.allowedOrigins.includes(origin) ||
           this.allowedOrigins.some(allowed => {
             // Allow subdomains of allowed origins
             if (allowed.startsWith('https://')) {
               const domain = allowed.replace('https://', '');
               return origin === allowed || origin.endsWith('.' + domain);
             }
             return origin === allowed;
           });
  }

  /**
   * Validate OTP request data
   */
  validateOTPRequest(data) {
    const errors = [];
    
    // Required fields
    if (!data.email || typeof data.email !== 'string') {
      errors.push('Invalid email');
    } else if (!this.validateEmail(data.email)) {
      errors.push('Invalid email format');
    }
    
    if (!data.otp_code || typeof data.otp_code !== 'string') {
      errors.push('Invalid OTP code');
    } else if (!this.validateOTPCode(data.otp_code)) {
      errors.push('Invalid OTP code format');
    }
    
    if (!data.otp_key || typeof data.otp_key !== 'string') {
      errors.push('Invalid OTP key');
    }
    
    if (!data.expires_in || typeof data.expires_in !== 'number') {
      errors.push('Invalid expiration time');
    } else if (data.expires_in < 60 || data.expires_in > 600) {
      errors.push('Invalid expiration time range');
    }
    
    if (!data.website || typeof data.website !== 'string') {
      errors.push('Invalid website');
    }
    
    // Validate timestamp
    if (data.timestamp) {
      const age = Date.now() - data.timestamp;
      if (age > 60000) { // 1 minute
        errors.push('Request too old');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * Validate email format
   */
  validateEmail(email) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  /**
   * Validate OTP code format
   */
  validateOTPCode(code) {
    // OTP should be 6 digits
    const otpRegex = /^\d{6}$/;
    return otpRegex.test(code);
  }

  /**
   * Sanitize input data
   */
  sanitizeInput(input) {
    if (typeof input === 'string') {
      return input
        .trim()
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .substring(0, 1000); // Limit length
    }
    
    if (typeof input === 'object' && input !== null) {
      const sanitized = {};
      for (const [key, value] of Object.entries(input)) {
        if (typeof key === 'string' && key.length <= 100) {
          sanitized[key] = this.sanitizeInput(value);
        }
      }
      return sanitized;
    }
    
    return input;
  }

  /**
   * Rate limiting check
   */
  checkRateLimit(identifier, maxRequests = 5, windowMs = 60000) {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    if (!this.rateLimits.has(identifier)) {
      this.rateLimits.set(identifier, []);
    }
    
    const requests = this.rateLimits.get(identifier);
    
    // Remove old requests outside the window
    const recentRequests = requests.filter(timestamp => timestamp > windowStart);
    
    // Check if limit exceeded
    if (recentRequests.length >= maxRequests) {
      this.logSuspiciousActivity(identifier, 'rate_limit_exceeded');
      return false;
    }
    
    // Add current request
    recentRequests.push(now);
    this.rateLimits.set(identifier, recentRequests);
    
    return true;
  }

  /**
   * Log suspicious activity
   */
  logSuspiciousActivity(identifier, activity) {
    if (!this.suspiciousActivity.has(identifier)) {
      this.suspiciousActivity.set(identifier, []);
    }
    
    const activities = this.suspiciousActivity.get(identifier);
    activities.push({
      activity: activity,
      timestamp: Date.now()
    });
    
    // Keep only recent activities (last hour)
    const recentActivities = activities.filter(
      item => Date.now() - item.timestamp < 3600000
    );
    
    this.suspiciousActivity.set(identifier, recentActivities);
    
    // Auto-block if too many suspicious activities
    if (recentActivities.length >= 10) {
      this.blockOrigin(identifier);
    }
    
    console.warn(`Suspicious activity from ${identifier}: ${activity}`);
  }

  /**
   * Block origin temporarily
   */
  blockOrigin(origin, durationMs = 3600000) { // 1 hour default
    this.blockedOrigins.add(origin);
    
    setTimeout(() => {
      this.blockedOrigins.delete(origin);
      console.log(`Unblocked origin: ${origin}`);
    }, durationMs);
    
    console.warn(`Blocked origin: ${origin} for ${durationMs}ms`);
  }

  /**
   * Generate secure random token
   */
  generateSecureToken(length = 32) {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Hash data using Web Crypto API
   */
  async hashData(data) {
    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(JSON.stringify(data));
      const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
      const hashArray = new Uint8Array(hashBuffer);
      return Array.from(hashArray, byte => byte.toString(16).padStart(2, '0')).join('');
    } catch (error) {
      console.error('Failed to hash data:', error);
      return null;
    }
  }

  /**
   * Verify message integrity
   */
  async verifyMessageIntegrity(message, expectedHash) {
    const actualHash = await this.hashData(message);
    return actualHash === expectedHash;
  }

  /**
   * Create secure message with integrity check
   */
  async createSecureMessage(data) {
    const timestamp = Date.now();
    const nonce = this.generateSecureToken(16);
    
    const message = {
      data: data,
      timestamp: timestamp,
      nonce: nonce
    };
    
    const hash = await this.hashData(message);
    
    return {
      ...message,
      hash: hash
    };
  }

  /**
   * Validate secure message
   */
  async validateSecureMessage(message) {
    if (!message || !message.hash) {
      return { isValid: false, error: 'Missing hash' };
    }
    
    // Check timestamp (message should not be older than 5 minutes)
    if (Date.now() - message.timestamp > 300000) {
      return { isValid: false, error: 'Message expired' };
    }
    
    // Verify integrity
    const { hash, ...messageWithoutHash } = message;
    const isValid = await this.verifyMessageIntegrity(messageWithoutHash, hash);
    
    return {
      isValid: isValid,
      error: isValid ? null : 'Integrity check failed'
    };
  }

  /**
   * Content Security Policy validation
   */
  validateCSP(content) {
    // Check for potentially dangerous content
    const dangerousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i, // Event handlers
      /<iframe/i,
      /<object/i,
      /<embed/i
    ];
    
    return !dangerousPatterns.some(pattern => pattern.test(content));
  }

  /**
   * Get security report
   */
  getSecurityReport() {
    const now = Date.now();
    const oneHourAgo = now - 3600000;
    
    // Count recent suspicious activities
    let totalSuspiciousActivities = 0;
    const suspiciousOrigins = [];
    
    for (const [origin, activities] of this.suspiciousActivity.entries()) {
      const recentActivities = activities.filter(
        activity => activity.timestamp > oneHourAgo
      );
      
      if (recentActivities.length > 0) {
        totalSuspiciousActivities += recentActivities.length;
        suspiciousOrigins.push({
          origin: origin,
          activities: recentActivities.length
        });
      }
    }
    
    // Count rate limited origins
    let rateLimitedOrigins = 0;
    for (const [origin, requests] of this.rateLimits.entries()) {
      const recentRequests = requests.filter(timestamp => timestamp > oneHourAgo);
      if (recentRequests.length >= 5) {
        rateLimitedOrigins++;
      }
    }
    
    return {
      timestamp: now,
      blockedOrigins: Array.from(this.blockedOrigins),
      suspiciousActivities: totalSuspiciousActivities,
      suspiciousOrigins: suspiciousOrigins,
      rateLimitedOrigins: rateLimitedOrigins,
      allowedOrigins: this.allowedOrigins.length
    };
  }

  /**
   * Reset security state (for testing)
   */
  reset() {
    this.rateLimits.clear();
    this.blockedOrigins.clear();
    this.suspiciousActivity.clear();
  }
}

// Input validator
class InputValidator {
  static validateURL(url) {
    try {
      const urlObj = new URL(url);
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  }

  static validateJSON(jsonString) {
    try {
      JSON.parse(jsonString);
      return true;
    } catch {
      return false;
    }
  }

  static sanitizeHTML(html) {
    const div = document.createElement('div');
    div.textContent = html;
    return div.innerHTML;
  }

  static validateLength(input, maxLength = 1000) {
    return typeof input === 'string' && input.length <= maxLength;
  }

  static validateAlphanumeric(input) {
    return /^[a-zA-Z0-9]+$/.test(input);
  }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    SecurityManager,
    InputValidator
  };
} else if (typeof window !== 'undefined') {
  window.SecurityManager = SecurityManager;
  window.InputValidator = InputValidator;
}
