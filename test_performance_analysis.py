#!/usr/bin/env python3
"""
Performance Analysis for Authentication System
Performance testing and optimization validation for authentication system
"""

import os
import sys
import json
import jwt
import time
import requests
import threading
import statistics
from datetime import datetime
from typing import Dict, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add backend directory to path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

class PerformanceAnalyzer:
    """Performance analysis class for authentication system"""
    
    def __init__(self, backend_url: str = "http://localhost:5001"):
        self.backend_url = backend_url
        self.test_results = []
        self.performance_metrics = {}
        
    def log_test(self, test_name: str, success: bool, message: str = "", details: Dict = None):
        """Log test result"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'details': details or {},
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        if details:
            for key, value in details.items():
                print(f"    {key}: {value}")
    
    def create_test_token(self, user_id: str, email: str, role: str = "user") -> str:
        """Create test JWT token"""
        try:
            payload = {
                'sub': user_id,
                'email': email,
                'user_metadata': {'role': role},
                'app_metadata': {'role': role},
                'aud': 'authenticated',
                'iss': 'supabase',
                'iat': int(time.time()),
                'exp': int(time.time()) + 3600
            }
            
            secret = os.getenv('JWT_SECRET_KEY', 'your-super-secret-jwt-key-change-in-production')
            return jwt.encode(payload, secret, algorithm='HS256')
            
        except Exception as e:
            return ""
    
    def measure_response_time(self, url: str, headers: Dict = None, method: str = 'GET') -> Dict:
        """Measure response time for a single request"""
        try:
            start_time = time.time()
            
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers or {}, timeout=10)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers or {}, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            return {
                'response_time_ms': response_time,
                'status_code': response.status_code,
                'success': response.status_code < 400,
                'content_length': len(response.content) if response.content else 0
            }
            
        except Exception as e:
            return {
                'response_time_ms': 0,
                'status_code': 0,
                'success': False,
                'error': str(e),
                'content_length': 0
            }
    
    def test_authentication_performance(self) -> bool:
        """Test authentication endpoint performance"""
        try:
            print("\n⚡ Testing Authentication Performance...")
            
            # Create test token
            token = self.create_test_token('perf-user', '<EMAIL>', 'user')
            if not token:
                return False
            
            # Test authentication endpoints
            auth_endpoints = [
                {
                    'name': 'Auth Health Check',
                    'url': f"{self.backend_url}/api/auth/health",
                    'headers': {},
                    'expected_max_ms': 100
                },
                {
                    'name': 'Auth Info',
                    'url': f"{self.backend_url}/api/auth/info",
                    'headers': {},
                    'expected_max_ms': 100
                },
                {
                    'name': 'Token Verification',
                    'url': f"{self.backend_url}/api/auth/verify",
                    'headers': {'Authorization': f'Bearer {token}'},
                    'method': 'POST',
                    'expected_max_ms': 200
                },
                {
                    'name': 'User Permissions',
                    'url': f"{self.backend_url}/api/auth/permissions",
                    'headers': {'Authorization': f'Bearer {token}'},
                    'expected_max_ms': 150
                }
            ]
            
            # Test each endpoint multiple times
            results = {}
            for endpoint in auth_endpoints:
                response_times = []
                success_count = 0
                
                for i in range(10):  # 10 requests per endpoint
                    result = self.measure_response_time(
                        endpoint['url'],
                        endpoint['headers'],
                        endpoint.get('method', 'GET')
                    )
                    
                    if result['success']:
                        response_times.append(result['response_time_ms'])
                        success_count += 1
                
                if response_times:
                    avg_time = statistics.mean(response_times)
                    median_time = statistics.median(response_times)
                    max_time = max(response_times)
                    min_time = min(response_times)
                    
                    performance_ok = avg_time <= endpoint['expected_max_ms']
                    
                    results[endpoint['name']] = {
                        'avg_ms': round(avg_time, 2),
                        'median_ms': round(median_time, 2),
                        'max_ms': round(max_time, 2),
                        'min_ms': round(min_time, 2),
                        'success_rate': (success_count / 10) * 100,
                        'performance_ok': performance_ok
                    }
                    
                    self.log_test(
                        f"auth_performance_{endpoint['name'].lower().replace(' ', '_')}",
                        performance_ok and success_count >= 9,
                        f"{endpoint['name']}: Avg {avg_time:.1f}ms ({'✅' if performance_ok else '⚠️'})",
                        results[endpoint['name']]
                    )
                else:
                    results[endpoint['name']] = {
                        'avg_ms': 0,
                        'success_rate': 0,
                        'performance_ok': False
                    }
                    
                    self.log_test(
                        f"auth_performance_{endpoint['name'].lower().replace(' ', '_')}",
                        False,
                        f"{endpoint['name']}: All requests failed"
                    )
            
            # Overall performance assessment
            all_performing = all(result['performance_ok'] for result in results.values())
            avg_success_rate = statistics.mean([result['success_rate'] for result in results.values()])
            
            self.performance_metrics['authentication'] = results
            
            self.log_test(
                "authentication_performance",
                all_performing and avg_success_rate >= 90,
                f"Authentication performance: {'excellent' if all_performing else 'needs optimization'}",
                {
                    'overall_performance': 'excellent' if all_performing else 'needs optimization',
                    'avg_success_rate': f"{avg_success_rate:.1f}%",
                    'endpoints_tested': len(auth_endpoints)
                }
            )
            
            return all_performing and avg_success_rate >= 90
            
        except Exception as e:
            self.log_test("authentication_performance", False, f"Authentication performance test failed: {str(e)}")
            return False
    
    def test_protected_endpoint_performance(self) -> bool:
        """Test protected endpoint performance"""
        try:
            print("\n🛡️ Testing Protected Endpoint Performance...")
            
            # Create test token
            token = self.create_test_token('perf-user', '<EMAIL>', 'user')
            if not token:
                return False
            
            # Test protected endpoints
            protected_endpoints = [
                {
                    'name': 'Categories',
                    'url': f"{self.backend_url}/api/categories",
                    'expected_max_ms': 300
                },
                {
                    'name': 'Competitors',
                    'url': f"{self.backend_url}/api/competitors",
                    'expected_max_ms': 300
                },
                {
                    'name': 'Category Data',
                    'url': f"{self.backend_url}/api/categories/test/data",
                    'expected_max_ms': 400
                }
            ]
            
            headers = {'Authorization': f'Bearer {token}'}
            results = {}
            
            for endpoint in protected_endpoints:
                response_times = []
                success_count = 0
                
                for i in range(10):  # 10 requests per endpoint
                    result = self.measure_response_time(endpoint['url'], headers)
                    
                    if result['success']:
                        response_times.append(result['response_time_ms'])
                        success_count += 1
                
                if response_times:
                    avg_time = statistics.mean(response_times)
                    performance_ok = avg_time <= endpoint['expected_max_ms']
                    
                    results[endpoint['name']] = {
                        'avg_ms': round(avg_time, 2),
                        'max_ms': round(max(response_times), 2),
                        'min_ms': round(min(response_times), 2),
                        'success_rate': (success_count / 10) * 100,
                        'performance_ok': performance_ok
                    }
                    
                    self.log_test(
                        f"protected_performance_{endpoint['name'].lower().replace(' ', '_')}",
                        performance_ok and success_count >= 8,
                        f"Protected {endpoint['name']}: Avg {avg_time:.1f}ms ({'✅' if performance_ok else '⚠️'})",
                        results[endpoint['name']]
                    )
                else:
                    results[endpoint['name']] = {
                        'avg_ms': 0,
                        'success_rate': 0,
                        'performance_ok': False
                    }
                    
                    self.log_test(
                        f"protected_performance_{endpoint['name'].lower().replace(' ', '_')}",
                        False,
                        f"Protected {endpoint['name']}: All requests failed"
                    )
            
            # Overall assessment
            all_performing = all(result['performance_ok'] for result in results.values())
            avg_success_rate = statistics.mean([result['success_rate'] for result in results.values()])
            
            self.performance_metrics['protected_endpoints'] = results
            
            self.log_test(
                "protected_endpoint_performance",
                all_performing and avg_success_rate >= 80,
                f"Protected endpoint performance: {'excellent' if all_performing else 'needs optimization'}",
                {
                    'overall_performance': 'excellent' if all_performing else 'needs optimization',
                    'avg_success_rate': f"{avg_success_rate:.1f}%"
                }
            )
            
            return all_performing and avg_success_rate >= 80
            
        except Exception as e:
            self.log_test("protected_endpoint_performance", False, f"Protected endpoint performance test failed: {str(e)}")
            return False
    
    def test_concurrent_load(self) -> bool:
        """Test concurrent load performance"""
        try:
            print("\n🚀 Testing Concurrent Load Performance...")
            
            # Create test token
            token = self.create_test_token('load-user', '<EMAIL>', 'user')
            if not token:
                return False
            
            headers = {'Authorization': f'Bearer {token}'}
            
            # Test concurrent requests
            def make_request():
                return self.measure_response_time(f"{self.backend_url}/api/categories", headers)
            
            # Test with different concurrency levels
            concurrency_tests = [
                {'concurrent_users': 5, 'total_requests': 25},
                {'concurrent_users': 10, 'total_requests': 50},
                {'concurrent_users': 20, 'total_requests': 100}
            ]
            
            results = {}
            
            for test in concurrency_tests:
                concurrent_users = test['concurrent_users']
                total_requests = test['total_requests']
                
                print(f"  Testing {concurrent_users} concurrent users, {total_requests} total requests...")
                
                start_time = time.time()
                
                with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
                    futures = [executor.submit(make_request) for _ in range(total_requests)]
                    request_results = [future.result() for future in as_completed(futures)]
                
                end_time = time.time()
                total_time = end_time - start_time
                
                # Analyze results
                successful_requests = [r for r in request_results if r['success']]
                failed_requests = [r for r in request_results if not r['success']]
                
                if successful_requests:
                    response_times = [r['response_time_ms'] for r in successful_requests]
                    avg_response_time = statistics.mean(response_times)
                    max_response_time = max(response_times)
                    requests_per_second = len(successful_requests) / total_time
                    
                    success_rate = (len(successful_requests) / total_requests) * 100
                    performance_ok = avg_response_time <= 1000 and success_rate >= 90  # 1 second max, 90% success
                    
                    results[f"{concurrent_users}_users"] = {
                        'concurrent_users': concurrent_users,
                        'total_requests': total_requests,
                        'successful_requests': len(successful_requests),
                        'failed_requests': len(failed_requests),
                        'success_rate': round(success_rate, 1),
                        'avg_response_time_ms': round(avg_response_time, 2),
                        'max_response_time_ms': round(max_response_time, 2),
                        'requests_per_second': round(requests_per_second, 2),
                        'total_time_seconds': round(total_time, 2),
                        'performance_ok': performance_ok
                    }
                    
                    self.log_test(
                        f"concurrent_load_{concurrent_users}_users",
                        performance_ok,
                        f"Concurrent load ({concurrent_users} users): {success_rate:.1f}% success, {avg_response_time:.1f}ms avg",
                        results[f"{concurrent_users}_users"]
                    )
                else:
                    results[f"{concurrent_users}_users"] = {
                        'concurrent_users': concurrent_users,
                        'success_rate': 0,
                        'performance_ok': False
                    }
                    
                    self.log_test(
                        f"concurrent_load_{concurrent_users}_users",
                        False,
                        f"Concurrent load ({concurrent_users} users): All requests failed"
                    )
            
            # Overall assessment
            all_performing = all(result['performance_ok'] for result in results.values())
            
            self.performance_metrics['concurrent_load'] = results
            
            self.log_test(
                "concurrent_load_performance",
                all_performing,
                f"Concurrent load performance: {'excellent' if all_performing else 'needs optimization'}",
                {
                    'tests_performed': len(concurrency_tests),
                    'overall_performance': 'excellent' if all_performing else 'needs optimization'
                }
            )
            
            return all_performing
            
        except Exception as e:
            self.log_test("concurrent_load_performance", False, f"Concurrent load test failed: {str(e)}")
            return False
    
    def test_memory_usage(self) -> bool:
        """Test memory usage patterns"""
        try:
            print("\n💾 Testing Memory Usage...")
            
            # This is a simplified memory test
            # In production, you'd use more sophisticated memory profiling
            
            token = self.create_test_token('memory-user', '<EMAIL>', 'user')
            if not token:
                return False
            
            headers = {'Authorization': f'Bearer {token}'}
            
            # Make multiple requests to test for memory leaks
            print("  Making 100 requests to test memory patterns...")
            
            response_times = []
            for i in range(100):
                result = self.measure_response_time(f"{self.backend_url}/api/categories", headers)
                if result['success']:
                    response_times.append(result['response_time_ms'])
                
                if i % 20 == 0:
                    print(f"    Progress: {i}/100 requests")
            
            if len(response_times) >= 80:  # At least 80% success rate
                # Check if response times are stable (no significant increase indicating memory issues)
                first_20 = response_times[:20]
                last_20 = response_times[-20:]
                
                avg_first = statistics.mean(first_20)
                avg_last = statistics.mean(last_20)
                
                # Memory usage is considered good if response times don't increase significantly
                memory_stable = (avg_last - avg_first) <= (avg_first * 0.5)  # Less than 50% increase
                
                self.performance_metrics['memory_usage'] = {
                    'total_requests': 100,
                    'successful_requests': len(response_times),
                    'avg_first_20_ms': round(avg_first, 2),
                    'avg_last_20_ms': round(avg_last, 2),
                    'response_time_increase': round(((avg_last - avg_first) / avg_first) * 100, 1),
                    'memory_stable': memory_stable
                }
                
                self.log_test(
                    "memory_usage",
                    memory_stable,
                    f"Memory usage: {'stable' if memory_stable else 'potential memory issues'}",
                    self.performance_metrics['memory_usage']
                )
                
                return memory_stable
            else:
                self.log_test("memory_usage", False, "Memory usage test failed: Too many failed requests")
                return False
            
        except Exception as e:
            self.log_test("memory_usage", False, f"Memory usage test failed: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict:
        """Run all performance analysis tests"""
        print("\n" + "="*60)
        print("⚡ PERFORMANCE ANALYSIS")
        print("="*60)
        
        tests = [
            self.test_authentication_performance,
            self.test_protected_endpoint_performance,
            self.test_concurrent_load,
            self.test_memory_usage,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                self.log_test(test.__name__, False, f"Test execution failed: {str(e)}")
        
        print("\n" + "="*60)
        print(f"📊 PERFORMANCE ANALYSIS RESULTS: {passed}/{total} tests passed")
        print("="*60)
        
        success_rate = (passed / total) * 100 if total > 0 else 0
        print(f"🎯 Performance Score: {success_rate:.1f}%")
        
        # Performance assessment
        if success_rate >= 90:
            print("🚀 EXCELLENT: Performance is outstanding!")
        elif success_rate >= 70:
            print("⚡ GOOD: Performance is solid with room for improvement")
        else:
            print("⚠️ NEEDS OPTIMIZATION: Performance has significant issues")
        
        # Performance summary
        print(f"\n📈 PERFORMANCE SUMMARY:")
        if 'authentication' in self.performance_metrics:
            auth_avg = statistics.mean([ep['avg_ms'] for ep in self.performance_metrics['authentication'].values() if ep['avg_ms'] > 0])
            print(f"   Authentication Avg: {auth_avg:.1f}ms")
        
        if 'protected_endpoints' in self.performance_metrics:
            protected_avg = statistics.mean([ep['avg_ms'] for ep in self.performance_metrics['protected_endpoints'].values() if ep['avg_ms'] > 0])
            print(f"   Protected Endpoints Avg: {protected_avg:.1f}ms")
        
        if 'concurrent_load' in self.performance_metrics:
            max_rps = max([test['requests_per_second'] for test in self.performance_metrics['concurrent_load'].values() if 'requests_per_second' in test])
            print(f"   Max Requests/Second: {max_rps:.1f}")
        
        return {
            'total_tests': total,
            'passed_tests': passed,
            'failed_tests': total - passed,
            'success_rate': success_rate,
            'metrics': self.performance_metrics,
            'results': self.test_results
        }

def main():
    """Main performance analysis function"""
    analyzer = PerformanceAnalyzer()
    results = analyzer.run_all_tests()
    
    print(f"\n⚡ PERFORMANCE ANALYSIS SUMMARY:")
    print(f"   Authentication: {'✅ Fast' if results['passed_tests'] >= 1 else '⚠️ Slow'}")
    print(f"   Protected Endpoints: {'✅ Fast' if results['passed_tests'] >= 2 else '⚠️ Slow'}")
    print(f"   Concurrent Load: {'✅ Scalable' if results['passed_tests'] >= 3 else '⚠️ Limited'}")
    print(f"   Memory Usage: {'✅ Stable' if results['passed_tests'] >= 4 else '⚠️ Issues'}")
    
    return results

if __name__ == "__main__":
    main()
