<!DOCTYPE html>
<html>
<head>
    <title>Content Script Injection Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .debug { background: #f8f9fa; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🧪 Content Script Injection Test</h1>
    
    <div class="status info">
        <strong>URL:</strong> <span id="current-url"></span><br>
        <strong>Protocol:</strong> <span id="protocol"></span><br>
        <strong>Host:</strong> <span id="host"></span>
    </div>
    
    <button onclick="testInjection()">🔍 Test Content Script</button>
    <button onclick="simulateLocalhost()">🌐 Test on Localhost</button>
    <button onclick="checkManifest()">📋 Check Manifest Match</button>
    
    <div id="results"></div>
    
    <script>
        // Display current page info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('host').textContent = window.location.host;
        
        function testInjection() {
            const results = document.getElementById('results');
            
            let html = '<h3>🔍 Content Script Test Results:</h3>';
            
            // Test 1: Window objects
            const tests = [
                {
                    name: 'window.progressDashboardExtension',
                    result: typeof window.progressDashboardExtension !== 'undefined',
                    value: window.progressDashboardExtension
                },
                {
                    name: 'window.progressDashboardContentScript',
                    result: window.progressDashboardContentScript === true,
                    value: window.progressDashboardContentScript
                },
                {
                    name: 'window.progressDashboardExtensionReady',
                    result: window.progressDashboardExtensionReady === true,
                    value: window.progressDashboardExtensionReady
                },
                {
                    name: 'DOM Attribute',
                    result: document.documentElement.hasAttribute('data-progress-dashboard-extension'),
                    value: document.documentElement.getAttribute('data-progress-dashboard-extension')
                },
                {
                    name: 'Chrome Runtime',
                    result: typeof chrome !== 'undefined' && chrome.runtime,
                    value: typeof chrome !== 'undefined' ? 'Available' : 'Not Available'
                }
            ];
            
            tests.forEach(test => {
                html += `
                    <div class="status ${test.result ? 'success' : 'error'}">
                        <strong>${test.name}:</strong> ${test.result ? '✅ PASS' : '❌ FAIL'}<br>
                        <small>Value: ${JSON.stringify(test.value)}</small>
                    </div>
                `;
            });
            
            // Test 2: Console messages
            html += '<h3>📝 Console Check:</h3>';
            html += '<div class="info">Check browser console for extension messages. Look for:<br>';
            html += '• [Extension] Content script loaded and ready<br>';
            html += '• [Extension] Initializing content script...<br>';
            html += '• [Extension Debug] messages</div>';
            
            results.innerHTML = html;
        }
        
        function simulateLocalhost() {
            const results = document.getElementById('results');
            results.innerHTML = `
                <h3>🌐 Localhost Test</h3>
                <div class="info">
                    <p>To test on localhost, open: <strong>http://localhost:5173/login</strong></p>
                    <p>Current page is file:// protocol, extension content scripts only work on http/https</p>
                    <button onclick="window.open('http://localhost:5173/login', '_blank')">Open Localhost Test</button>
                </div>
            `;
        }
        
        function checkManifest() {
            const results = document.getElementById('results');
            const currentUrl = window.location.href;
            
            // Manifest patterns from the extension
            const patterns = [
                /^http:\/\/localhost:/,
                /^https:\/\/localhost:/,
                /^https:\/\/.*\.progressdashboard\.com/
            ];
            
            let html = '<h3>📋 Manifest Pattern Matching:</h3>';
            html += `<div class="info"><strong>Current URL:</strong> ${currentUrl}</div>`;
            
            patterns.forEach((pattern, index) => {
                const matches = pattern.test(currentUrl);
                const patternStr = [
                    'http://localhost:*/*',
                    'https://localhost:*/*', 
                    'https://*.progressdashboard.com/*'
                ][index];
                
                html += `
                    <div class="status ${matches ? 'success' : 'error'}">
                        <strong>Pattern:</strong> ${patternStr}<br>
                        <strong>Matches:</strong> ${matches ? '✅ YES' : '❌ NO'}
                    </div>
                `;
            });
            
            if (currentUrl.startsWith('file://')) {
                html += `
                    <div class="status error">
                        <strong>⚠️ File Protocol Detected</strong><br>
                        Content scripts don't inject on file:// URLs.<br>
                        Test on http://localhost:5173 instead.
                    </div>
                `;
            }
            
            results.innerHTML = html;
        }
        
        // Auto-run tests
        setTimeout(() => {
            testInjection();
            checkManifest();
        }, 1000);
        
        // Listen for extension ready event
        window.addEventListener('progressDashboardExtensionReady', (event) => {
            console.log('🎉 Extension ready event received!', event.detail);
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="status success">
                    <h3>🎉 Extension Ready Event Received!</h3>
                    <div class="debug">${JSON.stringify(event.detail, null, 2)}</div>
                </div>
            ` + results.innerHTML;
        });
    </script>
</body>
</html>
