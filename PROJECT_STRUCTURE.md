# Progress Dashboard - Project Structure

## 📁 Complete File Structure

```
progress-dashboard/
├── 📄 README.md                    # Project overview and setup guide
├── 📄 CODING_CONTEXT.md            # Coding context and patterns
├── 📄 DEVELOPMENT_GUIDE.md         # Development workflow guide
├── 📄 PROJECT_STRUCTURE.md         # This file - project structure
├── 📄 package.json                 # Frontend dependencies
├── 📄 package-lock.json            # Locked dependency versions
├── 📄 vite.config.ts               # Vite build configuration
├── 📄 tailwind.config.js           # Tailwind CSS configuration
├── 📄 postcss.config.js            # PostCSS configuration
├── 📄 tsconfig.json                # TypeScript configuration
├── 📄 tsconfig.app.json            # App-specific TypeScript config
├── 📄 tsconfig.node.json           # Node-specific TypeScript config
├── 📄 eslint.config.js             # ESLint configuration
├── 📄 index.html                   # HTML template
│
├── 📂 public/                      # Static assets
│   └── 📄 analytics-favicon.svg    # Application favicon
│
├── 📂 src/                         # Frontend source code
│   ├── 📄 main.tsx                 # Application entry point
│   ├── 📄 App.tsx                  # Main app component with routing
│   ├── 📄 index.css                # Global styles
│   ├── 📄 vite-env.d.ts           # Vite environment types
│   │
│   ├── 📂 components/              # React components
│   │   ├── 📄 AnalysisLayout.tsx   # Layout for analysis pages
│   │   ├── 📄 Categories.tsx       # Categories analysis dashboard
│   │   ├── 📄 Competitors.tsx      # Competitors analysis dashboard
│   │   ├── 📄 DataTable.tsx        # Reusable data table component
│   │   ├── 📄 DeleteModal.tsx      # File deletion confirmation modal
│   │   ├── 📄 FileManagement.tsx   # File upload and management
│   │   ├── 📄 FileStats.tsx        # File statistics display
│   │   ├── 📄 FileTable.tsx        # File listing table
│   │   ├── 📄 Header.tsx           # Navigation header with burger menu
│   │   ├── 📄 MainContent.tsx      # Main content wrapper
│   │   ├── 📄 Modal.tsx            # Reusable modal component
│   │   ├── 📄 NotificationDropdown.tsx    # Notification dropdown
│   │   ├── 📄 NotificationHistory.tsx     # Notification history view
│   │   ├── 📄 NotificationItem.tsx        # Individual notification item
│   │   ├── 📄 NotificationSettings.tsx    # Notification preferences
│   │   ├── 📄 RenameModal.tsx      # File rename modal
│   │   ├── 📄 SystemSettings.tsx   # Application settings
│   │   ├── 📄 Toast.tsx            # Toast notification component
│   │   └── 📄 UploadModal.tsx      # File upload modal
│   │
│   ├── 📂 contexts/                # React contexts for global state
│   │   └── 📄 NotificationContext.tsx     # Notification state management
│   │
│   ├── 📂 services/                # API services and external integrations
│   │   ├── 📄 api.ts               # Main API service class
│   │   └── 📄 notificationService.ts      # Notification API calls
│   │
│   ├── 📂 types/                   # TypeScript type definitions
│   │   └── 📄 notification.ts      # Notification-related types
│   │
│   ├── 📂 hooks/                   # Custom React hooks
│   │   └── (empty - ready for custom hooks)
│   │
│   ├── 📂 utils/                   # Utility functions
│   │   └── (empty - ready for utilities)
│   │
│   ├── 📂 tests/                   # Test files
│   │   └── (empty - ready for tests)
│   │
│   └── 📂 debug/                   # Debug utilities
│       └── (empty - ready for debug tools)
│
├── 📂 backend/                     # Python Flask API
│   ├── 📄 README.md               # Backend setup guide
│   ├── 📄 README_OPTIMIZATIONS.md # Performance optimization guide
│   ├── 📄 requirements.txt        # Python dependencies
│   ├── 📄 app.py                  # Main Flask application
│   ├── 📄 config.py               # Configuration management
│   ├── 📄 app_integration.py      # Optimization integration
│   ├── 📄 circuit_breaker.py      # Circuit breaker pattern
│   ├── 📄 config_optimizer.py     # Configuration optimization
│   ├── 📄 feature_controller.py   # Feature flag management
│   ├── 📄 file_monitor.py         # File change monitoring
│   ├── 📄 final_optimizer.py      # Final optimization layer
│   ├── 📄 monitoring.py           # System monitoring
│   ├── 📄 monitoring_dashboard.html # Monitoring dashboard
│   ├── 📄 optimization_service.py # Performance optimization service
│   ├── 📄 optimized_endpoints.py  # Optimized API endpoints
│   ├── 📄 performance_validator.py # Performance validation
│   ├── 📄 redis_client.py         # Redis connection management
│   ├── 📄 smart_serializer.py     # Intelligent data serialization
│   ├── 📄 start.sh                # Basic startup script
│   ├── 📄 start_optimized.sh      # Optimized startup script
│   ├── 📄 setup_redis.sh          # Redis setup script
│   ├── 📄 notifications.db        # SQLite notification database
│   │
│   ├── 📂 data/                   # CSV data storage
│   │   └── (CSV files for analysis)
│   │
│   ├── 📂 uploads/                # File upload temporary storage
│   │   └── (uploaded files)
│   │
│   ├── 📂 backups/                # Backup storage
│   │   └── (backup files)
│   │
│   └── 📂 venv/                   # Python virtual environment
│       └── (Python packages)
│
├── 📂 dist/                       # Production build output
│   ├── 📄 index.html              # Built HTML
│   ├── 📄 analytics-favicon.svg   # Favicon
│   └── 📂 assets/                 # Built assets (JS, CSS)
│
└── 📂 node_modules/               # Frontend dependencies
    └── (npm packages)
```

## 🎯 Key File Categories

### 📋 Configuration Files
- **package.json** - Frontend dependencies and scripts
- **vite.config.ts** - Build tool configuration
- **tailwind.config.js** - CSS framework configuration
- **tsconfig.json** - TypeScript compiler settings
- **eslint.config.js** - Code linting rules
- **backend/config.py** - Backend configuration and feature flags
- **backend/requirements.txt** - Python dependencies

### 🚀 Entry Points
- **src/main.tsx** - Frontend application entry
- **src/App.tsx** - Main React component with routing
- **backend/app.py** - Flask API server
- **index.html** - HTML template

### 🧩 Core Components
- **src/components/Header.tsx** - Navigation and burger menu
- **src/components/Categories.tsx** - Category analysis dashboard
- **src/components/Competitors.tsx** - Competitor tracking dashboard
- **src/components/FileManagement.tsx** - File operations
- **src/components/SystemSettings.tsx** - Application settings

### 🔌 API & Services
- **src/services/api.ts** - Frontend API client
- **src/contexts/NotificationContext.tsx** - Global state management
- **backend/app.py** - All API endpoints
- **backend/optimization_service.py** - Performance optimizations
- **backend/monitoring.py** - System monitoring

### 📊 Data & Storage
- **backend/data/** - CSV files for analysis
- **backend/notifications.db** - SQLite database
- **backend/uploads/** - Temporary file storage
- **backend/backups/** - Backup files

## 🔄 Data Flow

### Frontend → Backend
```
React Components → API Service → Flask Endpoints → Data Processing → Response
```

### File Processing
```
Upload → Validation → Storage → Processing → Database → API Response
```

### Notification System
```
Backend Events → SQLite → API → React Context → UI Components
```

## 🛠️ Development Workflow

### 1. Frontend Development
```bash
# Start development server
npm run dev                 # http://localhost:5173

# Build for production
npm run build

# Lint code
npm run lint
```

### 2. Backend Development
```bash
# Start basic server
cd backend
python app.py              # http://localhost:5001

# Start with optimizations
./start_optimized.sh

# Run with monitoring
python monitoring.py
```

### 3. Full Stack Development
```bash
# Terminal 1 - Backend
cd backend && python app.py

# Terminal 2 - Frontend
npm run dev

# Terminal 3 - Redis (optional)
redis-server
```

## 🔧 Build & Deployment

### Production Build
```bash
# Frontend build
npm run build               # Output: dist/

# Backend setup
cd backend
pip install -r requirements.txt
export FLASK_ENV=production
```

### Environment Files
```bash
# Frontend (.env)
VITE_API_BASE_URL=http://localhost:5001

# Backend (environment variables)
FLASK_ENV=development
ENABLE_OPTIMIZATIONS=true
ENABLE_CACHING=true
REDIS_HOST=localhost
```

## 📝 File Naming Conventions

### Frontend
- **Components**: PascalCase (e.g., `FileManagement.tsx`)
- **Services**: camelCase (e.g., `api.ts`)
- **Types**: camelCase (e.g., `notification.ts`)
- **Contexts**: PascalCase with Context suffix (e.g., `NotificationContext.tsx`)

### Backend
- **Main modules**: snake_case (e.g., `optimization_service.py`)
- **Configuration**: snake_case (e.g., `config.py`)
- **Scripts**: snake_case (e.g., `start_optimized.sh`)
- **Database**: snake_case (e.g., `notifications.db`)

## 🎨 Asset Organization

### Static Assets
- **public/analytics-favicon.svg** - Application icon
- **src/index.css** - Global styles and Tailwind imports

### Built Assets
- **dist/assets/** - Compiled JavaScript and CSS
- **dist/index.html** - Production HTML with asset links

## 🔍 Important Directories

### Development
- **src/** - All frontend source code
- **backend/** - All backend source code
- **public/** - Static assets served directly

### Generated
- **dist/** - Production build output
- **node_modules/** - Frontend dependencies
- **backend/venv/** - Python virtual environment

### Data
- **backend/data/** - CSV files for analysis
- **backend/uploads/** - Temporary file storage
- **backend/backups/** - File backups

---

**💡 Navigation Tips:**
- Use `CODING_CONTEXT.md` for development patterns
- Use `DEVELOPMENT_GUIDE.md` for setup and workflow
- Use `README.md` for project overview
- Use `backend/README.md` for backend-specific info
