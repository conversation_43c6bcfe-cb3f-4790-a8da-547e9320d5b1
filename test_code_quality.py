#!/usr/bin/env python3
"""
Code Quality Assessment
Static code analysis, type safety validation, and documentation review
"""

import os
import sys
import ast
import json
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Set
from pathlib import Path

class CodeQualityAssessor:
    """Code quality assessment class"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.backend_path = self.project_root / "backend"
        self.frontend_path = self.project_root / "src"
        self.test_results = []
        self.quality_metrics = {}
        
    def log_test(self, test_name: str, success: bool, message: str = "", details: Dict = None, severity: str = "info"):
        """Log test result"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'details': details or {},
            'severity': severity,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        severity_icon = {"low": "🟡", "medium": "🟠", "high": "🔴", "critical": "🚨"}.get(severity, "ℹ️")
        
        print(f"{status} {severity_icon} {test_name}: {message}")
        
        if details:
            for key, value in details.items():
                print(f"    {key}: {value}")
    
    def analyze_python_code_structure(self) -> bool:
        """Analyze Python code structure and organization"""
        try:
            print("\n🐍 Analyzing Python Code Structure...")
            
            # Find all Python files in backend
            python_files = list(self.backend_path.glob("*.py"))
            
            if not python_files:
                self.log_test("python_code_structure", False, "No Python files found in backend", severity="high")
                return False
            
            # Analyze each Python file
            analysis_results = {}
            total_lines = 0
            total_functions = 0
            total_classes = 0
            files_with_docstrings = 0
            
            for py_file in python_files:
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        tree = ast.parse(content)
                    
                    # Count lines, functions, classes
                    lines = len(content.splitlines())
                    functions = len([node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)])
                    classes = len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)])
                    
                    # Check for module docstring
                    has_module_docstring = (
                        tree.body and 
                        isinstance(tree.body[0], ast.Expr) and 
                        isinstance(tree.body[0].value, ast.Constant) and 
                        isinstance(tree.body[0].value.value, str)
                    )
                    
                    if has_module_docstring:
                        files_with_docstrings += 1
                    
                    analysis_results[py_file.name] = {
                        'lines': lines,
                        'functions': functions,
                        'classes': classes,
                        'has_docstring': has_module_docstring
                    }
                    
                    total_lines += lines
                    total_functions += functions
                    total_classes += classes
                    
                except Exception as e:
                    analysis_results[py_file.name] = {'error': str(e)}
            
            # Calculate metrics
            avg_lines_per_file = total_lines / len(python_files) if python_files else 0
            docstring_coverage = (files_with_docstrings / len(python_files)) * 100 if python_files else 0
            
            # Quality assessment
            structure_quality = (
                avg_lines_per_file <= 500 and  # Files not too large
                docstring_coverage >= 70 and   # Good documentation
                total_functions > 0 and        # Has functions
                len(python_files) >= 5         # Reasonable modularity
            )
            
            self.quality_metrics['python_structure'] = {
                'total_files': len(python_files),
                'total_lines': total_lines,
                'total_functions': total_functions,
                'total_classes': total_classes,
                'avg_lines_per_file': round(avg_lines_per_file, 1),
                'docstring_coverage': round(docstring_coverage, 1),
                'files_analysis': analysis_results
            }
            
            self.log_test(
                "python_code_structure",
                structure_quality,
                f"Python code structure: {'well-organized' if structure_quality else 'needs improvement'}",
                {
                    'files': len(python_files),
                    'avg_lines_per_file': round(avg_lines_per_file, 1),
                    'docstring_coverage': f"{docstring_coverage:.1f}%",
                    'total_functions': total_functions,
                    'total_classes': total_classes
                },
                severity="medium" if not structure_quality else "info"
            )
            
            return structure_quality
            
        except Exception as e:
            self.log_test("python_code_structure", False, f"Python code analysis failed: {str(e)}", severity="high")
            return False
    
    def analyze_typescript_code_structure(self) -> bool:
        """Analyze TypeScript code structure and organization"""
        try:
            print("\n📘 Analyzing TypeScript Code Structure...")
            
            # Find TypeScript files
            ts_files = list(self.frontend_path.glob("**/*.ts")) + list(self.frontend_path.glob("**/*.tsx"))
            
            if not ts_files:
                self.log_test("typescript_code_structure", False, "No TypeScript files found", severity="medium")
                return False
            
            # Analyze TypeScript files
            analysis_results = {}
            total_lines = 0
            files_with_types = 0
            
            for ts_file in ts_files:
                try:
                    with open(ts_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    lines = len(content.splitlines())
                    
                    # Check for TypeScript features
                    has_interfaces = 'interface ' in content
                    has_types = 'type ' in content or ': ' in content
                    has_generics = '<' in content and '>' in content
                    
                    if has_types or has_interfaces:
                        files_with_types += 1
                    
                    analysis_results[ts_file.name] = {
                        'lines': lines,
                        'has_interfaces': has_interfaces,
                        'has_types': has_types,
                        'has_generics': has_generics
                    }
                    
                    total_lines += lines
                    
                except Exception as e:
                    analysis_results[ts_file.name] = {'error': str(e)}
            
            # Calculate metrics
            avg_lines_per_file = total_lines / len(ts_files) if ts_files else 0
            type_coverage = (files_with_types / len(ts_files)) * 100 if ts_files else 0
            
            # Quality assessment
            structure_quality = (
                avg_lines_per_file <= 300 and  # Files not too large
                type_coverage >= 60 and        # Good type usage
                len(ts_files) >= 3             # Reasonable modularity
            )
            
            self.quality_metrics['typescript_structure'] = {
                'total_files': len(ts_files),
                'total_lines': total_lines,
                'avg_lines_per_file': round(avg_lines_per_file, 1),
                'type_coverage': round(type_coverage, 1),
                'files_analysis': analysis_results
            }
            
            self.log_test(
                "typescript_code_structure",
                structure_quality,
                f"TypeScript code structure: {'well-organized' if structure_quality else 'needs improvement'}",
                {
                    'files': len(ts_files),
                    'avg_lines_per_file': round(avg_lines_per_file, 1),
                    'type_coverage': f"{type_coverage:.1f}%"
                },
                severity="medium" if not structure_quality else "info"
            )
            
            return structure_quality
            
        except Exception as e:
            self.log_test("typescript_code_structure", False, f"TypeScript code analysis failed: {str(e)}", severity="medium")
            return False
    
    def check_authentication_implementation_quality(self) -> bool:
        """Check authentication implementation quality"""
        try:
            print("\n🔐 Checking Authentication Implementation Quality...")
            
            # Check for required authentication files
            required_files = [
                'auth_middleware.py',
                'auth_routes.py',
                'auth_integration.py',
                'protected_endpoints.py'
            ]
            
            missing_files = []
            existing_files = []
            
            for file_name in required_files:
                file_path = self.backend_path / file_name
                if file_path.exists():
                    existing_files.append(file_name)
                else:
                    missing_files.append(file_name)
            
            # Analyze authentication code quality
            auth_quality_checks = {
                'required_files_present': len(missing_files) == 0,
                'proper_error_handling': False,
                'security_best_practices': False,
                'comprehensive_logging': False,
                'role_based_access': False
            }
            
            # Check auth_middleware.py for quality indicators
            auth_middleware_path = self.backend_path / 'auth_middleware.py'
            if auth_middleware_path.exists():
                try:
                    with open(auth_middleware_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for quality indicators
                    auth_quality_checks['proper_error_handling'] = (
                        'try:' in content and 'except' in content and 'raise' in content
                    )
                    auth_quality_checks['security_best_practices'] = (
                        'jwt.decode' in content and 'verify_signature' in content
                    )
                    auth_quality_checks['comprehensive_logging'] = (
                        'logger' in content or 'logging' in content
                    )
                    auth_quality_checks['role_based_access'] = (
                        'role' in content.lower() and 'permission' in content.lower()
                    )
                    
                except Exception as e:
                    pass
            
            # Calculate overall quality score
            quality_score = sum(auth_quality_checks.values()) / len(auth_quality_checks) * 100
            implementation_quality = quality_score >= 80
            
            self.quality_metrics['authentication_implementation'] = {
                'required_files': len(existing_files),
                'missing_files': missing_files,
                'quality_checks': auth_quality_checks,
                'quality_score': round(quality_score, 1)
            }
            
            self.log_test(
                "authentication_implementation_quality",
                implementation_quality,
                f"Authentication implementation: {quality_score:.1f}% quality score",
                {
                    'required_files_present': f"{len(existing_files)}/{len(required_files)}",
                    'missing_files': missing_files,
                    'quality_score': f"{quality_score:.1f}%"
                },
                severity="high" if not implementation_quality else "info"
            )
            
            return implementation_quality
            
        except Exception as e:
            self.log_test("authentication_implementation_quality", False, f"Authentication quality check failed: {str(e)}", severity="high")
            return False
    
    def check_test_coverage(self) -> bool:
        """Check test coverage and quality"""
        try:
            print("\n🧪 Checking Test Coverage...")
            
            # Find test files
            test_files = list(self.project_root.glob("test_*.py")) + list(self.project_root.glob("**/test_*.py"))
            
            if not test_files:
                self.log_test("test_coverage", False, "No test files found", severity="high")
                return False
            
            # Analyze test files
            test_analysis = {}
            total_test_lines = 0
            total_test_functions = 0
            
            for test_file in test_files:
                try:
                    with open(test_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    lines = len(content.splitlines())
                    
                    # Count test functions (functions starting with 'test_')
                    test_functions = content.count('def test_')
                    
                    # Check for quality indicators
                    has_assertions = 'assert' in content or 'self.assert' in content
                    has_setup_teardown = 'setUp' in content or 'tearDown' in content
                    has_mocking = 'mock' in content.lower() or 'patch' in content
                    
                    test_analysis[test_file.name] = {
                        'lines': lines,
                        'test_functions': test_functions,
                        'has_assertions': has_assertions,
                        'has_setup_teardown': has_setup_teardown,
                        'has_mocking': has_mocking
                    }
                    
                    total_test_lines += lines
                    total_test_functions += test_functions
                    
                except Exception as e:
                    test_analysis[test_file.name] = {'error': str(e)}
            
            # Calculate test coverage metrics
            avg_test_functions_per_file = total_test_functions / len(test_files) if test_files else 0
            
            # Quality assessment
            test_quality = (
                len(test_files) >= 3 and           # Multiple test files
                total_test_functions >= 10 and     # Reasonable number of tests
                avg_test_functions_per_file >= 3   # Good test density
            )
            
            self.quality_metrics['test_coverage'] = {
                'test_files': len(test_files),
                'total_test_functions': total_test_functions,
                'total_test_lines': total_test_lines,
                'avg_functions_per_file': round(avg_test_functions_per_file, 1),
                'test_analysis': test_analysis
            }
            
            self.log_test(
                "test_coverage",
                test_quality,
                f"Test coverage: {len(test_files)} test files, {total_test_functions} test functions",
                {
                    'test_files': len(test_files),
                    'test_functions': total_test_functions,
                    'avg_functions_per_file': round(avg_test_functions_per_file, 1)
                },
                severity="medium" if not test_quality else "info"
            )
            
            return test_quality
            
        except Exception as e:
            self.log_test("test_coverage", False, f"Test coverage check failed: {str(e)}", severity="medium")
            return False
    
    def check_documentation_quality(self) -> bool:
        """Check documentation quality"""
        try:
            print("\n📚 Checking Documentation Quality...")
            
            # Find documentation files
            doc_files = (
                list(self.project_root.glob("*.md")) +
                list(self.project_root.glob("docs/*.md")) +
                list(self.project_root.glob("**/*.md"))
            )
            
            if not doc_files:
                self.log_test("documentation_quality", False, "No documentation files found", severity="medium")
                return False
            
            # Analyze documentation
            doc_analysis = {}
            total_doc_lines = 0
            
            for doc_file in doc_files:
                try:
                    with open(doc_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    lines = len(content.splitlines())
                    
                    # Check for quality indicators
                    has_headers = content.count('#') > 0
                    has_code_blocks = '```' in content
                    has_links = '[' in content and '](' in content
                    has_examples = 'example' in content.lower() or 'usage' in content.lower()
                    
                    doc_analysis[doc_file.name] = {
                        'lines': lines,
                        'has_headers': has_headers,
                        'has_code_blocks': has_code_blocks,
                        'has_links': has_links,
                        'has_examples': has_examples
                    }
                    
                    total_doc_lines += lines
                    
                except Exception as e:
                    doc_analysis[doc_file.name] = {'error': str(e)}
            
            # Quality assessment
            avg_lines_per_doc = total_doc_lines / len(doc_files) if doc_files else 0
            
            documentation_quality = (
                len(doc_files) >= 2 and        # Multiple documentation files
                total_doc_lines >= 100 and     # Substantial documentation
                avg_lines_per_doc >= 20        # Detailed documentation
            )
            
            self.quality_metrics['documentation'] = {
                'doc_files': len(doc_files),
                'total_lines': total_doc_lines,
                'avg_lines_per_file': round(avg_lines_per_doc, 1),
                'doc_analysis': doc_analysis
            }
            
            self.log_test(
                "documentation_quality",
                documentation_quality,
                f"Documentation: {len(doc_files)} files, {total_doc_lines} total lines",
                {
                    'doc_files': len(doc_files),
                    'total_lines': total_doc_lines,
                    'avg_lines_per_file': round(avg_lines_per_doc, 1)
                },
                severity="low" if not documentation_quality else "info"
            )
            
            return documentation_quality
            
        except Exception as e:
            self.log_test("documentation_quality", False, f"Documentation check failed: {str(e)}", severity="low")
            return False
    
    def check_security_best_practices(self) -> bool:
        """Check security best practices in code"""
        try:
            print("\n🔒 Checking Security Best Practices...")
            
            security_checks = {
                'no_hardcoded_secrets': True,
                'proper_input_validation': False,
                'secure_error_handling': False,
                'environment_variables': False,
                'secure_headers': False
            }
            
            # Check all Python files for security issues
            python_files = list(self.backend_path.glob("*.py"))
            
            for py_file in python_files:
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for hardcoded secrets (basic check)
                    if any(pattern in content.lower() for pattern in ['password=', 'secret=', 'key=', 'token=']):
                        # More sophisticated check - ignore if it's in comments or using env vars
                        lines = content.split('\n')
                        for line in lines:
                            if any(pattern in line.lower() for pattern in ['password=', 'secret=', 'key=', 'token=']):
                                if not (line.strip().startswith('#') or 'os.getenv' in line or 'environ' in line):
                                    security_checks['no_hardcoded_secrets'] = False
                                    break
                    
                    # Check for input validation
                    if any(pattern in content for pattern in ['validate', 'sanitize', 'escape', 'clean']):
                        security_checks['proper_input_validation'] = True
                    
                    # Check for secure error handling
                    if 'try:' in content and 'except' in content and not 'print(' in content:
                        security_checks['secure_error_handling'] = True
                    
                    # Check for environment variables usage
                    if 'os.getenv' in content or 'os.environ' in content:
                        security_checks['environment_variables'] = True
                    
                    # Check for secure headers (in Flask apps)
                    if any(pattern in content for pattern in ['CORS', 'Content-Security-Policy', 'X-Frame-Options']):
                        security_checks['secure_headers'] = True
                        
                except Exception as e:
                    continue
            
            # Calculate security score
            security_score = sum(security_checks.values()) / len(security_checks) * 100
            security_quality = security_score >= 80
            
            self.quality_metrics['security_practices'] = {
                'security_checks': security_checks,
                'security_score': round(security_score, 1)
            }
            
            self.log_test(
                "security_best_practices",
                security_quality,
                f"Security practices: {security_score:.1f}% compliance",
                {
                    'security_score': f"{security_score:.1f}%",
                    'checks_passed': sum(security_checks.values()),
                    'total_checks': len(security_checks)
                },
                severity="high" if not security_quality else "info"
            )
            
            return security_quality
            
        except Exception as e:
            self.log_test("security_best_practices", False, f"Security check failed: {str(e)}", severity="high")
            return False
    
    def run_all_tests(self) -> Dict:
        """Run all code quality assessment tests"""
        print("\n" + "="*60)
        print("📊 CODE QUALITY ASSESSMENT")
        print("="*60)
        
        tests = [
            self.analyze_python_code_structure,
            self.analyze_typescript_code_structure,
            self.check_authentication_implementation_quality,
            self.check_test_coverage,
            self.check_documentation_quality,
            self.check_security_best_practices,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                self.log_test(test.__name__, False, f"Test execution failed: {str(e)}", severity="high")
        
        print("\n" + "="*60)
        print(f"📊 CODE QUALITY ASSESSMENT RESULTS: {passed}/{total} tests passed")
        print("="*60)
        
        success_rate = (passed / total) * 100 if total > 0 else 0
        print(f"🎯 Code Quality Score: {success_rate:.1f}%")
        
        # Quality assessment
        if success_rate >= 90:
            print("🏆 EXCELLENT: Code quality is outstanding!")
        elif success_rate >= 70:
            print("✅ GOOD: Code quality is solid with minor improvements needed")
        else:
            print("⚠️ NEEDS IMPROVEMENT: Code quality has significant issues")
        
        return {
            'total_tests': total,
            'passed_tests': passed,
            'failed_tests': total - passed,
            'success_rate': success_rate,
            'metrics': self.quality_metrics,
            'results': self.test_results
        }

def main():
    """Main code quality assessment function"""
    assessor = CodeQualityAssessor()
    results = assessor.run_all_tests()
    
    print(f"\n📊 CODE QUALITY SUMMARY:")
    print(f"   Python Structure: {'✅ Good' if results['passed_tests'] >= 1 else '⚠️ Issues'}")
    print(f"   TypeScript Structure: {'✅ Good' if results['passed_tests'] >= 2 else '⚠️ Issues'}")
    print(f"   Authentication Quality: {'✅ Good' if results['passed_tests'] >= 3 else '⚠️ Issues'}")
    print(f"   Test Coverage: {'✅ Good' if results['passed_tests'] >= 4 else '⚠️ Issues'}")
    print(f"   Documentation: {'✅ Good' if results['passed_tests'] >= 5 else '⚠️ Issues'}")
    print(f"   Security Practices: {'✅ Good' if results['passed_tests'] >= 6 else '⚠️ Issues'}")
    
    return results

if __name__ == "__main__":
    main()
