<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Extension UI Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            font-size: 12px;
        }
        .test-step {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .ui-preview {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            background: white;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Test Extension UI Flow</h1>
        <p>Test untuk memverifikasi flow notifikasi baru pada Chrome Extension.</p>

        <div class="info">
            <strong>📋 Fitur Baru yang Ditest:</strong>
            <ul>
                <li>Badge koneksi kecil di pojok kanan atas</li>
                <li>Detail informasi OTP dengan layout yang lebih baik</li>
                <li>Notifikasi screen untuk approve/reject dengan countdown timer</li>
                <li>Animasi dan transisi yang smooth</li>
            </ul>
        </div>

        <!-- Step 1: Check Extension Installation -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">1</div>
                <h3>Check Extension Installation</h3>
            </div>
            <p>Pastikan Chrome Extension sudah diinstall dan diupdate dengan UI baru.</p>
            <button onclick="checkExtension()">Check Extension</button>
            <div id="extension-check-results"></div>
        </div>

        <!-- Step 2: Test Connection Badge -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">2</div>
                <h3>Test Connection Badge</h3>
            </div>
            <p>Badge koneksi seharusnya muncul sebagai dot kecil di pojok kanan atas extension popup.</p>
            <div class="ui-preview">
                <strong>Expected UI:</strong><br>
                🔐 Progress Dashboard<br>
                OTP Authenticator<br>
                <small style="color: #6b7280;">Connected to backend</small><br>
                <div style="position: relative; display: inline-block; margin-top: 10px;">
                    <div style="width: 100px; height: 60px; border: 1px solid #ddd; border-radius: 5px; position: relative;">
                        <div style="position: absolute; top: 5px; right: 5px; width: 12px; height: 12px; background: #22c55e; border-radius: 50%; border: 2px solid #16a34a;"></div>
                    </div>
                    <small>Badge di pojok kanan atas</small>
                </div>
            </div>
            <button onclick="testConnectionBadge()">Test Connection Badge</button>
            <div id="badge-test-results"></div>
        </div>

        <!-- Step 3: Test OTP Request UI -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">3</div>
                <h3>Test OTP Request UI</h3>
            </div>
            <p>Saat menerima OTP request, extension akan menampilkan detail informasi yang lebih lengkap.</p>
            <input type="email" id="test-email" placeholder="Enter test email" value="<EMAIL>">
            <button onclick="generateOTPRequest()">Generate OTP Request</button>
            <div id="otp-request-results"></div>
            
            <div class="ui-preview">
                <strong>Expected UI:</strong><br>
                <div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin: 10px; background: white;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #e2e8f0;">
                        <span style="font-size: 18px;">🔑</span>
                        <strong>Authentication Request</strong>
                    </div>
                    <div style="text-align: left; font-size: 12px;">
                        <div style="display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #f1f5f9;">
                            <span style="color: #64748b;">Email:</span>
                            <span><EMAIL></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #f1f5f9;">
                            <span style="color: #64748b;">Website:</span>
                            <span>localhost:5173</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 5px 0;">
                            <span style="color: #64748b;">Expires in:</span>
                            <span style="color: #ef4444; font-weight: 600;">4:59</span>
                        </div>
                    </div>
                    <div style="display: flex; gap: 8px; margin-top: 15px;">
                        <button style="flex: 1; background: #9CEE69; color: #1A1919; border: 1px solid #9CEE69; border-radius: 6px; padding: 10px; font-size: 12px;">
                            ✓ Approve Login
                        </button>
                        <button style="flex: 1; background: #f8fafc; color: #374151; border: 1px solid #e2e8f0; border-radius: 6px; padding: 10px; font-size: 12px;">
                            ✕ Reject
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 4: Test Notification Screens -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">4</div>
                <h3>Test Notification Screens</h3>
            </div>
            <p>Setelah approve/reject, extension akan menampilkan notifikasi dengan countdown timer.</p>
            
            <div class="ui-preview">
                <strong>Success Notification:</strong><br>
                <div style="border: 1px solid rgba(156, 238, 105, 0.3); border-radius: 8px; padding: 20px; margin: 10px; background: linear-gradient(135deg, rgba(156, 238, 105, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%);">
                    <div style="font-size: 24px; margin-bottom: 10px;">✅</div>
                    <strong>Login Approved!</strong><br>
                    <small style="color: #64748b;">You have successfully authenticated the login request.</small><br>
                    <small style="color: #6b7280; margin-top: 10px; display: block;">Closing in 3s</small>
                </div>
                
                <strong>Reject Notification:</strong><br>
                <div style="border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 8px; padding: 20px; margin: 10px; background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);">
                    <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                    <strong>Login Rejected</strong><br>
                    <small style="color: #64748b;">The authentication request has been denied.</small><br>
                    <small style="color: #6b7280; margin-top: 10px; display: block;">Closing in 3s</small>
                </div>
            </div>
            
            <p><strong>Instruksi:</strong> Setelah generate OTP request di step 3, buka Chrome Extension dan klik tombol Approve atau Reject untuk melihat notifikasi.</p>
        </div>

        <!-- Step 5: Full Flow Test -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">5</div>
                <h3>Full Flow Test</h3>
            </div>
            <p>Test complete flow dari idle → OTP request → notification → close.</p>
            <button onclick="runFullFlowTest()">Run Full Flow Test</button>
            <div id="full-flow-results"></div>
            
            <div class="info">
                <strong>🔄 Expected Flow:</strong>
                <ol>
                    <li><strong>Idle State:</strong> Badge di pojok kanan atas, logo dan status koneksi</li>
                    <li><strong>OTP Request:</strong> Detail card dengan informasi lengkap dan tombol aksi</li>
                    <li><strong>User Action:</strong> Klik Approve/Reject dengan loading state</li>
                    <li><strong>Notification:</strong> Success/Reject screen dengan countdown timer</li>
                    <li><strong>Auto Close:</strong> Extension menutup otomatis setelah 3 detik</li>
                </ol>
            </div>
        </div>

        <!-- Debug Panel -->
        <div class="test-step">
            <div class="step-header">
                <div class="step-number">🔧</div>
                <h3>Debug Information</h3>
            </div>
            <button onclick="getDebugInfo()">Get Debug Info</button>
            <div id="debug-results"></div>
        </div>
    </div>

    <script>
        function checkExtension() {
            const resultsDiv = document.getElementById('extension-check-results');
            
            if (typeof window.progressDashboardExtension !== 'undefined') {
                resultsDiv.innerHTML = `
                    <div class="success">✅ Extension is available and loaded!</div>
                    <p>Extension bridge methods: ${Object.keys(window.progressDashboardExtension).join(', ')}</p>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Extension not found</div>
                    <p>Please make sure:</p>
                    <ul>
                        <li>Chrome Extension is installed</li>
                        <li>Extension is enabled</li>
                        <li>Extension has been reloaded after UI updates</li>
                        <li>Page has been refreshed</li>
                    </ul>
                `;
            }
        }

        function testConnectionBadge() {
            const resultsDiv = document.getElementById('badge-test-results');
            resultsDiv.innerHTML = `
                <div class="info">📱 Please open Chrome Extension popup to verify:</div>
                <ul>
                    <li>Badge muncul sebagai dot kecil di pojok kanan atas</li>
                    <li>Badge berwarna hijau jika connected, abu-abu jika tidak</li>
                    <li>Layout utama menampilkan logo dan status koneksi</li>
                    <li>Tidak ada text "Connected/Not Connected" yang besar</li>
                </ul>
                <div class="warning">⚠️ Jika badge tidak muncul, extension perlu direload.</div>
            `;
        }

        async function generateOTPRequest() {
            const email = document.getElementById('test-email').value;
            const resultsDiv = document.getElementById('otp-request-results');
            
            if (!email) {
                resultsDiv.innerHTML = '<div class="error">❌ Please enter an email address</div>';
                return;
            }

            resultsDiv.innerHTML = '<div class="info">Generating OTP request...</div>';

            try {
                const response = await fetch('http://localhost:5001/api/auth/generate-otp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ OTP request generated!</div>
                        <div class="info">📱 Now open Chrome Extension to see the new UI:</div>
                        <ul>
                            <li>Detailed request card with email, website, and timer</li>
                            <li>Styled action buttons with icons</li>
                            <li>Better layout and spacing</li>
                        </ul>
                        <div class="warning">⚠️ Click Approve or Reject to test notification screens!</div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">❌ Failed to generate OTP request</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function runFullFlowTest() {
            const resultsDiv = document.getElementById('full-flow-results');
            resultsDiv.innerHTML = `
                <div class="info">🚀 Running full flow test...</div>
                <div class="warning">📱 Please follow these steps:</div>
                <ol>
                    <li>Open Chrome Extension popup (should show idle state with badge)</li>
                    <li>Click "Generate OTP Request" below to trigger OTP</li>
                    <li>Extension should switch to detailed OTP request view</li>
                    <li>Click "Approve Login" or "Reject" in extension</li>
                    <li>Verify notification screen appears with countdown</li>
                    <li>Extension should auto-close after 3 seconds</li>
                </ol>
            `;
            
            // Auto-generate OTP for testing
            setTimeout(() => {
                generateOTPRequest();
            }, 1000);
        }

        function getDebugInfo() {
            const resultsDiv = document.getElementById('debug-results');
            
            const debugInfo = {
                extensionAvailable: typeof window.progressDashboardExtension !== 'undefined',
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: new Date().toISOString()
            };

            if (typeof window.progressDashboardExtension !== 'undefined') {
                try {
                    const bridge = window.progressDashboardExtension;
                    debugInfo.bridgeMethods = Object.keys(bridge);
                    
                    if (bridge.getDebugInfo) {
                        debugInfo.extensionDebugInfo = bridge.getDebugInfo();
                    }
                } catch (error) {
                    debugInfo.extensionError = error.message;
                }
            }

            resultsDiv.innerHTML = `
                <div class="info">Debug Information:</div>
                <pre>${JSON.stringify(debugInfo, null, 2)}</pre>
            `;
        }

        // Auto-check extension on page load
        window.addEventListener('load', () => {
            setTimeout(checkExtension, 1000);
        });
    </script>
</body>
</html>
