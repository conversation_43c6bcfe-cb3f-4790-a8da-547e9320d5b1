#!/bin/bash

# Test Agent Remote Setup
# Verifies that all components are properly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}🧪 Agent Remote Setup Test${NC}"
    echo -e "${BLUE}==========================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

test_file_structure() {
    print_info "Testing file structure..."
    
    local files=(
        ".agent/config.json"
        ".agent/remote-agent.py"
        ".agent/agent-remote.sh"
        ".agent/setup-env.sh"
        ".agent/README.md"
        ".github/workflows/ci-cd.yml"
        ".github/workflows/agent-remote.yml"
    )
    
    local missing_files=()
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            print_success "Found: $file"
        else
            print_error "Missing: $file"
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        print_success "All required files present"
        return 0
    else
        print_error "Missing ${#missing_files[@]} required files"
        return 1
    fi
}

test_file_permissions() {
    print_info "Testing file permissions..."
    
    local executable_files=(
        ".agent/remote-agent.py"
        ".agent/agent-remote.sh"
        ".agent/setup-env.sh"
    )
    
    for file in "${executable_files[@]}"; do
        if [ -x "$file" ]; then
            print_success "Executable: $file"
        else
            print_error "Not executable: $file"
            chmod +x "$file"
            print_warning "Fixed permissions for: $file"
        fi
    done
}

test_python_dependencies() {
    print_info "Testing Python dependencies..."
    
    if command -v python3 &> /dev/null; then
        print_success "Python 3 is available"
        python3 --version
    else
        print_error "Python 3 is not available"
        return 1
    fi
    
    if python3 -c "import requests" &> /dev/null; then
        print_success "requests package is available"
    else
        print_warning "requests package not found"
        print_info "Installing requests..."
        pip3 install requests --user
    fi
    
    if python3 -c "import json" &> /dev/null; then
        print_success "json package is available"
    else
        print_error "json package not available (should be built-in)"
        return 1
    fi
}

test_configuration() {
    print_info "Testing configuration files..."
    
    # Test agent config
    if python3 -c "import json; json.load(open('.agent/config.json'))" &> /dev/null; then
        print_success "Agent configuration is valid JSON"
    else
        print_error "Agent configuration is invalid JSON"
        return 1
    fi
    
    # Test workflow files
    if [ -f ".github/workflows/ci-cd.yml" ]; then
        print_success "CI/CD workflow file exists"
    else
        print_error "CI/CD workflow file missing"
        return 1
    fi
    
    if [ -f ".github/workflows/agent-remote.yml" ]; then
        print_success "Agent Remote workflow file exists"
    else
        print_error "Agent Remote workflow file missing"
        return 1
    fi
}

test_github_token() {
    print_info "Testing GitHub token..."
    
    if [ -z "$GITHUB_TOKEN" ]; then
        print_warning "GITHUB_TOKEN environment variable not set"
        print_info "To set your token:"
        echo "export GITHUB_TOKEN='your_token_here'"
        return 1
    else
        print_success "GITHUB_TOKEN is set"
        
        # Test token validity
        response=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
                       -H "Accept: application/vnd.github.v3+json" \
                       https://api.github.com/user)
        
        if echo "$response" | grep -q '"login"'; then
            username=$(echo "$response" | grep '"login"' | cut -d'"' -f4)
            print_success "GitHub API access successful (User: $username)"
            return 0
        else
            print_error "GitHub API access failed"
            echo "Response: $response"
            return 1
        fi
    fi
}

test_repository_access() {
    print_info "Testing repository access..."
    
    if [ -z "$GITHUB_TOKEN" ]; then
        print_warning "Skipping repository test - no GitHub token"
        return 1
    fi
    
    response=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
                   -H "Accept: application/vnd.github.v3+json" \
                   https://api.github.com/repos/hellozei/progress-dashboard)
    
    if echo "$response" | grep -q '"full_name"'; then
        print_success "Repository access successful"
        return 0
    else
        print_error "Repository access failed"
        echo "Response: $response"
        return 1
    fi
}

test_agent_script() {
    print_info "Testing agent script..."
    
    # Test help command
    if python3 .agent/remote-agent.py --help &> /dev/null; then
        print_success "Agent script help works"
    else
        print_error "Agent script help failed"
        return 1
    fi
    
    # Test list command
    if python3 .agent/remote-agent.py --list &> /dev/null; then
        print_success "Agent script list command works"
    else
        print_error "Agent script list command failed"
        return 1
    fi
    
    # Test health check if token is available
    if [ -n "$GITHUB_TOKEN" ]; then
        if python3 .agent/remote-agent.py health_check &> /dev/null; then
            print_success "Agent health check works"
        else
            print_warning "Agent health check failed (may need repository setup)"
        fi
    fi
}

test_shell_script() {
    print_info "Testing shell script..."
    
    # Test help
    if ./.agent/agent-remote.sh --help &> /dev/null; then
        print_success "Shell script help works"
    else
        print_error "Shell script help failed"
        return 1
    fi
    
    # Test list command
    if ./.agent/agent-remote.sh list &> /dev/null; then
        print_success "Shell script list command works"
    else
        print_warning "Shell script list command failed"
    fi
}

run_comprehensive_test() {
    print_header
    echo
    
    local tests=(
        "test_file_structure"
        "test_file_permissions"
        "test_python_dependencies"
        "test_configuration"
        "test_github_token"
        "test_repository_access"
        "test_agent_script"
        "test_shell_script"
    )
    
    local passed=0
    local failed=0
    local warnings=0
    
    for test in "${tests[@]}"; do
        echo
        if $test; then
            ((passed++))
        else
            ((failed++))
        fi
    done
    
    echo
    print_info "Test Summary:"
    echo "✅ Passed: $passed"
    echo "❌ Failed: $failed"
    
    if [ $failed -eq 0 ]; then
        print_success "All tests passed! Agent Remote is ready to use."
        echo
        print_info "Next steps:"
        echo "1. Set GITHUB_TOKEN if not already set"
        echo "2. Test with: ./.agent/agent-remote.sh health-check"
        echo "3. Check GitHub Actions tab for workflows"
        return 0
    else
        print_error "Some tests failed. Please fix the issues above."
        return 1
    fi
}

# Run the comprehensive test
run_comprehensive_test
