#!/bin/bash

# Environment Setup Script for Agent Remote
# This script helps configure environment variables and GitHub secrets

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}🔧 Agent Remote Environment Setup${NC}"
    echo -e "${BLUE}=================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_github_token() {
    if [ -z "$GITHUB_TOKEN" ]; then
        print_error "GITHUB_TOKEN environment variable is not set"
        echo
        echo "Please follow these steps to create a GitHub Personal Access Token:"
        echo "1. Go to GitHub Settings → Developer settings → Personal access tokens"
        echo "2. Click 'Generate new token (classic)'"
        echo "3. Select these scopes:"
        echo "   ✅ repo (Full control of private repositories)"
        echo "   ✅ workflow (Update GitHub Action workflows)"
        echo "   ✅ admin:repo_hook (Full control of repository hooks)"
        echo "   ✅ notifications (Access notifications)"
        echo "4. Copy the generated token"
        echo "5. Set it as environment variable:"
        echo "   export GITHUB_TOKEN='your_token_here'"
        echo
        return 1
    else
        print_success "GITHUB_TOKEN is set"
        return 0
    fi
}

test_github_api() {
    print_info "Testing GitHub API access..."
    
    response=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
                   -H "Accept: application/vnd.github.v3+json" \
                   https://api.github.com/user)
    
    if echo "$response" | grep -q '"login"'; then
        username=$(echo "$response" | grep '"login"' | cut -d'"' -f4)
        print_success "GitHub API access successful (User: $username)"
        return 0
    else
        print_error "GitHub API access failed"
        echo "Response: $response"
        return 1
    fi
}

test_repository_access() {
    print_info "Testing repository access..."
    
    response=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
                   -H "Accept: application/vnd.github.v3+json" \
                   https://api.github.com/repos/hellozei/progress-dashboard)
    
    if echo "$response" | grep -q '"full_name"'; then
        print_success "Repository access successful"
        return 0
    else
        print_error "Repository access failed"
        echo "Response: $response"
        return 1
    fi
}

create_env_file() {
    print_info "Creating .env file template..."
    
    cat > .env.template << EOF
# GitHub Configuration
GITHUB_TOKEN=your_github_token_here
GITHUB_REPOSITORY=hellozei/progress-dashboard
GITHUB_BRANCH=main

# Webhook Configuration (Optional)
WEBHOOK_SECRET=your_webhook_secret_here

# Agent Configuration
AGENT_VERSION=1.0.0
AGENT_DEBUG=false

# Environment Settings
NODE_ENV=production
PYTHON_ENV=production

# Deployment Settings
DEPLOY_STAGING_AUTO=true
DEPLOY_PRODUCTION_AUTO=false

# Monitoring Settings
HEALTH_CHECK_INTERVAL=300
LOG_RETENTION_DAYS=30

# Notification Settings
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=
EMAIL_NOTIFICATIONS=false
EOF

    print_success "Created .env.template file"
    print_warning "Copy .env.template to .env and fill in your values"
}

setup_github_secrets() {
    print_info "GitHub Secrets Setup Instructions"
    echo
    echo "To setup GitHub repository secrets:"
    echo "1. Go to your repository on GitHub"
    echo "2. Navigate to Settings → Secrets and variables → Actions"
    echo "3. Click 'New repository secret'"
    echo "4. Add these secrets:"
    echo
    echo "   Secret Name: GITHUB_TOKEN"
    echo "   Secret Value: [Your GitHub Personal Access Token]"
    echo
    echo "   Secret Name: WEBHOOK_SECRET (Optional)"
    echo "   Secret Value: [Random string for webhook security]"
    echo
    echo "   Secret Name: DEPLOY_KEY (If using SSH deployment)"
    echo "   Secret Value: [SSH private key for deployment]"
    echo
    print_warning "Never commit actual secrets to the repository!"
}

create_local_config() {
    print_info "Creating local configuration..."
    
    # Create local agent config
    mkdir -p ~/.config/agent-remote
    
    cat > ~/.config/agent-remote/config.json << EOF
{
  "repositories": {
    "progress-dashboard": {
      "owner": "hellozei",
      "repo": "progress-dashboard",
      "branch": "main",
      "local_path": "$(pwd)"
    }
  },
  "defaults": {
    "timeout": 300,
    "retry_count": 3,
    "log_level": "info"
  },
  "notifications": {
    "enabled": true,
    "channels": ["console"]
  }
}
EOF

    print_success "Created local configuration in ~/.config/agent-remote/"
}

install_dependencies() {
    print_info "Installing Python dependencies..."
    
    # Check if pip is available
    if command -v pip3 &> /dev/null; then
        pip3 install requests python-dotenv --user
        print_success "Python dependencies installed"
    else
        print_warning "pip3 not found. Please install Python dependencies manually:"
        echo "pip3 install requests python-dotenv"
    fi
}

create_shell_alias() {
    print_info "Creating shell alias..."
    
    alias_command="alias agent-remote='$(pwd)/.agent/agent-remote.sh'"
    
    # Add to .bashrc if it exists
    if [ -f ~/.bashrc ]; then
        if ! grep -q "alias agent-remote" ~/.bashrc; then
            echo "# Agent Remote alias" >> ~/.bashrc
            echo "$alias_command" >> ~/.bashrc
            print_success "Added alias to ~/.bashrc"
        fi
    fi
    
    # Add to .zshrc if it exists
    if [ -f ~/.zshrc ]; then
        if ! grep -q "alias agent-remote" ~/.zshrc; then
            echo "# Agent Remote alias" >> ~/.zshrc
            echo "$alias_command" >> ~/.zshrc
            print_success "Added alias to ~/.zshrc"
        fi
    fi
    
    echo "You can now use 'agent-remote' command from anywhere!"
    echo "Restart your shell or run: source ~/.bashrc (or ~/.zshrc)"
}

run_initial_test() {
    print_info "Running initial agent test..."
    
    if [ -n "$GITHUB_TOKEN" ]; then
        if python3 .agent/remote-agent.py health_check; then
            print_success "Agent remote test successful!"
        else
            print_error "Agent remote test failed"
            return 1
        fi
    else
        print_warning "Skipping test - GITHUB_TOKEN not set"
    fi
}

main() {
    print_header
    echo
    
    print_info "This script will help you setup Agent Remote environment"
    echo
    
    # Check GitHub token
    if check_github_token; then
        test_github_api
        test_repository_access
    fi
    
    # Create configuration files
    create_env_file
    create_local_config
    
    # Install dependencies
    install_dependencies
    
    # Create shell alias
    create_shell_alias
    
    # Setup instructions
    setup_github_secrets
    
    # Run test if token is available
    if [ -n "$GITHUB_TOKEN" ]; then
        run_initial_test
    fi
    
    echo
    print_success "Environment setup completed!"
    echo
    print_info "Next steps:"
    echo "1. Set your GITHUB_TOKEN environment variable"
    echo "2. Copy .env.template to .env and configure"
    echo "3. Setup GitHub repository secrets"
    echo "4. Test with: ./.agent/agent-remote.sh health-check"
    echo
}

# Run main function
main "$@"
