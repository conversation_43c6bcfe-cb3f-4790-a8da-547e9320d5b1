#!/bin/bash

# Progress Dashboard Remote Agent Script
# Provides easy interface for agent remote operations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AGENT_SCRIPT="$SCRIPT_DIR/remote-agent.py"
CONFIG_FILE="$SCRIPT_DIR/config.json"

# Functions
print_header() {
    echo -e "${BLUE}🤖 Progress Dashboard Remote Agent${NC}"
    echo -e "${BLUE}=================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_requirements() {
    local skip_token_check="$1"

    # Check if Python is available
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi

    # Check if required Python packages are available
    if ! python3 -c "import requests" &> /dev/null; then
        print_warning "requests package not found. Installing..."
        pip3 install requests
    fi

    # Check if GitHub token is set (skip for list command)
    if [ "$skip_token_check" != "true" ] && [ -z "$GITHUB_TOKEN" ]; then
        print_error "GITHUB_TOKEN environment variable is not set"
        echo "Please set your GitHub Personal Access Token:"
        echo "export GITHUB_TOKEN=your_token_here"
        exit 1
    fi

    # Check if config file exists
    if [ ! -f "$CONFIG_FILE" ]; then
        print_error "Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
}

show_usage() {
    print_header
    echo
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo
    echo "Commands:"
    echo "  health-check        Check agent and repository health"
    echo "  update-deps         Update project dependencies"
    echo "  run-tests          Execute test suite"
    echo "  deploy-staging     Deploy to staging environment"
    echo "  backup-data        Create data backup"
    echo "  generate-report    Generate project report"
    echo "  list               List all available commands"
    echo
    echo "Options:"
    echo "  --help, -h         Show this help message"
    echo "  --config FILE      Use custom configuration file"
    echo "  --params JSON      Pass parameters as JSON string"
    echo
    echo "Examples:"
    echo "  $0 health-check"
    echo "  $0 run-tests --params '{\"coverage\": true}'"
    echo "  $0 deploy-staging --params '{\"environment\": \"staging\"}'"
}

execute_command() {
    local command="$1"
    local params="$2"
    
    print_info "Executing command: $command"
    
    if [ -n "$params" ]; then
        python3 "$AGENT_SCRIPT" "$command" --parameters "$params"
    else
        python3 "$AGENT_SCRIPT" "$command"
    fi
}

# Main script logic
main() {
    local command=""
    local params=""
    local config_override=""

    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_usage
                exit 0
                ;;
            --config)
                config_override="$2"
                shift 2
                ;;
            --params)
                params="$2"
                shift 2
                ;;
            health-check|update-deps|run-tests|deploy-staging|backup-data|generate-report|list)
                command="$1"
                shift
                ;;
            *)
                if [ -z "$command" ]; then
                    command="$1"
                    shift
                else
                    print_error "Unknown option: $1"
                    show_usage
                    exit 1
                fi
                ;;
        esac
    done

    # Show usage if no command provided
    if [ -z "$command" ]; then
        show_usage
        exit 0
    fi

    print_header
    echo

    # Handle special commands that don't need token
    case "$command" in
        list)
            # Skip token check for list command
            check_requirements "true"
            python3 "$AGENT_SCRIPT" --list
            exit 0
            ;;
    esac

    # Check requirements (including token)
    check_requirements

    # Override config if specified
    if [ -n "$config_override" ]; then
        CONFIG_FILE="$config_override"
    fi

    # Handle other commands
    case "$command" in
        health-check)
            command="health_check"
            ;;
        update-deps)
            command="update_dependencies"
            ;;
        run-tests)
            command="run_tests"
            ;;
        deploy-staging)
            command="deploy_staging"
            ;;
        backup-data)
            command="backup_data"
            ;;
        generate-report)
            command="generate_report"
            ;;
    esac

    # Execute the command
    if execute_command "$command" "$params"; then
        print_success "Command completed successfully!"
    else
        print_error "Command failed!"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
