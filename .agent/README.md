# Agent Remote - Progress Dashboard

This directory contains the Agent Remote system for the Progress Dashboard application, enabling remote management and automation of repository operations.

## 🤖 What is Agent Remote?

Agent Remote is an intelligent automation system that allows you to:
- Execute commands remotely via GitHub Actions
- Monitor repository health and status
- Automate dependency updates
- Run tests and deployments
- Generate reports and backups
- Manage CI/CD pipelines

## 📁 Files Overview

- **`config.json`** - Agent configuration and capabilities
- **`remote-agent.py`** - Python script for agent operations
- **`agent-remote.sh`** - Shell script for easy command execution
- **`README.md`** - This documentation file

## 🚀 Quick Start

### 1. Prerequisites

- GitHub Personal Access Token with required permissions
- Python 3.8+ with `requests` package
- Repository with GitHub Actions enabled

### 2. Setup Environment

```bash
# Set your GitHub token
export GITHUB_TOKEN="your_github_token_here"

# Optional: Set webhook secret for enhanced security
export WEBHOOK_SECRET="your_webhook_secret"
```

### 3. Basic Usage

```bash
# Check agent health
./.agent/agent-remote.sh health-check

# List all available commands
./.agent/agent-remote.sh list

# Run tests
./.agent/agent-remote.sh run-tests

# Update dependencies
./.agent/agent-remote.sh update-deps

# Deploy to staging
./.agent/agent-remote.sh deploy-staging
```

## 📋 Available Commands

| Command | Description | Parameters |
|---------|-------------|------------|
| `health-check` | Check system health and status | None |
| `update-deps` | Update project dependencies | `force`, `security_only` |
| `run-tests` | Execute test suite | `test_type`, `coverage` |
| `deploy-staging` | Deploy to staging environment | `environment` |
| `backup-data` | Create data backup | `backup_type`, `retention_days` |
| `generate-report` | Generate project report | `report_type`, `format` |

## 🔧 Advanced Usage

### Using Python Script Directly

```bash
# Health check
python3 .agent/remote-agent.py health_check

# Run tests with parameters
python3 .agent/remote-agent.py run_tests --parameters '{"coverage": true}'

# List commands
python3 .agent/remote-agent.py --list
```

### Using GitHub API Directly

```bash
# Trigger workflow via repository dispatch
curl -X POST \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/hellozei/progress-dashboard/dispatches \
  -d '{
    "event_type": "agent-command",
    "client_payload": {
      "command": "health_check",
      "parameters": {}
    }
  }'
```

### Using GitHub Web Interface

1. Go to your repository on GitHub
2. Navigate to "Actions" tab
3. Select "Agent Remote Operations" workflow
4. Click "Run workflow"
5. Choose command and parameters
6. Click "Run workflow" button

## ⚙️ Configuration

### Agent Configuration (`config.json`)

The configuration file defines:
- Agent capabilities and metadata
- Available commands and their parameters
- GitHub API endpoints
- Security settings
- Environment configurations

### Required GitHub Token Permissions

Your GitHub Personal Access Token needs these scopes:
- `repo` - Full control of private repositories
- `workflow` - Update GitHub Action workflows
- `admin:repo_hook` - Full control of repository hooks

## 🔒 Security

### Environment Variables

- `GITHUB_TOKEN` - GitHub Personal Access Token (required)
- `WEBHOOK_SECRET` - Webhook secret for enhanced security (optional)

### Best Practices

1. **Token Security**: Never commit tokens to repository
2. **Scope Limitation**: Use minimal required token permissions
3. **Regular Rotation**: Rotate tokens periodically
4. **Environment Isolation**: Use different tokens for different environments

## 📊 Monitoring

### Workflow Runs

Monitor agent operations in GitHub Actions:
- Go to repository → Actions tab
- View workflow run history
- Check logs and artifacts

### Health Checks

Regular health checks provide:
- Repository status
- Workflow statistics
- Agent capabilities
- System health metrics

## 🐛 Troubleshooting

### Common Issues

1. **Token Permission Error**
   ```
   Error: GitHub API request failed: 403 Forbidden
   ```
   Solution: Check token permissions and scopes

2. **Configuration Not Found**
   ```
   Error: Configuration file not found: .agent/config.json
   ```
   Solution: Ensure you're in the repository root directory

3. **Python Dependencies Missing**
   ```
   Error: No module named 'requests'
   ```
   Solution: Install required packages
   ```bash
   pip3 install requests
   ```

### Debug Mode

Enable verbose logging:
```bash
export DEBUG=1
./.agent/agent-remote.sh health-check
```

## 🔄 Workflow Integration

### CI/CD Pipeline

The agent integrates with existing CI/CD workflows:
- Automatic testing on push
- Deployment to staging/production
- Security scanning
- Dependency updates

### Custom Workflows

Create custom workflows by:
1. Adding new commands to `config.json`
2. Implementing command logic in `remote-agent.py`
3. Creating corresponding GitHub Actions workflow

## 📈 Reporting

### Available Reports

- **Project Report**: Overview of repository statistics
- **Test Report**: Test execution results
- **Security Report**: Vulnerability scan results
- **Dependency Report**: Package update status

### Report Formats

- Markdown (default)
- JSON (structured data)
- HTML (web-friendly)

## 🤝 Contributing

To extend Agent Remote functionality:

1. Fork the repository
2. Add new commands to `config.json`
3. Implement command logic in `remote-agent.py`
4. Create corresponding workflow in `.github/workflows/`
5. Update documentation
6. Submit pull request

## 📞 Support

For issues and questions:
- Create GitHub issue in the repository
- Check workflow logs in Actions tab
- Review this documentation

## 🔗 Related Documentation

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [GitHub API Documentation](https://docs.github.com/en/rest)
- [Repository Dispatch Events](https://docs.github.com/en/rest/repos/repos#create-a-repository-dispatch-event)
