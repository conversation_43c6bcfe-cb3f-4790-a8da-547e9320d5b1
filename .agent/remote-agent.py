#!/usr/bin/env python3
"""
Progress Dashboard Remote Agent
Handles remote operations and GitHub API interactions
"""

import os
import json
import requests
import argparse
from datetime import datetime
from typing import Dict, Any, Optional

class ProgressDashboardAgent:
    def __init__(self, config_path: str = ".agent/config.json", require_token: bool = True):
        """Initialize the agent with configuration"""
        self.config = self.load_config(config_path)
        self.github_token = os.getenv('GITHUB_TOKEN')
        self.webhook_secret = os.getenv('WEBHOOK_SECRET')

        if require_token and not self.github_token:
            raise ValueError("GITHUB_TOKEN environment variable is required")
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Load agent configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON in configuration file: {config_path}")
    
    def github_api_request(self, endpoint: str, method: str = 'GET', data: Optional[Dict] = None) -> Dict:
        """Make authenticated request to GitHub API"""
        url = f"{self.config['endpoints']['github_api']}{endpoint}"
        headers = {
            'Authorization': f'token {self.github_token}',
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json'
        }
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers)
            elif method == 'POST':
                response = requests.post(url, headers=headers, json=data)
            elif method == 'PATCH':
                response = requests.patch(url, headers=headers, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            raise Exception(f"GitHub API request failed: {e}")
    
    def trigger_workflow(self, command: str, parameters: Dict = None) -> Dict:
        """Trigger a GitHub Actions workflow via repository dispatch"""
        if parameters is None:
            parameters = {}
        
        endpoint = f"/repos/{self.config['agent']['repository']}/dispatches"
        data = {
            "event_type": "agent-command",
            "client_payload": {
                "command": command,
                "parameters": parameters,
                "timestamp": datetime.now().isoformat(),
                "agent_version": self.config['agent']['version']
            }
        }
        
        return self.github_api_request(endpoint, 'POST', data)
    
    def get_workflow_runs(self, workflow_name: str = None) -> Dict:
        """Get workflow run history"""
        endpoint = f"/repos/{self.config['agent']['repository']}/actions/runs"
        if workflow_name:
            endpoint += f"?workflow={workflow_name}"
        
        return self.github_api_request(endpoint)
    
    def get_repository_info(self) -> Dict:
        """Get repository information"""
        endpoint = f"/repos/{self.config['agent']['repository']}"
        return self.github_api_request(endpoint)
    
    def health_check(self) -> Dict:
        """Perform agent health check"""
        try:
            repo_info = self.get_repository_info()
            recent_runs = self.get_workflow_runs()
            
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "repository": {
                    "name": repo_info.get('name'),
                    "full_name": repo_info.get('full_name'),
                    "default_branch": repo_info.get('default_branch'),
                    "last_updated": repo_info.get('updated_at')
                },
                "workflows": {
                    "total_runs": recent_runs.get('total_count', 0),
                    "recent_runs": len(recent_runs.get('workflow_runs', []))
                },
                "agent": {
                    "version": self.config['agent']['version'],
                    "capabilities": list(self.config['capabilities'].keys())
                }
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def execute_command(self, command: str, parameters: Dict = None) -> Dict:
        """Execute a remote command"""
        if command not in self.config['commands']:
            raise ValueError(f"Unknown command: {command}")
        
        command_config = self.config['commands'][command]
        
        print(f"🤖 Executing command: {command}")
        print(f"📝 Description: {command_config['description']}")
        
        if command == 'health_check':
            return self.health_check()
        else:
            # Trigger workflow for other commands
            result = self.trigger_workflow(command, parameters)
            return {
                "status": "triggered",
                "command": command,
                "parameters": parameters,
                "workflow": command_config['workflow'],
                "timestamp": datetime.now().isoformat()
            }
    
    def list_commands(self) -> None:
        """List available commands"""
        print("🤖 Available Agent Remote Commands:")
        print("=" * 50)
        
        for cmd, config in self.config['commands'].items():
            print(f"📋 {cmd}")
            print(f"   Description: {config['description']}")
            print(f"   Workflow: {config['workflow']}")
            if config['parameters']:
                print(f"   Parameters: {', '.join(config['parameters'])}")
            print()

def main():
    parser = argparse.ArgumentParser(description='Progress Dashboard Remote Agent')
    parser.add_argument('command', nargs='?', help='Command to execute')
    parser.add_argument('--list', action='store_true', help='List available commands')
    parser.add_argument('--config', default='.agent/config.json', help='Configuration file path')
    parser.add_argument('--parameters', type=str, help='Command parameters as JSON string')
    
    args = parser.parse_args()
    
    try:
        # For list command, don't require GitHub token
        require_token = not args.list
        agent = ProgressDashboardAgent(args.config, require_token)

        if args.list:
            agent.list_commands()
            return
        
        if not args.command:
            print("❌ No command specified. Use --list to see available commands.")
            return
        
        parameters = {}
        if args.parameters:
            try:
                parameters = json.loads(args.parameters)
            except json.JSONDecodeError:
                print("❌ Invalid JSON in parameters")
                return
        
        result = agent.execute_command(args.command, parameters)
        print("✅ Command executed successfully!")
        print(json.dumps(result, indent=2))
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
