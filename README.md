# 📊 Progress Dashboard

<div align="center">

  **A comprehensive dashboard for CSV data analysis and competitor tracking**

  [![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/hellozei/progress-dashboard)
  [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
  [![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/hellozei/progress-dashboard)
  [![React](https://img.shields.io/badge/React-18.3.1-61DAFB.svg)](https://reactjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-3178C6.svg)](https://www.typescriptlang.org/)
  [![Flask](https://img.shields.io/badge/Flask-2.3.3-000000.svg)](https://flask.palletsprojects.com/)

</div>

---

## 🚀 Quick Start

### Prerequisites
- **Node.js** 16+
- **Python** 3.8+
- **Git**

### ⚡ Installation (3 steps)
```bash
# 1. Clone and navigate
git clone https://github.com/hellozei/progress-dashboard.git
cd progress-dashboard

# 2. Install dependencies
npm install
cd backend && pip install -r requirements.txt && cd ..

# 3. Start both servers
# Terminal 1 - Backend
cd backend && python3 app.py

# Terminal 2 - Frontend
npm run dev
```

### 🌐 Access Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5001
- **API Health**: http://localhost:5001/api/health

## ✨ Features

### 📈 Analytics Dashboard
- **Category Analysis** - Deep dive into data categories with advanced filtering
- **Competitor Tracking** - Monitor competitor performance and trends
- **Real-time Updates** - Live data synchronization and updates
- **Interactive Charts** - Dynamic data visualization and insights

### 📁 File Management
- **Drag & Drop Upload** - Easy CSV file uploading with progress indicators
- **Automatic Backup** - Safe file operations with automatic backup creation
- **Batch Operations** - Bulk file management and processing
- **File Statistics** - Detailed file analytics and metadata

### 🔔 Notification System
- **Real-time Notifications** - Instant updates and alerts
- **Notification History** - Complete audit trail and history
- **Custom Settings** - Personalized notification preferences
- **Multi-channel Delivery** - Toast notifications and system alerts

### 🎨 Modern UI/UX
- **Responsive Design** - Mobile-first approach with burger menu
- **Performance Optimized** - Sub-2s load times with lazy loading
- **Accessibility** - WCAG 2.1 compliant interface
- **Glass Effects** - Modern UI with backdrop blur and transparency

## 🏗️ Tech Stack

### Frontend
- **React 18** with TypeScript - Modern UI library with type safety
- **Vite** - Lightning-fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework with custom theme
- **React Router DOM** - Client-side routing and navigation
- **React Query** - Data fetching, caching, and synchronization
- **Lucide React** - Beautiful and consistent icon library
- **Class Variance Authority** - Component variant management

### Backend
- **Python Flask** - Lightweight and flexible web framework
- **SQLite** - Embedded database for notifications and settings
- **Pandas** - Powerful data processing and analysis
- **Redis** - Optional caching layer for performance optimization
- **Flask-CORS** - Cross-origin resource sharing support

### Development Tools
- **TypeScript** - Static type checking and enhanced IDE support
- **ESLint** - Code linting and quality enforcement
- **Autoprefixer** - Automatic CSS vendor prefixing
- **React Query DevTools** - Development debugging tools (hidden by default)

## 📁 Project Structure

```
progress-dashboard/
├── 📁 src/                          # Frontend React application
│   ├── 📁 components/               # React components
│   │   ├── 📁 ui/                  # Base UI components
│   │   ├── Categories.tsx          # Category analysis dashboard
│   │   ├── Competitors.tsx         # Competitor tracking dashboard
│   │   ├── FileManagement.tsx      # File operations interface
│   │   └── SystemSettings.tsx      # Application settings
│   ├── 📁 contexts/                # React contexts (global state)
│   ├── 📁 hooks/                   # Custom React hooks
│   ├── 📁 services/                # API services and communication
│   ├── 📁 types/                   # TypeScript type definitions
│   ├── 📁 utils/                   # Utility functions and helpers
│   ├── 📁 styles/                  # CSS and styling files
│   └── 📁 tests/                   # Test files and fixtures
├── 📁 backend/                      # Python Flask API server
│   ├── app.py                      # Main Flask application
│   ├── 📁 data/                    # CSV data storage
│   ├── 📁 backups/                 # Automatic file backups
│   ├── optimization_service.py     # Performance optimizations
│   ├── monitoring.py               # System monitoring
│   ├── requirements.txt            # Python dependencies
│   └── start.sh                    # Backend startup script
├── 📁 public/                       # Static assets and favicon
├── 📁 docs/                         # Documentation files
├── 📁 dist/                         # Production build output
└── 📄 Configuration Files
    ├── package.json                # Node.js dependencies and scripts
    ├── vite.config.ts              # Vite build configuration
    ├── tailwind.config.js          # Tailwind CSS configuration
    ├── tsconfig.json               # TypeScript configuration
    └── eslint.config.js            # ESLint configuration
```

## 🛠️ Development

### Environment Setup
```bash
# Copy environment template (if needed)
cp .env.example .env

# Configure your settings
VITE_API_URL=http://localhost:5001
FLASK_ENV=development
```

### Running the Application

#### Option 1: Manual Start (Recommended for Development)
```bash
# Terminal 1 - Start Backend
cd backend
python3 app.py

# Terminal 2 - Start Frontend
npm run dev
```

#### Option 2: Using Scripts
```bash
# Start backend only
cd backend && ./start.sh

# Start frontend only
npm run dev
```

### 🌐 Application URLs
- **Frontend Development**: http://localhost:5173
- **Backend API**: http://localhost:5001
- **API Health Check**: http://localhost:5001/api/health
- **API Documentation**: Available through the running application

## 📜 Available Scripts

### Frontend Scripts
```bash
npm run dev              # Start development server (Vite)
npm run build            # Build for production
npm run preview          # Preview production build
npm run lint             # Run ESLint code analysis
```

### Backend Scripts
```bash
# Development
python3 app.py           # Start Flask development server
./start.sh              # Start with shell script (includes setup)

# Production (if configured)
./start_optimized.sh    # Start with Redis optimization
```

### Utility Scripts
```bash
# Code Quality
npm run lint             # Check code quality
npm run lint:fix         # Auto-fix linting issues (if available)

# Testing (when implemented)
npm run test             # Run test suite
npm run test:coverage    # Generate coverage report
```

## 🎯 Detailed Features

### 📊 Analytics Dashboard
- **Interactive Data Tables** - Sorting, filtering, and pagination
- **Real-time Data Updates** - Live synchronization with backend
- **Export Functionality** - Download data in various formats
- **Performance Metrics** - Built-in performance monitoring
- **Category Analysis** - Deep insights into data categories
- **Competitor Comparison** - Side-by-side competitor analysis

### 📁 Advanced File Management
- **Drag & Drop Upload** - Intuitive file uploading interface
- **File Operations** - Rename, delete with confirmation dialogs
- **Automatic Backup** - Safe operations with rollback capability
- **File Statistics** - Size, type, upload date, and metadata
- **Batch Processing** - Handle multiple files simultaneously
- **File Validation** - Type and size validation before upload

### 🔔 Notification System
- **Real-time Notifications** - Server-sent events for instant updates
- **Notification History** - Complete audit trail with timestamps
- **Custom Settings** - Personalized notification preferences
- **Toast Notifications** - Non-intrusive user feedback
- **Notification Categories** - Different types for different events
- **Persistence** - SQLite-based notification storage

### 🎨 User Experience
- **Responsive Design** - Mobile-first with burger menu navigation
- **Accessibility** - WCAG 2.1 AA compliant interface
- **Performance Optimized** - Lazy loading and code splitting
- **Glass Effects** - Modern UI with backdrop blur
- **Dark/Light Theme Support** - User preference adaptation
- **Keyboard Navigation** - Full keyboard accessibility

## 📊 Performance Metrics

### Current Performance
- **Load Time**: < 2 seconds (development)
- **First Contentful Paint**: < 1.5 seconds
- **Bundle Size**: Optimized with code splitting
- **API Response Time**: < 500ms for most endpoints

### Optimizations
- **Code Splitting**: Lazy loading of components
- **Image Optimization**: WebP support and compression
- **Caching**: Redis caching for API responses (optional)
- **Minification**: Production build optimization

## 🔒 Security Features

- **Input Validation**: All inputs sanitized and validated
- **CORS Protection**: Configured for secure cross-origin requests
- **File Upload Security**: Type and size validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Content sanitization

## 🚀 Deployment

### Production Build
```bash
# Build frontend for production
npm run build

# The dist/ folder contains the production build
# Serve with any static file server
```

### Environment Requirements
- **Node.js**: 16+ (LTS recommended)
- **Python**: 3.8+
- **Redis**: 6+ (optional, for caching)
- **Web Server**: Nginx/Apache (for production)

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Commit your changes**
   ```bash
   git commit -m 'Add some amazing feature'
   ```
4. **Push to the branch**
   ```bash
   git push origin feature/amazing-feature
   ```
5. **Open a Pull Request**

### Development Guidelines
- Follow the existing code style and patterns
- Add tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📚 Documentation

### 📖 For Users
- **[User Guide](./docs/guides/user-guide.md)** - Complete user manual *(coming soon)*
- **[FAQ](./docs/guides/faq.md)** - Frequently asked questions *(coming soon)*
- **[Troubleshooting](./docs/guides/troubleshooting.md)** - Common issues and solutions *(coming soon)*

### 👨‍💻 For Developers
- **[DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md)** - Complete development workflow
- **[CODING_CONTEXT.md](./CODING_CONTEXT.md)** - Coding patterns and context
- **[PROJECT_STRUCTURE.md](./PROJECT_STRUCTURE.md)** - Detailed file structure overview
- **[API Documentation](./docs/api/README.md)** - Complete API reference *(coming soon)*
- **[Component Library](./docs/components/README.md)** - UI components guide *(coming soon)*

### 🏗️ Architecture & Operations
- **[backend/README.md](./backend/README.md)** - Backend specific guide
- **[backend/README_OPTIMIZATIONS.md](./backend/README_OPTIMIZATIONS.md)** - Performance optimizations
- **[Architecture Guide](./docs/architecture/README.md)** - System design *(coming soon)*
- **[DevTools Guide](./docs/DEVTOOLS.md)** - Development tools configuration

### 📋 Project Management
- **[CHANGELOG.md](./CHANGELOG.md)** - Version history and changes
- **[COMPREHENSIVE_UI_UX_ANALYSIS.md](./COMPREHENSIVE_UI_UX_ANALYSIS.md)** - UI/UX analysis
- **[PHASE_1_IMPLEMENTATION_SUMMARY.md](./PHASE_1_IMPLEMENTATION_SUMMARY.md)** - Implementation summary

## 🙏 Acknowledgments

- **[React](https://reactjs.org/)** - The library for web and native user interfaces
- **[Flask](https://flask.palletsprojects.com/)** - A lightweight WSGI web application framework
- **[Tailwind CSS](https://tailwindcss.com/)** - A utility-first CSS framework
- **[Lucide](https://lucide.dev/)** - Beautiful & consistent icon toolkit
- **[Vite](https://vitejs.dev/)** - Next generation frontend tooling

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/hellozei/progress-dashboard/issues)
- **Discussions**: [GitHub Discussions](https://github.com/hellozei/progress-dashboard/discussions)
- **Documentation**: Available in the `/docs` folder

---

<div align="center">

  **Made with ❤️ for data analysis and competitor tracking**

  ⭐ Star this repository if you find it helpful!

</div>