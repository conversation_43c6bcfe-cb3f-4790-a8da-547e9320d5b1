/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      // Enhanced Typography System
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Fira Code', 'monospace'],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
      },
      // Enhanced Color System
      colors: {
        // Brand Colors - Core Identity
        brand: {
          'eerie-black': '#1A1919',
          'light-green': '#95E565',
          'nav-green': '#9CEE69',
          'jet': '#383838',
          'asparagus': '#608F44',
          'seashell': '#FEF5ED',
        },
        // Legacy theme support (for backward compatibility)
        theme: {
          'eerie-black': '#1A1919',
          'light-green': '#95E565',
          'nav-green': '#9CEE69',
          'jet': '#383838',
          'asparagus': '#608F44',
          'seashell': '#FEF5ED',
        },
        // Enhanced Primary Scale (Green-based)
        primary: {
          50: '#f0fdf4',   // Very light green
          100: '#dcfce7',  // Light green
          200: '#bbf7d0',  // Lighter green
          300: '#86efac',  // Light green
          400: '#4ade80',  // Medium light green
          500: '#95E565', // Brand light-green (main)
          600: '#608F44', // Brand asparagus
          700: '#4a7c59', // Darker green
          800: '#365a3d', // Dark green
          900: '#1f2937', // Very dark green
          950: '#0f172a', // Darkest green
        },
        // Enhanced Secondary Scale (Neutral-based)
        secondary: {
          50: '#f8fafc',   // Very light neutral
          100: '#f1f5f9',  // Light neutral
          200: '#e2e8f0',  // Lighter neutral
          300: '#cbd5e1',  // Light neutral
          400: '#94a3b8',  // Medium neutral
          500: '#64748b',  // Base neutral
          600: '#475569',  // Dark neutral
          700: '#334155',  // Darker neutral
          800: '#1e293b',  // Very dark neutral
          900: '#0f172a',  // Darkest neutral
          950: '#020617',  // Ultra dark neutral
        },
        // Enhanced Success Scale
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        // Enhanced Warning Scale
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        // Enhanced Error Scale
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        // Enhanced Dark Theme Scale
        dark: {
          50: '#f8fafc',   // Light (for dark mode text)
          100: '#f1f5f9',  // Light neutral
          200: '#e2e8f0',  // Medium light
          300: '#cbd5e1',  // Medium
          400: '#94a3b8',  // Medium dark
          500: '#383838',  // Brand jet
          600: '#2d2d2d',  // Darker
          700: '#1f1f1f',  // Very dark
          800: '#1A1919',  // Brand eerie-black
          900: '#0a0a0a',  // Ultra dark
          950: '#000000',  // Pure black
        },
        // Enhanced Glass Morphism Effects
        glass: {
          light: 'rgba(254, 245, 237, 0.1)',    // seashell based
          medium: 'rgba(254, 245, 237, 0.2)',   // seashell medium
          heavy: 'rgba(254, 245, 237, 0.3)',    // seashell heavy
          dark: 'rgba(26, 25, 25, 0.1)',        // eerie-black based
          'dark-medium': 'rgba(26, 25, 25, 0.2)', // eerie-black medium
          'dark-heavy': 'rgba(26, 25, 25, 0.3)',  // eerie-black heavy
          green: 'rgba(149, 229, 101, 0.1)',    // light-green based
          'green-medium': 'rgba(149, 229, 101, 0.2)', // light-green medium
          'green-heavy': 'rgba(149, 229, 101, 0.3)',  // light-green heavy
        },
      },
      // Enhanced Spacing System
      spacing: {
        '0.5': '0.125rem',   // 2px
        '1.5': '0.375rem',   // 6px
        '2.5': '0.625rem',   // 10px
        '3.5': '0.875rem',   // 14px
        '4.5': '1.125rem',   // 18px
        '5.5': '1.375rem',   // 22px
        '6.5': '1.625rem',   // 26px
        '7.5': '1.875rem',   // 30px
        '8.5': '2.125rem',   // 34px
        '9.5': '2.375rem',   // 38px
        '18': '4.5rem',      // 72px
        '22': '5.5rem',      // 88px
        '26': '6.5rem',      // 104px
        '30': '7.5rem',      // 120px
      },
      // Enhanced Border Radius
      borderRadius: {
        'xs': '0.125rem',    // 2px
        'sm': '0.25rem',     // 4px
        'md': '0.375rem',    // 6px
        'lg': '0.5rem',      // 8px
        'xl': '0.75rem',     // 12px
        '2xl': '1rem',       // 16px
        '3xl': '1.5rem',     // 24px
        '4xl': '2rem',       // 32px
      },
      // Enhanced Backdrop Blur
      backdropBlur: {
        xs: '2px',
        sm: '4px',
        md: '8px',
        lg: '12px',
        xl: '16px',
        '2xl': '24px',
        '3xl': '32px',
      },
      // Enhanced Box Shadow
      boxShadow: {
        'xs': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
        'sm': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
        'md': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
        'lg': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
        'xl': '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
        '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
        'inner': 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
        'glass-lg': '0 16px 64px 0 rgba(31, 38, 135, 0.37)',
      },
      // Enhanced Z-Index Scale
      zIndex: {
        hide: '-1',
        auto: 'auto',
        base: '0',
        docked: '10',
        dropdown: '1000',
        sticky: '1100',
        banner: '1200',
        overlay: '1300',
        modal: '1400',
        popover: '1500',
        skipLink: '1600',
        toast: '1700',
        tooltip: '1800',
      },
      animation: {
        'slide-in': 'slideIn 0.3s ease-out',
        'slide-out': 'slideOut 0.3s ease-in',
        'fade-in': 'fadeIn 0.2s ease-out',
        'fade-out': 'fadeOut 0.2s ease-in',
        'scale-in': 'scaleIn 0.2s ease-out',
        'scale-out': 'scaleOut 0.2s ease-in',
        'bounce-in': 'bounceIn 0.3s ease-out',
      },
      keyframes: {
        slideIn: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideOut: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        scaleOut: {
          '0%': { transform: 'scale(1)', opacity: '1' },
          '100%': { transform: 'scale(0.95)', opacity: '0' },
        },
        bounceIn: {
          '0%': { transform: 'scale(0.3)', opacity: '0' },
          '50%': { transform: 'scale(1.05)', opacity: '0.8' },
          '70%': { transform: 'scale(0.9)', opacity: '0.9' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
};