# 🚀 Quick Start Guide

Get up and running with Progress Dashboard in just 5 minutes!

## 📋 Prerequisites

Before you begin, make sure you have:
- **Node.js** 16 or higher ([Download here](https://nodejs.org/))
- **Python** 3.8 or higher ([Download here](https://python.org/))
- **Git** ([Download here](https://git-scm.com/))

## ⚡ Installation (3 Steps)

### Step 1: Clone the Repository
```bash
git clone https://github.com/hellozei/progress-dashboard.git
cd progress-dashboard
```

### Step 2: Install Dependencies
```bash
# Install frontend dependencies
npm install

# Install backend dependencies
cd backend
pip install -r requirements.txt
cd ..
```

### Step 3: Start the Application
```bash
# Terminal 1 - Start Backend
cd backend
python3 app.py

# Terminal 2 - Start Frontend (in a new terminal)
npm run dev
```

## 🌐 Access Your Dashboard

Once both servers are running:
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5001

You should see the Progress Dashboard interface with sample data!

## 🎯 First Steps

### 1. Explore the Dashboard
- Navigate through **Categories** and **Competitors** tabs
- Try the interactive data tables
- Check out the notification system

### 2. Upload Your First File
- Click on **File Management** in the header
- Drag and drop a CSV file or click to browse
- Watch the automatic processing and backup creation

### 3. Analyze Your Data
- Go back to the **Categories** tab
- Select your uploaded file from the dropdown
- Explore the data visualization and statistics

## 🆘 Troubleshooting

### Common Issues

#### Backend won't start
```bash
# Try using python3 instead of python
python3 app.py

# Or check if you're in the right directory
cd backend
ls -la  # Should see app.py
```

#### Frontend shows connection error
- Make sure backend is running on port 5001
- Check if any firewall is blocking the connection
- Verify the API URL in browser: http://localhost:5001/api/health

#### Port already in use
```bash
# Kill process using port 5001 (backend)
lsof -ti:5001 | xargs kill -9

# Kill process using port 5173 (frontend)
lsof -ti:5173 | xargs kill -9
```

## 📚 What's Next?

- **[User Guide](./user-guide.md)** - Complete feature walkthrough *(coming soon)*
- **[API Documentation](../api/README.md)** - Integrate with your systems *(coming soon)*
- **[Development Guide](../../DEVELOPMENT_GUIDE.md)** - Contribute to the project

## 🤝 Need Help?

- **Issues**: [GitHub Issues](https://github.com/hellozei/progress-dashboard/issues)
- **Documentation**: [Full Documentation](../README.md)
- **Community**: [GitHub Discussions](https://github.com/hellozei/progress-dashboard/discussions)

---

**Estimated Time**: 5 minutes  
**Difficulty**: Beginner  
**Last Updated**: 2025-07-19
