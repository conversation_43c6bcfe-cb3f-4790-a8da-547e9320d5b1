# Navigation System Documentation

## Overview

The Progress Dashboard navigation system provides a comprehensive, role-based navigation experience with responsive design and authentication integration.

## Components

### 1. Header Component (`src/components/Header.tsx`)

The main header component that contains the brand logo, navigation menu, and user authentication controls.

**Features:**
- Responsive design with mobile-first approach
- Authentication state integration
- Brand logo with fallback handling
- Integrated navigation and user profile controls

**Usage:**
```tsx
import Header from './components/Header';

// Used automatically in App.tsx for non-backend routes
<Header />
```

### 2. UserProfileDropdown Component (`src/components/navigation/UserProfileDropdown.tsx`)

A comprehensive user profile dropdown that provides access to user information and account actions.

**Features:**
- User avatar with initials
- Role-based badge display
- Quick access to profile settings
- Role-specific navigation items
- Secure logout functionality
- Keyboard navigation support (Escape to close)
- Click-outside-to-close functionality

**Role-Based Menu Items:**
- **All Users:** Profile Settings, Preferences, Activity Dashboard
- **Moderator+:** Admin Panel access
- **Admin Only:** Backend Dashboard access
- **All Users:** Help & Support, Sign Out

**Usage:**
```tsx
import UserProfileDropdown from './navigation/UserProfileDropdown';

<UserProfileDropdown className="custom-class" />
```

### 3. NavigationMenu Component (`src/components/navigation/NavigationMenu.tsx`)

A responsive navigation menu with role-based access control and mobile support.

**Features:**
- Desktop horizontal navigation
- Mobile slide-out menu
- Role-based menu items
- Active route highlighting
- Badge support for special items
- Responsive design with breakpoints

**Navigation Structure:**
- **Public:** Home
- **Authenticated:** Analysis, Files, Settings
- **Moderator+:** Admin Panel
- **Admin Only:** Backend Dashboard

**Usage:**
```tsx
import NavigationMenu from './navigation/NavigationMenu';

<NavigationMenu className="custom-class" />
```

## Role-Based Access Control

### Access Levels

1. **Public Users**
   - Home navigation only
   - Login button in header

2. **Authenticated Users**
   - Analysis Dashboard
   - File Management
   - User Settings
   - Profile dropdown with user info

3. **Moderators**
   - All user features
   - Admin Panel access
   - Enhanced profile dropdown

4. **Administrators**
   - All moderator features
   - Backend Dashboard access
   - Full system access

### Implementation

The navigation system uses the `RoleBasedAccess` components to conditionally render menu items:

```tsx
// Admin-only navigation
<AdminOnly>
  <NavigationItem path="/dashboard" />
</AdminOnly>

// Moderator+ navigation
<ModeratorPlus>
  <NavigationItem path="/admin" />
</ModeratorPlus>

// Authenticated users
<AuthenticatedOnly>
  <NavigationItem path="/analysis" />
</AuthenticatedOnly>
```

## Responsive Design

### Desktop (md+)
- Horizontal navigation menu
- Full user profile dropdown
- All navigation items visible

### Mobile (< md)
- Hamburger menu button
- Slide-out navigation panel
- Categorized menu sections
- Touch-friendly interactions

### Breakpoints
- `md:` 768px and above (desktop)
- Below 768px (mobile)

## Styling and Theming

### Color Scheme
- **Background:** `theme-eerie-black` with backdrop blur
- **Borders:** `gray-500/40` with transparency
- **Text:** White/gray with role-based colors
- **Hover:** Primary color transitions
- **Active:** Primary color highlighting

### Role-Based Colors
- **Admin:** Red accents (`red-300`, `red-900/20`)
- **Moderator:** Blue accents (`blue-300`, `blue-900/20`)
- **User:** Green accents (`green-400`)
- **Primary:** Primary color system

## Accessibility

### Features
- ARIA labels and roles
- Keyboard navigation support
- Focus management
- Screen reader friendly
- High contrast support

### Implementation
```tsx
// ARIA attributes
aria-label="Main navigation"
aria-expanded={isOpen}
aria-haspopup="true"

// Keyboard support
onKeyDown={handleEscape}
tabIndex={0}

// Focus management
focus:outline-none
focus:ring-2
focus:ring-primary-500/50
```

## State Management

### Authentication Integration
- Uses `useAuth` hook for user state
- Reactive to authentication changes
- Automatic menu updates on login/logout

### Navigation State
- Active route detection with `useLocation`
- Mobile menu state management
- Dropdown state management

## Performance Optimizations

### Code Splitting
- Lazy loading for heavy components
- Dynamic imports for role-specific features

### Rendering Optimization
- Conditional rendering based on auth state
- Memoized components where appropriate
- Efficient re-rendering patterns

## Security Considerations

### Route Protection
- Client-side route hiding (UI only)
- Server-side protection still required
- Role-based menu filtering

### User Data
- Secure user information display
- No sensitive data in client state
- Proper logout handling

## Customization

### Adding New Navigation Items

1. **Define the item:**
```tsx
const newNavItem: NavigationItem = {
  id: 'new-feature',
  label: 'New Feature',
  path: '/new-feature',
  icon: NewIcon,
  description: 'Access new feature',
  badge: 'New',
};
```

2. **Add to appropriate access level:**
```tsx
// In NavigationMenu.tsx
const authenticatedNavItems = [
  // ... existing items
  newNavItem,
];
```

3. **Add role-based rendering:**
```tsx
<AuthenticatedOnly>
  {authenticatedNavItems.map(renderNavItem)}
</AuthenticatedOnly>
```

### Styling Customization

The navigation system uses Tailwind CSS classes and can be customized through:

1. **CSS Variables:** Modify theme colors
2. **Tailwind Config:** Extend color palette
3. **Component Props:** Pass custom className
4. **CSS Overrides:** Target specific classes

## Testing

### Manual Testing Checklist
- [ ] Desktop navigation works
- [ ] Mobile menu opens/closes
- [ ] Role-based items show/hide correctly
- [ ] Active route highlighting works
- [ ] User dropdown functions properly
- [ ] Logout functionality works
- [ ] Keyboard navigation works
- [ ] Responsive design works

### Automated Testing
```tsx
// Example test structure
describe('NavigationMenu', () => {
  it('shows role-based navigation items', () => {
    // Test role-based rendering
  });
  
  it('handles mobile menu toggle', () => {
    // Test mobile functionality
  });
  
  it('highlights active routes', () => {
    // Test active state
  });
});
```

## Troubleshooting

### Common Issues

1. **Navigation items not showing**
   - Check user authentication state
   - Verify role-based access components
   - Check route protection configuration

2. **Mobile menu not working**
   - Verify responsive breakpoints
   - Check z-index stacking
   - Test touch interactions

3. **Styling issues**
   - Check Tailwind CSS compilation
   - Verify theme configuration
   - Check CSS specificity

### Debug Tools

```tsx
// Add debug logging
console.log('User role:', profile?.role);
console.log('Current path:', location.pathname);
console.log('Is authenticated:', !!user);
```

## Future Enhancements

### Planned Features
- Breadcrumb navigation
- Search functionality
- Notification center
- Quick actions menu
- Customizable navigation

### Performance Improvements
- Virtual scrolling for large menus
- Intersection observer for active states
- Service worker caching
- Progressive enhancement
