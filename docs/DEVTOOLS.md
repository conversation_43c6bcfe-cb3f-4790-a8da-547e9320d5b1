# React Query DevTools Configuration

This document explains how to control the visibility and usage of React Query DevTools in the Progress Dashboard application.

## Overview

The React Query DevTools have been configured to be **hidden by default** to provide a cleaner UI experience while still maintaining full debugging capabilities when needed during development.

## Key Features

- ✅ **Hidden by default** - No floating DevTools button on startup
- ✅ **Multiple activation methods** - Keyboard shortcuts, console commands, environment variables
- ✅ **Production safety** - Completely disabled in production builds
- ✅ **Persistent state** - Remembers your preference across browser sessions
- ✅ **Visual feedback** - Shows confirmation when toggling DevTools

## Activation Methods

### 1. Keyboard Shortcut (Recommended)
Press **`Ctrl + Shift + Q`** (or **`Cmd + Shift + Q`** on Mac) to toggle DevTools visibility.

### 2. Console Commands
Open browser DevTools console and use:

```javascript
// Toggle DevTools visibility
devtools.toggleReactQuery()

// Show DevTools
devtools.showReactQuery()

// Hide DevTools
devtools.hideReactQuery()

// Check current status
devtools.status()

// Show help
devtools.help()
```

### 3. Environment Variable
Create a `.env.local` file in the project root:

```bash
# Enable DevTools by default
VITE_ENABLE_REACT_QUERY_DEVTOOLS=true
```

### 4. Global Function
The toggle function is also available globally:

```javascript
// Available on window object
window.toggleReactQueryDevtools()
```

## Usage Examples

### Quick Toggle During Development
1. Open the application
2. Press `Ctrl + Shift + Q` to show DevTools
3. Debug your queries
4. Press `Ctrl + Shift + Q` again to hide DevTools

### Persistent DevTools Session
1. Open browser console
2. Run `devtools.showReactQuery()`
3. DevTools will remain visible across page refreshes
4. Run `devtools.hideReactQuery()` when done

### Team Development
Add to your `.env.local` file:
```bash
VITE_ENABLE_REACT_QUERY_DEVTOOLS=true
```
This will enable DevTools by default for your local development environment.

## DevTools Features

When enabled, the React Query DevTools provide:

- **Query Inspector** - View all active queries and their states
- **Cache Explorer** - Inspect cached data and cache keys
- **Network Activity** - Monitor query execution and timing
- **Mutation Tracking** - Debug mutations and their effects
- **Performance Metrics** - Analyze query performance

## Configuration Options

The DevTools wrapper accepts several configuration options:

```typescript
<ReactQueryDevtoolsWrapper
  forceShow={false}                    // Force show regardless of other settings
  keyboardShortcut="ctrl+shift+q"      // Custom keyboard shortcut
  envVariable="VITE_ENABLE_REACT_QUERY_DEVTOOLS"  // Custom env variable
  persistState={true}                  // Remember state across sessions
/>
```

## Production Behavior

In production builds:
- DevTools are **completely disabled** and never rendered
- All activation methods are no-ops
- Console commands show warning messages
- Zero performance impact

## Troubleshooting

### DevTools Not Appearing
1. Ensure you're in development mode (`npm run dev`)
2. Check browser console for error messages
3. Try refreshing the page and using keyboard shortcut again
4. Verify the application has fully loaded

### Keyboard Shortcut Not Working
1. Make sure the application window has focus
2. Try using console command: `devtools.toggleReactQuery()`
3. Check if other browser extensions are intercepting the shortcut

### DevTools Showing in Production
This should never happen. If it does:
1. Verify `NODE_ENV=production` in your build environment
2. Check that you're using the production build (`npm run build`)
3. Clear browser cache and reload

## Development Tips

### Quick Status Check
```javascript
// Check current DevTools state
devtools.status()
// Returns: { environment: 'development', reactQuery: 'hidden', shortcuts: {...} }
```

### Debugging Queries
1. Enable DevTools: `Ctrl + Shift + Q`
2. Navigate to the query you want to debug
3. Use the DevTools panel to inspect:
   - Query state (loading, success, error)
   - Cached data
   - Query key
   - Fetch status
   - Last updated time

### Performance Analysis
1. Enable DevTools
2. Perform actions that trigger queries
3. Use the DevTools to analyze:
   - Query execution time
   - Cache hit/miss ratio
   - Background refetch behavior
   - Stale data handling

## Integration with Other Tools

### Browser DevTools
The React Query DevTools work alongside browser DevTools:
- Use browser Network tab for HTTP request details
- Use React DevTools for component state
- Use React Query DevTools for cache and query state

### VS Code Extensions
Recommended extensions for better development experience:
- React Query DevTools (if available)
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer

## Best Practices

1. **Keep DevTools hidden by default** - Cleaner UI for development
2. **Use keyboard shortcuts** - Fastest way to toggle during debugging
3. **Enable for specific debugging sessions** - Only when needed
4. **Document team preferences** - Share environment variable settings
5. **Never commit `.env.local`** - Keep personal preferences local

## Support

If you encounter issues with the DevTools configuration:

1. Check the browser console for error messages
2. Verify your environment setup
3. Try the different activation methods
4. Refer to the [React Query DevTools documentation](https://tanstack.com/query/latest/docs/react/devtools)

## File Structure

```
src/
├── components/
│   └── ReactQueryDevtools.tsx    # DevTools wrapper component
├── utils/
│   └── devtools.ts               # Development utilities
└── main.tsx                      # DevTools integration
```

## Environment Variables

```bash
# .env.example
VITE_ENABLE_REACT_QUERY_DEVTOOLS=false

# .env.local (not committed)
VITE_ENABLE_REACT_QUERY_DEVTOOLS=true
```
