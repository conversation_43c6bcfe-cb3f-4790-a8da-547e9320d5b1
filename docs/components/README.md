# 🎨 Component Library

Welcome to the Progress Dashboard Component Library. This documentation covers all reusable UI components, their props, usage patterns, and design guidelines.

## 📋 Component Overview

### Core Components
- **[Header](#header)** - Main navigation and user controls
- **[Categories](#categories)** - Category analysis dashboard
- **[Competitors](#competitors)** - Competitor tracking interface
- **[FileManagement](#filemanagement)** - File operations interface
- **[SystemSettings](#systemsettings)** - Application settings panel

### UI Components
- **[Modal](#modal)** - Reusable modal dialogs
- **[Toast](#toast)** - Notification toasts
- **[DataTable](#datatable)** - Interactive data tables
- **[PaginationControls](#paginationcontrols)** - Table pagination
- **[DeleteModal](#deletemodal)** - Confirmation dialogs

### Form Components
- **[UploadModal](#uploadmodal)** - File upload interface
- **[RenameModal](#renamemodal)** - File rename dialog

## 🏗️ Architecture

### Component Structure
```
src/components/
├── ui/                     # Base UI components
│   ├── Modal.tsx          # Reusable modal
│   ├── Toast.tsx          # Notification toast
│   └── Button.tsx         # Button variants
├── layout/                # Layout components
│   ├── Header.tsx         # Main navigation
│   └── AnalysisLayout.tsx # Analysis page layout
├── features/              # Feature-specific components
│   ├── Categories.tsx     # Category dashboard
│   ├── Competitors.tsx    # Competitor tracking
│   ├── FileManagement.tsx # File operations
│   └── SystemSettings.tsx # Settings panel
└── shared/                # Shared components
    ├── DataTable.tsx      # Data table
    ├── PaginationControls.tsx
    └── NotificationDropdown.tsx
```

### Design System

#### Color Palette
```typescript
const colors = {
  primary: '#95E565',      // Light green
  secondary: '#608F44',    // Asparagus
  dark: '#1A1919',         // Eerie black
  background: '#FEF5ED',   // Seashell
  white: '#FFFFFF',
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    // ... more gray shades
  }
};
```

#### Typography
```typescript
const typography = {
  fontFamily: 'Inter, system-ui, sans-serif',
  fontSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
  }
};
```

#### Spacing
```typescript
const spacing = {
  1: '0.25rem',
  2: '0.5rem',
  3: '0.75rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  8: '2rem',
  10: '2.5rem',
  12: '3rem',
};
```

## 🧩 Component Documentation

### Header

Main navigation component with burger menu, notifications, and user controls.

**Props:**
```typescript
interface HeaderProps {
  activeAnalysisTab: 'categories' | 'competitors';
  onAnalysisTabChange: (tab: 'categories' | 'competitors') => void;
  showSystemSettings: boolean;
  onSystemSettingsClick: () => void;
  showTools: boolean;
  onToolsClick: () => void;
  onFileManagementClick: () => void;
}
```

**Usage:**
```tsx
<Header
  activeAnalysisTab="categories"
  onAnalysisTabChange={setActiveTab}
  showSystemSettings={false}
  onSystemSettingsClick={() => setShowSettings(true)}
  showTools={false}
  onToolsClick={() => setShowTools(true)}
  onFileManagementClick={() => setShowFiles(true)}
/>
```

### Categories

Category analysis dashboard with data tables and statistics.

**Features:**
- Interactive data tables with sorting and filtering
- Real-time statistics and insights
- Export functionality
- Performance monitoring

**Props:**
```typescript
interface CategoriesProps {
  // No props - uses internal state and API calls
}
```

**Usage:**
```tsx
<Categories />
```

### Modal

Reusable modal component with backdrop and close functionality.

**Props:**
```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showCloseButton?: boolean;
}
```

**Usage:**
```tsx
<Modal
  isOpen={isModalOpen}
  onClose={() => setIsModalOpen(false)}
  title="Modal Title"
  size="md"
>
  <p>Modal content goes here</p>
</Modal>
```

### DataTable

Interactive data table with sorting, filtering, and pagination.

**Props:**
```typescript
interface DataTableProps {
  data: any[];
  columns: Column[];
  loading?: boolean;
  error?: string;
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  onFilter?: (filters: Record<string, any>) => void;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    onPageChange: (page: number) => void;
  };
}
```

**Usage:**
```tsx
<DataTable
  data={tableData}
  columns={[
    { key: 'title', label: 'Title', sortable: true },
    { key: 'author', label: 'Author', sortable: true },
    { key: 'sales', label: 'Sales', sortable: true, type: 'number' }
  ]}
  loading={isLoading}
  onSort={handleSort}
  pagination={{
    page: currentPage,
    limit: 20,
    total: totalItems,
    onPageChange: setCurrentPage
  }}
/>
```

### Toast

Notification toast component for user feedback.

**Props:**
```typescript
interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  onClose: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}
```

**Usage:**
```tsx
<Toast
  message="File uploaded successfully!"
  type="success"
  duration={3000}
  onClose={() => setShowToast(false)}
  position="top-right"
/>
```

## 🎨 Styling Guidelines

### CSS Classes
Use Tailwind CSS utility classes with consistent patterns:

```tsx
// Good: Consistent spacing and colors
<div className="p-4 bg-white rounded-lg shadow-md">
  <h2 className="text-xl font-semibold text-gray-900 mb-2">Title</h2>
  <p className="text-gray-600">Description</p>
</div>

// Avoid: Inconsistent or arbitrary values
<div className="p-3 bg-gray-50 rounded-md shadow-sm">
  <h2 className="text-lg font-medium text-black mb-1">Title</h2>
</div>
```

### Component Variants
Use `class-variance-authority` for component variants:

```typescript
import { cva } from 'class-variance-authority';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md font-medium transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-white hover:bg-primary/90',
        secondary: 'bg-secondary text-white hover:bg-secondary/90',
        outline: 'border border-gray-300 bg-white hover:bg-gray-50',
      },
      size: {
        sm: 'h-8 px-3 text-sm',
        md: 'h-10 px-4',
        lg: 'h-12 px-6 text-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);
```

### Responsive Design
Follow mobile-first approach:

```tsx
<div className="
  grid grid-cols-1 gap-4
  md:grid-cols-2 md:gap-6
  lg:grid-cols-3 lg:gap-8
">
  {/* Content */}
</div>
```

## 🧪 Testing Components

### Unit Testing
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { Modal } from './Modal';

describe('Modal', () => {
  it('renders when open', () => {
    render(
      <Modal isOpen={true} onClose={jest.fn()} title="Test Modal">
        <p>Modal content</p>
      </Modal>
    );
    
    expect(screen.getByText('Test Modal')).toBeInTheDocument();
    expect(screen.getByText('Modal content')).toBeInTheDocument();
  });

  it('calls onClose when close button clicked', () => {
    const onClose = jest.fn();
    render(
      <Modal isOpen={true} onClose={onClose} title="Test Modal">
        <p>Content</p>
      </Modal>
    );
    
    fireEvent.click(screen.getByRole('button', { name: /close/i }));
    expect(onClose).toHaveBeenCalled();
  });
});
```

### Visual Testing
Use Storybook for component development and testing:

```typescript
// Modal.stories.tsx
export default {
  title: 'Components/Modal',
  component: Modal,
};

export const Default = {
  args: {
    isOpen: true,
    title: 'Example Modal',
    children: <p>This is modal content</p>,
  },
};

export const Large = {
  args: {
    ...Default.args,
    size: 'lg',
  },
};
```

## 📚 Best Practices

### Component Design
1. **Single Responsibility**: Each component should have one clear purpose
2. **Composition over Inheritance**: Use composition patterns
3. **Props Interface**: Always define TypeScript interfaces for props
4. **Default Props**: Provide sensible defaults
5. **Error Boundaries**: Handle errors gracefully

### Performance
1. **Lazy Loading**: Use React.lazy for large components
2. **Memoization**: Use React.memo for expensive renders
3. **Callback Optimization**: Use useCallback for event handlers
4. **Bundle Size**: Keep components lightweight

### Accessibility
1. **Semantic HTML**: Use proper HTML elements
2. **ARIA Labels**: Add appropriate ARIA attributes
3. **Keyboard Navigation**: Support keyboard interactions
4. **Screen Readers**: Test with screen reader software

## 📝 Contributing

### Adding New Components
1. Create component in appropriate folder
2. Add TypeScript interfaces
3. Write unit tests
4. Add Storybook stories
5. Update this documentation

### Component Checklist
- [ ] TypeScript interfaces defined
- [ ] Props documented
- [ ] Unit tests written
- [ ] Accessibility tested
- [ ] Responsive design verified
- [ ] Storybook story created
- [ ] Documentation updated

---

**Last Updated**: 2025-07-19  
**Maintainer**: Frontend Team  
**Status**: Active Development
