# 🔌 API Documentation

Welcome to the Progress Dashboard API documentation. This API provides comprehensive access to CSV data processing, file management, and analytics capabilities.

## 🌐 Base URL

```
http://localhost:5001/api
```

## 🔐 Authentication

Currently, the API does not require authentication for development. In production, implement proper authentication mechanisms.

## 📊 API Overview

### Core Endpoints

#### Health Check
- **GET** `/health` - System health status

#### Data Management
- **GET** `/categories` - List all available categories
- **GET** `/categories/{name}/data` - Get category data with analysis
- **GET** `/competitors` - List all competitors
- **GET** `/competitors/{name}/data` - Get competitor data

#### File Operations
- **GET** `/files` - List all uploaded files
- **POST** `/upload` - Upload new CSV file
- **PUT** `/files/{id}/rename` - Rename existing file
- **DELETE** `/files/{id}` - Delete file (with backup)

#### Analytics
- **GET** `/authors/top` - Get top authors by frequency or points
- **GET** `/authors/detailed` - Get detailed author analysis

#### Notifications
- **GET** `/notifications` - Get notification list
- **POST** `/notifications` - Create new notification
- **PUT** `/notifications/{id}` - Update notification
- **DELETE** `/notifications/{id}` - Delete notification
- **GET** `/notifications/settings` - Get notification settings
- **PUT** `/notifications/settings` - Update notification settings

## 🚀 Quick Start

### Basic API Call
```javascript
// Check API health
fetch('http://localhost:5001/api/health')
  .then(response => response.json())
  .then(data => console.log(data));
```

### Get Categories
```javascript
// Fetch all categories
fetch('http://localhost:5001/api/categories')
  .then(response => response.json())
  .then(data => {
    console.log('Available categories:', data.categories);
  });
```

### Upload File
```javascript
// Upload CSV file
const formData = new FormData();
formData.append('file', fileInput.files[0]);

fetch('http://localhost:5001/api/upload', {
  method: 'POST',
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('File uploaded:', data.filename);
  }
});
```

## 📋 Response Format

All API responses follow this standard format:

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data here
  },
  "meta": {
    "timestamp": "2025-07-19T10:30:00Z",
    "version": "1.0.0"
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    // Additional error details
  }
}
```

## 📚 Detailed Endpoints

### Data Endpoints

#### GET /categories
List all available data categories.

**Response:**
```json
{
  "success": true,
  "categories": [
    {
      "name": "Graphics Design",
      "count": 1250,
      "files": ["file1.csv", "file2.csv"]
    }
  ]
}
```

#### GET /categories/{name}/data
Get detailed data for a specific category.

**Parameters:**
- `name` (path): Category name
- `limit` (query): Number of items (default: 50)
- `offset` (query): Pagination offset (default: 0)

**Response:**
```json
{
  "success": true,
  "category": "Graphics Design",
  "data": [
    {
      "Title": "Modern Logo Design",
      "Author": "DesignStudio",
      "Sales": 150,
      "Rating": 4.8
    }
  ],
  "total": 1250,
  "stats": {
    "averageRating": 4.5,
    "totalSales": 15000
  }
}
```

### File Management Endpoints

#### POST /upload
Upload a new CSV file for processing.

**Request:**
- Content-Type: `multipart/form-data`
- Body: File data

**Response:**
```json
{
  "success": true,
  "filename": "uploaded_file.csv",
  "size": 1024000,
  "rows": 500,
  "backup_created": true
}
```

#### DELETE /files/{id}
Delete a file with automatic backup.

**Response:**
```json
{
  "success": true,
  "message": "File deleted successfully",
  "backup_location": "/backups/file_20250719_103000.csv"
}
```

## 🔍 Error Codes

| Code | Description |
|------|-------------|
| `INVALID_REQUEST` | Malformed request |
| `FILE_NOT_FOUND` | Requested file doesn't exist |
| `UPLOAD_FAILED` | File upload failed |
| `PROCESSING_ERROR` | Data processing error |
| `INVALID_FORMAT` | Invalid file format |
| `SIZE_LIMIT_EXCEEDED` | File too large |

## 📊 Rate Limiting

- **Development**: No rate limiting
- **Production**: 100 requests per minute per IP

Rate limit headers:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## 🧪 Testing

### Using curl
```bash
# Health check
curl http://localhost:5001/api/health

# Get categories
curl http://localhost:5001/api/categories

# Upload file
curl -X POST \
  -F "file=@data.csv" \
  http://localhost:5001/api/upload
```

### Using Postman
Import the Postman collection: *(coming soon)*

## 📈 Performance

### Response Times
- **Health check**: < 50ms
- **Data queries**: < 500ms
- **File upload**: Depends on file size
- **File processing**: 1-5 seconds for typical CSV files

### Optimization
- Enable Redis caching for better performance
- Use pagination for large datasets
- Implement request compression

## 🔧 Configuration

### Environment Variables
```bash
FLASK_ENV=development
API_PORT=5001
UPLOAD_MAX_SIZE=16777216  # 16MB
REDIS_URL=redis://localhost:6379  # Optional
```

## 📚 SDKs and Libraries

### JavaScript/TypeScript
```typescript
// API client example
class ProgressDashboardAPI {
  private baseURL = 'http://localhost:5001/api';

  async getCategories() {
    const response = await fetch(`${this.baseURL}/categories`);
    return response.json();
  }

  async uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch(`${this.baseURL}/upload`, {
      method: 'POST',
      body: formData
    });
    
    return response.json();
  }
}
```

### Python
```python
import requests

class ProgressDashboardAPI:
    def __init__(self, base_url='http://localhost:5001/api'):
        self.base_url = base_url
    
    def get_categories(self):
        response = requests.get(f'{self.base_url}/categories')
        return response.json()
    
    def upload_file(self, file_path):
        with open(file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f'{self.base_url}/upload', files=files)
        return response.json()
```

## 📝 Changelog

### Version 1.0.0 (2025-07-19)
- Initial API implementation
- Core CRUD operations
- File upload and management
- Notification system
- Analytics endpoints

---

**API Version**: 1.0.0  
**Last Updated**: 2025-07-19  
**Status**: Active Development
