# Solusi untuk Memperbaiki Komunikasi dari Chrome Extension ke Frontend

Sistem login OTP Anda menghadapi masalah komunikasi dari Chrome Extension ke Frontend, ditandai dengan pesan "Extension bridge not available" dan kegagalan deteksi ekstensi berdasarkan uji yang dilakukan. Be<PERSON><PERSON> adalah analisis dan solusi terperinci untuk memastikan komunikasi berfungsi dengan benar antara Chrome Extension dan aplikasi web (React, TypeScript, Tailwind CSS, Vite), dengan asumsi Anda hanya menggunakan `http://localhost:5173` sebagai domain pengembangan.

## Analisis Masalah
Berdasarkan hasil uji:
- **Extension Bridge**: ❌ Fail (Jembatan komunikasi gagal).
- **Content Script Marker**: ❌ Fail (Skrip konten tidak terdeteksi).
- **Extension Ready Flag**: ❌ Fail (Ekstensi tidak siap).
- **Background Communication**: ❌ Fail (Error dalam `chrome.runtime.sendMessage` karena ID ekstensi tidak disediakan).
- **Penyebab Utama**:
  - Sk<PERSON> konten (`content.js`) tidak diinjeksikan ke halaman web.
  - Panggilan `chrome.runtime.sendMessage` dari halaman web salah digunakan tanpa ID ekstensi.
  - Konfigurasi `manifest.json` mungkin tidak mencakup domain aplikasi web (`http://localhost:5173`) dengan benar.

## Solusi Terperinci

### 1. Perbaiki Konfigurasi `manifest.json`
Pastikan izin dan `content_scripts` mencakup domain aplikasi web `http://localhost:5173` untuk memungkinkan injeksi skrip konten.
- **Perubahan**:
  - Tambahkan izin `"scripting"`, `"tabs"`, dan `"host_permissions"`.
  - Konfigurasi `content_scripts` hanya untuk `http://localhost:5173` karena Anda hanya menggunakan domain ini.
  - Tambahkan service worker untuk komunikasi latar belakang.
- **Kode**:
  ```json
  {
    "manifest_version": 3,
    "name": "OTP Login Extension",
    "version": "1.0",
    "permissions": ["activeTab", "tabs", "scripting"],
    "host_permissions": ["http://localhost:5173/*"],
    "content_scripts": [
      {
        "matches": ["http://localhost:5173/*"],
        "js": ["content.js"],
        "run_at": "document_idle"
      }
    ],
    "background": {
      "service_worker": "background.js"
    },
    "action": {
      "default_popup": "popup.html"
    }
  }
  ```

### 2. Perbaiki `content.js` untuk Komunikasi yang Benar
Pastikan `content.js` menerima pesan dari halaman web dan popup, lalu meneruskannya ke halaman web menggunakan `window.postMessage`.
- **Perubahan**:
  - Gunakan `chrome.runtime.onMessage` untuk menerima pesan dari popup atau background.
  - Gunakan `window.postMessage` untuk mengirim pesan ke halaman web.
  - Tambahkan respons untuk deteksi ekstensi (`EXTENSION_CHECK`).
- **Kode**:
  ```javascript
  // Menerima pesan dari popup atau background
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Content script received:', message);
    if (message.type === 'OTP_RESPONSE') {
      window.postMessage(message, 'http://localhost:5173'); // Kirim ke halaman web
      sendResponse({ status: 'Message forwarded to frontend' });
    }
  });

  // Menerima pesan dari halaman web
  window.addEventListener('message', (event) => {
    console.log('Content script received from frontend:', event);
    if (event.data.type === 'OTP_REQUEST' && event.origin === 'http://localhost:5173') {
      chrome.runtime.sendMessage({
        type: 'SHOW_OTP_POPUP',
        email: event.data.email,
        otp: event.data.otp,
        secret: event.data.secret,
      }, (response) => {
        console.log('Runtime response:', response);
      });
    } else if (event.data.type === 'EXTENSION_CHECK') {
      window.postMessage({ type: 'EXTENSION_CHECK_RESPONSE' }, 'http://localhost:5173');
    }
  });
  ```

### 3. Tambahkan Service Worker (`background.js`)
Gunakan service worker untuk menangani komunikasi latar belakang dan memicu popup.
- **Perubahan**:
  - Konfigurasi `chrome.runtime.onMessage` untuk menerima pesan dan mengatur popup.
- **Kode**:
  ```javascript
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Background received:', message);
    if (message.type === 'SHOW_OTP_POPUP') {
      chrome.action.setPopup({ popup: 'popup.html' });
      sendResponse({ status: 'Popup triggered' });
    }
  });
  ```

### 4. Perbaiki `popup.js`
Pastikan popup mengirim pesan melalui runtime ke content script.
- **Perubahan**:
  - Gunakan `chrome.runtime.sendMessage` untuk mengirim pesan ke background atau content script.
  - Tambahkan logging untuk debugging.
- **Kode**:
  ```javascript
  document.addEventListener('DOMContentLoaded', () => {
    const loginButton = document.getElementById('login');
    const rejectButton = document.getElementById('reject');
    const emailElement = document.getElementById('email');

    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log('Popup received:', message);
      if (message.type === 'SHOW_OTP_POPUP') {
        emailElement.textContent = `Login request for: ${message.email}`;
        loginButton.onclick = () => {
          chrome.runtime.sendMessage({
            type: 'OTP_RESPONSE',
            action: 'LOGIN',
            otp: message.otp,
          }, (response) => {
            console.log('Response from background:', response);
          });
        };
        rejectButton.onclick = () => {
          chrome.runtime.sendMessage({
            type: 'OTP_RESPONSE',
            action: 'REJECT',
          }, (response) => {
            console.log('Response from background:', response);
          });
        };
        sendResponse({ status: 'Popup displayed' });
      }
    });
  });
  ```

### 5. Perbarui Frontend (`index.tsx`)
Tambahkan deteksi ekstensi dan tangani pesan dari Chrome Extension dengan benar.
- **Perubahan**:
  - Tambahkan logika deteksi ekstensi dengan `EXTENSION_CHECK`.
  - Perbarui event listener untuk menangani `OTP_RESPONSE` tanpa validasi `event.origin` yang ketat, dengan fokus pada `http://localhost:5173`.
- **Kode**:
  ```typescript
  import React, { useState, FormEvent, useEffect } from 'react';
  import { createRoot } from 'react-dom/client';
  import DOMPurify from 'dompurify';

  interface LoginFormState {
    email: string;
    otp: string;
    error: string;
    isLoading: boolean;
    extensionAvailable: boolean;
  }

  const LoginForm: React.FC = () => {
    const [formState, setFormState] = useState<LoginFormState>({
      email: '',
      otp: '',
      error: '',
      isLoading: false,
      extensionAvailable: false,
    });

    useEffect(() => {
      const checkExtension = () => {
        const listener = (event: MessageEvent) => {
          if (event.data.type === 'EXTENSION_CHECK_RESPONSE' && event.origin === 'http://localhost:5173') {
            setFormState((prev) => ({ ...prev, extensionAvailable: true }));
            window.removeEventListener('message', listener);
          }
        };
        window.addEventListener('message', listener);
        window.postMessage({ type: 'EXTENSION_CHECK' }, 'http://localhost:5173');
        setTimeout(() => {
          if (!formState.extensionAvailable) {
            setFormState((prev) => ({ ...prev, error: 'Extension bridge not available' }));
          }
          window.removeEventListener('message', listener);
        }, 2000);
      };
      checkExtension();
    }, []);

    const sendOtpRequest = async (email: string) => {
      if (!formState.extensionAvailable) {
        setFormState((prev) => ({ ...prev, error: 'Extension not available', isLoading: false }));
        return;
      }
      try {
        setFormState((prev) => ({ ...prev, isLoading: true }));
        const sanitizedEmail = DOMPurify.sanitize(email);
        const response = await fetch('/api/generate-otp', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: sanitizedEmail }),
        });
        if (!response.ok) throw new Error('Failed to fetch OTP');
        const { secret } = await response.json();
        console.log('Sending OTP request to extension:', { email: sanitizedEmail, secret });
        window.postMessage(
          { type: 'OTP_REQUEST', email: sanitizedEmail, secret },
          'http://localhost:5173'
        );
      } catch (error) {
        console.error('OTP request error:', error);
        setFormState((prev) => ({ ...prev, error: 'Failed to request OTP', isLoading: false }));
      }
    };

    React.useEffect(() => {
      const handleMessage = (event: MessageEvent) => {
        console.log('Frontend received message:', event);
        if (event.data.type === 'OTP_RESPONSE' && event.origin === 'http://localhost:5173') {
          if (event.data.action === 'LOGIN') {
            setFormState((prev) => ({ ...prev, otp: event.data.otp }));
            validateOtp(event.data.otp, formState.email);
          } else if (event.data.action === 'REJECT') {
            setFormState((prev) => ({
              ...prev,
              error: 'Login request rejected',
              isLoading: false,
            }));
          }
        }
      };

      window.addEventListener('message', handleMessage);
      return () => window.removeEventListener('message', handleMessage);
    }, [formState.email]);

    const validateOtp = async (otp: string, email: string) => {
      try {
        const response = await fetch('/api/validate-otp', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: DOMPurify.sanitize(email), otp }),
        });
        const data = await response.json();
        if (data.success) {
          setFormState((prev) => ({ ...prev, isLoading: false, error: '' }));
          localStorage.setItem('token', data.token);
          alert('Login successful!');
        } else {
          setFormState((prev) => ({
            ...prev,
            isLoading: false,
            error: 'Invalid OTP',
          }));
        }
      } catch (error) {
        console.error('OTP validation error:', error);
        setFormState((prev) => ({
          ...prev,
          isLoading: false,
          error: 'Failed to validate OTP',
        }));
      }
    };

    const handleSubmit = (e: FormEvent) => {
      e.preventDefault();
      setFormState((prev) => ({ ...prev, isLoading: true }));
      sendOtpRequest(formState.email);
    };

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
          <h2 className="text-2xl font-bold mb-6 text-center">OTP Flow Test</h2>
          {formState.error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" role="alert">
              <strong className="font-bold">×</strong>
              <span className="block sm:inline"> {formState.error}</span>
            </div>
          )}
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email/Username
              </label>
              <input
                type="text"
                id="email"
                value={formState.email}
                onChange={(e) =>
                  setFormState((prev) => ({ ...prev, email: e.target.value }))
                }
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                required
              />
            </div>
            {formState.otp && (
              <div className="mb-4">
                <label htmlFor="otp" className="block text-sm font-medium text-gray-700">
                  OTP
                </label>
                <input
                  type="text"
                  id="otp"
                  value={formState.otp}
                  readOnly
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 sm:text-sm"
                />
              </div>
            )}
            <button
              type="submit"
              disabled={formState.isLoading}
              className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-300"
            >
              {formState.isLoading ? 'Sending OTP...' : 'Test Complete OTP Flow'}
            </button>
          </form>
        </div>
      </div>
    );
  };

  const root = createRoot(document.getElementById('root')!);
  root.render(<LoginForm />);
  ```

### Langkah Verifikasi
1. **Muat Ulang Ekstensi**:
   - Buka `chrome://extensions/`, klik "Reload" pada ekstensi.
2. **Jalankan Aplikasi**:
   - Pastikan backend (`node server.js`) dan aplikasi web (`npm run dev`) berjalan di `http://localhost:5173`.
3. **Uji Deteksi Ekstensi**:
   - Buka aplikasi web di `http://localhost:5173` dan periksa apakah pesan error "Extension bridge not available" hilang.
4. **Uji Alur OTP**:
   - Masukkan email dan klik "Test Complete OTP Flow".
   - Periksa konsol untuk log dari `content.js`, `popup.js`, dan `index.tsx`.
5. **Debugging**:
   - Jika error tetap muncul, periksa konsol untuk pesan spesifik dan laporkan ke AI Agent.

### Catatan untuk AI Agent
- **Fokus Utama**: Pastikan `content_scripts` di `manifest.json` hanya mencakup `http://localhost:5173/*` karena Anda hanya menggunakan domain ini.
- **Kesalahan Umum**: Hindari penggunaan `chrome.runtime.sendMessage` dari halaman web tanpa ID ekstensi; gunakan `window.postMessage` sebagai gantinya.
- **Lingkungan Pengembangan**: Uji hanya dengan `http://localhost:5173` karena ini adalah domain yang digunakan.
- **Logging**: Gunakan log konsol untuk mendiagnosis kegagalan lebih lanjut.

Dengan solusi ini, komunikasi dari Chrome Extension ke Frontend seharusnya berfungsi, dan "Extension bridge" akan terdeteksi dengan benar pada lingkungan `http://localhost:5173`.