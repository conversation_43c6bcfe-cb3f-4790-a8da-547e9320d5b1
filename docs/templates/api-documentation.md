# [API Endpoint Name]

> **Template for documenting API endpoints in Progress Dashboard**
> 
> Replace placeholders with actual content and remove this note when using.

## 📋 Overview

Brief description of what this API endpoint does and its purpose in the system.

## 🔗 Endpoint Details

### HTTP Method and URL
```
[METHOD] /api/[endpoint-path]
```

### Authentication
- **Required**: Yes/No
- **Type**: Bearer Token / API Key / None
- **Scope**: Required permissions

## 📥 Request

### Headers
```http
Content-Type: application/json
Authorization: Bearer [token]
X-Custom-Header: [value]
```

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | `string` | ✅ | Unique identifier |
| `type` | `string` | ❌ | Resource type |

### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `limit` | `number` | `20` | Number of items to return |
| `offset` | `number` | `0` | Number of items to skip |
| `filter` | `string` | `null` | Filter criteria |
| `sort` | `string` | `'created_at'` | Sort field |
| `order` | `string` | `'desc'` | Sort order (asc/desc) |

### Request Body
```typescript
interface RequestBody {
  field1: string;           // Required field description
  field2?: number;          // Optional field description
  field3: {                 // Nested object
    subField1: string;
    subField2: boolean;
  };
  field4: string[];         // Array of strings
}
```

### Example Request
```bash
curl -X POST \
  'http://localhost:5001/api/endpoint?limit=10&sort=name' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your-token' \
  -d '{
    "field1": "example value",
    "field2": 42,
    "field3": {
      "subField1": "nested value",
      "subField2": true
    },
    "field4": ["item1", "item2"]
  }'
```

## 📤 Response

### Success Response (200 OK)
```typescript
interface SuccessResponse {
  success: true;
  data: {
    id: string;
    field1: string;
    field2: number;
    createdAt: string;      // ISO 8601 timestamp
    updatedAt: string;      // ISO 8601 timestamp
  };
  meta?: {
    total: number;          // Total items available
    page: number;           // Current page
    limit: number;          // Items per page
  };
}
```

### Example Success Response
```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "field1": "example value",
    "field2": 42,
    "createdAt": "2025-07-19T10:30:00Z",
    "updatedAt": "2025-07-19T10:30:00Z"
  },
  "meta": {
    "total": 150,
    "page": 1,
    "limit": 20
  }
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "success": false,
  "error": "Invalid request parameters",
  "details": {
    "field1": ["This field is required"],
    "field2": ["Must be a positive number"]
  }
}
```

#### 401 Unauthorized
```json
{
  "success": false,
  "error": "Authentication required",
  "code": "UNAUTHORIZED"
}
```

#### 403 Forbidden
```json
{
  "success": false,
  "error": "Insufficient permissions",
  "code": "FORBIDDEN"
}
```

#### 404 Not Found
```json
{
  "success": false,
  "error": "Resource not found",
  "code": "NOT_FOUND"
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Internal server error",
  "code": "INTERNAL_ERROR"
}
```

## 💻 Code Examples

### JavaScript/TypeScript
```typescript
// Using fetch API
async function callEndpoint(data: RequestBody): Promise<SuccessResponse> {
  const response = await fetch('/api/endpoint', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

// Usage
try {
  const result = await callEndpoint({
    field1: "example",
    field2: 42,
    field3: { subField1: "test", subField2: true },
    field4: ["item1", "item2"]
  });
  console.log('Success:', result.data);
} catch (error) {
  console.error('Error:', error);
}
```

### Python
```python
import requests
import json

def call_endpoint(data, token):
    url = 'http://localhost:5001/api/endpoint'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()

# Usage
try:
    result = call_endpoint({
        'field1': 'example',
        'field2': 42,
        'field3': {
            'subField1': 'test',
            'subField2': True
        },
        'field4': ['item1', 'item2']
    }, 'your-token')
    print('Success:', result['data'])
except requests.exceptions.RequestException as e:
    print('Error:', e)
```

## 🧪 Testing

### Unit Test Example
```typescript
describe('API Endpoint', () => {
  it('should return success response', async () => {
    const mockData = {
      field1: 'test',
      field2: 42,
      field3: { subField1: 'test', subField2: true },
      field4: ['item1']
    };

    const response = await request(app)
      .post('/api/endpoint')
      .send(mockData)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data).toBeDefined();
  });

  it('should handle validation errors', async () => {
    const invalidData = { field2: -1 }; // Missing required field1

    const response = await request(app)
      .post('/api/endpoint')
      .send(invalidData)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.details.field1).toBeDefined();
  });
});
```

## 📊 Performance

### Expected Performance
- **Response Time**: < 200ms (95th percentile)
- **Throughput**: 1000 requests/second
- **Error Rate**: < 0.1%

### Rate Limiting
- **Limit**: 100 requests per minute per IP
- **Headers**: 
  - `X-RateLimit-Limit`: Maximum requests
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset timestamp

## 🔍 Monitoring

### Metrics to Track
- Response time distribution
- Error rate by status code
- Request volume over time
- Authentication failures

### Logging
```json
{
  "timestamp": "2025-07-19T10:30:00Z",
  "method": "POST",
  "path": "/api/endpoint",
  "status": 200,
  "duration": 150,
  "user_id": "user123",
  "request_id": "req456"
}
```

## 📚 Related Documentation

- **[API Overview](../api/README.md)** - Complete API documentation
- **[Authentication](../api/authentication.md)** - Authentication guide
- **[Error Handling](../api/errors.md)** - Error codes and handling
- **[Rate Limiting](../api/rate-limiting.md)** - Rate limiting details

## 📝 Changelog

### Version 1.1.0 (2025-07-19)
- Added new field4 parameter
- Improved error messages
- Enhanced performance

### Version 1.0.0 (2025-07-01)
- Initial API endpoint implementation
- Basic CRUD operations
- Authentication support

---

**Last Updated**: 2025-07-19  
**Maintainer**: Backend Team  
**Status**: Stable
