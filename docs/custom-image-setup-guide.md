# Custom Image Setup Guide - Login Page

## Overview

Panduan lengkap untuk mengganti gambar custom di halaman login dengan layout 30/70 (30% form, 70% image area).

## Current Implementation

### **Layout Structure (30/70)**
```
Desktop (≥ 1024px):
┌─────────┬─────────────────────────────────────────────┐
│         │                                             │
│ Login   │           Custom Image Area                 │
│ Form    │              (70% width)                    │
│ (30%)   │                                             │
│         │                                             │
└─────────┴─────────────────────────────────────────────┘

Mobile (< 1024px):
┌─────────────────────────────────────┐
│                                     │
│            Login Form               │
│           (Full Width)              │
│                                     │
└─────────────────────────────────────┘
```

### **Current Features**
- ✅ **30/70 Layout Split** - Optimal space for image showcase
- ✅ **Google Login Only** - Simplified social authentication
- ✅ **Custom Image Support** - Easy image replacement
- ✅ **Responsive Design** - Mobile-optimized layout
- ✅ **Fallback System** - Graceful degradation if image fails

## How to Add Your Custom Image

### **Method 1: Replace with Local Image**

#### **Step 1: Add Image to Project**
```bash
# Create images directory if it doesn't exist
mkdir -p public/images/auth

# Add your image file
# Recommended formats: JPG, PNG, WebP
# Recommended size: 1920x1080 or higher
cp your-custom-image.jpg public/images/auth/login-hero.jpg
```

#### **Step 2: Update Image Source**
Edit `src/pages/AuthPage.tsx`, find this line:
```jsx
<img 
  src="/api/placeholder/800/600"  // ← Change this line
  alt="Progress Dashboard - Analytics Platform"
  className="w-full h-full object-cover"
  // ... rest of props
/>
```

Replace with:
```jsx
<img 
  src="/images/auth/login-hero.jpg"  // ← Your custom image path
  alt="Progress Dashboard - Analytics Platform"
  className="w-full h-full object-cover"
  // ... rest of props
/>
```

### **Method 2: Use External URL**

```jsx
<img 
  src="https://your-domain.com/path/to/image.jpg"
  alt="Progress Dashboard - Analytics Platform"
  className="w-full h-full object-cover"
  // ... rest of props
/>
```

### **Method 3: Multiple Images (Responsive)**

```jsx
<picture>
  <source 
    media="(min-width: 1280px)" 
    srcSet="/images/auth/login-hero-large.jpg" 
  />
  <source 
    media="(min-width: 1024px)" 
    srcSet="/images/auth/login-hero-medium.jpg" 
  />
  <img 
    src="/images/auth/login-hero-small.jpg"
    alt="Progress Dashboard - Analytics Platform"
    className="w-full h-full object-cover"
  />
</picture>
```

## Image Specifications

### **Recommended Dimensions**
- **Minimum**: 1280x720px (HD)
- **Recommended**: 1920x1080px (Full HD)
- **Optimal**: 2560x1440px (2K) for high-DPI displays

### **Aspect Ratio**
- **Best**: 16:9 (landscape)
- **Acceptable**: 4:3, 3:2
- **Avoid**: Portrait orientations

### **File Formats**
1. **WebP** - Best compression, modern browsers
2. **JPG** - Good compression, universal support
3. **PNG** - Lossless, larger file size
4. **SVG** - Vector graphics, perfect for illustrations

### **File Size Optimization**
- **Target**: < 500KB for fast loading
- **Maximum**: < 1MB
- **Tools**: TinyPNG, ImageOptim, Squoosh

### **Content Guidelines**
- **Subject**: Dashboard, analytics, business, technology
- **Style**: Professional, modern, clean
- **Colors**: Complement brand colors (#95E565, #608F44)
- **Composition**: Leave space for overlay text (bottom-left)

## Customization Options

### **Option 1: Pure Image (No Overlay)**

Remove the content overlay for pure image display:

```jsx
{/* Remove this entire section for pure image */}
<div className="relative z-10 flex items-end justify-start w-full h-full p-12">
  <div className="text-white max-w-md">
    {/* Overlay content */}
  </div>
</div>
```

### **Option 2: Custom Overlay Content**

Modify the overlay text and positioning:

```jsx
<div className="relative z-10 flex items-center justify-center w-full h-full p-12">
  <div className="text-center text-white">
    <h1 className="text-4xl font-bold mb-6">Your Custom Title</h1>
    <p className="text-xl text-white/90">Your custom description</p>
  </div>
</div>
```

### **Option 3: Different Overlay Positions**

```jsx
{/* Top-left */}
<div className="relative z-10 flex items-start justify-start w-full h-full p-12">

{/* Top-right */}
<div className="relative z-10 flex items-start justify-end w-full h-full p-12">

{/* Bottom-right */}
<div className="relative z-10 flex items-end justify-end w-full h-full p-12">

{/* Center */}
<div className="relative z-10 flex items-center justify-center w-full h-full p-12">
```

### **Option 4: Adjust Overlay Opacity**

```jsx
{/* Light overlay */}
<div className="absolute inset-0 bg-black/10"></div>

{/* Medium overlay */}
<div className="absolute inset-0 bg-black/30"></div>

{/* Dark overlay */}
<div className="absolute inset-0 bg-black/50"></div>

{/* No overlay */}
{/* Remove the overlay div completely */}
```

## Advanced Customizations

### **Parallax Effect**

```jsx
<div 
  className="absolute inset-0 bg-cover bg-center bg-fixed"
  style={{
    backgroundImage: 'url(/images/auth/login-hero.jpg)',
    backgroundAttachment: 'fixed'
  }}
></div>
```

### **Gradient Overlay**

```jsx
<div className="absolute inset-0 bg-gradient-to-r from-black/50 via-transparent to-primary-500/30"></div>
```

### **Multiple Background Images**

```jsx
<div 
  className="absolute inset-0"
  style={{
    background: `
      linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)),
      url(/images/auth/pattern.png),
      url(/images/auth/main-image.jpg)
    `,
    backgroundSize: 'cover, 100px 100px, cover',
    backgroundPosition: 'center, top left, center'
  }}
></div>
```

### **CSS Filters**

```jsx
<img 
  src="/images/auth/login-hero.jpg"
  alt="Progress Dashboard"
  className="w-full h-full object-cover filter brightness-90 contrast-110 saturate-110"
/>
```

## Fallback System

The implementation includes automatic fallback:

```jsx
onError={(e) => {
  // Hide failed image
  e.currentTarget.style.display = 'none';
  
  // Show gradient background
  const parent = e.currentTarget.parentElement;
  if (parent) {
    parent.style.background = 'linear-gradient(135deg, #95E565 0%, #608F44 100%)';
  }
}}
```

### **Custom Fallback**

```jsx
onError={(e) => {
  // Replace with different image
  e.currentTarget.src = '/images/auth/fallback-image.jpg';
  
  // Or show custom content
  document.getElementById('fallback-content').style.display = 'flex';
}}
```

## Testing Your Image

### **Checklist**
- [ ] Image loads correctly on desktop
- [ ] Image scales properly on different screen sizes
- [ ] File size is optimized (< 500KB)
- [ ] Alt text is descriptive
- [ ] Overlay text is readable
- [ ] Fallback works if image fails
- [ ] Colors complement the brand theme

### **Browser Testing**
- [ ] Chrome/Edge (WebP support)
- [ ] Firefox (image formats)
- [ ] Safari (image rendering)
- [ ] Mobile browsers (performance)

### **Performance Testing**
```bash
# Check image size
ls -lh public/images/auth/

# Test loading speed
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:5173/images/auth/login-hero.jpg"
```

## Examples

### **Business Dashboard Theme**
```jsx
<img 
  src="/images/auth/business-dashboard.jpg"
  alt="Modern Business Dashboard"
  className="w-full h-full object-cover"
/>
```

### **Analytics Visualization**
```jsx
<img 
  src="/images/auth/analytics-charts.jpg"
  alt="Data Analytics Visualization"
  className="w-full h-full object-cover"
/>
```

### **Team Collaboration**
```jsx
<img 
  src="/images/auth/team-working.jpg"
  alt="Team Collaboration"
  className="w-full h-full object-cover"
/>
```

## Troubleshooting

### **Image Not Loading**
1. Check file path is correct
2. Verify file exists in public directory
3. Check file permissions
4. Test image URL directly in browser

### **Image Quality Issues**
1. Use higher resolution source image
2. Optimize compression settings
3. Try different file format
4. Check CSS object-fit property

### **Performance Issues**
1. Compress image file size
2. Use WebP format
3. Implement lazy loading
4. Consider using CDN

### **Responsive Issues**
1. Test on different screen sizes
2. Adjust object-position property
3. Use responsive images with srcSet
4. Check mobile layout

## Quick Start

1. **Add your image**: `public/images/auth/your-image.jpg`
2. **Update source**: Change `src="/api/placeholder/800/600"` to `src="/images/auth/your-image.jpg"`
3. **Test**: Refresh browser and verify image loads
4. **Optimize**: Compress image if needed

Your custom image is now ready! 🎉
