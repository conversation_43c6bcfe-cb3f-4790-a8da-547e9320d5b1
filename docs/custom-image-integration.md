# Custom Image Integration Guide

## Overview

Guide untuk mengintegrasikan desain gambar custom Anda ke dalam area kanan (Image Area) dari layout split-screen authentication.

## Current Placeholder

Saat ini, area kanan menggunakan placeholder dengan:
- Logo "PD" besar di tengah
- Background gradient hijau
- Elemen dekoratif geometris
- Teks deskriptif platform

## Integration Options

### **Option 1: Replace Placeholder Container**

Ganti container placeholder dengan gambar custom Anda:

```jsx
{/* Replace this placeholder */}
<div className="w-64 h-64 mx-auto mb-8 bg-white/10 backdrop-blur-sm rounded-3xl border border-white/20 flex items-center justify-center">
  <div className="text-6xl font-bold opacity-50">PD</div>
</div>

{/* With your custom image */}
<div className="w-64 h-64 mx-auto mb-8">
  <img 
    src="/path/to/your/image.svg" 
    alt="Progress Dashboard Analytics"
    className="w-full h-full object-contain"
  />
</div>
```

### **Option 2: Background Image**

Gunakan gambar sebagai background dengan overlay:

```jsx
{/* Background Image Approach */}
<div className="absolute inset-0">
  <img 
    src="/path/to/your/background.jpg"
    alt="Analytics Background"
    className="w-full h-full object-cover opacity-30"
  />
  <div className="absolute inset-0 bg-gradient-to-br from-primary-500/80 via-primary-600/80 to-primary-700/80"></div>
</div>
```

### **Option 3: SVG Illustration**

Untuk ilustrasi custom yang scalable:

```jsx
{/* SVG Illustration */}
<div className="w-80 h-80 mx-auto mb-8">
  <svg viewBox="0 0 400 400" className="w-full h-full">
    {/* Your custom SVG content */}
    <defs>
      <linearGradient id="customGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#95E565" />
        <stop offset="100%" stopColor="#608F44" />
      </linearGradient>
    </defs>
    
    {/* Example: Dashboard illustration */}
    <rect x="50" y="50" width="300" height="200" rx="20" fill="url(#customGradient)" opacity="0.2"/>
    <rect x="70" y="70" width="80" height="60" rx="8" fill="white" opacity="0.9"/>
    <rect x="170" y="70" width="80" height="60" rx="8" fill="white" opacity="0.9"/>
    <rect x="270" y="70" width="60" height="60" rx="8" fill="white" opacity="0.9"/>
    
    {/* Add your custom elements here */}
  </svg>
</div>
```

## Implementation Steps

### **Step 1: Prepare Your Image**

1. **Format Recommendations:**
   - **SVG**: Best for illustrations, logos, icons
   - **PNG**: Good for complex graphics with transparency
   - **WebP**: Modern format for better compression
   - **JPG**: For photographic content

2. **Size Optimization:**
   - **Recommended size**: 400x400px to 800x800px
   - **File size**: Keep under 500KB for fast loading
   - **Responsive**: Consider different sizes for mobile

3. **Color Harmony:**
   - Use colors that complement the green theme
   - Consider white/light elements for contrast
   - Test with the gradient background

### **Step 2: Add Image to Project**

```bash
# Add image to public folder
public/
  images/
    auth/
      dashboard-illustration.svg
      analytics-hero.png
      custom-design.webp
```

### **Step 3: Update AuthPage Component**

Replace the placeholder section in `src/pages/AuthPage.tsx`:

```jsx
{/* Content Area for Custom Image/Design */}
<div className="relative z-10 flex items-center justify-center w-full h-full p-12">
  <div className="text-center text-white">
    {/* Your Custom Image */}
    <div className="w-80 h-80 mx-auto mb-8">
      <img 
        src="/images/auth/your-custom-image.svg"
        alt="Progress Dashboard - Transform Your Data"
        className="w-full h-full object-contain drop-shadow-2xl"
      />
    </div>
    
    {/* Keep existing text content or customize */}
    <h2 className="text-3xl font-bold mb-4">
      Your Custom Headline
    </h2>
    <p className="text-lg text-white/80 max-w-md mx-auto leading-relaxed">
      Your custom description text here.
    </p>
    
    {/* Feature points remain the same or customize */}
  </div>
</div>
```

## Design Recommendations

### **Visual Style**
1. **Consistency**: Match the green color palette
2. **Contrast**: Ensure readability against gradient background
3. **Modern**: Use clean, minimalist design
4. **Professional**: Maintain business/analytics theme

### **Content Ideas**
1. **Dashboard Mockup**: Show sample analytics interface
2. **Data Visualization**: Charts, graphs, metrics
3. **Device Mockups**: Desktop/mobile dashboard views
4. **Abstract Analytics**: Geometric data representations
5. **Team Collaboration**: People working with data

### **Animation Options**
Add subtle animations to enhance the experience:

```jsx
{/* Animated entrance */}
<div className="w-80 h-80 mx-auto mb-8 animate-fade-in">
  <img 
    src="/images/auth/animated-dashboard.svg"
    alt="Analytics Dashboard"
    className="w-full h-full object-contain transform hover:scale-105 transition-transform duration-300"
  />
</div>
```

## Advanced Customization

### **Interactive Elements**

```jsx
{/* Hover effects on image */}
<div className="group w-80 h-80 mx-auto mb-8 cursor-pointer">
  <div className="relative overflow-hidden rounded-2xl">
    <img 
      src="/images/auth/interactive-dashboard.svg"
      alt="Interactive Dashboard"
      className="w-full h-full object-contain transform group-hover:scale-110 transition-transform duration-500"
    />
    <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  </div>
</div>
```

### **Multiple Images Carousel**

```jsx
{/* Image carousel */}
const [currentImage, setCurrentImage] = useState(0);
const images = [
  '/images/auth/dashboard-1.svg',
  '/images/auth/analytics-2.svg',
  '/images/auth/reports-3.svg'
];

// In component:
<div className="w-80 h-80 mx-auto mb-8">
  <img 
    src={images[currentImage]}
    alt={`Dashboard View ${currentImage + 1}`}
    className="w-full h-full object-contain transition-opacity duration-500"
  />
</div>
```

### **Responsive Images**

```jsx
{/* Responsive image sizing */}
<div className="w-64 h-64 lg:w-80 lg:h-80 xl:w-96 xl:h-96 mx-auto mb-8">
  <picture>
    <source media="(min-width: 1280px)" srcSet="/images/auth/large-dashboard.webp" />
    <source media="(min-width: 1024px)" srcSet="/images/auth/medium-dashboard.webp" />
    <img 
      src="/images/auth/small-dashboard.webp"
      alt="Progress Dashboard Analytics"
      className="w-full h-full object-contain"
      loading="lazy"
    />
  </picture>
</div>
```

## Performance Optimization

### **Image Loading**
1. **Lazy Loading**: Use `loading="lazy"` attribute
2. **Preload**: Consider preloading critical images
3. **WebP Format**: Use modern formats with fallbacks
4. **Compression**: Optimize file sizes

### **Code Splitting**
```jsx
// Lazy load heavy images
const HeroImage = React.lazy(() => import('../components/HeroImage'));

// In component:
<Suspense fallback={<div className="w-80 h-80 bg-white/10 animate-pulse rounded-2xl"></div>}>
  <HeroImage />
</Suspense>
```

## Testing Your Implementation

### **Checklist**
- [ ] Image loads correctly on desktop
- [ ] Responsive behavior on mobile (hidden)
- [ ] File size optimized (< 500KB)
- [ ] Alt text provided for accessibility
- [ ] Colors harmonize with theme
- [ ] No layout shift during loading
- [ ] Fallback for failed image loads

### **Browser Testing**
- Chrome/Edge (WebP support)
- Firefox (SVG animations)
- Safari (Image formats)
- Mobile browsers (performance)

## Example Implementation

Here's a complete example replacing the placeholder:

```jsx
{/* Custom Dashboard Illustration */}
<div className="relative z-10 flex items-center justify-center w-full h-full p-12">
  <div className="text-center text-white">
    {/* Custom SVG Dashboard */}
    <div className="w-80 h-80 mx-auto mb-8">
      <svg viewBox="0 0 320 320" className="w-full h-full drop-shadow-2xl">
        {/* Dashboard Frame */}
        <rect x="20" y="20" width="280" height="200" rx="16" fill="white" fillOpacity="0.95"/>
        
        {/* Header Bar */}
        <rect x="20" y="20" width="280" height="40" rx="16" fill="#95E565"/>
        <circle cx="45" cy="40" r="6" fill="white"/>
        <circle cx="65" cy="40" r="6" fill="white"/>
        <circle cx="85" cy="40" r="6" fill="white"/>
        
        {/* Charts */}
        <rect x="40" y="80" width="80" height="60" rx="8" fill="#f0fdf4"/>
        <rect x="140" y="80" width="80" height="60" rx="8" fill="#f0fdf4"/>
        <rect x="240" y="80" width="40" height="60" rx="8" fill="#f0fdf4"/>
        
        {/* Data Bars */}
        <rect x="50" y="120" width="60" height="8" rx="4" fill="#95E565"/>
        <rect x="150" y="110" width="40" height="8" rx="4" fill="#608F44"/>
        <rect x="150" y="125" width="50" height="8" rx="4" fill="#95E565"/>
        
        {/* Bottom Section */}
        <rect x="40" y="160" width="240" height="40" rx="8" fill="#f0fdf4"/>
        <rect x="50" y="170" width="220" height="4" rx="2" fill="#95E565"/>
        <rect x="50" y="180" width="180" height="4" rx="2" fill="#608F44"/>
      </svg>
    </div>
    
    <h2 className="text-3xl font-bold mb-4">
      Transform Your Data Into Insights
    </h2>
    <p className="text-lg text-white/80 max-w-md mx-auto leading-relaxed">
      Powerful analytics dashboard that turns complex data into actionable business intelligence.
    </p>
    
    {/* Feature Points */}
    <div className="mt-8 space-y-3">
      <div className="flex items-center justify-center space-x-3 text-white/90">
        <div className="w-2 h-2 bg-white rounded-full"></div>
        <span>Real-time Data Processing</span>
      </div>
      <div className="flex items-center justify-center space-x-3 text-white/90">
        <div className="w-2 h-2 bg-white rounded-full"></div>
        <span>Interactive Visualizations</span>
      </div>
      <div className="flex items-center justify-center space-x-3 text-white/90">
        <div className="w-2 h-2 bg-white rounded-full"></div>
        <span>Secure Cloud Platform</span>
      </div>
    </div>
  </div>
</div>
```

This creates a custom dashboard illustration that perfectly matches your application's theme and purpose!
