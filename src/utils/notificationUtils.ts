/**
 * Notification Utilities
 * Helper functions for notification management and formatting
 */

import {
  Notification as AppNotification,
  NotificationType,
  NotificationPriority,
  CreateNotificationRequest,
  NotificationSettings
} from '../types/notification';

/**
 * Format relative time for notification timestamps
 */
export const formatRelativeTime = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} min${diffInMinutes > 1 ? 's' : ''} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
  }

  // For older notifications, show the actual date
  return date.toLocaleDateString();
};

/**
 * Get notification type icon and color
 */
export const getNotificationTypeConfig = (type: NotificationType) => {
  const configs = {
    success: {
      icon: 'CheckCircle',
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    error: {
      icon: 'XCircle',
      color: 'text-red-500',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    warning: {
      icon: 'AlertTriangle',
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200'
    },
    info: {
      icon: 'Info',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    data_update: {
      icon: 'Database',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    system: {
      icon: 'Settings',
      color: 'text-gray-500',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200'
    },
    file_upload: {
      icon: 'Upload',
      color: 'text-indigo-500',
      bgColor: 'bg-indigo-50',
      borderColor: 'border-indigo-200'
    },
    analysis_complete: {
      icon: 'BarChart3',
      color: 'text-orange-jasper',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    }
  };

  return configs[type] || configs.info;
};

/**
 * Get priority configuration
 */
export const getPriorityConfig = (priority: NotificationPriority) => {
  const configs = {
    low: {
      label: 'Low',
      color: 'text-gray-500',
      bgColor: 'bg-gray-100',
      dotColor: 'bg-gray-400'
    },
    medium: {
      label: 'Medium',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      dotColor: 'bg-blue-500'
    },
    high: {
      label: 'High',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      dotColor: 'bg-orange-500'
    },
    urgent: {
      label: 'Urgent',
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      dotColor: 'bg-red-500'
    }
  };

  return configs[priority] || configs.medium;
};

/**
 * Check if notification should be shown based on settings
 */
export const shouldShowNotification = (
  notification: AppNotification,
  settings: NotificationSettings
): boolean => {
  if (!settings.enabled) return false;

  // Check priority settings
  if (!settings.priorities[notification.priority]) return false;

  // Check type-specific settings
  switch (notification.type) {
    case 'data_update':
      return settings.dataUpdates;
    case 'system':
      return settings.systemAlerts;
    case 'analysis_complete':
      return settings.analysisComplete;
    case 'file_upload':
      return settings.fileOperations;
    default:
      return true;
  }
};

/**
 * Check if it's quiet hours
 */
export const isQuietHours = (settings: NotificationSettings): boolean => {
  if (!settings.quietHours.enabled) return false;

  const now = new Date();
  const currentTime = now.getHours() * 60 + now.getMinutes();
  
  const [startHour, startMin] = settings.quietHours.start.split(':').map(Number);
  const [endHour, endMin] = settings.quietHours.end.split(':').map(Number);
  
  const startTime = startHour * 60 + startMin;
  const endTime = endHour * 60 + endMin;

  if (startTime <= endTime) {
    // Same day range (e.g., 09:00 to 17:00)
    return currentTime >= startTime && currentTime <= endTime;
  } else {
    // Overnight range (e.g., 22:00 to 08:00)
    return currentTime >= startTime || currentTime <= endTime;
  }
};

/**
 * Create system notification
 */
export const createSystemNotification = (
  message: string,
  category: string = 'system',
  priority: NotificationPriority = 'medium'
): CreateNotificationRequest => ({
  type: 'system',
  priority,
  title: 'System Notification',
  message,
  category,
  source: 'system',
  dismissible: true
});

/**
 * Create data update notification
 */
export const createDataUpdateNotification = (
  competitor: string,
  recordCount: number,
  processingTime: number
): CreateNotificationRequest => ({
  type: 'data_update',
  priority: 'medium',
  title: 'Data Update Complete',
  message: `${competitor} data has been updated with ${recordCount} records`,
  category: 'processing',
  source: 'data_processor',
  metadata: {
    competitor,
    recordCount,
    processingTime
  },
  dismissible: true
});

/**
 * Create file upload notification
 */
export const createFileUploadNotification = (
  filename: string,
  success: boolean,
  error?: string
): CreateNotificationRequest => ({
  type: success ? 'success' : 'error',
  priority: success ? 'low' : 'high',
  title: success ? 'File Upload Successful' : 'File Upload Failed',
  message: success 
    ? `${filename} has been uploaded successfully`
    : `Failed to upload ${filename}: ${error}`,
  category: 'upload',
  source: 'file_manager',
  metadata: {
    filename,
    success,
    error
  },
  dismissible: true
});

/**
 * Create analysis complete notification
 */
export const createAnalysisCompleteNotification = (
  analysisType: string,
  competitor: string,
  duration: number,
  resultCount: number
): CreateNotificationRequest => ({
  type: 'analysis_complete',
  priority: 'medium',
  title: 'Analysis Complete',
  message: `${analysisType} analysis for ${competitor} completed with ${resultCount} results`,
  category: 'analysis',
  source: 'analysis_engine',
  metadata: {
    analysisType,
    competitor,
    duration,
    resultCount
  },
  dismissible: true
});

/**
 * Filter notifications based on criteria
 */
export const filterNotifications = (
  notifications: AppNotification[],
  searchTerm: string = '',
  types: NotificationType[] = [],
  priorities: NotificationPriority[] = [],
  statuses: string[] = []
): AppNotification[] => {
  return notifications.filter(notification => {
    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = 
        notification.title.toLowerCase().includes(searchLower) ||
        notification.message.toLowerCase().includes(searchLower) ||
        (notification.category && notification.category.toLowerCase().includes(searchLower));
      
      if (!matchesSearch) return false;
    }

    // Type filter
    if (types.length > 0 && !types.includes(notification.type)) {
      return false;
    }

    // Priority filter
    if (priorities.length > 0 && !priorities.includes(notification.priority)) {
      return false;
    }

    // Status filter
    if (statuses.length > 0 && !statuses.includes(notification.status)) {
      return false;
    }

    return true;
  });
};

/**
 * Sort notifications
 */
export const sortNotifications = (
  notifications: AppNotification[],
  sortBy: 'date' | 'priority' | 'type' = 'date',
  sortOrder: 'asc' | 'desc' = 'desc'
): AppNotification[] => {
  return [...notifications].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'date':
        comparison = a.createdAt.getTime() - b.createdAt.getTime();
        break;
      case 'priority':
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
    }

    return sortOrder === 'desc' ? -comparison : comparison;
  });
};

/**
 * Group notifications by date
 */
export const groupNotificationsByDate = (notifications: AppNotification[]) => {
  const groups: { [key: string]: AppNotification[] } = {};
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  notifications.forEach(notification => {
    const notificationDate = new Date(notification.createdAt);
    let groupKey: string;

    if (notificationDate.toDateString() === today.toDateString()) {
      groupKey = 'Today';
    } else if (notificationDate.toDateString() === yesterday.toDateString()) {
      groupKey = 'Yesterday';
    } else {
      groupKey = notificationDate.toLocaleDateString();
    }

    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(notification);
  });

  return groups;
};

/**
 * Request browser notification permission
 */
export const requestNotificationPermission = async (): Promise<boolean> => {
  if (!('Notification' in window)) {
    console.warn('This browser does not support notifications');
    return false;
  }

  if (Notification.permission === 'granted') {
    return true;
  }

  if (Notification.permission === 'denied') {
    return false;
  }

  const permission = await Notification.requestPermission();
  return permission === 'granted';
};

/**
 * Show browser notification
 */
export const showBrowserNotification = (
  notification: AppNotification,
  settings: NotificationSettings
): void => {
  if (!settings.browser || !shouldShowNotification(notification, settings)) {
    return;
  }

  if (isQuietHours(settings)) {
    return;
  }

  if (Notification.permission === 'granted') {
    const browserNotification = new Notification(notification.title, {
      body: notification.message,
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: notification.id,
      requireInteraction: notification.priority === 'urgent'
    });

    browserNotification.onclick = () => {
      window.focus();
      browserNotification.close();
    };

    // Auto close after 5 seconds for non-urgent notifications
    if (notification.priority !== 'urgent') {
      setTimeout(() => {
        browserNotification.close();
      }, 5000);
    }
  }
};

// ===== NEW UTILITIES FOR API ERROR HANDLING =====

/**
 * Enhanced notification message for API errors
 */
export interface ApiNotificationMessage {
  type: 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  duration?: number;
}

/**
 * Create notification message for API errors
 */
export const createApiErrorNotification = (error: string): ApiNotificationMessage => {
  if (error.includes('fetch') || error.includes('Failed to fetch') || error.includes('CONNECTION_REFUSED')) {
    return {
      type: 'error',
      title: 'Koneksi Backend Gagal',
      message: 'Backend server tidak dapat diakses. Silakan coba lagi nanti.',
      duration: 5000
    };
  }
  if (error.includes('404')) {
    return {
      type: 'warning',
      title: 'Data Tidak Ditemukan',
      message: 'Data tidak ditemukan untuk permintaan ini.',
      duration: 4000
    };
  }
  if (error.includes('500')) {
    return {
      type: 'error',
      title: 'Kesalahan Server',
      message: 'Terjadi kesalahan server saat memproses data.',
      duration: 5000
    };
  }
  if (error.includes('timeout')) {
    return {
      type: 'warning',
      title: 'Koneksi Timeout',
      message: 'Koneksi timeout. Periksa koneksi backend.',
      duration: 4000
    };
  }
  return {
    type: 'error',
    title: 'Kesalahan Sistem',
    message: 'Terjadi kesalahan yang tidak diketahui.',
    duration: 5000
  };
};

/**
 * Create notification for empty data scenarios
 */
export const createEmptyDataNotification = (dataType: string): ApiNotificationMessage => {
  const messages = {
    categories: 'Tidak ada kategori yang ditemukan.',
    competitors: 'Tidak ada data kompetitor yang tersedia.',
    files: 'Tidak ada file yang ditemukan.',
    authors: 'Tidak ada data author yang tersedia.',
    system: 'Data sistem tidak tersedia.'
  };

  return {
    type: 'info',
    title: 'Data Kosong',
    message: messages[dataType as keyof typeof messages] || `Tidak ada data ${dataType} yang ditemukan.`,
    duration: 3000
  };
};

/**
 * Create notification for backend unavailable scenarios
 */
export const createBackendUnavailableNotification = (): ApiNotificationMessage => {
  return {
    type: 'error',
    title: 'Backend Tidak Tersedia',
    message: 'Backend server tidak dapat diakses. Silakan coba lagi nanti.',
    duration: 5000
  };
};

/**
 * Create comprehensive error state for components
 */
export interface ErrorState {
  hasError: boolean;
  errorType: 'connection' | 'empty' | 'server' | 'unknown';
  title: string;
  message: string;
  suggestion?: string;
}

export const createErrorState = (error: string | null, dataType: string): ErrorState => {
  if (!error) {
    return {
      hasError: false,
      errorType: 'unknown',
      title: '',
      message: ''
    };
  }

  if (error.includes('fetch') || error.includes('Failed to fetch') || error.includes('CONNECTION_REFUSED')) {
    return {
      hasError: true,
      errorType: 'connection',
      title: 'Backend Tidak Tersedia',
      message: 'Backend server tidak dapat diakses. Silakan coba lagi nanti.',
      suggestion: 'Pastikan backend server berjalan di http://localhost:5001'
    };
  }

  if (error.includes('404')) {
    return {
      hasError: true,
      errorType: 'server',
      title: 'Data Tidak Ditemukan',
      message: 'Data tidak ditemukan untuk permintaan ini.',
      suggestion: `Periksa apakah data ${dataType} tersedia di backend`
    };
  }

  return {
    hasError: true,
    errorType: 'unknown',
    title: 'Kesalahan Memuat Data',
    message: error,
    suggestion: 'Refresh halaman atau coba lagi nanti'
  };
};
