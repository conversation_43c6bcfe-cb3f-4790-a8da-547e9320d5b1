/**
 * Notification Helper Functions
 * Utility functions for creating and managing notifications
 */

import { CreateNotificationRequest, NotificationType, NotificationPriority } from '../types/notification';
import notificationService from '../services/notificationService';

/**
 * Create a data processing notification
 */
export const createDataProcessingNotification = async (
  competitor: string,
  recordCount: number,
  processingTime: number,
  success: boolean = true
): Promise<void> => {
  const notification: CreateNotificationRequest = {
    type: success ? 'data_update' : 'error',
    priority: success ? 'medium' : 'high',
    title: success ? 'Data Processing Complete' : 'Data Processing Failed',
    message: success 
      ? `${competitor} data has been processed with ${recordCount} records in ${processingTime}s`
      : `Failed to process ${competitor} data after ${processingTime}s`,
    category: 'processing',
    source: 'data_processor',
    metadata: {
      competitor,
      recordCount,
      processingTime,
      success
    },
    dismissible: true
  };

  await notificationService.createNotification(notification);
};

/**
 * Create a file upload notification
 */
export const createFileUploadNotification = async (
  filename: string,
  success: boolean,
  fileSize?: number,
  error?: string
): Promise<void> => {
  const notification: CreateNotificationRequest = {
    type: success ? 'success' : 'error',
    priority: success ? 'low' : 'high',
    title: success ? 'File Upload Successful' : 'File Upload Failed',
    message: success 
      ? `${filename} has been uploaded successfully${fileSize ? ` (${(fileSize / 1024 / 1024).toFixed(1)} MB)` : ''}`
      : `Failed to upload ${filename}${error ? `: ${error}` : ''}`,
    category: 'upload',
    source: 'file_manager',
    metadata: {
      filename,
      success,
      fileSize,
      error
    },
    dismissible: true
  };

  await notificationService.createNotification(notification);
};

/**
 * Create an analysis completion notification
 */
export const createAnalysisNotification = async (
  analysisType: string,
  competitor: string,
  duration: number,
  resultCount: number,
  success: boolean = true
): Promise<void> => {
  const notification: CreateNotificationRequest = {
    type: success ? 'analysis_complete' : 'error',
    priority: success ? 'medium' : 'high',
    title: success ? 'Analysis Complete' : 'Analysis Failed',
    message: success 
      ? `${analysisType} analysis for ${competitor} completed with ${resultCount} results in ${duration}s`
      : `${analysisType} analysis for ${competitor} failed after ${duration}s`,
    category: 'analysis',
    source: 'analysis_engine',
    metadata: {
      analysisType,
      competitor,
      duration,
      resultCount,
      success
    },
    dismissible: true
  };

  await notificationService.createNotification(notification);
};

/**
 * Create a system notification
 */
export const createSystemNotification = async (
  message: string,
  priority: NotificationPriority = 'medium',
  category: string = 'system',
  metadata?: Record<string, any>
): Promise<void> => {
  const notification: CreateNotificationRequest = {
    type: 'system',
    priority,
    title: 'System Notification',
    message,
    category,
    source: 'system',
    metadata,
    dismissible: true
  };

  await notificationService.createNotification(notification);
};

/**
 * Create a warning notification
 */
export const createWarningNotification = async (
  title: string,
  message: string,
  category: string = 'warning',
  metadata?: Record<string, any>
): Promise<void> => {
  const notification: CreateNotificationRequest = {
    type: 'warning',
    priority: 'high',
    title,
    message,
    category,
    source: 'system',
    metadata,
    dismissible: true
  };

  await notificationService.createNotification(notification);
};

/**
 * Create an error notification
 */
export const createErrorNotification = async (
  title: string,
  message: string,
  category: string = 'error',
  metadata?: Record<string, any>
): Promise<void> => {
  const notification: CreateNotificationRequest = {
    type: 'error',
    priority: 'urgent',
    title,
    message,
    category,
    source: 'system',
    metadata,
    dismissible: true,
    persistent: true
  };

  await notificationService.createNotification(notification);
};

/**
 * Create sample notifications for testing
 */
export const createSampleNotifications = async (): Promise<void> => {
  const sampleNotifications: CreateNotificationRequest[] = [
    {
      type: 'data_update',
      priority: 'medium',
      title: 'Data Processing Complete',
      message: 'GraphicRiver data has been processed with 1,247 new records',
      category: 'processing',
      source: 'data_processor',
      metadata: { competitor: 'GraphicRiver', recordCount: 1247, processingTime: 45 }
    },
    {
      type: 'analysis_complete',
      priority: 'medium',
      title: 'Analysis Complete',
      message: 'Competitor analysis for ThemeForest completed with 892 results',
      category: 'analysis',
      source: 'analysis_engine',
      metadata: { analysisType: 'competitor', competitor: 'ThemeForest', resultCount: 892 }
    },
    {
      type: 'success',
      priority: 'low',
      title: 'File Upload Successful',
      message: 'EE_GraphicRiver_2025-01-16.csv has been uploaded successfully',
      category: 'upload',
      source: 'file_manager',
      metadata: { filename: 'EE_GraphicRiver_2025-01-16.csv', fileSize: 2048576 }
    },
    {
      type: 'warning',
      priority: 'high',
      title: 'Data Quality Warning',
      message: 'Some records in CodeCanyon data may have incomplete information',
      category: 'validation',
      source: 'data_validator',
      metadata: { competitor: 'CodeCanyon', issueCount: 23 }
    },
    {
      type: 'system',
      priority: 'medium',
      title: 'System Maintenance',
      message: 'Scheduled maintenance will begin at 2:00 AM UTC',
      category: 'maintenance',
      source: 'system',
      metadata: { scheduledTime: '2025-01-17T02:00:00Z' }
    },
    {
      type: 'error',
      priority: 'urgent',
      title: 'Processing Error',
      message: 'Failed to process AudioJungle data due to format issues',
      category: 'error',
      source: 'data_processor',
      metadata: { competitor: 'AudioJungle', error: 'Invalid CSV format' }
    },
    {
      type: 'info',
      priority: 'low',
      title: 'New Feature Available',
      message: 'Advanced filtering options are now available in the analysis dashboard',
      category: 'feature',
      source: 'system',
      metadata: { feature: 'advanced_filtering', version: '1.2.0' }
    },
    {
      type: 'data_update',
      priority: 'medium',
      title: 'Bulk Data Import',
      message: 'Successfully imported 5,432 records from multiple competitors',
      category: 'import',
      source: 'bulk_importer',
      metadata: { totalRecords: 5432, competitors: ['ThemeForest', 'CodeCanyon', 'GraphicRiver'] }
    }
  ];

  for (const notification of sampleNotifications) {
    await notificationService.createNotification(notification);
  }
};

/**
 * Monitor system events and create notifications
 */
export class NotificationEventMonitor {
  private static instance: NotificationEventMonitor;
  private eventListeners: Map<string, Function[]> = new Map();

  static getInstance(): NotificationEventMonitor {
    if (!NotificationEventMonitor.instance) {
      NotificationEventMonitor.instance = new NotificationEventMonitor();
    }
    return NotificationEventMonitor.instance;
  }

  /**
   * Register event listener
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * Emit event
   */
  emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  /**
   * Initialize system event monitoring
   */
  initialize(): void {
    // Monitor data processing events
    this.on('data:processing:complete', async (data: any) => {
      await createDataProcessingNotification(
        data.competitor,
        data.recordCount,
        data.processingTime,
        data.success
      );
    });

    // Monitor file upload events
    this.on('file:upload:complete', async (data: any) => {
      await createFileUploadNotification(
        data.filename,
        data.success,
        data.fileSize,
        data.error
      );
    });

    // Monitor analysis events
    this.on('analysis:complete', async (data: any) => {
      await createAnalysisNotification(
        data.analysisType,
        data.competitor,
        data.duration,
        data.resultCount,
        data.success
      );
    });

    // Monitor system events
    this.on('system:maintenance', async (data: any) => {
      await createSystemNotification(
        data.message,
        'medium',
        'maintenance',
        data.metadata
      );
    });

    // Monitor error events
    this.on('system:error', async (data: any) => {
      await createErrorNotification(
        data.title,
        data.message,
        data.category,
        data.metadata
      );
    });

    // Monitor warning events
    this.on('system:warning', async (data: any) => {
      await createWarningNotification(
        data.title,
        data.message,
        data.category,
        data.metadata
      );
    });
  }
}

// Initialize the event monitor
export const eventMonitor = NotificationEventMonitor.getInstance();
eventMonitor.initialize();
