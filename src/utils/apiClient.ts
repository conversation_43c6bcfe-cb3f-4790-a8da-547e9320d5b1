/**
 * API Client
 * 
 * Provides API calls without authentication
 */

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
}

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  /**
   * Make API request
   */
  async request<T = any>(
    endpoint: string, 
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body
    } = config;

    try {
      // Prepare headers
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...headers
      };

      // Prepare request options
      const requestOptions: RequestInit = {
        method,
        headers: requestHeaders
      };

      // Add body for non-GET requests
      if (body && method !== 'GET') {
        requestOptions.body = JSON.stringify(body);
      }

      // Make request
      const url = `${this.baseURL}${endpoint}`;
      const response = await fetch(url, requestOptions);
      
      // Parse response
      const responseData = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: responseData.error || `Request failed: ${response.statusText}`,
          code: responseData.code || 'REQUEST_ERROR'
        };
      }

      return {
        success: true,
        data: responseData
      };

    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * GET request
   */
  async get<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body: data });
  }

  /**
   * PUT request
   */
  async put<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body: data });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PATCH', body: data });
  }

  /**
   * Upload file
   */
  async uploadFile(
    endpoint: string,
    file: File,
    additionalData?: Record<string, string>
  ): Promise<ApiResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      // Add additional data if provided
      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, value);
        });
      }

      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        body: formData
      });

      const responseData = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: responseData.error || `Upload failed: ${response.statusText}`,
          code: responseData.code || 'UPLOAD_ERROR'
        };
      }

      return {
        success: true,
        data: responseData
      };

    } catch (error) {
      console.error('File upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload error',
        code: 'UPLOAD_ERROR'
      };
    }
  }
}

// Create default API client instance
export const apiClient = new ApiClient();

// Export class for custom instances
export { ApiClient };

// Convenience functions for common operations
export const api = {
  // Data operations
  getCategories: () => apiClient.get('/api/categories'),
  getCompetitors: () => apiClient.get('/api/competitors'),
  getFiles: () => apiClient.get('/api/files'),
  uploadFile: (file: File, data?: Record<string, string>) => 
    apiClient.uploadFile('/api/files/upload', file, data),

  // System operations
  getSystemInfo: () => apiClient.get('/api/system/info'),
  clearNotifications: () => apiClient.post('/api/notifications/clear-all'),
  
  // Generic requests
  get: <T = any>(endpoint: string) => apiClient.get<T>(endpoint),
  post: <T = any>(endpoint: string, data?: any) => apiClient.post<T>(endpoint, data),
  put: <T = any>(endpoint: string, data?: any) => apiClient.put<T>(endpoint, data),
  delete: <T = any>(endpoint: string) => apiClient.delete<T>(endpoint),
};

export default apiClient;
