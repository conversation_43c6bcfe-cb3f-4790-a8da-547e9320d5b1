/**
 * Supabase Connection Test Utility
 * 
 * This utility helps test and validate Supabase connection and configuration
 */

import { supabase } from '../lib/supabase';

// Test Supabase connection
export const testSupabaseConnection = async () => {
  try {
    console.log('🔍 Testing Supabase connection...');
    
    // Test 1: Basic connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message);
      return {
        success: false,
        error: error.message,
        tests: {
          connection: false,
          auth: false,
          database: false
        }
      };
    }
    
    console.log('✅ Supabase connection successful');
    
    // Test 2: Auth service
    const { data: { session } } = await supabase.auth.getSession();
    console.log('🔐 Auth session:', session ? 'Active' : 'None');
    
    // Test 3: Database schema
    const { data: tables, error: schemaError } = await supabase
      .from('profiles')
      .select('*')
      .limit(0);
    
    if (schemaError) {
      console.warn('⚠️ Database schema test failed:', schemaError.message);
    } else {
      console.log('✅ Database schema accessible');
    }
    
    return {
      success: true,
      tests: {
        connection: true,
        auth: true,
        database: !schemaError
      },
      session: !!session
    };
    
  } catch (error) {
    console.error('❌ Supabase test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      tests: {
        connection: false,
        auth: false,
        database: false
      }
    };
  }
};

// Test authentication flow
export const testAuthFlow = async () => {
  try {
    console.log('🔍 Testing authentication flow...');
    
    // Test auth state
    const { data: { user } } = await supabase.auth.getUser();
    console.log('👤 Current user:', user ? user.email : 'Not authenticated');
    
    // Test auth listeners
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('🔄 Auth state changed:', event, session?.user?.email);
      }
    );
    
    // Cleanup listener after 1 second
    setTimeout(() => {
      subscription.unsubscribe();
      console.log('🧹 Auth listener cleaned up');
    }, 1000);
    
    return {
      success: true,
      user: user,
      authenticated: !!user
    };
    
  } catch (error) {
    console.error('❌ Auth flow test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      authenticated: false
    };
  }
};

// Validate environment variables
export const validateEnvironment = () => {
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];
  
  const missing = requiredVars.filter(varName => !import.meta.env[varName]);
  
  if (missing.length > 0) {
    console.error('❌ Missing environment variables:', missing);
    return {
      valid: false,
      missing: missing
    };
  }
  
  console.log('✅ All required environment variables present');
  return {
    valid: true,
    missing: []
  };
};

// Run all tests
export const runAllTests = async () => {
  console.log('🚀 Running Supabase integration tests...');
  
  const envTest = validateEnvironment();
  if (!envTest.valid) {
    return {
      success: false,
      error: 'Environment validation failed',
      results: { environment: envTest }
    };
  }
  
  const connectionTest = await testSupabaseConnection();
  const authTest = await testAuthFlow();
  
  const allPassed = connectionTest.success && authTest.success;
  
  console.log(allPassed ? '✅ All tests passed!' : '❌ Some tests failed');
  
  return {
    success: allPassed,
    results: {
      environment: envTest,
      connection: connectionTest,
      auth: authTest
    }
  };
};

export default {
  testSupabaseConnection,
  testAuthFlow,
  validateEnvironment,
  runAllTests
};
