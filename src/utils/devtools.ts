/**
 * Development Tools Utilities
 * 
 * Provides easy access to development tools and debugging utilities.
 * Only available in development mode.
 */

/**
 * React Query DevTools Control
 */
export const devtools = {
  /**
   * Toggle React Query DevTools visibility
   */
  toggleReactQuery: () => {
    if (import.meta.env.PROD) {
      console.warn('DevTools are not available in production');
      return false;
    }
    
    if ((window as any).toggleReactQueryDevtools) {
      (window as any).toggleReactQueryDevtools();
      return true;
    }
    
    console.warn('React Query DevTools not available. Make sure the app is fully loaded.');
    return false;
  },

  /**
   * Show React Query DevTools
   */
  showReactQuery: () => {
    if (import.meta.env.PROD) return false;
    
    const isVisible = localStorage.getItem('reactQueryDevtoolsVisible') === 'true';
    if (!isVisible) {
      return devtools.toggleReactQuery();
    }
    
    console.log('React Query DevTools are already visible');
    return true;
  },

  /**
   * Hide React Query DevTools
   */
  hideReactQuery: () => {
    if (import.meta.env.PROD) return false;
    
    const isVisible = localStorage.getItem('reactQueryDevtoolsVisible') === 'true';
    if (isVisible) {
      return devtools.toggleReactQuery();
    }
    
    console.log('React Query DevTools are already hidden');
    return true;
  },

  /**
   * Get current DevTools status
   */
  status: () => {
    if (import.meta.env.PROD) {
      return { environment: 'production', reactQuery: 'disabled' };
    }
    
    const reactQueryVisible = localStorage.getItem('reactQueryDevtoolsVisible') === 'true';
    
    return {
      environment: 'development',
      reactQuery: reactQueryVisible ? 'visible' : 'hidden',
      shortcuts: {
        toggleReactQuery: 'Ctrl+Shift+Q (Cmd+Shift+Q on Mac)',
        console: 'devtools.toggleReactQuery()'
      }
    };
  },

  /**
   * Show help information
   */
  help: () => {
    if (import.meta.env.PROD) {
      console.log('DevTools are not available in production');
      return;
    }

    console.group('🔧 Development Tools Help');
    console.log('Available commands:');
    console.log('• devtools.toggleReactQuery() - Toggle React Query DevTools');
    console.log('• devtools.showReactQuery() - Show React Query DevTools');
    console.log('• devtools.hideReactQuery() - Hide React Query DevTools');
    console.log('• devtools.status() - Get current status');
    console.log('• devtools.help() - Show this help');
    console.log('');
    console.log('Keyboard shortcuts:');
    console.log('• Ctrl+Shift+Q (Cmd+Shift+Q on Mac) - Toggle React Query DevTools');
    console.log('');
    console.log('Environment variables:');
    console.log('• VITE_ENABLE_REACT_QUERY_DEVTOOLS=true - Enable DevTools by default');
    console.groupEnd();
  }
};

/**
 * Performance monitoring utilities
 */
export const performance = {
  /**
   * Log React Query cache statistics
   */
  cacheStats: () => {
    if (import.meta.env.PROD) return null;
    
    // This would need to be implemented with access to QueryClient
    console.log('Cache statistics would be displayed here');
    return null;
  },

  /**
   * Clear all React Query cache
   */
  clearCache: () => {
    if (import.meta.env.PROD) return false;
    
    console.log('Cache clearing would be implemented here');
    return true;
  }
};

/**
 * Make devtools available globally in development
 */
if (!import.meta.env.PROD) {
  (window as any).devtools = devtools;
  (window as any).performance = performance;
  
  // Show initial help message
  console.log(
    '%c🚀 Development Tools Loaded',
    'color: #00d8ff; font-weight: bold; font-size: 16px;'
  );
  console.log(
    '%cType "devtools.help()" for available commands',
    'color: #888; font-size: 12px;'
  );
}

export default devtools;
