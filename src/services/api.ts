/**
 * API Service for CSV Data Processing Backend
 * Handles all communication with the Python Flask API
 */

const API_BASE_URL = 'http://localhost:5001/api';

export interface CompetitorData {
  Title: string;
  Author: string;
  'Page Old': number;
  'Page New': number;
  'Order Old': number;
  'Order New': number;
  new_item?: boolean;
}

export interface CompetitorInfo {
  name: string;
  filename: string;
  date: string;
  filepath: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface CompetitorDataResponse {
  success: boolean;
  competitor: string;
  type: string;
  data: CompetitorData[];
  file: string;
  count: number;
}

export interface AllCompetitorDataResponse {
  success: boolean;
  competitor: string;
  data: {
    popular: CompetitorData[];
    new: CompetitorData[];
    all: CompetitorData[];
    old: CompetitorData[];
  };
  file: string;
}

export interface CompetitorsListResponse {
  success: boolean;
  competitors: string[];
  details: CompetitorInfo[];
}

export interface AuthorData {
  author: string;
  count: number;
  rank: number;
  points?: number;
}

export interface TopAuthorsResponse {
  success: boolean;
  data: AuthorData[];
  total_authors: number;
  total_files_processed: number;
  total_items: number;
  analysis_type: string;
  limit: number;
}

export interface DetailedAuthorData {
  author: string;
  total_points: number;
  rank: number;
  page_distribution: Record<string, number>;
}

export interface DetailedAuthorsResponse {
  success: boolean;
  data: DetailedAuthorData[];
  total_authors: number;
  total_files_processed: number;
  total_items: number;
  max_page: number;
  limit: number;
}

export interface CategoryData {
  Title: string;
  Author: string;
  'Page Old': number;
  'Page New': number;
  'Order Old': number;
  'Order New': number;
  Change: number;
  Status: 'Up' | 'Down' | 'Same';
  Link?: string;
}

export interface CategoryInfo {
  name: string;
  filename: string;
  date: string;
  filepath: string;
}

export interface CategoriesListResponse {
  success: boolean;
  categories: string[];
  details: CategoryInfo[];
}

export interface CategoryDataResponse {
  success: boolean;
  category: string;
  data: CategoryData[];
  file: string;
  count: number;
}

class ApiService {
  private async fetchWithErrorHandling<T>(url: string, options?: RequestInit): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error('API Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get list of available competitors
   */
  async getCompetitors(): Promise<ApiResponse<CompetitorsListResponse>> {
    return this.fetchWithErrorHandling<CompetitorsListResponse>(`${API_BASE_URL}/competitors`);
  }

  /**
   * Get specific data type for a competitor
   */
  async getCompetitorData(
    competitorName: string, 
    dataType: 'popular' | 'new' | 'all' | 'old' = 'all'
  ): Promise<ApiResponse<CompetitorDataResponse>> {
    const url = `${API_BASE_URL}/competitors/${encodeURIComponent(competitorName)}/data?type=${dataType}`;
    return this.fetchWithErrorHandling<CompetitorDataResponse>(url);
  }

  /**
   * Get all categorized data for a competitor
   */
  async getAllCompetitorData(competitorName: string): Promise<ApiResponse<AllCompetitorDataResponse>> {
    const url = `${API_BASE_URL}/competitors/${encodeURIComponent(competitorName)}/all-data`;
    return this.fetchWithErrorHandling<AllCompetitorDataResponse>(url);
  }

  /**
   * Get list of all files
   */
  async getFiles(): Promise<ApiResponse<any>> {
    return this.fetchWithErrorHandling(`${API_BASE_URL}/files`);
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<ApiResponse<any>> {
    return this.fetchWithErrorHandling(`${API_BASE_URL}/health`);
  }

  /**
   * Check if API is available
   */
  async isApiAvailable(): Promise<boolean> {
    try {
      const response = await this.healthCheck();
      return response.success;
    } catch {
      return false;
    }
  }

  /**
   * Get top authors by frequency or points
   */
  async getTopAuthors(limit: number = 20, type: 'frequency' | 'points' = 'frequency'): Promise<ApiResponse<TopAuthorsResponse>> {
    const url = `${API_BASE_URL}/authors/top?limit=${limit}&type=${type}`;
    return this.fetchWithErrorHandling<TopAuthorsResponse>(url);
  }

  /**
   * Get detailed authors analysis with page distribution
   */
  async getDetailedAuthors(limit: number = 20): Promise<ApiResponse<DetailedAuthorsResponse>> {
    const url = `${API_BASE_URL}/authors/detailed?limit=${limit}`;
    return this.fetchWithErrorHandling<DetailedAuthorsResponse>(url);
  }

  /**
   * Get list of available categories
   */
  async getCategories(): Promise<ApiResponse<CategoriesListResponse>> {
    return this.fetchWithErrorHandling<CategoriesListResponse>(`${API_BASE_URL}/categories`);
  }

  /**
   * Get data for a specific category
   */
  async getCategoryData(categoryName: string): Promise<ApiResponse<CategoryDataResponse>> {
    const url = `${API_BASE_URL}/categories/${encodeURIComponent(categoryName)}/data`;
    return this.fetchWithErrorHandling<CategoryDataResponse>(url);
  }

  /**
   * Upload CSV file
   */
  async uploadFile(file: File): Promise<ApiResponse<any>> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${API_BASE_URL}/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error('Upload Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Validate CSV file without uploading
   */
  async validateFile(file: File): Promise<ApiResponse<any>> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${API_BASE_URL}/upload/validate`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error('Validation Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Validation failed'
      };
    }
  }

  /**
   * Download a specific CSV file
   */
  async downloadFile(fileId: string): Promise<{ success: boolean; blob?: Blob; error?: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/files/${encodeURIComponent(fileId)}/download`, {
        method: 'GET',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      return { success: true, blob };
    } catch (error) {
      console.error('Download Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Download failed'
      };
    }
  }

  /**
   * Delete a specific CSV file
   */
  async deleteFile(fileId: string): Promise<ApiResponse<any>> {
    try {
      const response = await fetch(`${API_BASE_URL}/files/${encodeURIComponent(fileId)}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error('Delete Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete failed'
      };
    }
  }

  /**
   * Rename a specific CSV file
   */
  async renameFile(fileId: string, newName: string): Promise<ApiResponse<any>> {
    try {
      const response = await fetch(`${API_BASE_URL}/files/${encodeURIComponent(fileId)}/rename`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ new_name: newName }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error('Rename Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Rename failed'
      };
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();

// Export utility functions
export const formatCompetitorName = (name: string): string => {
  return name.replace(/_/g, ' ').toUpperCase();
};

export const parseCompetitorName = (displayName: string): string => {
  return displayName.toLowerCase().replace(/\s+/g, '_');
};

// Data transformation utilities
export const transformCompetitorData = (data: CompetitorData[]): any[] => {
  return data.map(item => ({
    ...item,
    Change: (item['Order Old'] || 0) - (item['Order New'] || 0),
    Status: getStatusFromChange((item['Order Old'] || 0) - (item['Order New'] || 0))
  }));
};

export const getStatusFromChange = (change: number): 'Up' | 'Down' | 'Same' => {
  if (change > 0) return 'Up';
  if (change < 0) return 'Down';
  return 'Same';
};

// Error handling utilities
export const handleApiError = (error: string): string => {
  if (error.includes('fetch') || error.includes('Failed to fetch') || error.includes('CONNECTION_REFUSED')) {
    return 'Backend server tidak dapat diakses. Silakan coba lagi nanti.';
  }
  if (error.includes('404')) {
    return 'Data tidak ditemukan untuk permintaan ini.';
  }
  if (error.includes('500')) {
    return 'Terjadi kesalahan server saat memproses data.';
  }
  if (error.includes('timeout')) {
    return 'Koneksi timeout. Periksa koneksi backend.';
  }
  return error;
};

// Enhanced error types for better user messaging
export interface ApiErrorInfo {
  type: 'connection' | 'not_found' | 'server_error' | 'timeout' | 'unknown';
  message: string;
  userMessage: string;
}

export const getApiErrorInfo = (error: string): ApiErrorInfo => {
  if (error.includes('fetch') || error.includes('Failed to fetch') || error.includes('CONNECTION_REFUSED')) {
    return {
      type: 'connection',
      message: error,
      userMessage: 'Backend server tidak dapat diakses. Silakan coba lagi nanti.'
    };
  }
  if (error.includes('404')) {
    return {
      type: 'not_found',
      message: error,
      userMessage: 'Data tidak ditemukan untuk permintaan ini.'
    };
  }
  if (error.includes('500')) {
    return {
      type: 'server_error',
      message: error,
      userMessage: 'Terjadi kesalahan server saat memproses data.'
    };
  }
  if (error.includes('timeout')) {
    return {
      type: 'timeout',
      message: error,
      userMessage: 'Koneksi timeout. Periksa koneksi backend.'
    };
  }
  return {
    type: 'unknown',
    message: error,
    userMessage: 'Terjadi kesalahan yang tidak diketahui.'
  };
};
