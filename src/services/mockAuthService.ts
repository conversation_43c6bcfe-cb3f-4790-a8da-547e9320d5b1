/**
 * Mock Authentication Service
 * 
 * Provides fallback authentication for testing when backend is not available
 */

import type {
  ApiResponse,
  GenerateOTPResponse,
  ValidateOTPResponse,
  SessionInfoResponse,
  AuthServiceType
} from '../types/auth';

class MockAuthService implements AuthServiceType {
  private mockOTPs: Map<string, { code: string; expires: number; key: string }> = new Map();
  private mockSessions: Map<string, any> = new Map();

  /**
   * Generate mock OTP for email address
   */
  async generateOTP(email: string): Promise<ApiResponse<GenerateOTPResponse>> {
    try {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Generate mock OTP
      const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
      const otpKey = `mock_otp_${email}_${Date.now()}`;
      const expiresAt = Date.now() + 5 * 60 * 1000; // 5 minutes

      // Store mock OTP
      this.mockOTPs.set(email, {
        code: otpCode,
        expires: expiresAt,
        key: otpKey
      });

      // Send to Chrome Extension (mock)
      this.sendOTPToExtension(email, otpCode);

      console.log(`🧪 Mock OTP generated for ${email}: ${otpCode}`);

      return {
        success: true,
        data: {
          success: true,
          message: 'Mock OTP generated successfully',
          email: email,
          otp_key: otpKey,
          expires_in: 300,
          created_at: new Date().toISOString(),
          otp_code: otpCode // For extension
        }
      };
    } catch (error) {
      console.error('Mock Generate OTP error:', error);
      return {
        success: false,
        error: 'Mock service error',
        code: 'MOCK_ERROR'
      };
    }
  }

  /**
   * Validate mock OTP and create session
   */
  async validateOTP(
    email: string, 
    otpCode: string, 
    otpKey?: string
  ): Promise<ApiResponse<ValidateOTPResponse>> {
    try {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 300));

      const storedOTP = this.mockOTPs.get(email);
      
      if (!storedOTP) {
        return {
          success: false,
          error: 'OTP not found or expired',
          code: 'OTP_NOT_FOUND'
        };
      }

      if (Date.now() > storedOTP.expires) {
        this.mockOTPs.delete(email);
        return {
          success: false,
          error: 'OTP has expired',
          code: 'OTP_EXPIRED'
        };
      }

      if (storedOTP.code !== otpCode.trim()) {
        return {
          success: false,
          error: 'Invalid OTP code',
          code: 'INVALID_OTP'
        };
      }

      // Generate mock session
      const sessionToken = `mock_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const refreshToken = `mock_refresh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const sessionData = {
        session_token: sessionToken,
        refresh_token: refreshToken,
        user: {
          email: email,
          id: `mock_user_${email.replace('@', '_').replace('.', '_')}`,
          name: email.split('@')[0],
          created_at: new Date().toISOString()
        },
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
        created_at: new Date().toISOString()
      };

      // Store mock session
      this.mockSessions.set(sessionToken, sessionData);

      // Clean up used OTP
      this.mockOTPs.delete(email);

      console.log(`🧪 Mock OTP validated for ${email}, session created`);

      return {
        success: true,
        data: sessionData
      };
    } catch (error) {
      console.error('Mock Validate OTP error:', error);
      return {
        success: false,
        error: 'Mock validation error',
        code: 'MOCK_ERROR'
      };
    }
  }

  /**
   * Mock session refresh
   */
  async refreshSession(refreshToken: string): Promise<ApiResponse<ValidateOTPResponse>> {
    await new Promise(resolve => setTimeout(resolve, 200));

    // Find session by refresh token
    for (const [sessionToken, sessionData] of this.mockSessions.entries()) {
      if (sessionData.refresh_token === refreshToken) {
        // Generate new tokens
        const newSessionToken = `mock_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const newRefreshToken = `mock_refresh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        const newSessionData = {
          ...sessionData,
          session_token: newSessionToken,
          refresh_token: newRefreshToken,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          refreshed_at: new Date().toISOString()
        };

        // Update session storage
        this.mockSessions.delete(sessionToken);
        this.mockSessions.set(newSessionToken, newSessionData);

        return {
          success: true,
          data: newSessionData
        };
      }
    }

    return {
      success: false,
      error: 'Invalid refresh token',
      code: 'INVALID_REFRESH_TOKEN'
    };
  }

  /**
   * Mock logout
   */
  async logout(sessionToken: string): Promise<ApiResponse> {
    await new Promise(resolve => setTimeout(resolve, 100));

    if (this.mockSessions.has(sessionToken)) {
      this.mockSessions.delete(sessionToken);
      return { success: true };
    }

    return {
      success: false,
      error: 'Session not found',
      code: 'SESSION_NOT_FOUND'
    };
  }

  /**
   * Mock logout all sessions
   */
  async logoutAll(sessionToken: string): Promise<ApiResponse> {
    await new Promise(resolve => setTimeout(resolve, 150));

    const sessionData = this.mockSessions.get(sessionToken);
    if (sessionData) {
      // Remove all sessions for this user
      const userEmail = sessionData.user.email;
      for (const [token, data] of this.mockSessions.entries()) {
        if (data.user.email === userEmail) {
          this.mockSessions.delete(token);
        }
      }
      return { success: true };
    }

    return {
      success: false,
      error: 'Session not found',
      code: 'SESSION_NOT_FOUND'
    };
  }

  /**
   * Get mock session info
   */
  async getSessionInfo(sessionToken: string): Promise<ApiResponse<SessionInfoResponse>> {
    await new Promise(resolve => setTimeout(resolve, 100));

    const sessionData = this.mockSessions.get(sessionToken);
    if (sessionData) {
      return {
        success: true,
        data: {
          valid: true,
          user: sessionData.user,
          expires_at: sessionData.expires_at,
          created_at: sessionData.created_at
        }
      };
    }

    return {
      success: false,
      error: 'Session not found or expired',
      code: 'SESSION_NOT_FOUND'
    };
  }

  /**
   * Mock health check
   */
  async healthCheck(): Promise<ApiResponse> {
    await new Promise(resolve => setTimeout(resolve, 50));
    
    return {
      success: true,
      data: {
        status: 'healthy',
        service: 'mock-auth-service',
        timestamp: new Date().toISOString(),
        version: '1.0.0-mock'
      }
    };
  }

  /**
   * Send OTP to Chrome Extension (mock)
   */
  private sendOTPToExtension(email: string, otpCode: string): void {
    try {
      // Mock extension communication
      const mockExtensionData = {
        type: 'OTP_GENERATED',
        email: email,
        otp_code: otpCode,
        timestamp: new Date().toISOString(),
        source: 'mock-service'
      };

      // Try to send to extension if available
      if (window.postMessage) {
        window.postMessage(mockExtensionData, '*');
      }

      // Also log to console for testing
      console.log('🧪 Mock OTP sent to extension:', mockExtensionData);
      
      // Show notification for testing
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('Mock OTP Generated', {
          body: `OTP for ${email}: ${otpCode}`,
          icon: '/favicon.ico'
        });
      }
    } catch (error) {
      console.warn('Mock extension communication failed:', error);
    }
  }

  /**
   * Get error message for auth error code
   */
  getErrorMessage(code: string): string {
    const errorMessages: Record<string, string> = {
      'EMAIL_REQUIRED': 'Email address is required',
      'INVALID_EMAIL': 'Please enter a valid email address',
      'RATE_LIMIT_EXCEEDED': 'Too many requests. Please wait before trying again',
      'OTP_NOT_FOUND': 'OTP not found or expired. Please request a new one',
      'OTP_EXPIRED': 'OTP has expired. Please request a new one',
      'INVALID_OTP': 'Invalid OTP code. Please check and try again',
      'MAX_ATTEMPTS_EXCEEDED': 'Maximum attempts exceeded. Please request a new OTP',
      'SESSION_NOT_FOUND': 'Session not found. Please login again',
      'SESSION_EXPIRED': 'Session has expired. Please login again',
      'INVALID_REFRESH_TOKEN': 'Invalid refresh token. Please login again',
      'MOCK_ERROR': 'Mock service error - Backend not available',
      'NETWORK_ERROR': 'Network error - Using mock service'
    };

    return errorMessages[code] || 'An unexpected error occurred';
  }
}

// Create and export mock service instance
export const mockAuthService = new MockAuthService();
export { MockAuthService };
