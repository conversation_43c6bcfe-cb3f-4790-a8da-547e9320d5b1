/**
 * Chrome Extension Communication Service
 * 
 * Handles communication with Chrome Extension for OTP flow
 */

import type {
  ExtensionServiceType,
  ExtensionMessage,
  OTPRequest,
  OTPResponse,
  OTPData
} from '../types/auth';
import { AUTH_CONFIG } from '../types/auth';

class ExtensionService implements ExtensionServiceType {
  private listeners: Set<(response: OTPResponse) => void> = new Set();
  private messageListener: ((event: MessageEvent) => void) | null = null;
  private extensionId: string | null = null;
  private isListening = false;

  constructor() {
    this.extensionId = import.meta.env.VITE_CHROME_EXTENSION_ID || null;
    this.initializeListener();
  }

  /**
   * Initialize message listener for Chrome Extension communication
   */
  private initializeListener(): void {
    if (this.messageListener) {
      return; // Already initialized
    }

    this.messageListener = (event: MessageEvent) => {
      // Validate origin for security
      const allowedOrigins = [
        window.location.origin,
        'chrome-extension://*'
      ];

      // For Chrome Extension, origin will be chrome-extension://extension-id
      const isValidOrigin = event.origin === window.location.origin || 
                           event.origin.startsWith('chrome-extension://');

      if (!isValidOrigin) {
        console.warn('Received message from invalid origin:', event.origin);
        return;
      }

      try {
        const message: ExtensionMessage = event.data;

        // Validate message structure
        if (!message || typeof message !== 'object' || !message.type) {
          return;
        }

        // Handle OTP responses
        if (message.type === 'OTP_RESPONSE' || message.type === 'OTP_REJECTED') {
          const response = message as OTPResponse;
          this.notifyListeners(response);
        }

        // Handle extension ready signal
        if (message.type === 'EXTENSION_READY') {
          console.log('🔗 Chrome Extension is ready');
        }

        // Handle extension errors
        if (message.type === 'EXTENSION_ERROR') {
          console.error('Chrome Extension error:', message.data);
        }

      } catch (error) {
        console.error('Error processing extension message:', error);
      }
    };

    window.addEventListener('message', this.messageListener);
    this.isListening = true;
  }

  /**
   * Check if Chrome Extension is available (CSP-safe)
   */
  isExtensionAvailable(): boolean {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return false;
    }

    // Check if postMessage is available
    if (!window.postMessage) {
      return false;
    }

    // Multiple detection methods for CSP-safe environments
    let bridge = null;

    // Method 1: Direct window property
    if (typeof window.progressDashboardExtension !== 'undefined') {
      bridge = window.progressDashboardExtension;
    }
    // Method 2: Fallback global variable
    else if (typeof (window as any).__progressDashboardExtensionBridge__ !== 'undefined') {
      bridge = (window as any).__progressDashboardExtensionBridge__;
      // Assign to main property for consistency
      (window as any).progressDashboardExtension = bridge;
    }

    if (!bridge) {
      return false;
    }

    // Check if extension bridge has required methods
    if (!bridge.sendMessage || typeof bridge.sendMessage !== 'function') {
      return false;
    }

    // Check if extension is actually available
    if (bridge.isAvailable && typeof bridge.isAvailable === 'function') {
      return bridge.isAvailable();
    }

    return true;
  }

  /**
   * Check Chrome Extension connection (CSP-safe)
   */
  async checkExtensionConnection(): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.isExtensionAvailable()) {
        resolve(false);
        return;
      }

      try {
        // Get bridge reference (may be from fallback location)
        let bridge = (window as any).progressDashboardExtension;
        if (!bridge && (window as any).__progressDashboardExtensionBridge__) {
          bridge = (window as any).__progressDashboardExtensionBridge__;
          (window as any).progressDashboardExtension = bridge;
        }

        if (!bridge) {
          resolve(false);
          return;
        }

        // Try to get extension info to test connection
        if (bridge.getDebugInfo) {
          const info = bridge.getDebugInfo();
          console.log('Extension info:', info);

          // Test actual availability
          if (bridge.isAvailable && !bridge.isAvailable()) {
            resolve(false);
            return;
          }

          resolve(true);
          return;
        }

        // Fallback: Send test message to extension
        const testMessage = {
          type: 'GET_EXTENSION_STATUS',
          timestamp: Date.now()
        };

        bridge.sendMessage(testMessage)
          .then((response: any) => {
            resolve(response && response.success);
          })
          .catch((error: any) => {
            console.warn('Extension connection test failed:', error);
            resolve(false);
          });

      } catch (error) {
        console.warn('Extension connection check failed:', error);
        resolve(false);
      }
    });
  }

  /**
   * Check extension availability using EXTENSION_CHECK pattern (recommended)
   */
  async checkExtensionAvailability(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        // Set up listener for extension response
        const responseListener = (event: MessageEvent) => {
          if (event.data.type === 'EXTENSION_CHECK_RESPONSE' &&
              event.origin === window.location.origin) {
            console.log('Extension check response received:', event.data);
            window.removeEventListener('message', responseListener);
            resolve(true);
          }
        };

        // Listen for response
        window.addEventListener('message', responseListener);

        // Send extension check message
        window.postMessage({ type: 'EXTENSION_CHECK' }, window.location.origin);

        // Timeout after 2 seconds
        setTimeout(() => {
          window.removeEventListener('message', responseListener);
          resolve(false);
        }, 2000);

      } catch (error) {
        console.warn('Extension availability check failed:', error);
        resolve(false);
      }
    });
  }

  /**
   * Send OTP request using window.postMessage pattern (recommended)
   */
  async sendOTPRequestDirect(otpData: OTPData): Promise<OTPResponse> {
    return new Promise((resolve, reject) => {
      try {
        // Set up listener for OTP response
        const responseListener = (event: MessageEvent) => {
          if (event.data.type === 'OTP_RESPONSE' &&
              event.origin === window.location.origin) {
            console.log('OTP response received:', event.data);
            window.removeEventListener('message', responseListener);

            if (event.data.action === 'LOGIN') {
              resolve({
                success: true,
                action: 'approved',
                otp_code: event.data.otp,
                timestamp: Date.now()
              });
            } else if (event.data.action === 'REJECT') {
              reject(new Error('OTP request rejected by user'));
            }
          }
        };

        // Listen for response
        window.addEventListener('message', responseListener);

        // Send OTP request message
        window.postMessage({
          type: 'OTP_REQUEST',
          email: otpData.email,
          otp: otpData.otp_code,
          secret: otpData.otp_key
        }, window.location.origin);

        // Timeout after 30 seconds
        setTimeout(() => {
          window.removeEventListener('message', responseListener);
          reject(new Error('OTP request timeout'));
        }, 30000);

      } catch (error) {
        console.error('OTP request failed:', error);
        reject(error);
      }
    });
  }

  /**
   * Send OTP request to Chrome Extension (Production-ready - Legacy)
   */
  async sendOTPRequest(otpData: OTPData): Promise<OTPResponse> {
    return new Promise((resolve, reject) => {
      // Enhanced security checks
      if (!this.isExtensionSecure()) {
        reject(new Error('Chrome Extension not available or not secure'));
        return;
      }

      // Validate input data
      if (!otpData.email || !otpData.otp_code || !otpData.otp_key) {
        reject(new Error('Invalid OTP data provided'));
        return;
      }

      // Create OTP request message
      const request: OTPRequest = {
        type: 'OTP_REQUEST',
        data: {
          email: otpData.email,
          otp_code: otpData.otp_code,
          otp_key: otpData.otp_key,
          expires_in: otpData.expires_in,
          website: window.location.hostname
        },
        timestamp: Date.now(),
        origin: window.location.origin
      };

      // Set timeout for response
      const timeout = setTimeout(() => {
        this.stopListening();
        reject(new Error('Extension response timeout - user may have closed the notification'));
      }, AUTH_CONFIG.EXTENSION_TIMEOUT);

      // Listen for response with validation
      const responseHandler = (response: OTPResponse) => {
        clearTimeout(timeout);
        this.stopListening();

        // Validate response before resolving
        if (!this.validateExtensionResponse(response)) {
          reject(new Error('Invalid extension response received'));
          return;
        }

        resolve(response);
      };

      this.listenForResponses(responseHandler);

      // Send request to extension (CSP-safe)
      try {
        // Get bridge reference (may be from fallback location)
        let bridge = (window as any).progressDashboardExtension;
        if (!bridge && (window as any).__progressDashboardExtensionBridge__) {
          bridge = (window as any).__progressDashboardExtensionBridge__;
          (window as any).progressDashboardExtension = bridge;
        }

        if (bridge && bridge.sendMessage) {
          // Use direct bridge communication
          bridge.sendMessage(request)
            .then((response: any) => {
              clearTimeout(timeout);
              this.stopListening();

              if (response && response.success) {
                resolve(response.data);
              } else {
                reject(new Error(response?.error || 'Extension request failed'));
              }
            })
            .catch((error: any) => {
              clearTimeout(timeout);
              this.stopListening();
              reject(new Error(error.message || 'Extension communication failed'));
            });
        } else {
          // Fallback to postMessage
          window.postMessage(request, window.location.origin);
        }

        // Only log in development
        if (import.meta.env.DEV) {
          console.log('🔗 OTP request sent to Chrome Extension');
        }
      } catch (error) {
        clearTimeout(timeout);
        this.stopListening();
        reject(new Error('Failed to send message to extension'));
      }
    });
  }

  /**
   * Listen for responses from Chrome Extension
   */
  listenForResponses(callback: (response: OTPResponse) => void): void {
    this.listeners.add(callback);
  }

  /**
   * Stop listening for responses
   */
  stopListening(): void {
    this.listeners.clear();
  }

  /**
   * Notify all listeners of response
   */
  private notifyListeners(response: OTPResponse): void {
    this.listeners.forEach(callback => {
      try {
        callback(response);
      } catch (error) {
        console.error('Error in extension response callback:', error);
      }
    });
  }

  /**
   * Get extension information (CSP-safe)
   */
  async getExtensionInfo(): Promise<any> {
    return new Promise((resolve) => {
      try {
        // Get bridge reference (may be from fallback location)
        let bridge = (window as any).progressDashboardExtension;
        if (!bridge && (window as any).__progressDashboardExtensionBridge__) {
          bridge = (window as any).__progressDashboardExtensionBridge__;
          (window as any).progressDashboardExtension = bridge;
        }

        if (bridge && bridge.getDebugInfo) {
          const debugInfo = bridge.getDebugInfo();
          resolve({
            available: this.isExtensionAvailable(),
            version: debugInfo.version || 'unknown',
            id: this.extensionId || 'unknown',
            injectionMethod: debugInfo.injectionMethod || 'unknown',
            environment: debugInfo.environment || 'unknown'
          });
        } else {
          // Fallback to basic info
          resolve({
            available: this.isExtensionAvailable(),
            version: 'unknown',
            id: this.extensionId || 'unknown'
          });
        }
      } catch (error) {
        resolve({
          available: false,
          version: 'error',
          id: 'error',
          error: error.message
        });
      }
    });
  }

  /**
   * Validate extension response for security
   */
  private validateExtensionResponse(response: OTPResponse): boolean {
    // Validate response structure
    if (!response || typeof response !== 'object') {
      return false;
    }

    // Validate required fields
    if (!response.type || !response.data || !response.timestamp) {
      return false;
    }

    // Validate response type
    const validTypes = ['OTP_RESPONSE', 'OTP_REJECTED'];
    if (!validTypes.includes(response.type)) {
      return false;
    }

    // Validate action
    const validActions = ['APPROVE', 'REJECT'];
    if (!validActions.includes(response.data.action)) {
      return false;
    }

    // Validate timestamp (not too old)
    const maxAge = 5 * 60 * 1000; // 5 minutes
    if (Date.now() - response.timestamp > maxAge) {
      return false;
    }

    return true;
  }

  /**
   * Enhanced security check for extension availability
   */
  isExtensionSecure(): boolean {
    // Check if we're in a secure context
    if (!window.isSecureContext && window.location.protocol !== 'http:') {
      return false;
    }

    // Check if extension bridge is properly loaded
    if (typeof window.progressDashboardExtension === 'undefined') {
      return false;
    }

    // Validate extension bridge structure
    const bridge = window.progressDashboardExtension;
    if (!bridge.sendMessage || typeof bridge.sendMessage !== 'function') {
      return false;
    }

    return true;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.messageListener) {
      window.removeEventListener('message', this.messageListener);
      this.messageListener = null;
    }
    this.listeners.clear();
    this.isListening = false;
  }
}

// Create and export singleton instance
export const extensionService = new ExtensionService();

// Production-ready service - no development bypasses
// All authentication must go through actual Chrome Extension

export default extensionService;
