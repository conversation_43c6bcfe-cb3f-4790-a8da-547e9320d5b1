/**
 * Notification Service
 * Handles all notification-related API calls and business logic
 */

import {
  Notification as AppNotification,
  CreateNotificationRequest,
  NotificationFilter,
  NotificationSettings,
  NotificationStats,
  NotificationApiResponse,
  NotificationListResponse,
  NotificationEvent
} from '../types/notification';

const API_BASE_URL = 'http://localhost:5001/api';

class NotificationService {
  private eventSource: EventSource | null = null;
  private eventListeners: ((event: NotificationEvent) => void)[] = [];
  private isServiceAvailable: boolean = true;
  private connectionRetryCount: number = 0;
  private maxRetries: number = 3;

  /**
   * Get all notifications with optional filtering and pagination
   */
  async getNotifications(
    filter?: NotificationFilter,
    page: number = 1,
    limit: number = 50
  ): Promise<NotificationApiResponse<NotificationListResponse>> {
    if (!this.isServiceAvailable) {
      return {
        success: true,
        data: {
          notifications: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      if (filter) {
        if (filter.type?.length) params.append('type', filter.type.join(','));
        if (filter.priority?.length) params.append('priority', filter.priority.join(','));
        if (filter.status?.length) params.append('status', filter.status.join(','));
        if (filter.category?.length) params.append('category', filter.category.join(','));
        if (filter.search) params.append('search', filter.search);
        if (filter.dateRange) {
          params.append('startDate', filter.dateRange.start.toISOString());
          params.append('endDate', filter.dateRange.end.toISOString());
        }
      }

      const response = await fetch(`${API_BASE_URL}/notifications?${params}`, {
        signal: AbortSignal.timeout(5000)
      });
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch notifications');
      }

      // Convert string dates to Date objects
      data.data.notifications = data.data.notifications.map((notification: any) => ({
        ...notification,
        createdAt: new Date(notification.createdAt),
        readAt: notification.readAt ? new Date(notification.readAt) : undefined,
        expiresAt: notification.expiresAt ? new Date(notification.expiresAt) : undefined
      }));

      return data;
    } catch (error) {
      // Mark service as unavailable on connection errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        this.isServiceAvailable = false;
        console.warn('Notification service marked as unavailable');
        return {
          success: true,
          data: {
            notifications: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Check if notification service is available
   */
  private async checkServiceAvailability(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(2000) // 2 second timeout
      });
      return response.ok;
    } catch (error) {
      console.warn('Notification service not available:', error);
      return false;
    }
  }

  /**
   * Create a new notification
   */
  async createNotification(notification: CreateNotificationRequest): Promise<NotificationApiResponse<AppNotification>> {
    if (!this.isServiceAvailable) {
      console.warn('Notification service not available, skipping notification creation');
      return {
        success: false,
        error: 'Notification service not available'
      };
    }

    try {
      const response = await fetch(`${API_BASE_URL}/notifications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(notification),
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create notification');
      }

      // Convert string dates to Date objects
      if (data.data) {
        data.data.createdAt = new Date(data.data.createdAt);
        if (data.data.readAt) data.data.readAt = new Date(data.data.readAt);
        if (data.data.expiresAt) data.data.expiresAt = new Date(data.data.expiresAt);
      }

      return data;
    } catch (error) {
      // Mark service as unavailable on connection errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        this.isServiceAvailable = false;
        console.warn('Notification service marked as unavailable due to connection error');
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(id: string): Promise<NotificationApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/${id}/read`, {
        method: 'PATCH'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to mark notification as read');
      }

      return data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<NotificationApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/read-all`, {
        method: 'PATCH'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to mark all notifications as read');
      }

      return data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Delete notification
   */
  async deleteNotification(id: string): Promise<NotificationApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/${id}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete notification');
      }

      return data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Archive notification
   */
  async archiveNotification(id: string): Promise<NotificationApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/${id}/archive`, {
        method: 'PATCH'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to archive notification');
      }

      return data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get notification statistics
   */
  async getStats(): Promise<NotificationApiResponse<NotificationStats>> {
    if (!this.isServiceAvailable) {
      // Return default stats when service unavailable
      return {
        success: true,
        data: {
          total: 0,
          unread: 0,
          byType: {
            success: 0,
            error: 0,
            warning: 0,
            info: 0,
            data_update: 0,
            system: 0,
            file_upload: 0,
            analysis_complete: 0
          },
          byPriority: {
            low: 0,
            medium: 0,
            high: 0,
            urgent: 0
          },
          todayCount: 0,
          weekCount: 0
        }
      };
    }

    try {
      const response = await fetch(`${API_BASE_URL}/notifications/stats`, {
        signal: AbortSignal.timeout(5000)
      });
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch notification stats');
      }

      return data;
    } catch (error) {
      // Mark service as unavailable on connection errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        this.isServiceAvailable = false;
        console.warn('Notification service marked as unavailable');
        // Return default stats
        return {
          success: true,
          data: {
            total: 0,
            unread: 0,
            byType: {
              success: 0,
              error: 0,
              warning: 0,
              info: 0,
              data_update: 0,
              system: 0,
              file_upload: 0,
              analysis_complete: 0
            },
            byPriority: {
              low: 0,
              medium: 0,
              high: 0,
              urgent: 0
            },
            todayCount: 0,
            weekCount: 0
          }
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get notification settings
   */
  async getSettings(): Promise<NotificationApiResponse<NotificationSettings>> {
    if (!this.isServiceAvailable) {
      // Return default settings when service unavailable
      return {
        success: true,
        data: {
          enabled: true,
          email: true,
          browser: true,
          sound: true,
          desktop: false,
          dataUpdates: true,
          systemAlerts: true,
          analysisComplete: true,
          fileOperations: true,
          priorities: {
            low: true,
            medium: true,
            high: true,
            urgent: true
          },
          quietHours: {
            enabled: false,
            start: '22:00',
            end: '08:00'
          }
        }
      };
    }

    try {
      const response = await fetch(`${API_BASE_URL}/notifications/settings`, {
        signal: AbortSignal.timeout(5000)
      });
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch notification settings');
      }

      return data;
    } catch (error) {
      // Mark service as unavailable on connection errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        this.isServiceAvailable = false;
        console.warn('Notification service marked as unavailable');
        // Return default settings
        return {
          success: true,
          data: {
            enabled: true,
            email: true,
            browser: true,
            sound: true,
            desktop: false,
            dataUpdates: true,
            systemAlerts: true,
            analysisComplete: true,
            fileOperations: true,
            priorities: {
              low: true,
              medium: true,
              high: true,
              urgent: true
            },
            quietHours: {
              enabled: false,
              start: '22:00',
              end: '08:00'
            }
          }
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Update notification settings
   */
  async updateSettings(settings: Partial<NotificationSettings>): Promise<NotificationApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/settings`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update notification settings');
      }

      return data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Bulk operations
   */
  async bulkMarkAsRead(ids: string[]): Promise<NotificationApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/bulk/read`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ ids })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to mark notifications as read');
      }

      return data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async bulkDelete(ids: string[]): Promise<NotificationApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/bulk/delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ ids })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete notifications');
      }

      return data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async bulkArchive(ids: string[]): Promise<NotificationApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/notifications/bulk/archive`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ ids })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to archive notifications');
      }

      return data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Real-time event handling
   */
  async startEventStream(): Promise<void> {
    // Check service availability first
    if (!this.isServiceAvailable) {
      console.warn('Notification service not available, skipping event stream');
      return;
    }

    // Check if service is actually available before starting
    const isAvailable = await this.checkServiceAvailability();
    if (!isAvailable) {
      this.isServiceAvailable = false;
      console.warn('Notification service health check failed, skipping event stream');
      return;
    }

    if (this.eventSource) {
      this.eventSource.close();
    }

    try {
      this.eventSource = new EventSource(`${API_BASE_URL}/notifications/events`);

      this.eventSource.onmessage = (event) => {
        try {
          const notificationEvent: NotificationEvent = JSON.parse(event.data);
          this.eventListeners.forEach(listener => listener(notificationEvent));
          // Reset retry count on successful connection
          this.connectionRetryCount = 0;
        } catch (error) {
          console.error('Failed to parse notification event:', error);
        }
      };

      this.eventSource.onerror = (error) => {
        console.error('Notification event stream error:', error);
        this.connectionRetryCount++;

        // Mark service as unavailable after max retries
        if (this.connectionRetryCount >= this.maxRetries) {
          console.warn('Max retries reached, marking notification service as unavailable');
          this.isServiceAvailable = false;
          this.stopEventStream();
        }
      };

      this.eventSource.onopen = () => {
        console.log('Notification event stream connected');
        this.connectionRetryCount = 0;
      };
    } catch (error) {
      console.error('Failed to start notification event stream:', error);
      this.isServiceAvailable = false;
    }
  }

  stopEventStream(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  addEventListener(listener: (event: NotificationEvent) => void): void {
    this.eventListeners.push(listener);
  }

  removeEventListener(listener: (event: NotificationEvent) => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  /**
   * Check if service is available
   */
  isAvailable(): boolean {
    return this.isServiceAvailable;
  }

  /**
   * Reset service availability and retry connection
   */
  async resetServiceAvailability(): Promise<boolean> {
    this.connectionRetryCount = 0;
    this.isServiceAvailable = await this.checkServiceAvailability();
    return this.isServiceAvailable;
  }
}

export const notificationService = new NotificationService();
export default notificationService;
