/**
 * Authentication Service
 * 
 * Handles API communication with backend authentication endpoints
 */

import { apiClient } from '../utils/apiClient';
import { mockAuthService } from './mockAuthService';
import type {
  ApiResponse,
  GenerateOTPResponse,
  ValidateOTPResponse,
  SessionInfoResponse,
  AuthServiceType
} from '../types/auth';

class AuthService implements AuthServiceType {
  private baseURL: string;
  private useMockService: boolean = false;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:5001';
  }

  /**
   * Check if backend is available
   */
  private async checkBackendAvailability(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseURL}/api/auth/health`, {
        method: 'GET',
        timeout: 3000
      } as RequestInit);
      return response.ok;
    } catch (error) {
      console.warn('Backend not available, using mock service:', error);
      return false;
    }
  }

  /**
   * Get appropriate service (real or mock)
   */
  private async getService(): Promise<AuthServiceType> {
    if (this.useMockService) {
      return mockAuthService;
    }

    const isBackendAvailable = await this.checkBackendAvailability();
    if (!isBackendAvailable) {
      console.warn('🧪 Backend not available, switching to mock service');
      this.useMockService = true;
      return mockAuthService;
    }

    return this;
  }

  /**
   * Generate OTP for email address
   */
  async generateOTP(email: string): Promise<ApiResponse<GenerateOTPResponse>> {
    // Check if we should use mock service
    const service = await this.getService();
    if (service !== this) {
      return service.generateOTP(email);
    }

    try {
      const response = await apiClient.post('/api/auth/generate-otp', {
        email: email.trim().toLowerCase()
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data
        };
      } else {
        return {
          success: false,
          error: response.error || 'Failed to generate OTP',
          code: response.code || 'GENERATION_ERROR'
        };
      }
    } catch (error) {
      console.error('Generate OTP error:', error);

      // If network error, return error instead of auto-fallback
      if (error instanceof Error && error.message.includes('fetch')) {
        console.error('🚫 Backend not available - Chrome Extension required for authentication');
        return {
          success: false,
          error: 'Backend not available. Please ensure the backend server is running.',
          code: 'BACKEND_UNAVAILABLE'
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Validate OTP and create session
   */
  async validateOTP(
    email: string,
    otpCode: string,
    otpKey?: string
  ): Promise<ApiResponse<ValidateOTPResponse>> {
    // Check if we should use mock service
    const service = await this.getService();
    if (service !== this) {
      return service.validateOTP(email, otpCode, otpKey);
    }

    try {
      const requestData: any = {
        email: email.trim().toLowerCase(),
        otp_code: otpCode.trim()
      };

      if (otpKey) {
        requestData.otp_key = otpKey;
      }

      const response = await apiClient.post('/api/auth/validate-otp', requestData);

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data
        };
      } else {
        return {
          success: false,
          error: response.error || 'Failed to validate OTP',
          code: response.code || 'VALIDATION_ERROR'
        };
      }
    } catch (error) {
      console.error('Validate OTP error:', error);

      // If network error, return error instead of auto-fallback
      if (error instanceof Error && error.message.includes('fetch')) {
        console.error('🚫 Backend not available - Cannot validate OTP');
        return {
          success: false,
          error: 'Backend not available. Please ensure the backend server is running.',
          code: 'BACKEND_UNAVAILABLE'
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Refresh session using refresh token
   */
  async refreshSession(refreshToken: string): Promise<ApiResponse<ValidateOTPResponse>> {
    try {
      const response = await apiClient.post('/api/auth/refresh-session', {
        refresh_token: refreshToken
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data
        };
      } else {
        return {
          success: false,
          error: response.error || 'Failed to refresh session',
          code: response.code || 'REFRESH_ERROR'
        };
      }
    } catch (error) {
      console.error('Refresh session error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Logout and invalidate current session
   */
  async logout(sessionToken: string): Promise<ApiResponse> {
    try {
      const response = await apiClient.request('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${sessionToken}`
        }
      });

      return {
        success: response.success,
        error: response.error,
        code: response.code
      };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Logout from all sessions
   */
  async logoutAll(sessionToken: string): Promise<ApiResponse> {
    try {
      const response = await apiClient.request('/api/auth/logout-all', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${sessionToken}`
        }
      });

      return {
        success: response.success,
        error: response.error,
        code: response.code
      };
    } catch (error) {
      console.error('Logout all error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Get current session information
   */
  async getSessionInfo(sessionToken: string): Promise<ApiResponse<SessionInfoResponse>> {
    try {
      const response = await apiClient.request('/api/auth/session', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${sessionToken}`
        }
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data
        };
      } else {
        return {
          success: false,
          error: response.error || 'Failed to get session info',
          code: response.code || 'SESSION_ERROR'
        };
      }
    } catch (error) {
      console.error('Get session info error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Health check for authentication service
   */
  async healthCheck(): Promise<ApiResponse> {
    try {
      const response = await apiClient.get('/api/auth/health');

      return {
        success: response.success,
        data: response.data,
        error: response.error,
        code: response.code
      };
    } catch (error) {
      console.error('Health check error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Test authenticated endpoint
   */
  async testAuth(sessionToken: string): Promise<ApiResponse> {
    try {
      const response = await apiClient.request('/api/test-auth', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${sessionToken}`
        }
      });

      return {
        success: response.success,
        data: response.data,
        error: response.error,
        code: response.code
      };
    } catch (error) {
      console.error('Test auth error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Test optional auth endpoint
   */
  async testOptionalAuth(sessionToken?: string): Promise<ApiResponse> {
    try {
      const headers: Record<string, string> = {};
      if (sessionToken) {
        headers['Authorization'] = `Bearer ${sessionToken}`;
      }

      const response = await apiClient.request('/api/test-optional-auth', {
        method: 'GET',
        headers
      });

      return {
        success: response.success,
        data: response.data,
        error: response.error,
        code: response.code
      };
    } catch (error) {
      console.error('Test optional auth error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email.trim());
  }

  /**
   * Get error message for auth error code
   */
  getErrorMessage(code: string): string {
    const errorMessages: Record<string, string> = {
      'EMAIL_REQUIRED': 'Email address is required',
      'INVALID_EMAIL': 'Please enter a valid email address',
      'RATE_LIMIT_EXCEEDED': 'Too many requests. Please wait before trying again',
      'OTP_NOT_FOUND': 'OTP not found or expired. Please request a new one',
      'OTP_EXPIRED': 'OTP has expired. Please request a new one',
      'INVALID_OTP': 'Invalid OTP code. Please check and try again',
      'MAX_ATTEMPTS_EXCEEDED': 'Maximum attempts exceeded. Please request a new OTP',
      'SESSION_NOT_FOUND': 'Session not found. Please login again',
      'SESSION_EXPIRED': 'Session has expired. Please login again',
      'REFRESH_TOKEN_NOT_FOUND': 'Session expired. Please login again',
      'NETWORK_ERROR': 'Network error. Please check your connection',
      'INTERNAL_ERROR': 'Internal server error. Please try again later'
    };

    return errorMessages[code] || 'An unexpected error occurred';
  }
}

// Create and export singleton instance
export const authService = new AuthService();
export default authService;
