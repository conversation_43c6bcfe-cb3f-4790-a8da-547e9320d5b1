import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import App from '../App';

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('App Component', () => {
  it('renders correctly without sidebar', () => {
    renderWithRouter(<App />);

    // Header should be present
    const header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();

    // Sidebar should not exist
    const sidebar = screen.queryByRole('navigation', { name: /main navigation/i });
    expect(sidebar).not.toBeInTheDocument();
  });

  it('renders main content area', () => {
    renderWithRouter(<App />);

    const main = screen.getByRole('main');
    expect(main).toBeInTheDocument();
  });
});
