import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Categories from '../components/Categories';
import { apiService } from '../services/api';

// Mock the API service
jest.mock('../services/api', () => ({
  apiService: {
    getCategories: jest.fn(),
    getCategoryData: jest.fn(),
    getDetailedAuthors: <AUTHORS>
  },
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Plus: () => <div data-testid="plus-icon" />,
  Settings: () => <div data-testid="settings-icon" />,
  Edit2: () => <div data-testid="edit-icon" />,
  Trash2: () => <div data-testid="trash-icon" />,
  BarChart3: () => <div data-testid="chart-icon" />,
  Crown: () => <div data-testid="crown-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
  TrendingUp: () => <div data-testid="trending-up-icon" />,
  TrendingDown: () => <div data-testid="trending-down-icon" />,
  ArrowUpDown: () => <div data-testid="arrow-updown-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  XCircle: () => <div data-testid="x-circle-icon" />,
  Minus: () => <div data-testid="minus-icon" />,
  ExternalLink: () => <div data-testid="external-link-icon" />,
  ChevronLeft: () => <div data-testid="chevron-left-icon" />,
  ChevronRight: () => <div data-testid="chevron-right-icon" />,
  ChevronDown: () => <div data-testid="chevron-down-icon" />,
  ChevronUp: () => <div data-testid="chevron-up-icon" />,
  Info: () => <div data-testid="info-icon" />,
  X: () => <div data-testid="x-icon" />,
}));

const mockApiService = apiService as jest.Mocked<typeof apiService>;

describe('Categories Component - Manage Categories Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock responses
    mockApiService.getCategories.mockResolvedValue({
      success: true,
      data: {
        success: true,
        categories: ['graphic_templates', 'fonts', 'photos', 'illustrations'],
        details: []
      }
    });

    mockApiService.getCategoryData.mockResolvedValue({
      success: true,
      data: {
        success: true,
        category: 'graphic_templates',
        data: [
          {
            Title: 'Test Template',
            Author: 'TestAuthor',
            'Page Old': 2,
            'Page New': 1,
            'Order Old': 15,
            'Order New': 8,
            Change: 7,
            Status: 'Up' as const,
            Link: 'https://example.com/test'
          }
        ],
        file: 'test.csv',
        count: 1
      }
    });

    mockApiService.getDetailedAuthors.mockResolvedValue({
      success: true,
      data: {
        data: [
          {
            author: 'TestAuthor',
            total_points: 100,
            rank: 1,
            page_distribution: { page_1: 5, page_2: 3 }
          }
        ]
      }
    });
  });

  test('renders Categories Analysis component', async () => {
    render(<Categories />);
    
    await waitFor(() => {
      expect(screen.getByText('Categories Analysis')).toBeInTheDocument();
    });
  });

  test('displays settings button and opens settings modal', async () => {
    render(<Categories />);
    
    await waitFor(() => {
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      expect(settingsButton).toBeInTheDocument();
    });

    const settingsButton = screen.getByTestId('settings-icon').closest('button');
    fireEvent.click(settingsButton!);

    await waitFor(() => {
      expect(screen.getByText('Category Settings')).toBeInTheDocument();
      expect(screen.getByText('Manage Categories')).toBeInTheDocument();
    });
  });

  test('shows Add New Category button in settings modal', async () => {
    render(<Categories />);
    
    await waitFor(() => {
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      fireEvent.click(settingsButton!);
    });

    await waitFor(() => {
      expect(screen.getByText('Add New Category')).toBeInTheDocument();
    });
  });

  test('opens Add Category modal when clicking Add New Category', async () => {
    render(<Categories />);
    
    // Open settings modal
    await waitFor(() => {
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      fireEvent.click(settingsButton!);
    });

    // Click Add New Category
    const addButton = screen.getByText('Add New Category');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText('Category Name')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter category name')).toBeInTheDocument();
    });
  });

  test('displays categories list in settings modal', async () => {
    render(<Categories />);
    
    await waitFor(() => {
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      fireEvent.click(settingsButton!);
    });

    await waitFor(() => {
      expect(screen.getByText('GRAPHIC TEMPLATES')).toBeInTheDocument();
      expect(screen.getByText('FONTS')).toBeInTheDocument();
      expect(screen.getByText('PHOTOS')).toBeInTheDocument();
      expect(screen.getByText('ILLUSTRATIONS')).toBeInTheDocument();
    });
  });

  test('shows Edit and Delete buttons for each category', async () => {
    render(<Categories />);
    
    await waitFor(() => {
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      fireEvent.click(settingsButton!);
    });

    await waitFor(() => {
      const editButtons = screen.getAllByText('Edit');
      const deleteButtons = screen.getAllByText('Delete');
      
      expect(editButtons.length).toBeGreaterThan(0);
      expect(deleteButtons.length).toBeGreaterThan(0);
      expect(editButtons.length).toBe(deleteButtons.length);
    });
  });

  test('opens Edit Category modal when clicking Edit', async () => {
    render(<Categories />);
    
    // Open settings modal
    await waitFor(() => {
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      fireEvent.click(settingsButton!);
    });

    // Click first Edit button
    await waitFor(() => {
      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);
    });

    await waitFor(() => {
      expect(screen.getByText('Edit Category')).toBeInTheDocument();
      expect(screen.getByText('Save Changes')).toBeInTheDocument();
    });
  });

  test('opens Delete Confirmation modal when clicking Delete', async () => {
    render(<Categories />);
    
    // Open settings modal
    await waitFor(() => {
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      fireEvent.click(settingsButton!);
    });

    // Click first Delete button
    await waitFor(() => {
      const deleteButtons = screen.getAllByText('Delete');
      fireEvent.click(deleteButtons[0]);
    });

    await waitFor(() => {
      expect(screen.getByText('Delete Category')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to delete this category?')).toBeInTheDocument();
    });
  });

  test('can add a new category', async () => {
    render(<Categories />);
    
    // Open settings modal
    await waitFor(() => {
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      fireEvent.click(settingsButton!);
    });

    // Click Add New Category
    const addButton = screen.getByText('Add New Category');
    fireEvent.click(addButton);

    // Fill in category name
    await waitFor(() => {
      const input = screen.getByPlaceholderText('Enter category name');
      fireEvent.change(input, { target: { value: 'New Test Category' } });
    });

    // Click Add Category button
    const addCategoryButton = screen.getByText('Add Category');
    fireEvent.click(addCategoryButton);

    // Modal should close
    await waitFor(() => {
      expect(screen.queryByText('Category Name')).not.toBeInTheDocument();
    });
  });

  test('can edit a category name', async () => {
    render(<Categories />);
    
    // Open settings modal and edit first category
    await waitFor(() => {
      const settingsButton = screen.getByTestId('settings-icon').closest('button');
      fireEvent.click(settingsButton!);
    });

    await waitFor(() => {
      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);
    });

    // Change category name
    await waitFor(() => {
      const input = screen.getByDisplayValue('graphic templates');
      fireEvent.change(input, { target: { value: 'Updated Category Name' } });
    });

    // Save changes
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);

    // Modal should close
    await waitFor(() => {
      expect(screen.queryByText('Edit Category')).not.toBeInTheDocument();
    });
  });

  test('displays category tabs with navigation', async () => {
    render(<Categories />);
    
    await waitFor(() => {
      expect(screen.getByText('GRAPHIC TEMPLATES')).toBeInTheDocument();
      expect(screen.getByText('FONTS')).toBeInTheDocument();
    });
  });

  test('loads category data when clicking on a category tab', async () => {
    render(<Categories />);
    
    await waitFor(() => {
      const fontsTab = screen.getByText('FONTS');
      fireEvent.click(fontsTab);
    });

    await waitFor(() => {
      expect(mockApiService.getCategoryData).toHaveBeenCalledWith('fonts');
    });
  });

  test('displays Top Authors section', async () => {
    render(<Categories />);
    
    await waitFor(() => {
      expect(screen.getByText('Top Authors')).toBeInTheDocument();
      expect(screen.getByText('Active creators')).toBeInTheDocument();
    });
  });

  test('handles API errors gracefully', async () => {
    mockApiService.getCategories.mockRejectedValue(new Error('API Error'));
    
    render(<Categories />);
    
    await waitFor(() => {
      // Should fallback to default categories
      expect(screen.getByText('GRAPHIC TEMPLATES')).toBeInTheDocument();
    });
  });

  test('shows loading state while fetching data', async () => {
    // Make API call take longer
    mockApiService.getCategoryData.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({
        success: true,
        data: {
          success: true,
          category: 'test',
          data: [],
          file: 'test.csv',
          count: 0
        }
      }), 100))
    );

    render(<Categories />);
    
    await waitFor(() => {
      expect(screen.getByText('Loading data...')).toBeInTheDocument();
    });
  });

  test('displays pagination when there are many items', async () => {
    // Mock data with many items
    const manyItems = Array.from({ length: 50 }, (_, i) => ({
      Title: `Test Template ${i}`,
      Author: 'TestAuthor',
      'Page Old': 2,
      'Page New': 1,
      'Order Old': 15,
      'Order New': 8,
      Change: 7,
      Status: 'Up' as const,
      Link: 'https://example.com/test'
    }));

    mockApiService.getCategoryData.mockResolvedValue({
      success: true,
      data: {
        success: true,
        category: 'graphic_templates',
        data: manyItems,
        file: 'test.csv',
        count: 50
      }
    });

    render(<Categories />);
    
    await waitFor(() => {
      expect(screen.getByText('Previous')).toBeInTheDocument();
      expect(screen.getByText('Next')).toBeInTheDocument();
    });
  });
});
