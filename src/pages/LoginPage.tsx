/**
 * Login Page
 * 
 * Main authentication page with OTP flow
 */

import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft, Chrome, Shield, Zap } from 'lucide-react';
import { LoginForm, OTPWaiting, LoginStatus } from '../components/auth';
import { Button } from '../components/ui';
import { useAuth } from '../contexts/AuthContext';
import { useOTP } from '../hooks/useOTP';
import { useExtension } from '../hooks/useExtension';
import type { LoginPageProps } from '../types/auth';

const LoginPage: React.FC<LoginPageProps> = ({
  onSuccess,
  redirectTo = '/',
  showHeader = true,
  showFooter = true
}) => {
  // State
  const [showExtensionInfo, setShowExtensionInfo] = useState(false);

  // Hooks
  const navigate = useNavigate();
  const location = useLocation();
  const { authState, otpFlowState } = useAuth();
  const { waitingState, error: otpError, resetFlow } = useOTP();
  const { status: extensionStatus, enableDevMode } = useExtension();

  // Get return URL from location state
  const returnUrl = (location.state as any)?.from || redirectTo;

  // Redirect if already authenticated
  useEffect(() => {
    if (authState.isAuthenticated) {
      onSuccess?.(authState.user!);
      navigate(returnUrl, { replace: true });
    }
  }, [authState.isAuthenticated, navigate, returnUrl, onSuccess, authState.user]);

  // Handle successful authentication
  const handleAuthSuccess = () => {
    onSuccess?.(authState.user!);
    navigate(returnUrl, { replace: true });
  };

  // Handle auth error
  const handleAuthError = (error: string) => {
    console.error('Authentication error:', error);
  };

  // Handle back to form
  const handleBackToForm = () => {
    resetFlow();
  };

  // Render current step based on OTP flow state
  const renderCurrentStep = () => {
    // Show waiting component if in waiting state
    if (otpFlowState === 'waiting' && waitingState) {
      return (
        <OTPWaiting
          waitingState={waitingState}
          onCancel={handleBackToForm}
          onResend={() => {
            // Resend will be handled by the OTP hook
          }}
        />
      );
    }

    // Show login status if authenticated
    if (authState.isAuthenticated) {
      return (
        <div className="w-full max-w-md mx-auto">
          <LoginStatus
            showDetails={true}
            showExtensionStatus={true}
            showSessionTimer={true}
            onLogout={() => navigate('/login')}
          />
          <div className="mt-6 text-center">
            <Button
              variant="primary"
              size="lg"
              onClick={() => navigate(returnUrl)}
              className="w-full"
            >
              Continue to Dashboard
            </Button>
          </div>
        </div>
      );
    }

    // Default: show login form
    return (
      <LoginForm
        onSuccess={handleAuthSuccess}
        onError={handleAuthError}
        showTitle={true}
        autoFocus={true}
      />
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-theme-seashell via-primary-50 to-primary-100/30">
      {/* Main Content - Centered */}
      <main className="min-h-screen flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-md bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/60 p-8">
          {renderCurrentStep()}
        </div>
      </main>


    </div>
  );
};

export default LoginPage;
