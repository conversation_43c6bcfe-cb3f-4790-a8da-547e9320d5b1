/**
 * Notification Context
 * Global state management for notification system
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import {
  Notification as AppNotification,
  NotificationContextType,
  NotificationFilter,
  NotificationSettings,
  NotificationStats,
  CreateNotificationRequest,
  NotificationEvent
} from '../types/notification';
import notificationService from '../services/notificationService';

// Initial state
const initialFilter: NotificationFilter = {};

const initialSettings: NotificationSettings = {
  enabled: true,
  email: true,
  browser: true,
  sound: true,
  desktop: false,
  dataUpdates: true,
  systemAlerts: true,
  analysisComplete: true,
  fileOperations: true,
  priorities: {
    low: true,
    medium: true,
    high: true,
    urgent: true
  },
  quietHours: {
    enabled: false,
    start: '22:00',
    end: '08:00'
  }
};

const initialStats: NotificationStats = {
  total: 0,
  unread: 0,
  byType: {
    success: 0,
    error: 0,
    warning: 0,
    info: 0,
    data_update: 0,
    system: 0,
    file_upload: 0,
    analysis_complete: 0
  },
  byPriority: {
    low: 0,
    medium: 0,
    high: 0,
    urgent: 0
  },
  todayCount: 0,
  weekCount: 0
};

// State interface
interface NotificationState {
  notifications: AppNotification[];
  unreadCount: number;
  stats: NotificationStats;
  settings: NotificationSettings;
  filter: NotificationFilter;
  isLoading: boolean;
  error: string | null;
}

// Action types
type NotificationAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_NOTIFICATIONS'; payload: AppNotification[] }
  | { type: 'ADD_NOTIFICATION'; payload: AppNotification }
  | { type: 'UPDATE_NOTIFICATION'; payload: { id: string; updates: Partial<AppNotification> } }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'SET_STATS'; payload: NotificationStats }
  | { type: 'SET_SETTINGS'; payload: NotificationSettings }
  | { type: 'SET_FILTER'; payload: NotificationFilter }
  | { type: 'CLEAR_FILTER' }
  | { type: 'MARK_AS_READ'; payload: string }
  | { type: 'MARK_ALL_AS_READ' }
  | { type: 'BULK_UPDATE'; payload: { ids: string[]; updates: Partial<AppNotification> } };

// Initial state
const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  stats: initialStats,
  settings: initialSettings,
  filter: initialFilter,
  isLoading: false,
  error: null
};

// Reducer
function notificationReducer(state: NotificationState, action: NotificationAction): NotificationState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };

    case 'SET_NOTIFICATIONS':
      const unreadCount = action.payload.filter(n => n.status === 'unread').length;
      return {
        ...state,
        notifications: action.payload,
        unreadCount,
        isLoading: false,
        error: null
      };

    case 'ADD_NOTIFICATION':
      const newNotifications = [action.payload, ...state.notifications];
      const newUnreadCount = action.payload.status === 'unread' 
        ? state.unreadCount + 1 
        : state.unreadCount;
      return {
        ...state,
        notifications: newNotifications,
        unreadCount: newUnreadCount
      };

    case 'UPDATE_NOTIFICATION':
      const updatedNotifications = state.notifications.map(notification =>
        notification.id === action.payload.id
          ? { ...notification, ...action.payload.updates }
          : notification
      );
      const updatedUnreadCount = updatedNotifications.filter(n => n.status === 'unread').length;
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: updatedUnreadCount
      };

    case 'REMOVE_NOTIFICATION':
      const filteredNotifications = state.notifications.filter(n => n.id !== action.payload);
      const filteredUnreadCount = filteredNotifications.filter(n => n.status === 'unread').length;
      return {
        ...state,
        notifications: filteredNotifications,
        unreadCount: filteredUnreadCount
      };

    case 'SET_STATS':
      return { ...state, stats: action.payload };

    case 'SET_SETTINGS':
      return { ...state, settings: action.payload };

    case 'SET_FILTER':
      return { ...state, filter: action.payload };

    case 'CLEAR_FILTER':
      return { ...state, filter: initialFilter };

    case 'MARK_AS_READ':
      const readNotifications = state.notifications.map(notification =>
        notification.id === action.payload
          ? { ...notification, status: 'read' as const, readAt: new Date() }
          : notification
      );
      const readUnreadCount = readNotifications.filter(n => n.status === 'unread').length;
      return {
        ...state,
        notifications: readNotifications,
        unreadCount: readUnreadCount
      };

    case 'MARK_ALL_AS_READ':
      const allReadNotifications = state.notifications.map(notification => ({
        ...notification,
        status: 'read' as const,
        readAt: notification.status === 'unread' ? new Date() : notification.readAt
      }));
      return {
        ...state,
        notifications: allReadNotifications,
        unreadCount: 0
      };

    case 'BULK_UPDATE':
      const bulkUpdatedNotifications = state.notifications.map(notification =>
        action.payload.ids.includes(notification.id)
          ? { ...notification, ...action.payload.updates }
          : notification
      );
      const bulkUnreadCount = bulkUpdatedNotifications.filter(n => n.status === 'unread').length;
      return {
        ...state,
        notifications: bulkUpdatedNotifications,
        unreadCount: bulkUnreadCount
      };

    default:
      return state;
  }
}

// Context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Provider component
interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(notificationReducer, initialState);

  // Load initial data
  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        // Load data with graceful degradation
        await Promise.allSettled([
          loadNotifications(),
          loadSettings(),
          loadStats()
        ]);

        // Start real-time event stream (will check availability internally)
        await notificationService.startEventStream();
        notificationService.addEventListener(handleNotificationEvent);
      } catch (error) {
        console.warn('Failed to initialize notifications:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Notification service temporarily unavailable' });
      }
    };

    initializeNotifications();

    return () => {
      notificationService.stopEventStream();
      notificationService.removeEventListener(handleNotificationEvent);
    };
  }, []);

  // Handle real-time events
  const handleNotificationEvent = useCallback((event: NotificationEvent) => {
    switch (event.type) {
      case 'notification_created':
        dispatch({ type: 'ADD_NOTIFICATION', payload: event.data });
        break;
      case 'notification_updated':
        dispatch({ 
          type: 'UPDATE_NOTIFICATION', 
          payload: { id: event.data.id, updates: event.data } 
        });
        break;
      case 'notification_deleted':
        dispatch({ type: 'REMOVE_NOTIFICATION', payload: event.data.id });
        break;
      case 'settings_updated':
        dispatch({ type: 'SET_SETTINGS', payload: event.data });
        break;
    }
  }, []);

  // Load notifications
  const loadNotifications = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const response = await notificationService.getNotifications(state.filter);

      if (response.success && response.data) {
        dispatch({ type: 'SET_NOTIFICATIONS', payload: response.data.notifications });
      } else {
        // Don't show error for service unavailable, just use empty state
        if (response.error?.includes('not available')) {
          dispatch({ type: 'SET_NOTIFICATIONS', payload: [] });
        } else {
          dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to load notifications' });
        }
      }
    } catch (error) {
      console.warn('Failed to load notifications:', error);
      dispatch({ type: 'SET_NOTIFICATIONS', payload: [] });
    }
  };

  // Load settings
  const loadSettings = async () => {
    try {
      const response = await notificationService.getSettings();
      if (response.success && response.data) {
        dispatch({ type: 'SET_SETTINGS', payload: response.data });
      }
      // Use default settings if service unavailable
    } catch (error) {
      console.warn('Failed to load notification settings:', error);
      // Keep default settings
    }
  };

  // Load stats
  const loadStats = async () => {
    try {
      const response = await notificationService.getStats();
      if (response.success && response.data) {
        dispatch({ type: 'SET_STATS', payload: response.data });
      }
      // Use default stats if service unavailable
    } catch (error) {
      console.warn('Failed to load notification stats:', error);
      // Keep default stats
    }
  };

  // Context value
  const contextValue: NotificationContextType = {
    notifications: state.notifications,
    unreadCount: state.unreadCount,
    stats: state.stats,
    settings: state.settings,
    filter: state.filter,
    isLoading: state.isLoading,
    error: state.error,

    createNotification: async (notification: CreateNotificationRequest) => {
      const response = await notificationService.createNotification(notification);
      if (response.success && response.data) {
        dispatch({ type: 'ADD_NOTIFICATION', payload: response.data });
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to create notification' });
      }
    },

    markAsRead: async (id: string) => {
      const response = await notificationService.markAsRead(id);
      if (response.success) {
        dispatch({ type: 'MARK_AS_READ', payload: id });
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to mark as read' });
      }
    },

    markAllAsRead: async () => {
      const response = await notificationService.markAllAsRead();
      if (response.success) {
        dispatch({ type: 'MARK_ALL_AS_READ' });
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to mark all as read' });
      }
    },

    deleteNotification: async (id: string) => {
      const response = await notificationService.deleteNotification(id);
      if (response.success) {
        dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to delete notification' });
      }
    },

    archiveNotification: async (id: string) => {
      const response = await notificationService.archiveNotification(id);
      if (response.success) {
        dispatch({ 
          type: 'UPDATE_NOTIFICATION', 
          payload: { id, updates: { status: 'archived' } } 
        });
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to archive notification' });
      }
    },

    updateSettings: async (settings: Partial<NotificationSettings>) => {
      const response = await notificationService.updateSettings(settings);
      if (response.success) {
        dispatch({ type: 'SET_SETTINGS', payload: { ...state.settings, ...settings } });
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to update settings' });
      }
    },

    setFilter: (filter: Partial<NotificationFilter>) => {
      const newFilter = { ...state.filter, ...filter };
      dispatch({ type: 'SET_FILTER', payload: newFilter });
      // Reload notifications with new filter
      loadNotifications();
    },

    clearFilter: () => {
      dispatch({ type: 'CLEAR_FILTER' });
      loadNotifications();
    },

    refreshNotifications: loadNotifications,

    bulkMarkAsRead: async (ids: string[]) => {
      const response = await notificationService.bulkMarkAsRead(ids);
      if (response.success) {
        dispatch({ 
          type: 'BULK_UPDATE', 
          payload: { ids, updates: { status: 'read', readAt: new Date() } } 
        });
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to mark notifications as read' });
      }
    },

    bulkDelete: async (ids: string[]) => {
      const response = await notificationService.bulkDelete(ids);
      if (response.success) {
        ids.forEach(id => dispatch({ type: 'REMOVE_NOTIFICATION', payload: id }));
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to delete notifications' });
      }
    },

    bulkArchive: async (ids: string[]) => {
      const response = await notificationService.bulkArchive(ids);
      if (response.success) {
        dispatch({ 
          type: 'BULK_UPDATE', 
          payload: { ids, updates: { status: 'archived' } } 
        });
      } else {
        dispatch({ type: 'SET_ERROR', payload: response.error || 'Failed to archive notifications' });
      }
    }
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook to use notification context
export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationContext;
