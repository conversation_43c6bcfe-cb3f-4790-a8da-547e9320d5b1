/**
 * Authentication Context
 * 
 * Provides authentication state and methods throughout the application
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import type {
  AuthState,
  AuthContextType,
  OTPFlowState,
  User
} from '../types/auth';
import { AUTH_STORAGE_KEYS } from '../types/auth';
import { authService } from '../services/authService';
import { extensionService } from '../services/extensionService';

// Initial auth state
const initialAuthState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  sessionToken: null,
  refreshToken: null
};

// Auth action types
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_TOKENS'; payload: { sessionToken: string; refreshToken: string } }
  | { type: 'CLEAR_AUTH' }
  | { type: 'UPDATE_USER'; payload: Partial<User> };

// Auth reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
        error: null
      };
    
    case 'SET_TOKENS':
      return {
        ...state,
        sessionToken: action.payload.sessionToken,
        refreshToken: action.payload.refreshToken
      };
    
    case 'CLEAR_AUTH':
      return {
        ...initialAuthState
      };
    
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null
      };
    
    default:
      return state;
  }
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: React.ReactNode;
}

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, dispatch] = useReducer(authReducer, initialAuthState);
  const [otpFlowState, setOtpFlowState] = React.useState<OTPFlowState>('idle');

  // Load stored auth data on mount
  useEffect(() => {
    loadStoredAuthData();
  }, []);

  // Auto-refresh session before expiry
  useEffect(() => {
    if (authState.isAuthenticated && authState.refreshToken) {
      const refreshInterval = setInterval(() => {
        const timeUntilExpiry = getTimeUntilExpiry();
        // Refresh if less than 1 hour remaining
        if (timeUntilExpiry < 3600) {
          refreshSession();
        }
      }, 60000); // Check every minute

      return () => clearInterval(refreshInterval);
    }
  }, [authState.isAuthenticated, authState.refreshToken]);

  /**
   * Load stored authentication data from localStorage
   */
  const loadStoredAuthData = useCallback(async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      const sessionToken = localStorage.getItem(AUTH_STORAGE_KEYS.SESSION_TOKEN);
      const refreshToken = localStorage.getItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);
      const userData = localStorage.getItem(AUTH_STORAGE_KEYS.USER_DATA);

      if (sessionToken && refreshToken && userData) {
        const user: User = JSON.parse(userData);
        
        // Validate session with backend
        const sessionResult = await authService.getSessionInfo(sessionToken);
        
        if (sessionResult.success && sessionResult.data) {
          dispatch({ type: 'SET_USER', payload: sessionResult.data.user });
          dispatch({ type: 'SET_TOKENS', payload: { sessionToken, refreshToken } });
        } else {
          // Session invalid, try to refresh
          await refreshSession();
        }
      }
    } catch (error) {
      console.error('Error loading stored auth data:', error);
      clearStoredAuthData();
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  /**
   * Store authentication data in localStorage
   */
  const storeAuthData = useCallback((user: User, sessionToken: string, refreshToken: string) => {
    localStorage.setItem(AUTH_STORAGE_KEYS.SESSION_TOKEN, sessionToken);
    localStorage.setItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    localStorage.setItem(AUTH_STORAGE_KEYS.USER_DATA, JSON.stringify(user));
    localStorage.setItem(AUTH_STORAGE_KEYS.LAST_EMAIL, user.email);
  }, []);

  /**
   * Clear stored authentication data
   */
  const clearStoredAuthData = useCallback(() => {
    localStorage.removeItem(AUTH_STORAGE_KEYS.SESSION_TOKEN);
    localStorage.removeItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(AUTH_STORAGE_KEYS.USER_DATA);
    // Keep last email for convenience
  }, []);

  /**
   * Generate OTP for email
   */
  const generateOTP = useCallback(async (email: string) => {
    try {
      setOtpFlowState('generating');
      dispatch({ type: 'SET_ERROR', payload: null });

      const result = await authService.generateOTP(email);

      if (result.success && result.data) {
        setOtpFlowState('waiting');
        
        // Send OTP to Chrome Extension using new pattern
        try {
          const extensionResponse = await extensionService.sendOTPRequestDirect({
            email: result.data.email,
            otp_key: result.data.otp_key,
            otp_code: result.data.otp_code,
            expires_in: result.data.expires_in,
            created_at: result.data.created_at
          });

          if (extensionResponse.action === 'approved') {
            // User approved, validate OTP
            await validateOTP(
              email,
              extensionResponse.otp_code || result.data.otp_code,
              result.data.otp_key
            );
          } else {
            // User rejected
            setOtpFlowState('rejected');
            dispatch({ type: 'SET_ERROR', payload: 'Authentication was rejected' });
          }
        } catch (extensionError) {
          console.error('Extension communication error:', extensionError);
          setOtpFlowState('error');
          dispatch({ 
            type: 'SET_ERROR', 
            payload: 'Chrome Extension not available. Please install the extension.' 
          });
        }
      } else {
        setOtpFlowState('error');
        dispatch({ 
          type: 'SET_ERROR', 
          payload: authService.getErrorMessage(result.code || 'GENERATION_ERROR')
        });
      }
    } catch (error) {
      console.error('Generate OTP error:', error);
      setOtpFlowState('error');
      dispatch({ type: 'SET_ERROR', payload: 'Failed to generate OTP' });
    }
  }, []);

  /**
   * Validate OTP and create session
   */
  const validateOTP = useCallback(async (email: string, otpCode: string, otpKey?: string) => {
    try {
      setOtpFlowState('validating');
      dispatch({ type: 'SET_ERROR', payload: null });

      const result = await authService.validateOTP(email, otpCode, otpKey);

      if (result.success && result.data) {
        const { user, session_token, refresh_token } = result.data;
        
        // Store auth data
        storeAuthData(user, session_token, refresh_token);
        
        // Update state
        dispatch({ type: 'SET_USER', payload: user });
        dispatch({ type: 'SET_TOKENS', payload: { 
          sessionToken: session_token, 
          refreshToken: refresh_token 
        }});
        
        setOtpFlowState('success');
      } else {
        setOtpFlowState('error');
        dispatch({ 
          type: 'SET_ERROR', 
          payload: authService.getErrorMessage(result.code || 'VALIDATION_ERROR')
        });
      }
    } catch (error) {
      console.error('Validate OTP error:', error);
      setOtpFlowState('error');
      dispatch({ type: 'SET_ERROR', payload: 'Failed to validate OTP' });
    }
  }, [storeAuthData]);

  /**
   * Login with email (starts OTP flow)
   */
  const login = useCallback(async (email: string) => {
    await generateOTP(email);
  }, [generateOTP]);

  /**
   * Refresh session
   */
  const refreshSession = useCallback(async () => {
    try {
      if (!authState.refreshToken) {
        throw new Error('No refresh token available');
      }

      const result = await authService.refreshSession(authState.refreshToken);

      if (result.success && result.data) {
        const { user, session_token, refresh_token } = result.data;
        
        // Store new auth data
        storeAuthData(user, session_token, refresh_token);
        
        // Update state
        dispatch({ type: 'SET_USER', payload: user });
        dispatch({ type: 'SET_TOKENS', payload: { 
          sessionToken: session_token, 
          refreshToken: refresh_token 
        }});
      } else {
        // Refresh failed, logout
        await logout();
      }
    } catch (error) {
      console.error('Refresh session error:', error);
      await logout();
    }
  }, [authState.refreshToken]);

  /**
   * Logout
   */
  const logout = useCallback(async () => {
    try {
      if (authState.sessionToken) {
        await authService.logout(authState.sessionToken);
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearStoredAuthData();
      dispatch({ type: 'CLEAR_AUTH' });
      setOtpFlowState('idle');
    }
  }, [authState.sessionToken, clearStoredAuthData]);

  /**
   * Logout from all sessions
   */
  const logoutAll = useCallback(async () => {
    try {
      if (authState.sessionToken) {
        await authService.logoutAll(authState.sessionToken);
      }
    } catch (error) {
      console.error('Logout all error:', error);
    } finally {
      clearStoredAuthData();
      dispatch({ type: 'CLEAR_AUTH' });
      setOtpFlowState('idle');
    }
  }, [authState.sessionToken, clearStoredAuthData]);

  /**
   * Reset OTP flow
   */
  const resetOTPFlow = useCallback(() => {
    setOtpFlowState('idle');
    dispatch({ type: 'SET_ERROR', payload: null });
  }, []);

  /**
   * Check if session is valid
   */
  const isSessionValid = useCallback(() => {
    if (!authState.user || !authState.sessionToken) {
      return false;
    }

    const timeUntilExpiry = getTimeUntilExpiry();
    return timeUntilExpiry > 0;
  }, [authState.user, authState.sessionToken]);

  /**
   * Get time until session expiry
   */
  const getTimeUntilExpiry = useCallback(() => {
    if (!authState.user?.expires_at) {
      return 0;
    }

    const expiryTime = new Date(authState.user.expires_at).getTime();
    const currentTime = Date.now();
    return Math.max(0, Math.floor((expiryTime - currentTime) / 1000));
  }, [authState.user?.expires_at]);

  /**
   * Check authentication status
   */
  const checkAuthStatus = useCallback(async () => {
    if (authState.sessionToken) {
      const result = await authService.getSessionInfo(authState.sessionToken);
      if (!result.success) {
        await logout();
      }
    }
  }, [authState.sessionToken, logout]);

  // Context value
  const contextValue: AuthContextType = {
    authState,
    otpFlowState,
    login,
    logout,
    logoutAll,
    refreshSession,
    generateOTP,
    validateOTP,
    resetOTPFlow,
    isSessionValid,
    getTimeUntilExpiry,
    checkAuthStatus
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
