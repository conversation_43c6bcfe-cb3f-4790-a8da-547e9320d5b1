/**
 * OTP Flow Hook
 * 
 * Custom hook for managing OTP generation, validation, and flow state
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import type {
  OTPFlowState,
  OTPWaitingState
} from '../types/auth';
import { AUTH_CONFIG } from '../types/auth';
import { authService } from '../services/authService';
import { extensionService } from '../services/extensionService';

interface UseOTPOptions {
  onSuccess?: (email: string, sessionData: any) => void;
  onError?: (error: string, code?: string) => void;
  onExtensionError?: () => void;
  autoReset?: boolean;
  resetDelay?: number;
}

interface UseOTPReturn {
  // State
  flowState: OTPFlowState;
  waitingState: OTPWaitingState | null;
  error: string | null;
  isLoading: boolean;
  
  // Actions
  generateOTP: (email: string) => Promise<void>;
  validateOTP: (email: string, otpCode: string, otpKey?: string) => Promise<void>;
  resendOTP: () => Promise<void>;
  cancelOTP: () => void;
  resetFlow: () => void;
  
  // Utilities
  getTimeRemaining: () => number;
  canResend: () => boolean;
  getResendCooldown: () => number;
}

export const useOTP = (options: UseOTPOptions = {}): UseOTPReturn => {
  const {
    onSuccess,
    onError,
    onExtensionError,
    autoReset = false,
    resetDelay = 5000
  } = options;

  // State
  const [flowState, setFlowState] = useState<OTPFlowState>('idle');
  const [waitingState, setWaitingState] = useState<OTPWaitingState | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [lastEmail, setLastEmail] = useState<string>('');
  const [lastResendTime, setLastResendTime] = useState<number>(0);

  // Refs
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Auto-reset flow after success/error
  useEffect(() => {
    if (autoReset && (flowState === 'success' || flowState === 'error')) {
      timeoutRef.current = setTimeout(() => {
        resetFlow();
      }, resetDelay);
    }
  }, [flowState, autoReset, resetDelay]);

  // Update waiting state timer
  useEffect(() => {
    if (waitingState && flowState === 'waiting') {
      intervalRef.current = setInterval(() => {
        setWaitingState(prev => {
          if (!prev) return null;
          
          const now = Date.now();
          const expiresAt = new Date(prev.expires_at).getTime();
          const timeRemaining = Math.max(0, Math.floor((expiresAt - now) / 1000));
          
          if (timeRemaining === 0) {
            setFlowState('expired');
            setError('OTP has expired. Please request a new one.');
            return null;
          }
          
          return {
            ...prev,
            time_remaining: timeRemaining
          };
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [waitingState, flowState]);

  /**
   * Generate OTP for email
   */
  const generateOTP = useCallback(async (email: string) => {
    try {
      setFlowState('generating');
      setError(null);
      setLastEmail(email);

      // Validate email
      if (!authService.validateEmail(email)) {
        throw new Error('Please enter a valid email address');
      }

      // Generate OTP
      const result = await authService.generateOTP(email);

      if (result.success && result.data) {
        // Set waiting state
        const expiresAt = new Date(Date.now() + result.data.expires_in * 1000).toISOString();
        
        setWaitingState({
          email: result.data.email,
          otp_key: result.data.otp_key,
          expires_at: expiresAt,
          time_remaining: result.data.expires_in,
          status: 'waiting',
          can_resend: false,
          resend_cooldown: AUTH_CONFIG.RESEND_COOLDOWN
        });

        setFlowState('waiting');

        // Send to Chrome Extension using new pattern
        try {
          const extensionResponse = await extensionService.sendOTPRequestDirect({
            email: result.data.email,
            otp_key: result.data.otp_key,
            otp_code: result.data.otp_code,
            expires_in: result.data.expires_in,
            created_at: result.data.created_at
          });

          if (extensionResponse.action === 'approved') {
            // User approved, validate OTP
            await validateOTP(
              email,
              extensionResponse.otp_code || result.data.otp_code,
              result.data.otp_key
            );
          } else {
            // User rejected
            setFlowState('rejected');
            setError('Authentication was rejected');
            setWaitingState(null);
          }
        } catch (extensionError) {
          console.error('Extension communication error:', extensionError);
          setFlowState('error');
          setError('Chrome Extension not available. Please install the extension.');
          setWaitingState(null);
          onExtensionError?.();
        }
      } else {
        throw new Error(authService.getErrorMessage(result.code || 'GENERATION_ERROR'));
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate OTP';
      setFlowState('error');
      setError(errorMessage);
      setWaitingState(null);
      onError?.(errorMessage, 'GENERATION_ERROR');
    }
  }, [onError, onExtensionError]);

  /**
   * Validate OTP
   */
  const validateOTP = useCallback(async (email: string, otpCode: string, otpKey?: string) => {
    try {
      setFlowState('validating');
      setError(null);

      const result = await authService.validateOTP(email, otpCode, otpKey);

      if (result.success && result.data) {
        setFlowState('success');
        setWaitingState(null);
        onSuccess?.(email, result.data);
      } else {
        throw new Error(authService.getErrorMessage(result.code || 'VALIDATION_ERROR'));
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to validate OTP';
      setFlowState('error');
      setError(errorMessage);
      setWaitingState(null);
      onError?.(errorMessage, 'VALIDATION_ERROR');
    }
  }, [onSuccess, onError]);

  /**
   * Resend OTP
   */
  const resendOTP = useCallback(async () => {
    if (!lastEmail) {
      setError('No email available for resend');
      return;
    }

    if (!canResend()) {
      setError('Please wait before requesting another OTP');
      return;
    }

    setLastResendTime(Date.now());
    await generateOTP(lastEmail);
  }, [lastEmail, generateOTP]);

  /**
   * Cancel OTP flow
   */
  const cancelOTP = useCallback(() => {
    setFlowState('idle');
    setWaitingState(null);
    setError(null);
    
    // Stop extension listening
    extensionService.stopListening();
  }, []);

  /**
   * Reset flow to initial state
   */
  const resetFlow = useCallback(() => {
    setFlowState('idle');
    setWaitingState(null);
    setError(null);
    setLastEmail('');
    setLastResendTime(0);
    
    // Clear timers
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    
    // Stop extension listening
    extensionService.stopListening();
  }, []);

  /**
   * Get time remaining for current OTP
   */
  const getTimeRemaining = useCallback(() => {
    return waitingState?.time_remaining || 0;
  }, [waitingState?.time_remaining]);

  /**
   * Check if can resend OTP
   */
  const canResend = useCallback(() => {
    if (lastResendTime === 0) return true;
    
    const timeSinceLastResend = (Date.now() - lastResendTime) / 1000;
    return timeSinceLastResend >= AUTH_CONFIG.RESEND_COOLDOWN;
  }, [lastResendTime]);

  /**
   * Get resend cooldown time
   */
  const getResendCooldown = useCallback(() => {
    if (lastResendTime === 0) return 0;
    
    const timeSinceLastResend = (Date.now() - lastResendTime) / 1000;
    return Math.max(0, AUTH_CONFIG.RESEND_COOLDOWN - timeSinceLastResend);
  }, [lastResendTime]);

  // Computed loading state
  const isLoading = flowState === 'generating' || flowState === 'validating';

  return {
    // State
    flowState,
    waitingState,
    error,
    isLoading,
    
    // Actions
    generateOTP,
    validateOTP,
    resendOTP,
    cancelOTP,
    resetFlow,
    
    // Utilities
    getTimeRemaining,
    canResend,
    getResendCooldown
  };
};

export default useOTP;
