/**
 * Chrome Extension Hook
 * 
 * Custom hook for managing Chrome Extension communication and status
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { extensionService } from '../services/extensionService';
import type { OTPResponse } from '../types/auth';

interface UseExtensionOptions {
  autoCheck?: boolean;
  checkInterval?: number;
  onResponse?: (response: OTPResponse) => void;
  onError?: (error: string) => void;
}

interface ExtensionStatus {
  available: boolean;
  connected: boolean;
  version?: string;
  id?: string;
  lastChecked?: Date;
}

interface UseExtensionReturn {
  // Status
  status: ExtensionStatus;
  isChecking: boolean;
  error: string | null;
  
  // Actions
  checkConnection: () => Promise<boolean>;
  sendOTPRequest: (otpData: any) => Promise<OTPResponse>;
  startListening: () => void;
  stopListening: () => void;
  
  // Utilities
  isExtensionReady: () => boolean;
  getExtensionInfo: () => Promise<any>;
  refreshStatus: () => Promise<void>;
}

export const useExtension = (options: UseExtensionOptions = {}): UseExtensionReturn => {
  const {
    autoCheck = true,
    checkInterval = 30000, // 30 seconds
    onResponse,
    onError
  } = options;

  // State
  const [status, setStatus] = useState<ExtensionStatus>({
    available: false,
    connected: false
  });
  const [isChecking, setIsChecking] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isListening, setIsListening] = useState(false);

  // Refs
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const responseCallbackRef = useRef<((response: OTPResponse) => void) | null>(null);

  // Update response callback ref
  useEffect(() => {
    responseCallbackRef.current = onResponse || null;
  }, [onResponse]);

  // Initial check and auto-check setup
  useEffect(() => {
    if (autoCheck) {
      checkConnection();
      
      // Set up periodic checks
      intervalRef.current = setInterval(() => {
        checkConnection();
      }, checkInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoCheck, checkInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopListening();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  /**
   * Check Chrome Extension connection
   */
  const checkConnection = useCallback(async (): Promise<boolean> => {
    try {
      setIsChecking(true);
      setError(null);

      const available = extensionService.isExtensionAvailable();
      
      if (!available) {
        setStatus({
          available: false,
          connected: false,
          lastChecked: new Date()
        });
        return false;
      }

      const connected = await extensionService.checkExtensionAvailability();
      
      // Get extension info if connected
      let extensionInfo = {};
      if (connected) {
        try {
          extensionInfo = await extensionService.getExtensionInfo();
        } catch (err) {
          console.warn('Failed to get extension info:', err);
        }
      }

      setStatus({
        available,
        connected,
        version: extensionInfo.version,
        id: extensionInfo.id,
        lastChecked: new Date()
      });

      return connected;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Extension check failed';
      setError(errorMessage);
      onError?.(errorMessage);
      
      setStatus({
        available: false,
        connected: false,
        lastChecked: new Date()
      });
      
      return false;
    } finally {
      setIsChecking(false);
    }
  }, [onError]);

  /**
   * Send OTP request to Chrome Extension
   */
  const sendOTPRequest = useCallback(async (otpData: any): Promise<OTPResponse> => {
    try {
      setError(null);

      if (!status.available) {
        throw new Error('Chrome Extension not available');
      }

      if (!status.connected) {
        // Try to reconnect
        const connected = await checkConnection();
        if (!connected) {
          throw new Error('Chrome Extension not connected');
        }
      }

      const response = await extensionService.sendOTPRequestDirect(otpData);
      
      // Notify callback if provided
      if (responseCallbackRef.current) {
        responseCallbackRef.current(response);
      }
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send OTP request';
      setError(errorMessage);
      onError?.(errorMessage);
      throw err;
    }
  }, [status.available, status.connected, checkConnection, onError]);

  /**
   * Start listening for extension responses
   */
  const startListening = useCallback(() => {
    if (isListening) return;

    const responseHandler = (response: OTPResponse) => {
      if (responseCallbackRef.current) {
        responseCallbackRef.current(response);
      }
    };

    extensionService.listenForResponses(responseHandler);
    setIsListening(true);
  }, [isListening]);

  /**
   * Stop listening for extension responses
   */
  const stopListening = useCallback(() => {
    if (!isListening) return;

    extensionService.stopListening();
    setIsListening(false);
  }, [isListening]);

  /**
   * Check if extension is ready for use
   */
  const isExtensionReady = useCallback(() => {
    return status.available && status.connected;
  }, [status.available, status.connected]);

  /**
   * Get extension information
   */
  const getExtensionInfo = useCallback(async () => {
    try {
      return await extensionService.getExtensionInfo();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get extension info';
      setError(errorMessage);
      onError?.(errorMessage);
      throw err;
    }
  }, [onError]);

  /**
   * Force refresh extension status
   */
  const refreshStatus = useCallback(async () => {
    await checkConnection();
  }, [checkConnection]);

  return {
    // Status
    status,
    isChecking,
    error,

    // Actions
    checkConnection,
    sendOTPRequest,
    startListening,
    stopListening,

    // Utilities
    isExtensionReady,
    getExtensionInfo,
    refreshStatus
  };
};

export default useExtension;
