/**
 * Performance Monitoring Hook
 * 
 * This hook provides utilities for monitoring and optimizing performance
 * including Web Vitals, component render times, and memory usage.
 */

import { useEffect, useRef, useCallback, useState } from 'react';

// Performance metrics interface
interface PerformanceMetrics {
  renderTime: number;
  componentName: string;
  timestamp: number;
}

interface WebVitals {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

// Performance observer for Web Vitals
const observeWebVitals = (callback: (vitals: WebVitals) => void) => {
  const vitals: WebVitals = {};

  // Observe FCP and LCP
  if ('PerformanceObserver' in window) {
    try {
      const paintObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            vitals.fcp = entry.startTime;
          }
        }
        callback(vitals);
      });
      paintObserver.observe({ entryTypes: ['paint'] });

      const lcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          vitals.lcp = entry.startTime;
        }
        callback(vitals);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          vitals.fid = (entry as any).processingStart - entry.startTime;
        }
        callback(vitals);
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        vitals.cls = clsValue;
        callback(vitals);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });

    } catch (error) {
      console.warn('Performance Observer not supported:', error);
    }
  }

  // TTFB from Navigation Timing
  if ('performance' in window && 'getEntriesByType' in performance) {
    const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
    if (navigationEntries.length > 0) {
      vitals.ttfb = navigationEntries[0].responseStart - navigationEntries[0].requestStart;
      callback(vitals);
    }
  }
};

// Safe performance.now() wrapper
const safePerformanceNow = (): number => {
  if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
    return performance.now();
  }
  // Fallback to Date.now() if performance.now() is not available
  return Date.now();
};

// Hook for component render time measurement
export const useRenderTime = (componentName: string) => {
  const renderStartTime = useRef<number>(0);
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const lastRenderTime = useRef<number>(0);

  // Record start time on each render
  renderStartTime.current = safePerformanceNow();

  useEffect(() => {
    const renderTime = safePerformanceNow() - renderStartTime.current;
    lastRenderTime.current = renderTime;

    const metric: PerformanceMetrics = {
      renderTime,
      componentName,
      timestamp: Date.now(),
    };

    setMetrics(prev => [...prev.slice(-9), metric]); // Keep last 10 measurements

    // Log slow renders (> 16ms for 60fps) - only in development
    if (renderTime > 16 && process.env.NODE_ENV === 'development') {
      console.warn(`Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
    }
  }, []); // Empty dependency array to prevent infinite loop

  return {
    metrics,
    averageRenderTime: metrics.length > 0
      ? metrics.reduce((sum, m) => sum + m.renderTime, 0) / metrics.length
      : 0,
    lastRenderTime: lastRenderTime.current,
  };
};

// Hook for Web Vitals monitoring
export const useWebVitals = () => {
  const [vitals, setVitals] = useState<WebVitals>({});

  useEffect(() => {
    observeWebVitals(setVitals);
  }, []);

  return vitals;
};

// Hook for memory usage monitoring
export const useMemoryUsage = () => {
  const [memoryInfo, setMemoryInfo] = useState<{
    usedJSHeapSize?: number;
    totalJSHeapSize?: number;
    jsHeapSizeLimit?: number;
  }>({});

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMemoryInfo({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        });
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
};

// Hook for debounced performance-sensitive operations
export const useDebounce = <T extends any[]>(
  callback: (...args: T) => void,
  delay: number
) => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedCallback = useCallback((...args: T) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
};

// Hook for throttled performance-sensitive operations
export const useThrottle = <T extends any[]>(
  callback: (...args: T) => void,
  delay: number
) => {
  const lastRun = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const throttledCallback = useCallback((...args: T) => {
    const now = Date.now();
    
    if (now - lastRun.current >= delay) {
      callback(...args);
      lastRun.current = now;
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
        lastRun.current = Date.now();
      }, delay - (now - lastRun.current));
    }
  }, [callback, delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return throttledCallback;
};

// Hook for intersection observer (lazy loading)
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
      if (entry.isIntersecting && !hasIntersected) {
        setHasIntersected(true);
      }
    }, options);

    observer.observe(element);

    return () => observer.disconnect();
  }, [options, hasIntersected]);

  return { elementRef, isIntersecting, hasIntersected };
};

// Performance utilities
export const performanceUtils = {
  // Mark performance milestones
  mark: (name: string) => {
    if (typeof performance !== 'undefined' && typeof performance.mark === 'function') {
      try {
        performance.mark(name);
      } catch (error) {
        console.warn('Performance mark failed:', error);
      }
    }
  },

  // Measure performance between marks
  measure: (name: string, startMark: string, endMark?: string) => {
    if (typeof performance !== 'undefined' && typeof performance.measure === 'function') {
      try {
        performance.measure(name, startMark, endMark);
        const measures = performance.getEntriesByName(name, 'measure');
        return measures[measures.length - 1]?.duration || 0;
      } catch (error) {
        console.warn('Performance measure failed:', error);
        return 0;
      }
    }
    return 0;
  },

  // Clear performance entries
  clear: () => {
    if (typeof performance !== 'undefined' && typeof performance.clearMarks === 'function') {
      try {
        performance.clearMarks();
        performance.clearMeasures();
      } catch (error) {
        console.warn('Performance clear failed:', error);
      }
    }
  },
};

export default {
  useRenderTime,
  useWebVitals,
  useMemoryUsage,
  useDebounce,
  useThrottle,
  useIntersectionObserver,
  performanceUtils,
};
