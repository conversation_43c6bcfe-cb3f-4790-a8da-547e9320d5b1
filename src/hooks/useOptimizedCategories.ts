import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { apiService, CategoryData, CategoriesListResponse, CategoryDataResponse } from '../services/api';

/**
 * Optimized Categories Hook dengan React Query
 * 
 * Implementasi caching layer yang powerful untuk mengatasi performance issues
 * pada Categories.tsx component.
 * 
 * Features:
 * - Automatic caching dengan smart invalidation
 * - Background refetching untuk fresh data
 * - Optimistic updates untuk better UX
 * - Error handling dengan retry mechanism
 * - Offline support dengan stale data
 * - Prefetching untuk anticipated data needs
 */

// Query keys untuk consistent caching
export const categoryQueryKeys = {
  all: ['categories'] as const,
  lists: () => [...categoryQueryKeys.all, 'list'] as const,
  list: (filters: string) => [...categoryQueryKeys.lists(), { filters }] as const,
  details: () => [...categoryQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...categoryQueryKeys.details(), id] as const,
  stats: (id: string) => [...categoryQueryKeys.detail(id), 'stats'] as const,
  authors: (id: string) => [...categoryQueryKeys.detail(id), 'authors'] as const,
};

/**
 * Hook untuk mendapatkan daftar categories dengan caching
 */
export const useCategories = () => {
  return useQuery({
    queryKey: categoryQueryKeys.lists(),
    queryFn: async (): Promise<string[]> => {
      const response = await apiService.getCategories();
      if (response.success && response.data) {
        return response.data.categories;
      }
      throw new Error(response.error || 'Failed to load categories');
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // No placeholder data - let errors be handled by UI
  });
};

/**
 * Hook untuk mendapatkan data category dengan advanced caching
 */
export const useCategoryData = (categoryName: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: categoryQueryKeys.detail(categoryName),
    queryFn: async (): Promise<CategoryData[]> => {
      const response = await apiService.getCategoryData(categoryName);
      if (response.success && response.data) {
        return response.data.data;
      }
      throw new Error(response.error || 'Failed to load category data');
    },
    enabled: enabled && !!categoryName,
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    // Background refetch untuk fresh data
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });
};

/**
 * Hook untuk prefetch category data (anticipatory loading)
 */
export const usePrefetchCategoryData = () => {
  const queryClient = useQueryClient();

  const prefetchCategory = async (categoryName: string) => {
    await queryClient.prefetchQuery({
      queryKey: categoryQueryKeys.detail(categoryName),
      queryFn: async () => {
        const response = await apiService.getCategoryData(categoryName);
        if (response.success && response.data) {
          return response.data.data;
        }
        throw new Error(response.error || 'Failed to load category data');
      },
      staleTime: 2 * 60 * 1000,
    });
  };

  return { prefetchCategory };
};

/**
 * Hook untuk optimistic updates pada category data
 */
export const useCategoryMutations = () => {
  const queryClient = useQueryClient();

  const updateCategoryData = useMutation({
    mutationFn: async ({ categoryName, newData }: { categoryName: string; newData: CategoryData[] }) => {
      // Simulate API call untuk update data
      // Dalam implementasi nyata, ini akan memanggil API endpoint
      return { categoryName, data: newData };
    },
    onMutate: async ({ categoryName, newData }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: categoryQueryKeys.detail(categoryName) });

      // Snapshot previous value
      const previousData = queryClient.getQueryData(categoryQueryKeys.detail(categoryName));

      // Optimistically update to new value
      queryClient.setQueryData(categoryQueryKeys.detail(categoryName), newData);

      return { previousData, categoryName };
    },
    onError: (err, variables, context) => {
      // Rollback pada error
      if (context?.previousData) {
        queryClient.setQueryData(
          categoryQueryKeys.detail(context.categoryName),
          context.previousData
        );
      }
    },
    onSettled: (data, error, variables) => {
      // Refetch untuk sync dengan server
      queryClient.invalidateQueries({ queryKey: categoryQueryKeys.detail(variables.categoryName) });
    },
  });

  return { updateCategoryData };
};

/**
 * Hook untuk bulk operations dengan batch updates
 */
export const useBulkCategoryOperations = () => {
  const queryClient = useQueryClient();

  const bulkUpdateCategories = useMutation({
    mutationFn: async (updates: Array<{ categoryName: string; data: CategoryData[] }>) => {
      // Batch API calls
      const promises = updates.map(update => 
        apiService.getCategoryData(update.categoryName)
      );
      return Promise.all(promises);
    },
    onSuccess: (results, variables) => {
      // Update cache untuk semua categories yang di-update
      variables.forEach((update, index) => {
        const result = results[index];
        if (result.success && result.data) {
          queryClient.setQueryData(
            categoryQueryKeys.detail(update.categoryName),
            result.data.data
          );
        }
      });
    },
  });

  return { bulkUpdateCategories };
};

/**
 * Hook untuk cache management dan cleanup
 */
export const useCacheManagement = () => {
  const queryClient = useQueryClient();

  const clearCategoryCache = (categoryName?: string) => {
    if (categoryName) {
      queryClient.removeQueries({ queryKey: categoryQueryKeys.detail(categoryName) });
    } else {
      queryClient.removeQueries({ queryKey: categoryQueryKeys.all });
    }
  };

  const refreshCategoryData = async (categoryName: string) => {
    await queryClient.refetchQueries({ queryKey: categoryQueryKeys.detail(categoryName) });
  };

  const getCachedCategoryData = (categoryName: string): CategoryData[] | undefined => {
    return queryClient.getQueryData(categoryQueryKeys.detail(categoryName));
  };

  const preloadAllCategories = async (categories: string[]) => {
    const promises = categories.map(category =>
      queryClient.prefetchQuery({
        queryKey: categoryQueryKeys.detail(category),
        queryFn: async () => {
          const response = await apiService.getCategoryData(category);
          if (response.success && response.data) {
            return response.data.data;
          }
          throw new Error('Failed to load category data');
        },
      })
    );
    await Promise.all(promises);
  };

  return {
    clearCategoryCache,
    refreshCategoryData,
    getCachedCategoryData,
    preloadAllCategories,
  };
};

/**
 * Performance monitoring hook
 */
export const useCategoryPerformance = () => {
  const queryClient = useQueryClient();

  const getPerformanceMetrics = () => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    const categoryQueries = queries.filter(query => 
      query.queryKey[0] === 'categories'
    );

    return {
      totalQueries: categoryQueries.length,
      cacheHitRate: categoryQueries.filter(q => q.state.data).length / categoryQueries.length,
      staleCacheCount: categoryQueries.filter(q => q.isStale()).length,
      errorCount: categoryQueries.filter(q => q.state.error).length,
      lastUpdated: Math.max(...categoryQueries.map(q => q.state.dataUpdatedAt || 0)),
    };
  };

  return { getPerformanceMetrics };
};

export default useCategories;
