import { useEffect, useRef, useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { categoryQueryKeys } from './useOptimizedCategories';
import { CategoryData } from '../services/api';

/**
 * Real-time Data Hook dengan WebSocket
 * 
 * Implementasi real-time updates untuk Categories data menggunakan WebSocket.
 * Menyediakan live synchronization dengan server dan collaborative features.
 * 
 * Features:
 * - Real-time data synchronization
 * - Automatic reconnection dengan exponential backoff
 * - Optimistic updates dengan conflict resolution
 * - Collaborative editing support
 * - Connection status monitoring
 * - Message queuing untuk offline scenarios
 * - Rate limiting untuk prevent spam
 */

interface WebSocketMessage {
  type: 'CATEGORY_UPDATE' | 'CATEGORY_DELETE' | 'BULK_UPDATE' | 'USER_ACTIVITY' | 'HEARTBEAT';
  payload: any;
  timestamp: number;
  userId?: string;
  sessionId?: string;
}

interface ConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  lastConnected: number | null;
  reconnectAttempts: number;
  error: string | null;
}

interface UseRealTimeDataOptions {
  wsUrl?: string;
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
  heartbeatInterval?: number;
  enableOptimisticUpdates?: boolean;
  enableCollaboration?: boolean;
}

export const useRealTimeData = (options: UseRealTimeDataOptions = {}) => {
  const {
    wsUrl = 'ws://localhost:5001/ws',
    maxReconnectAttempts = 5,
    reconnectInterval = 1000,
    heartbeatInterval = 30000,
    enableOptimisticUpdates = true,
    enableCollaboration = false
  } = options;

  const queryClient = useQueryClient();
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const messageQueueRef = useRef<WebSocketMessage[]>([]);

  const [connectionState, setConnectionState] = useState<ConnectionState>({
    isConnected: false,
    isConnecting: false,
    lastConnected: null,
    reconnectAttempts: 0,
    error: null
  });

  const [activeUsers, setActiveUsers] = useState<Array<{
    userId: string;
    username: string;
    lastActivity: number;
    currentCategory?: string;
  }>>([]);

  // Generate unique session ID
  const sessionId = useRef(`session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.CONNECTING || 
        wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('🔗 WebSocket connected');
        setConnectionState({
          isConnected: true,
          isConnecting: false,
          lastConnected: Date.now(),
          reconnectAttempts: 0,
          error: null
        });

        // Send initial handshake
        sendMessage({
          type: 'USER_ACTIVITY',
          payload: {
            action: 'connect',
            sessionId: sessionId.current,
            timestamp: Date.now()
          },
          timestamp: Date.now()
        });

        // Process queued messages
        processMessageQueue();

        // Start heartbeat
        startHeartbeat();
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleIncomingMessage(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', event.code, event.reason);
        setConnectionState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false,
          error: event.reason || 'Connection closed'
        }));

        stopHeartbeat();

        // Attempt reconnection
        if (connectionState.reconnectAttempts < maxReconnectAttempts) {
          scheduleReconnect();
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        setConnectionState(prev => ({
          ...prev,
          error: 'Connection error',
          isConnecting: false
        }));
      };

    } catch (error) {
      setConnectionState(prev => ({
        ...prev,
        isConnecting: false,
        error: `Failed to connect: ${error}`
      }));
    }
  }, [wsUrl, maxReconnectAttempts, connectionState.reconnectAttempts]);

  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    stopHeartbeat();

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setConnectionState({
      isConnected: false,
      isConnecting: false,
      lastConnected: null,
      reconnectAttempts: 0,
      error: null
    });
  }, []);

  // Schedule reconnection dengan exponential backoff
  const scheduleReconnect = useCallback(() => {
    const delay = reconnectInterval * Math.pow(2, connectionState.reconnectAttempts);
    
    setConnectionState(prev => ({
      ...prev,
      reconnectAttempts: prev.reconnectAttempts + 1
    }));

    reconnectTimeoutRef.current = setTimeout(() => {
      console.log(`🔄 Attempting reconnection (${connectionState.reconnectAttempts + 1}/${maxReconnectAttempts})`);
      connect();
    }, delay);
  }, [connect, reconnectInterval, connectionState.reconnectAttempts, maxReconnectAttempts]);

  // Send message to WebSocket
  const sendMessage = useCallback((message: Omit<WebSocketMessage, 'timestamp'>) => {
    const fullMessage: WebSocketMessage = {
      ...message,
      timestamp: Date.now(),
      sessionId: sessionId.current
    };

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(fullMessage));
    } else {
      // Queue message untuk dikirim nanti
      messageQueueRef.current.push(fullMessage);
    }
  }, []);

  // Process queued messages
  const processMessageQueue = useCallback(() => {
    while (messageQueueRef.current.length > 0) {
      const message = messageQueueRef.current.shift();
      if (message && wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify(message));
      }
    }
  }, []);

  // Handle incoming messages
  const handleIncomingMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'CATEGORY_UPDATE':
        handleCategoryUpdate(message.payload);
        break;

      case 'CATEGORY_DELETE':
        handleCategoryDelete(message.payload);
        break;

      case 'BULK_UPDATE':
        handleBulkUpdate(message.payload);
        break;

      case 'USER_ACTIVITY':
        if (enableCollaboration) {
          handleUserActivity(message.payload);
        }
        break;

      case 'HEARTBEAT':
        // Respond to heartbeat
        sendMessage({ type: 'HEARTBEAT', payload: { pong: true } });
        break;

      default:
        console.log('Unknown message type:', message.type);
    }
  }, [enableCollaboration]);

  // Handle category update
  const handleCategoryUpdate = useCallback((payload: {
    categoryName: string;
    data: CategoryData[];
    userId?: string;
    changeType: 'add' | 'update' | 'delete';
  }) => {
    const { categoryName, data, userId, changeType } = payload;

    // Skip updates dari user yang sama (untuk prevent loops)
    if (userId === sessionId.current) return;

    // Update cache dengan data baru
    queryClient.setQueryData(
      categoryQueryKeys.detail(categoryName),
      data
    );

    // Invalidate related queries
    queryClient.invalidateQueries({
      queryKey: categoryQueryKeys.stats(categoryName)
    });

    console.log(`📊 Real-time update: ${changeType} in ${categoryName}`);
  }, [queryClient]);

  // Handle category delete
  const handleCategoryDelete = useCallback((payload: {
    categoryName: string;
    userId?: string;
  }) => {
    const { categoryName, userId } = payload;

    if (userId === sessionId.current) return;

    // Remove dari cache
    queryClient.removeQueries({
      queryKey: categoryQueryKeys.detail(categoryName)
    });

    console.log(`🗑️ Real-time delete: ${categoryName}`);
  }, [queryClient]);

  // Handle bulk update
  const handleBulkUpdate = useCallback((payload: {
    updates: Array<{ categoryName: string; data: CategoryData[] }>;
    userId?: string;
  }) => {
    const { updates, userId } = payload;

    if (userId === sessionId.current) return;

    updates.forEach(({ categoryName, data }) => {
      queryClient.setQueryData(
        categoryQueryKeys.detail(categoryName),
        data
      );
    });

    console.log(`📦 Real-time bulk update: ${updates.length} categories`);
  }, [queryClient]);

  // Handle user activity (untuk collaboration)
  const handleUserActivity = useCallback((payload: {
    userId: string;
    username: string;
    action: string;
    categoryName?: string;
    timestamp: number;
  }) => {
    const { userId, username, action, categoryName, timestamp } = payload;

    setActiveUsers(prev => {
      const filtered = prev.filter(user => user.userId !== userId);
      
      if (action === 'disconnect') {
        return filtered;
      }

      return [
        ...filtered,
        {
          userId,
          username,
          lastActivity: timestamp,
          currentCategory: categoryName
        }
      ];
    });
  }, []);

  // Start heartbeat
  const startHeartbeat = useCallback(() => {
    heartbeatTimeoutRef.current = setInterval(() => {
      sendMessage({
        type: 'HEARTBEAT',
        payload: { ping: true }
      });
    }, heartbeatInterval);
  }, [sendMessage, heartbeatInterval]);

  // Stop heartbeat
  const stopHeartbeat = useCallback(() => {
    if (heartbeatTimeoutRef.current) {
      clearInterval(heartbeatTimeoutRef.current);
      heartbeatTimeoutRef.current = null;
    }
  }, []);

  // Public API methods
  const broadcastCategoryUpdate = useCallback((
    categoryName: string,
    data: CategoryData[],
    changeType: 'add' | 'update' | 'delete' = 'update'
  ) => {
    sendMessage({
      type: 'CATEGORY_UPDATE',
      payload: {
        categoryName,
        data,
        userId: sessionId.current,
        changeType
      }
    });
  }, [sendMessage]);

  const broadcastUserActivity = useCallback((
    action: string,
    categoryName?: string
  ) => {
    if (!enableCollaboration) return;

    sendMessage({
      type: 'USER_ACTIVITY',
      payload: {
        userId: sessionId.current,
        username: 'Current User', // Bisa diambil dari auth context
        action,
        categoryName,
        timestamp: Date.now()
      }
    });
  }, [sendMessage, enableCollaboration]);

  // Initialize connection
  useEffect(() => {
    connect();
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Cleanup pada unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      stopHeartbeat();
    };
  }, [stopHeartbeat]);

  return {
    // Connection state
    connectionState,
    activeUsers,
    
    // Connection management
    connect,
    disconnect,
    
    // Broadcasting
    broadcastCategoryUpdate,
    broadcastUserActivity,
    
    // Raw message sending
    sendMessage,
    
    // Status
    isConnected: connectionState.isConnected,
    isConnecting: connectionState.isConnecting,
    error: connectionState.error
  };
};

export default useRealTimeData;
