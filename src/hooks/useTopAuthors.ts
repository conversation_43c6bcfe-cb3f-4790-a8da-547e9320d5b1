import { useMemo } from 'react';
import { CategoryData, AuthorData } from '../services/api';

/**
 * Top Author Interface - Compatible with AuthorData
 */
export interface TopAuthor extends AuthorData {
  points: number; // Make points required for our calculations
}

/**
 * useTopAuthors Hook
 * 
 * Extracts top authors calculation logic from Categories.tsx component.
 * Provides memoized calculation of top authors with complex point system.
 * 
 * Features:
 * - Memoized calculations for performance
 * - Complex point system (Up: +3, Down: -1, Same: +1)
 * - Filters out unknown/empty authors
 * - Sorts by points and assigns ranks
 * - Reusable across components
 * 
 * @param categoryData - Array of category data items
 * @returns Array of TopAuthor objects sorted by points
 */
export const useTopAuthors = (categoryData: CategoryData[]): TopAuthor[] => {
  // ✅ PERFORMANCE OPTIMIZATION: Memoized top authors calculation
  const memoizedTopAuthors = useMemo(() => {
    if (!categoryData || categoryData.length === 0) return [];

    // Calculate author statistics with point system
    const authorStats = categoryData.reduce((acc, item) => {
      const author = item.Author;
      if (!author || author === 'Unknown Author' || author.trim() === '') {
        return acc;
      }

      if (!acc[author]) {
        acc[author] = { count: 0, totalPoints: 0 };
      }

      acc[author].count += 1;

      // Point system: Up = +3, Down = -1, Same = +1
      switch (item.Status) {
        case 'Up':
          acc[author].totalPoints += 3;
          break;
        case 'Down':
          acc[author].totalPoints -= 1;
          break;
        case 'Same':
          acc[author].totalPoints += 1;
          break;
        default:
          // No points for unknown status
          break;
      }

      return acc;
    }, {} as Record<string, { count: number; totalPoints: number }>);

    // Convert to array and sort by points
    const authorsArray = Object.entries(authorStats)
      .map(([author, stats], index) => ({
        author,
        count: stats.count,
        points: stats.totalPoints,
        rank: index + 1
      }))
      .filter(item => item.author && item.author !== 'Unknown Author' && item.author.trim() !== '')
      .sort((a, b) => b.points - a.points)
      .map((item, index) => ({ ...item, rank: index + 1 }));

    return authorsArray;
  }, [categoryData]);

  return memoizedTopAuthors;
};

/**
 * calculateTopAuthorsFromCategorySync - Synchronous version for backward compatibility
 * 
 * Calculates top authors synchronously for any given data.
 * Used for cases where memoization isn't needed or for testing.
 * 
 * @param categoryData - Array of category data items
 * @returns Array of TopAuthor objects sorted by points
 */
export const calculateTopAuthorsFromCategorySync = (categoryData: CategoryData[]): TopAuthor[] => {
  if (!categoryData || categoryData.length === 0) return [];

  // Calculate author statistics with point system
  const authorStats = categoryData.reduce((acc, item) => {
    const author = item.Author;
    if (!author || author === 'Unknown Author' || author.trim() === '') {
      return acc;
    }

    if (!acc[author]) {
      acc[author] = { count: 0, totalPoints: 0 };
    }

    acc[author].count += 1;

    // Point system: Up = +3, Down = -1, Same = +1
    switch (item.Status) {
      case 'Up':
        acc[author].totalPoints += 3;
        break;
      case 'Down':
        acc[author].totalPoints -= 1;
        break;
      case 'Same':
        acc[author].totalPoints += 1;
        break;
      default:
        // No points for unknown status
        break;
    }

    return acc;
  }, {} as Record<string, { count: number; totalPoints: number }>);

  // Convert to array and sort by points
  const authorsArray = Object.entries(authorStats)
    .map(([author, stats], index) => ({
      author,
      count: stats.count,
      points: stats.totalPoints,
      rank: index + 1
    }))
    .filter(item => item.author && item.author !== 'Unknown Author' && item.author.trim() !== '')
    .sort((a, b) => b.points - a.points)
    .map((item, index) => ({ ...item, rank: index + 1 }));

  return authorsArray;
};

export default useTopAuthors;
