import { useState, useEffect, useCallback } from 'react';

// Types
interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime_seconds: number;
  components: {
    [key: string]: {
      status: 'healthy' | 'degraded' | 'unhealthy' | 'disabled';
      [key: string]: any;
    };
  };
  alerts: string[];
}

interface PerformanceMetrics {
  cache_performance?: {
    hit_rate: number;
    total_requests: number;
  };
  system_performance?: {
    cpu: { percent: number };
    memory: { percent: number };
    error?: string;
  };
  timestamp: string;
}

interface AlertsData {
  alerts: string[];
  status: string;
  timestamp: string;
}

interface MonitoringState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

// Generic monitoring hook
const useMonitoringData = <T>(
  endpoint: string,
  refreshInterval: number = 30000, // 30 seconds default
  autoRefresh: boolean = true
): MonitoringState<T> & { refresh: () => void } => {
  const [state, setState] = useState<MonitoringState<T>>({
    data: null,
    loading: true,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const response = await fetch(endpoint);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      setState({
        data,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      
      console.error(`Error fetching ${endpoint}:`, error);
    }
  }, [endpoint]);

  // Auto-refresh effect
  useEffect(() => {
    if (!autoRefresh) return;

    // Initial fetch
    fetchData();

    // Set up interval
    const interval = setInterval(fetchData, refreshInterval);

    return () => clearInterval(interval);
  }, [fetchData, refreshInterval, autoRefresh]);

  return {
    ...state,
    refresh: fetchData,
  };
};

// Specific monitoring hooks
export const useSystemHealth = (autoRefresh: boolean = true) => {
  return useMonitoringData<SystemHealth>(
    '/api/monitoring/health',
    30000, // 30 seconds
    autoRefresh
  );
};

export const usePerformanceMetrics = (autoRefresh: boolean = true) => {
  return useMonitoringData<PerformanceMetrics>(
    '/api/monitoring/performance',
    15000, // 15 seconds for more frequent performance updates
    autoRefresh
  );
};

export const useSystemAlerts = (autoRefresh: boolean = true) => {
  return useMonitoringData<AlertsData>(
    '/api/monitoring/alerts',
    30000, // 30 seconds
    autoRefresh
  );
};

// Combined monitoring hook for dashboard
export const useMonitoringDashboard = (autoRefresh: boolean = true) => {
  const health = useSystemHealth(autoRefresh);
  const performance = usePerformanceMetrics(autoRefresh);
  const alerts = useSystemAlerts(autoRefresh);

  const refreshAll = useCallback(() => {
    health.refresh();
    performance.refresh();
    alerts.refresh();
  }, [health.refresh, performance.refresh, alerts.refresh]);

  const isLoading = health.loading || performance.loading || alerts.loading;
  const hasError = health.error || performance.error || alerts.error;
  
  const lastUpdated = [health.lastUpdated, performance.lastUpdated, alerts.lastUpdated]
    .filter(Boolean)
    .sort((a, b) => (b?.getTime() || 0) - (a?.getTime() || 0))[0];

  return {
    health,
    performance,
    alerts,
    refreshAll,
    isLoading,
    hasError,
    lastUpdated,
  };
};

// Hook for monitoring configuration
export const useMonitoringConfig = () => {
  const [config, setConfig] = useState({
    autoRefresh: true,
    refreshInterval: 30000,
    enableNotifications: true,
  });

  const updateConfig = useCallback((updates: Partial<typeof config>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  }, []);

  return {
    config,
    updateConfig,
  };
};

// Export types for use in components
export type { SystemHealth, PerformanceMetrics, AlertsData, MonitoringState };
