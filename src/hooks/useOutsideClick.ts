import { useEffect, useRef } from 'react';

/**
 * Custom hook to handle clicks outside of a referenced element
 * @param handler - Function to call when clicking outside
 * @param enabled - Whether the hook should be active
 */
export const useOutsideClick = (
  handler: () => void,
  enabled: boolean = true
) => {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!enabled) return;

    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler();
      }
    };

    // Add event listeners for both mouse and touch events
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [handler, enabled]);

  return ref;
};
