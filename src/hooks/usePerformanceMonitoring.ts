import { useEffect, useRef, useState, useCallback } from 'react';

/**
 * Performance Monitoring Hook
 * 
 * Comprehensive performance monitoring untuk Categories component dan
 * data processing operations. Menyediakan real-time metrics dan
 * performance insights untuk optimization.
 * 
 * Features:
 * - Real-time performance metrics tracking
 * - Memory usage monitoring
 * - API call performance analysis
 * - Render performance tracking
 * - User interaction metrics
 * - Performance alerts dan warnings
 * - Historical data untuk trend analysis
 * - Export metrics untuk external analysis
 */

interface PerformanceMetrics {
  // Timing metrics
  apiCallDuration: number[];
  renderDuration: number[];
  calculationDuration: number[];
  
  // Memory metrics
  memoryUsage: number[];
  cacheSize: number;
  
  // User interaction metrics
  userInteractions: number;
  errorCount: number;
  
  // Data metrics
  dataSize: number;
  processedItems: number;
  
  // Timestamps
  lastUpdated: number;
  sessionStart: number;
}

interface PerformanceAlert {
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: number;
  metric: string;
  value: number;
  threshold: number;
}

interface PerformanceThresholds {
  apiCallDuration: number;
  renderDuration: number;
  memoryUsage: number;
  errorRate: number;
}

interface UsePerformanceMonitoringOptions {
  enableRealTimeMonitoring?: boolean;
  sampleRate?: number;
  maxHistorySize?: number;
  thresholds?: Partial<PerformanceThresholds>;
  enableAlerts?: boolean;
  enableMemoryMonitoring?: boolean;
}

export const usePerformanceMonitoring = (options: UsePerformanceMonitoringOptions = {}) => {
  const {
    enableRealTimeMonitoring = true,
    sampleRate = 1000, // ms
    maxHistorySize = 100,
    thresholds = {},
    enableAlerts = true,
    enableMemoryMonitoring = true
  } = options;

  const defaultThresholds: PerformanceThresholds = {
    apiCallDuration: 2000, // 2 seconds
    renderDuration: 16, // 16ms for 60fps
    memoryUsage: 100 * 1024 * 1024, // 100MB
    errorRate: 0.05 // 5%
  };

  const finalThresholds = { ...defaultThresholds, ...thresholds };

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    apiCallDuration: [],
    renderDuration: [],
    calculationDuration: [],
    memoryUsage: [],
    cacheSize: 0,
    userInteractions: 0,
    errorCount: 0,
    dataSize: 0,
    processedItems: 0,
    lastUpdated: Date.now(),
    sessionStart: Date.now()
  });

  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  const monitoringIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const performanceObserverRef = useRef<PerformanceObserver | null>(null);
  const memoryObserverRef = useRef<NodeJS.Timeout | null>(null);

  // Add metric value dengan automatic trimming
  const addMetric = useCallback((metricName: keyof PerformanceMetrics, value: number) => {
    setMetrics(prev => {
      const currentArray = prev[metricName] as number[];
      const newArray = [...currentArray, value];
      
      // Trim array jika melebihi maxHistorySize
      if (newArray.length > maxHistorySize) {
        newArray.splice(0, newArray.length - maxHistorySize);
      }

      return {
        ...prev,
        [metricName]: newArray,
        lastUpdated: Date.now()
      };
    });

    // Check thresholds dan create alerts
    if (enableAlerts) {
      checkThreshold(metricName, value);
    }
  }, [maxHistorySize, enableAlerts, finalThresholds]);

  // Check threshold dan create alert jika diperlukan
  const checkThreshold = useCallback((metricName: string, value: number) => {
    const threshold = finalThresholds[metricName as keyof PerformanceThresholds];
    
    if (threshold && value > threshold) {
      const alert: PerformanceAlert = {
        type: value > threshold * 1.5 ? 'error' : 'warning',
        message: `${metricName} exceeded threshold: ${value.toFixed(2)} > ${threshold}`,
        timestamp: Date.now(),
        metric: metricName,
        value,
        threshold
      };

      setAlerts(prev => {
        const newAlerts = [alert, ...prev];
        // Keep only last 50 alerts
        return newAlerts.slice(0, 50);
      });
    }
  }, [finalThresholds]);

  // Track API call performance
  const trackApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    operationName: string = 'API Call'
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await apiCall();
      const duration = performance.now() - startTime;
      
      addMetric('apiCallDuration', duration);
      
      console.log(`📊 ${operationName} completed in ${duration.toFixed(2)}ms`);
      return result;
      
    } catch (error) {
      const duration = performance.now() - startTime;
      addMetric('apiCallDuration', duration);
      
      setMetrics(prev => ({
        ...prev,
        errorCount: prev.errorCount + 1
      }));
      
      console.error(`❌ ${operationName} failed after ${duration.toFixed(2)}ms:`, error);
      throw error;
    }
  }, [addMetric]);

  // Track calculation performance
  const trackCalculation = useCallback(<T>(
    calculation: () => T,
    operationName: string = 'Calculation'
  ): T => {
    const startTime = performance.now();
    
    try {
      const result = calculation();
      const duration = performance.now() - startTime;
      
      addMetric('calculationDuration', duration);
      
      console.log(`🧮 ${operationName} completed in ${duration.toFixed(2)}ms`);
      return result;
      
    } catch (error) {
      const duration = performance.now() - startTime;
      addMetric('calculationDuration', duration);
      
      setMetrics(prev => ({
        ...prev,
        errorCount: prev.errorCount + 1
      }));
      
      console.error(`❌ ${operationName} failed after ${duration.toFixed(2)}ms:`, error);
      throw error;
    }
  }, [addMetric]);

  // Track user interaction
  const trackUserInteraction = useCallback((interactionType: string) => {
    setMetrics(prev => ({
      ...prev,
      userInteractions: prev.userInteractions + 1
    }));
    
    console.log(`👆 User interaction: ${interactionType}`);
  }, []);

  // Update data size metrics
  const updateDataSize = useCallback((size: number, processedItems: number = 0) => {
    setMetrics(prev => ({
      ...prev,
      dataSize: size,
      processedItems: processedItems || prev.processedItems
    }));
  }, []);

  // Get performance summary
  const getPerformanceSummary = useCallback(() => {
    const now = Date.now();
    const sessionDuration = now - metrics.sessionStart;
    
    const avgApiCall = metrics.apiCallDuration.length > 0
      ? metrics.apiCallDuration.reduce((a, b) => a + b, 0) / metrics.apiCallDuration.length
      : 0;
      
    const avgRender = metrics.renderDuration.length > 0
      ? metrics.renderDuration.reduce((a, b) => a + b, 0) / metrics.renderDuration.length
      : 0;
      
    const avgCalculation = metrics.calculationDuration.length > 0
      ? metrics.calculationDuration.reduce((a, b) => a + b, 0) / metrics.calculationDuration.length
      : 0;

    const errorRate = metrics.userInteractions > 0
      ? metrics.errorCount / metrics.userInteractions
      : 0;

    const currentMemory = enableMemoryMonitoring && (performance as any).memory
      ? (performance as any).memory.usedJSHeapSize
      : 0;

    return {
      sessionDuration,
      averageApiCallDuration: avgApiCall,
      averageRenderDuration: avgRender,
      averageCalculationDuration: avgCalculation,
      errorRate,
      currentMemoryUsage: currentMemory,
      totalInteractions: metrics.userInteractions,
      totalErrors: metrics.errorCount,
      dataSize: metrics.dataSize,
      processedItems: metrics.processedItems,
      alertCount: alerts.length,
      lastUpdated: metrics.lastUpdated
    };
  }, [metrics, alerts, enableMemoryMonitoring]);

  // Export metrics untuk external analysis
  const exportMetrics = useCallback(() => {
    const summary = getPerformanceSummary();
    const exportData = {
      summary,
      rawMetrics: metrics,
      alerts,
      thresholds: finalThresholds,
      exportTimestamp: Date.now()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-metrics-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [getPerformanceSummary, metrics, alerts, finalThresholds]);

  // Clear metrics dan alerts
  const clearMetrics = useCallback(() => {
    setMetrics({
      apiCallDuration: [],
      renderDuration: [],
      calculationDuration: [],
      memoryUsage: [],
      cacheSize: 0,
      userInteractions: 0,
      errorCount: 0,
      dataSize: 0,
      processedItems: 0,
      lastUpdated: Date.now(),
      sessionStart: Date.now()
    });
    setAlerts([]);
  }, []);

  // Start monitoring
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return;

    setIsMonitoring(true);

    // Performance Observer untuk render metrics
    if ('PerformanceObserver' in window) {
      performanceObserverRef.current = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'measure') {
            addMetric('renderDuration', entry.duration);
          }
        });
      });

      performanceObserverRef.current.observe({ entryTypes: ['measure'] });
    }

    // Memory monitoring
    if (enableMemoryMonitoring && (performance as any).memory) {
      memoryObserverRef.current = setInterval(() => {
        const memoryInfo = (performance as any).memory;
        addMetric('memoryUsage', memoryInfo.usedJSHeapSize);
      }, sampleRate);
    }

    console.log('📊 Performance monitoring started');
  }, [isMonitoring, enableMemoryMonitoring, sampleRate, addMetric]);

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    if (!isMonitoring) return;

    setIsMonitoring(false);

    if (performanceObserverRef.current) {
      performanceObserverRef.current.disconnect();
      performanceObserverRef.current = null;
    }

    if (memoryObserverRef.current) {
      clearInterval(memoryObserverRef.current);
      memoryObserverRef.current = null;
    }

    console.log('📊 Performance monitoring stopped');
  }, [isMonitoring]);

  // Auto-start monitoring jika enabled
  useEffect(() => {
    if (enableRealTimeMonitoring) {
      startMonitoring();
    }

    return () => {
      stopMonitoring();
    };
  }, [enableRealTimeMonitoring, startMonitoring, stopMonitoring]);

  // Cleanup pada unmount
  useEffect(() => {
    return () => {
      if (monitoringIntervalRef.current) {
        clearInterval(monitoringIntervalRef.current);
      }
      stopMonitoring();
    };
  }, [stopMonitoring]);

  return {
    // Metrics
    metrics,
    alerts,
    
    // Tracking methods
    trackApiCall,
    trackCalculation,
    trackUserInteraction,
    updateDataSize,
    
    // Control
    startMonitoring,
    stopMonitoring,
    isMonitoring,
    
    // Analysis
    getPerformanceSummary,
    exportMetrics,
    clearMetrics,
    
    // Status
    thresholds: finalThresholds
  };
};

export default usePerformanceMonitoring;
