import { useMemo } from 'react';
import { CategoryData } from '../services/api';

/**
 * Category Statistics Interface
 */
export interface CategoryStats {
  totalItems: number;
  upItems: number;
  downItems: number;
  sameItems: number;
  uniqueAuthors: <AUTHORS>
}

/**
 * useCategoryStats Hook
 * 
 * Extracts category statistics calculation logic from Categories.tsx component.
 * Provides memoized calculation of category statistics to prevent unnecessary recalculations.
 * 
 * Features:
 * - Memoized calculations for performance
 * - Type-safe statistics interface
 * - Handles empty/invalid data gracefully
 * - Reusable across components
 * 
 * @param selectedCategory - Currently selected category name
 * @param categoryData - Array of category data items
 * @returns CategoryStats object or null if no valid data
 */
export const useCategoryStats = (
  selectedCategory: string,
  categoryData: CategoryData[]
): CategoryStats | null => {
  // ✅ PERFORMANCE OPTIMIZATION: Memoized category statistics calculation
  const categoryStats = useMemo(() => {
    // Return null if no category selected or no data
    if (!selectedCategory || !categoryData.length) return null;

    // Calculate statistics from category data
    const totalItems = categoryData.length;
    const upItems = categoryData.filter(item => item.Status === 'Up').length;
    const downItems = categoryData.filter(item => item.Status === 'Down').length;
    const sameItems = categoryData.filter(item => item.Status === 'Same').length;
    const uniqueAuthors = new Set(categoryData.map(item => item.Author)).size;

    return {
      totalItems,
      upItems,
      downItems,
      sameItems,
      uniqueAuthors
    };
  }, [selectedCategory, categoryData]);

  return categoryStats;
};

/**
 * getCategoryStatsSync - Synchronous version for backward compatibility
 * 
 * Calculates category statistics synchronously for any given data.
 * Used for dropdown stats and other cases where memoization isn't needed.
 * 
 * @param categoryData - Array of category data items
 * @returns CategoryStats object
 */
export const getCategoryStatsSync = (categoryData: CategoryData[]): CategoryStats => {
  const totalItems = categoryData.length;
  const upItems = categoryData.filter(item => item.Status === 'Up').length;
  const downItems = categoryData.filter(item => item.Status === 'Down').length;
  const sameItems = categoryData.filter(item => item.Status === 'Same').length;
  const uniqueAuthors = new Set(categoryData.map(item => item.Author)).size;

  return {
    totalItems,
    upItems,
    downItems,
    sameItems,
    uniqueAuthors
  };
};

export default useCategoryStats;
