@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for enhanced burger menu functionality */
@layer base {
  /* Table responsive fixes */
  .table-container {
    overflow-x: auto;
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
  }

  .table-grid {
    min-width: max-content;
    width: 100%;
  }

  /* Ensure table cells don't break layout */
  .table-cell {
    min-width: 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  /* Improve touch targets for mobile devices */
  button, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Override global button min-size for badge-style buttons */
  .inline-flex.items-center.px-2.py-0\.5.rounded-full.text-xs.font-medium.border {
    min-height: auto !important;
    min-width: auto !important;
    height: auto !important;
  }

  /* Smooth scrolling for better UX */
  html {
    scroll-behavior: smooth;
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    html {
      scroll-behavior: auto;
    }

    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .glass-effect {
      backdrop-filter: none;
      background: white;
      border: 2px solid black;
    }
  }

  /* Focus styles for better accessibility */
  :focus {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
  }

  /* Hide focus outline for mouse users */
  :focus:not(:focus-visible) {
    outline: none;
  }

  /* Smooth scrolling for modal content */
  .modal-scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar for modal */
  .modal-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .modal-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .modal-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .modal-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}

@layer utilities {
  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Show screen reader content when focused */
  .sr-only:focus,
  .focus-within\:not-sr-only:focus-within .sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* Skip to content link */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: theme('colors.primary.500');
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    z-index: theme('zIndex.skipLink');
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 0;
  }

  /* High contrast borders */
  .border-contrast {
    border: 1px solid;
  }

  /* Focus ring utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500;
  }

  /* Glass morphism with accessibility fallback */
  .glass-accessible {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  @media (prefers-contrast: high) {
    .glass-accessible {
      background: white;
      backdrop-filter: none;
      border: 2px solid black;
    }
  }

  /* Animation utilities with reduced motion support */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in,
    .animate-scale-in {
      animation: none;
    }
  }

  /* Custom scrollbar for Points Breakdown */
  .points-breakdown-scroll::-webkit-scrollbar {
    width: 4px;
  }

  .points-breakdown-scroll::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 2px;
  }

  .points-breakdown-scroll::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
  }

  .points-breakdown-scroll::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Dynamic modal expansion */
  .modal-dynamic-height {
    transition: max-height 0.5s ease-in-out, min-height 0.5s ease-in-out;
  }

  /* Smooth content expansion */
  .content-expansion {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Ensure table shows all authors without cutting */
  .authors-table-container {
    min-height: 400px;
    max-height: 70vh;
  }

  .authors-table-body {
    min-height: 300px;
    scrollbar-width: thin;
    scrollbar-color: #9CEE69 #f1f1f1;
  }

  /* Enhanced table scrolling */
  .authors-table-body::-webkit-scrollbar {
    width: 6px;
  }

  .authors-table-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .authors-table-body::-webkit-scrollbar-thumb {
    background: #9CEE69;
    border-radius: 3px;
  }

  .authors-table-body::-webkit-scrollbar-thumb:hover {
    background: #7BC142;
  }

  /* Enhanced scrolling for competitor settings */
  .competitor-settings-scroll {
    scrollbar-width: thin;
    scrollbar-color: #9CEE69 #f1f1f1;
    scroll-behavior: smooth;
  }

  .competitor-settings-scroll::-webkit-scrollbar {
    width: 6px;
  }

  .competitor-settings-scroll::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 3px;
  }

  .competitor-settings-scroll::-webkit-scrollbar-thumb {
    background: #9CEE69;
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .competitor-settings-scroll::-webkit-scrollbar-thumb:hover {
    background: #7BC142;
  }

  /* Smooth scroll animation for competitor cards */
  .competitor-card-enter {
    opacity: 0;
    transform: translateY(10px);
    animation: competitorCardEnter 0.3s ease-out forwards;
  }

  @keyframes competitorCardEnter {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Enhanced button hover effects */
  .competitor-action-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .competitor-action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .competitor-action-button:hover::before {
    left: 100%;
  }

  /* Pulse animation for active states */
  .competitor-pulse {
    animation: competitorPulse 2s infinite;
  }

  @keyframes competitorPulse {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(156, 238, 105, 0.4);
    }
    50% {
      box-shadow: 0 0 0 10px rgba(156, 238, 105, 0);
    }
  }

  /* Loading spinner enhancement */
  .competitor-loading-spinner {
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: competitorSpin 1s linear infinite;
  }

  @keyframes competitorSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Modal center positioning with max padding */
  .modal-center-constrained {
    padding: 4rem 1rem;
    max-height: 100vh;
    box-sizing: border-box;
  }

  /* Ensure modal content uses available space efficiently */
  .modal-content-optimized {
    height: 100%;
    max-height: calc(100vh - 8rem);
    display: flex;
    flex-direction: column;
  }

  /* Focus styles for better accessibility */
  *:focus-visible {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
  }
}

@layer components {
  /* Enhanced burger menu animations */
  .burger-line {
    @apply block absolute h-0.5 w-full bg-current transition-all duration-300 ease-in-out;
  }

  /* Override global button min-size for badge-style buttons */
  .inline-flex.items-center.px-2.py-0\.5.rounded-full.text-xs.font-medium.border {
    min-height: auto !important;
    min-width: auto !important;
    height: auto !important;
  }

  /* Legacy view-button override */
  .view-button {
    min-height: 35px !important;
    min-width: 35px !important;
  }

  /* Mobile-first responsive utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Backdrop blur fallback for older browsers */
  .backdrop-blur-fallback {
    background-color: rgba(254, 245, 237, 0.8); /* seashell based */
  }

  @supports (backdrop-filter: blur(12px)) {
    .backdrop-blur-fallback {
      background-color: rgba(254, 245, 237, 0.6);
      backdrop-filter: blur(12px);
    }
  }
}

@layer utilities {
  /* Custom animation utilities */
  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  .animate-slide-out-left {
    animation: slideOutLeft 0.3s ease-in;
  }

  .animate-fade-in {
    animation: fadeIn 0.2s ease-out;
  }

  .animate-dropdown-in {
    animation: dropdownIn 0.2s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-slide-up {
    animation: slideUp 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Safe area utilities for mobile devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dropdownIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-12px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
  }
}

@keyframes slideUp {
  from {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
  }
  to {
    opacity: 0;
    transform: translateY(-12px);
    max-height: 0;
  }
}

/* Responsive table utilities */
@layer utilities {
  .table-responsive {
    @apply overflow-x-auto overflow-y-visible;
    -webkit-overflow-scrolling: touch;
  }

  .table-grid-responsive {
    display: grid;
    min-width: max-content;
    width: 100%;
  }

  .table-cell-responsive {
    @apply min-w-0;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Mobile-first responsive breakpoints for tables */
  @media (max-width: 479px) {
    .hide-on-mobile {
      display: none !important;
    }
  }

  @media (max-width: 639px) {
    .hide-on-small {
      display: none !important;
    }
  }
}
