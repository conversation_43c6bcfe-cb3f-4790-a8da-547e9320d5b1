import React, { useState, useEffect, useRef } from 'react';
import {
  Flame,
  Plus,
  List,
  Clock,
  Settings,
  ChevronLeft,
  ChevronRight,
  Filter,
  X,
  AlertCircle,
  Edit2,
  Copy,
  Trash2,
  Building2,
  XCircle,
  Info
} from 'lucide-react';
import DataTable from './DataTable';
import Modal from './Modal';
import { api } from '../utils/apiClient';
import { createApiErrorNotification, createEmptyDataNotification, createErrorState, ErrorState } from '../utils/notificationUtils';


interface CompetitorData {
  Title: string;
  Author: string;
  Link: string;
  Page: number;
  Order: number;
  new_item?: boolean;
  'Page Old'?: number;
  'Order Old'?: number;
}

interface CompetitorSummary {
  name: string;
  popularCount: number;
  newCount: number;
  allCount: number;
}

const Competitors: React.FC = () => {
  // State management
  const [viewMode, setViewMode] = useState<'summary' | 'detail'>('summary');
  const [competitors, setCompetitors] = useState<string[]>([]);
  const [competitorsSummary, setCompetitorsSummary] = useState<CompetitorSummary[]>([]);
  const [selectedCompetitor, setSelectedCompetitor] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'popular' | 'new' | 'all'>('popular');
  const [competitorData, setCompetitorData] = useState<{
    popular: CompetitorData[];
    new: CompetitorData[];
    all: CompetitorData[];
  }>({
    popular: [],
    new: [],
    all: []
  });
  const [loading, setLoading] = useState(false);
  const [isApiAvailable, setIsApiAvailable] = useState(true);
  const [apiError, setApiError] = useState<string | null>(null);
  const [errorState, setErrorState] = useState<ErrorState>({ hasError: false, errorType: 'unknown', title: '', message: '' });

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [editingCompetitor, setEditingCompetitor] = useState<string | null>(null);
  const [newCompetitorName, setNewCompetitorName] = useState('');
  const [editCompetitorName, setEditCompetitorName] = useState('');
  const [competitorToDelete, setCompetitorToDelete] = useState<string | null>(null);

  // Loading states for operations
  const [isAddingCompetitor, setIsAddingCompetitor] = useState(false);
  const [isEditingCompetitor, setIsEditingCompetitor] = useState(false);
  const [isDeletingCompetitor, setIsDeletingCompetitor] = useState(false);

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(0);
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: { start: '', end: '' },
    year: '',
    competitors: [] as string[]
  });

  const filterDropdownRef = useRef<HTMLDivElement>(null);
  const competitorsPerPage = 5;

  // Tab configuration
  const tabs = [
    { key: 'popular', label: 'Popular', icon: Flame, color: 'text-red-500' },
    { key: 'new', label: 'New', icon: Plus, color: 'text-green-500' },
    { key: 'all', label: 'All Items', icon: List, color: 'text-blue-500' }
  ];

  // API functions
  const fetchCompetitors = async () => {
    try {
      const response = await api.getCompetitors();
      if (response.success && response.data) {
        setCompetitors(response.data.competitors || []);
        setIsApiAvailable(true);
        setApiError(null);
        setErrorState({ hasError: false, errorType: 'unknown', title: '', message: '' });
        return response.data.competitors || [];
      } else {
        throw new Error(response.error || 'Failed to load competitors');
      }
    } catch (error) {
      console.error('Error loading competitors:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setApiError(errorMessage);
      setIsApiAvailable(false);

      // Create error state for UI display
      const newErrorState = createErrorState(errorMessage, 'kompetitor');
      setErrorState(newErrorState);

      // Clear competitors instead of using fallback
      setCompetitors([]);

      // Show notification
      const notification = createApiErrorNotification(errorMessage);
      console.log(`[${notification.type.toUpperCase()}] ${notification.title}: ${notification.message}`);

      return [];
    }
  };

  const fetchCompetitorData = async (competitorName: string) => {
    try {
      const response = await api.get(`/api/competitors/${encodeURIComponent(competitorName)}/all-data`);
      if (response.success && response.data) {
        return response.data.data;
      } else {
        throw new Error(response.error || 'Failed to fetch competitor data');
      }
    } catch (error) {
      console.warn(`Failed to fetch data for ${competitorName}:`, error);
      return { popular: [], new: [], all: [], old: [] };
    }
  };

  // Load competitors summary
  const loadCompetitorsSummary = async () => {
    setLoading(true);
    try {
      const competitorsList = await fetchCompetitors();
      const summaryPromises = competitorsList.map(async (competitor: string) => {
        const data = await fetchCompetitorData(competitor);
        return {
          name: competitor,
          popularCount: data.popular?.length || 0,
          newCount: data.new?.length || 0,
          allCount: data.all?.length || 0
        };
      });

      const summaries = await Promise.all(summaryPromises);
      // Sort by popular count descending
      summaries.sort((a, b) => b.popularCount - a.popularCount);
      setCompetitorsSummary(summaries);
    } catch (error) {
      console.error('Error loading competitors summary:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load competitor detail data
  const loadCompetitorData = async (competitorName: string) => {
    setLoading(true);
    try {
      const data = await fetchCompetitorData(competitorName);
      setCompetitorData(data);
    } catch (error) {
      console.error('Error loading competitor data:', error);
      setCompetitorData({ popular: [], new: [], all: [] });
    } finally {
      setLoading(false);
    }
  };

  // Event handlers
  const handleSummaryRowClick = (competitorName: string) => {
    setSelectedCompetitor(competitorName);
    setViewMode('detail');
    loadCompetitorData(competitorName);
  };

  const handleBackToSummary = () => {
    setViewMode('summary');
    setSelectedCompetitor('');
  };

  const handleCompetitorClick = (competitorName: string) => {
    setSelectedCompetitor(competitorName);
    loadCompetitorData(competitorName);
  };

  // Enhanced Competitor management with loading states
  const handleAddCompetitor = async () => {
    if (newCompetitorName.trim() && !isAddingCompetitor) {
      setIsAddingCompetitor(true);
      try {
        // Simulate API call delay for better UX
        await new Promise(resolve => setTimeout(resolve, 800));

        const formattedName = newCompetitorName.toLowerCase().replace(/\s+/g, '_');
        setCompetitors(prev => [...prev, formattedName]);
        setNewCompetitorName('');
        setShowAddModal(false);
        loadCompetitorsSummary(); // Refresh summary
      } catch (error) {
        console.error('Error adding competitor:', error);
      } finally {
        setIsAddingCompetitor(false);
      }
    }
  };

  const handleEditCompetitor = (competitor: string) => {
    setEditingCompetitor(competitor);
    setEditCompetitorName(competitor.replace(/_/g, ' '));
    setShowSettingsModal(false);
  };

  const handleSaveCompetitorEdit = async () => {
    if (editingCompetitor && editCompetitorName.trim() && !isEditingCompetitor) {
      setIsEditingCompetitor(true);
      try {
        // Simulate API call delay for better UX
        await new Promise(resolve => setTimeout(resolve, 600));

        const formattedName = editCompetitorName.toLowerCase().replace(/\s+/g, '_');
        setCompetitors(prev =>
          prev.map(comp => comp === editingCompetitor ? formattedName : comp)
        );
        setEditingCompetitor(null);
        setEditCompetitorName('');
        loadCompetitorsSummary(); // Refresh summary
      } catch (error) {
        console.error('Error editing competitor:', error);
      } finally {
        setIsEditingCompetitor(false);
      }
    }
  };

  const handleConfirmDelete = (competitor: string) => {
    setCompetitorToDelete(competitor);
    setShowDeleteConfirm(true);
    setShowSettingsModal(false);
  };

  const handleDeleteCompetitor = async () => {
    if (competitorToDelete && !isDeletingCompetitor) {
      setIsDeletingCompetitor(true);
      try {
        // Simulate API call delay for better UX
        await new Promise(resolve => setTimeout(resolve, 500));

        setCompetitors(prev => prev.filter(comp => comp !== competitorToDelete));
        setCompetitorToDelete(null);
        setShowDeleteConfirm(false);
        loadCompetitorsSummary(); // Refresh summary
      } catch (error) {
        console.error('Error deleting competitor:', error);
      } finally {
        setIsDeletingCompetitor(false);
      }
    }
  };

  // Table columns configuration - Responsive design
  const getColumns = (tabKey: string) => {
    const standardColumns = [
      {
        key: 'Title',
        label: (
          <div className="flex items-center justify-start px-1">
            <span className="text-xs font-semibold text-[#1A1919] uppercase tracking-wide">Title</span>
          </div>
        ),
        sortable: true,
        width: 'flex-1',
        cellClass: 'text-sm text-[#1A1919] px-2 py-2 whitespace-normal break-words leading-relaxed font-medium'
      },
      {
        key: 'Page',
        label: (
          <div className="flex items-center justify-center">
            <span className="text-xs font-semibold text-[#1A1919] uppercase tracking-wide">Page</span>
          </div>
        ),
        sortable: true,
        width: 'w-20',
        cellClass: 'text-sm text-[#1A1919] text-center px-1 py-2 font-medium'
      },
      {
        key: 'Order',
        label: (
          <div className="flex items-center justify-center">
            <span className="text-xs font-semibold text-[#1A1919] uppercase tracking-wide">Rank</span>
          </div>
        ),
        sortable: true,
        width: 'w-20',
        cellClass: 'text-sm text-[#1A1919] text-center px-1 py-2 font-medium'
      },
      {
        key: 'Link',
        label: (
          <div className="flex items-center justify-center">
            <span className="text-xs font-semibold text-[#1A1919] uppercase tracking-wide">Link</span>
          </div>
        ),
        sortable: false,
        width: 'w-16',
        cellClass: 'text-center px-1 py-2',
        render: (value: string) => (
          <a
            href={value}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center justify-center w-6 h-6 md:w-7 md:h-7 text-blue-600 hover:text-white hover:bg-blue-600 rounded-lg transition-all duration-200 border border-blue-200 hover:border-blue-600"
            title="View Item"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </a>
        )
      }
    ];

    return standardColumns;
  };

  // Effects
  useEffect(() => {
    loadCompetitorsSummary();
  }, []);

  return (
    <div className="w-full max-w-full">
      {/* Main Grid Layout - Responsive based on screen size */}
      <div className="grid gap-2 md:gap-3 lg:gap-4 xl:gap-5 2xl:gap-6 p-2 md:p-3 lg:p-4 xl:p-5 2xl:p-6 pt-6 md:pt-8 lg:pt-12 xl:pt-14 2xl:pt-16 grid-cols-1">
        {/* API Status Indicator - Minimal */}
        {!isApiAvailable && (
          <div className="flex items-center space-x-2 mb-3 px-3 py-2 bg-warning-50 border border-warning-200 rounded-lg">
            <AlertCircle size={14} className="text-warning-600" />
            <span className="text-sm font-medium text-warning-700">Offline mode - Using mock data</span>
          </div>
        )}

        {/* Summary View - Default Screen */}
        {viewMode === 'summary' && (
          <div
            className="bg-white border border-gray-200 rounded-lg shadow-sm min-w-0 p-2 md:p-3 lg:p-6 xl:p-7 2xl:p-8"
          >
            {/* Header - Simple & Clean */}
            <div className="flex items-center justify-between mb-3 md:mb-4">
              <div className="flex items-center gap-2.5">
                <div className="flex items-center justify-center w-8 h-8 bg-[#9CEE69]/10 rounded-lg">
                  <Settings size={16} className="text-[#1A1919]" />
                </div>
                <div>
                  <h2 className="text-base md:text-lg font-semibold text-[#1A1919]">Competitors Analysis</h2>
                  <p className="text-xs text-gray-600">Click competitor for detailed analysis</p>
                </div>
              </div>

              <button
                onClick={() => setShowSettingsModal(true)}
                className="flex items-center justify-center w-8 h-8 md:w-9 md:h-9 text-gray-500 hover:text-[#1A1919] hover:bg-[#9CEE69]/10 rounded-lg transition-all duration-200 border border-gray-200 hover:border-[#9CEE69]/30"
                title="Settings"
              >
                <Settings size={16} className="md:w-[17px] md:h-[17px]" />
              </button>
            </div>

          {loading ? (
            <div className="flex items-center justify-center py-16 flex-1">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-transparent border-t-[#9CEE69] border-r-[#9CEE69]/30 mx-auto mb-4"></div>
                <span className="text-base font-medium text-gray-600">
                  Loading competitors data...
                </span>
              </div>
            </div>
          ) : competitorsSummary.length > 0 ? (
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
              <div className="overflow-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-semibold text-[#1A1919] uppercase tracking-wide">
                        Competitor
                      </th>
                      <th className="px-3 py-3 text-center text-xs font-semibold text-[#1A1919] uppercase tracking-wide">
                        <div className="flex items-center justify-center gap-1.5">
                          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                          <span className="hidden sm:inline">Popular</span>
                          <span className="sm:hidden">Pop</span>
                        </div>
                      </th>
                      <th className="px-3 py-3 text-center text-xs font-semibold text-[#1A1919] uppercase tracking-wide">
                        <div className="flex items-center justify-center gap-1.5">
                          <div className="w-2 h-2 bg-[#9CEE69] rounded-full"></div>
                          <span>New</span>
                        </div>
                      </th>
                      <th className="px-3 py-3 text-center text-xs font-semibold text-[#1A1919] uppercase tracking-wide">
                        <div className="flex items-center justify-center gap-1.5">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span>All</span>
                        </div>
                      </th>

                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-100">
                    {competitorsSummary.map((competitor, index) => (
                      <tr
                        key={competitor.name}
                        onClick={() => handleSummaryRowClick(competitor.name)}
                        className="hover:bg-[#9CEE69]/10 cursor-pointer transition-all duration-200 group"
                      >
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="flex items-center gap-3">
                            <div className="flex-shrink-0 w-7 h-7 bg-[#9CEE69] rounded-lg flex items-center justify-center text-[#1A1919] text-sm font-semibold shadow-sm">
                              {index + 1}
                            </div>
                            <div className="font-medium text-[#1A1919] text-sm truncate group-hover:text-[#1A1919]">
                              {competitor.name.replace('_', ' ').toUpperCase()}
                            </div>
                          </div>
                        </td>
                        <td className="px-3 py-3 whitespace-nowrap text-center">
                          <span className="inline-flex items-center justify-center w-8 h-6 rounded-md text-xs font-semibold bg-red-50 text-red-700 border border-red-200">
                            {competitor.popularCount}
                          </span>
                        </td>
                        <td className="px-3 py-3 whitespace-nowrap text-center">
                          <span className="inline-flex items-center justify-center w-8 h-6 rounded-md text-xs font-semibold bg-[#9CEE69]/20 text-[#1A1919] border border-[#9CEE69]/30">
                            {competitor.newCount}
                          </span>
                        </td>
                        <td className="px-3 py-3 whitespace-nowrap text-center">
                          <span className="inline-flex items-center justify-center w-8 h-6 rounded-md text-xs font-semibold bg-blue-50 text-blue-700 border border-blue-200">
                            {competitor.allCount}
                          </span>
                        </td>

                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : errorState.hasError ? (
            <div className="flex flex-col items-center justify-center py-12 flex-1">
              <div className="text-center max-w-md">
                {errorState.errorType === 'connection' ? (
                  <div className="text-red-500 mb-4">
                    <XCircle size={48} className="mx-auto mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{errorState.title}</h3>
                    <p className="text-sm text-gray-600 mb-4">{errorState.message}</p>
                    {errorState.suggestion && (
                      <p className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                        💡 {errorState.suggestion}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="text-yellow-500 mb-4">
                    <Info size={48} className="mx-auto mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{errorState.title}</h3>
                    <p className="text-sm text-gray-600 mb-4">{errorState.message}</p>
                    {errorState.suggestion && (
                      <p className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                        💡 {errorState.suggestion}
                      </p>
                    )}
                  </div>
                )}
                <button
                  onClick={() => fetchCompetitors()}
                  className="mt-4 px-4 py-2 bg-[#9CEE69] text-[#1A1919] rounded-lg hover:bg-[#9CEE69]/80 transition-all duration-200"
                >
                  Coba Lagi
                </button>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center py-12 text-gray-500">
              <div className="text-center">
                <Flame size={48} className="mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak Ada Data Kompetitor</h3>
                <p className="text-sm text-gray-600 mb-4">Tidak ada data kompetitor yang tersedia saat ini.</p>
                <p className="text-xs text-gray-500">Tambahkan kompetitor untuk mulai analisis.</p>
              </div>
            </div>
          )}
          </div>
        )}

        {/* Detail View - Competitor Analysis */}
        {viewMode === 'detail' && selectedCompetitor && (
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm min-w-0 p-2 md:p-3 lg:p-4 xl:p-5 2xl:p-6">
            {/* Header with Back Button */}
            <div className="flex items-center justify-between mb-3 md:mb-4">
              <div className="flex items-center gap-2.5">
                <button
                  onClick={handleBackToSummary}
                  className="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-[#1A1919] hover:bg-[#9CEE69]/10 rounded-lg transition-all duration-200 border border-gray-200 hover:border-[#9CEE69]/30"
                  title="Back to Overview"
                >
                  <ChevronLeft size={16} />
                </button>
                <div className="flex items-center justify-center w-8 h-8 bg-[#9CEE69]/10 rounded-lg">
                  <Settings size={16} className="text-[#1A1919]" />
                </div>
                <div>
                  <h2 className="text-base md:text-lg font-semibold text-[#1A1919]">
                    {selectedCompetitor.replace('_', ' ').toUpperCase()} Analysis
                  </h2>
                  <p className="text-xs text-gray-600">Detailed performance analysis</p>
                </div>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="flex space-x-1 mb-3 md:mb-4 bg-gray-100/50 p-1 rounded-lg flex-shrink-0 overflow-x-auto">
              {tabs.map((tab) => {
                const TabIcon = tab.icon;
                const isActive = activeTab === tab.key;
                const count = competitorData[tab.key as keyof typeof competitorData]?.length || 0;

                return (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key as 'popular' | 'new' | 'all')}
                    className={`flex-shrink-0 flex items-center justify-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 md:py-3 rounded-md transition-all duration-200 min-w-0 ${
                      isActive
                        ? 'bg-white shadow-sm text-[#1A1919] border border-gray-200'
                        : 'text-gray-600 hover:text-[#1A1919] hover:bg-white/50'
                    }`}
                  >
                    <TabIcon size={14} className={`md:w-4 md:h-4 ${isActive ? 'text-[#1A1919]' : tab.color}`} />
                    <span className="font-medium text-xs md:text-sm truncate">{tab.label}</span>
                    <span className={`px-1.5 md:px-2 py-0.5 rounded-full text-xs font-medium ${
                      isActive
                        ? 'bg-[#9CEE69]/20 text-[#1A1919]'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {count}
                    </span>
                  </button>
                );
              })}
            </div>

            {/* Tab Content */}
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm mb-4 md:mb-6">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-6 w-6 md:h-8 md:w-8 border-b-2 border-[#9CEE69]"></div>
                  <span className="ml-3 text-[#1A1919] text-sm md:text-base font-medium">Loading {activeTab} items...</span>
                </div>
              ) : (
                <div className="overflow-x-auto overflow-y-visible">
                  <div className="min-w-full">
                    <DataTable
                      data={competitorData[activeTab] || []}
                      columns={getColumns(activeTab)}
                      pageSize={8}
                      tableLayout="auto"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Tab Summary Cards */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 md:gap-4">
              {tabs.map((tab) => {
                const TabIcon = tab.icon;
                const count = competitorData[tab.key as keyof typeof competitorData]?.length || 0;

                return (
                  <div
                    key={tab.key}
                    className={`bg-white rounded-lg p-3 md:p-4 border border-gray-200 cursor-pointer transition-all duration-200 hover:shadow-md hover:border-[#9CEE69]/30 ${
                      activeTab === tab.key ? 'ring-2 ring-[#9CEE69]/50 border-[#9CEE69]/30' : ''
                    }`}
                    onClick={() => setActiveTab(tab.key as 'popular' | 'new' | 'all')}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${
                        tab.key === 'popular' ? 'bg-red-50 border border-red-200' :
                        tab.key === 'new' ? 'bg-[#9CEE69]/20 border border-[#9CEE69]/30' :
                        tab.key === 'all' ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 border border-gray-200'
                      }`}>
                        <TabIcon size={16} className={`${
                          tab.key === 'popular' ? 'text-red-600' :
                          tab.key === 'new' ? 'text-[#1A1919]' :
                          tab.key === 'all' ? 'text-blue-600' : 'text-gray-600'
                        }`} />
                      </div>
                      <div className="min-w-0">
                        <p className="text-xs md:text-sm font-medium text-gray-600 truncate">{tab.label}</p>
                        <p className="text-lg md:text-2xl font-bold text-[#1A1919]">{count}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Enhanced Settings Modal */}
      <Modal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        title="Competitor Settings"
        size="lg"
      >
        <div className="flex flex-col h-full">
          {/* Enhanced Header with Add Button */}
          <div className="flex items-center justify-between pb-6 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-lg bg-[#9CEE69] flex items-center justify-center">
                <Settings size={20} className="text-[#1A1919]" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-[#1A1919]">Manage Competitors</h3>
                <p className="text-sm text-gray-500 mt-0.5">
                  {competitors.length} competitor{competitors.length !== 1 ? 's' : ''} configured
                </p>
              </div>
            </div>
            <button
              onClick={() => {
                setShowSettingsModal(false);
                setShowAddModal(true);
              }}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-[#9CEE69] hover:bg-[#608F44] text-[#1A1919] hover:text-white font-medium rounded-lg transition-colors duration-200 border border-[#9CEE69]/30"
            >
              <Plus size={16} />
              <span>Add Competitor</span>
            </button>
          </div>

          {/* Enhanced Competitors List with Scroll */}
          <div className="flex-1 mt-6">
            <div
              className="space-y-2 overflow-y-auto modal-scroll-smooth competitor-settings-scroll pr-2"
              style={{
                maxHeight: '300px',
                minHeight: '150px'
              }}
            >
              {competitors.length > 0 ? (
                competitors.map((competitor, index) => (
                  <div
                    key={competitor}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#9CEE69] rounded-lg flex items-center justify-center text-[#1A1919] text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 text-sm">
                          {competitor.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </p>
                        <p className="text-xs text-gray-500">
                          {competitor}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditCompetitor(competitor)}
                        className="flex items-center space-x-1 px-3 py-1.5 text-sm text-[#1A1919] hover:text-white hover:bg-[#9CEE69] rounded-md transition-colors duration-200 border border-[#9CEE69]/30"
                        title="Edit"
                      >
                        <Edit2 size={14} />
                        <span>Edit</span>
                      </button>
                      <button
                        onClick={() => handleConfirmDelete(competitor)}
                        className="flex items-center space-x-1 px-3 py-1.5 text-sm text-red-600 hover:text-white hover:bg-red-500 rounded-md transition-colors duration-200 border border-red-300"
                        title="Delete"
                      >
                        <Trash2 size={14} />
                        <span>Delete</span>
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Settings size={48} className="mx-auto mb-4 text-gray-300" />
                  <p className="text-gray-500 mb-4">No competitors added yet</p>
                  <button
                    onClick={() => {
                      setShowSettingsModal(false);
                      setShowAddModal(true);
                    }}
                    className="inline-flex items-center space-x-2 px-4 py-2 bg-[#9CEE69] hover:bg-[#608F44] text-[#1A1919] hover:text-white font-medium rounded-lg transition-colors duration-200 border border-[#9CEE69]/30"
                  >
                    <Plus size={16} />
                    <span>Add Competitor</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </Modal>

      {/* Enhanced Add Competitor Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Competitor"
        size="md"
      >
        <div className="space-y-6">
          {/* Header with Icon */}
          <div className="text-center pb-4">
            <div className="w-12 h-12 rounded-lg bg-[#9CEE69] flex items-center justify-center mx-auto mb-4">
              <Plus size={20} className="text-[#1A1919]" />
            </div>
            <h3 className="text-lg font-semibold text-[#1A1919] mb-2">Add New Competitor</h3>
            <p className="text-sm text-gray-500">Enter the name of the competitor you want to track</p>
          </div>

          {/* Enhanced Input Field */}
          <div className="space-y-2">
            <label htmlFor="competitor-name" className="block text-sm font-medium text-gray-700">
              Competitor Name
            </label>
            <div className="relative">
              <input
                id="competitor-name"
                type="text"
                value={newCompetitorName}
                onChange={(e) => setNewCompetitorName(e.target.value)}
                placeholder="e.g., Company Name or Brand"
                className="w-full px-4 py-2 pl-10 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#9CEE69]/20 focus:border-[#9CEE69] transition-all duration-200 bg-gray-50 focus:bg-white"
                autoFocus
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAddCompetitor();
                  }
                }}
              />
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <Building2 size={16} className="text-gray-400" />
              </div>
            </div>
            {newCompetitorName && (
              <p className="text-xs text-gray-500 mt-2">
                Will be saved as: <span className="font-mono bg-gray-100 px-2 py-1 rounded">{newCompetitorName.toLowerCase().replace(/\s+/g, '_')}</span>
              </p>
            )}
          </div>

          {/* Enhanced Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              onClick={handleAddCompetitor}
              disabled={!newCompetitorName.trim() || isAddingCompetitor}
              className="flex-1 inline-flex items-center justify-center space-x-2 px-4 py-2 bg-[#9CEE69] hover:bg-[#608F44] disabled:bg-gray-200 disabled:text-gray-400 text-[#1A1919] hover:text-white font-medium rounded-lg transition-colors duration-200 disabled:cursor-not-allowed border border-[#9CEE69]/30"
            >
              {isAddingCompetitor ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Adding...</span>
                </>
              ) : (
                <>
                  <Plus size={16} />
                  <span>Add Competitor</span>
                </>
              )}
            </button>
            <button
              onClick={() => setShowAddModal(false)}
              className="flex-1 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg transition-colors duration-200 border border-gray-300"
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>

      {/* Enhanced Edit Competitor Modal */}
      <Modal
        isOpen={editingCompetitor !== null}
        onClose={() => {
          setEditingCompetitor(null);
          setEditCompetitorName('');
        }}
        title="Edit Competitor"
        size="md"
      >
        <div className="space-y-6">
          {/* Header with Icon */}
          <div className="text-center pb-4">
            <div className="w-12 h-12 rounded-lg bg-[#9CEE69] flex items-center justify-center mx-auto mb-4">
              <Edit2 size={20} className="text-[#1A1919]" />
            </div>
            <h3 className="text-lg font-semibold text-[#1A1919] mb-2">Edit Competitor</h3>
            <p className="text-sm text-gray-500">Update the competitor information</p>
          </div>

          {/* Enhanced Input Field */}
          <div className="space-y-2">
            <label htmlFor="edit-competitor-name" className="block text-sm font-medium text-gray-700">
              Competitor Name
            </label>
            <div className="relative">
              <input
                id="edit-competitor-name"
                type="text"
                value={editCompetitorName}
                onChange={(e) => setEditCompetitorName(e.target.value)}
                placeholder="Enter competitor name"
                className="w-full px-4 py-2 pl-10 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#9CEE69]/20 focus:border-[#9CEE69] transition-all duration-200 bg-gray-50 focus:bg-white"
                autoFocus
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleSaveCompetitorEdit();
                  }
                }}
              />
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <Building2 size={16} className="text-gray-400" />
              </div>
            </div>
            {editCompetitorName && (
              <p className="text-xs text-gray-500 mt-2">
                Will be saved as: <span className="font-mono bg-gray-100 px-2 py-1 rounded">{editCompetitorName.toLowerCase().replace(/\s+/g, '_')}</span>
              </p>
            )}
          </div>

          {/* Enhanced Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              onClick={handleSaveCompetitorEdit}
              disabled={!editCompetitorName.trim() || isEditingCompetitor}
              className="flex-1 inline-flex items-center justify-center space-x-2 px-4 py-2 bg-[#9CEE69] hover:bg-[#608F44] disabled:bg-gray-200 disabled:text-gray-400 text-[#1A1919] hover:text-white font-medium rounded-lg transition-colors duration-200 disabled:cursor-not-allowed border border-[#9CEE69]/30"
            >
              {isEditingCompetitor ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Edit2 size={16} />
                  <span>Save Changes</span>
                </>
              )}
            </button>
            <button
              onClick={() => {
                setEditingCompetitor(null);
                setEditCompetitorName('');
              }}
              className="flex-1 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg transition-colors duration-200 border border-gray-300"
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={() => {
          setShowDeleteConfirm(false);
          setCompetitorToDelete(null);
        }}
        title="Delete Competitor"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-4 rounded-lg border bg-red-50 border-red-200">
            <div className="flex-shrink-0">
              <Trash2 size={24} className="text-red-600" />
            </div>
            <div>
              <p className="text-sm font-semibold text-red-800">
                Are you sure you want to delete this competitor?
              </p>
              <p className="text-sm mt-1 text-red-700">
                <strong>{competitorToDelete?.replace('_', ' ').toUpperCase()}</strong>
              </p>
              <p className="text-xs mt-2 text-red-600">
                This action cannot be undone. All data for this competitor will be removed.
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleDeleteCompetitor}
              disabled={isDeletingCompetitor}
              className="flex-1 px-4 py-2 text-sm text-red-600 hover:text-white hover:bg-red-500 disabled:bg-gray-200 disabled:text-gray-400 font-medium rounded-lg transition-colors duration-200 border border-red-300 disabled:cursor-not-allowed"
            >
              {isDeletingCompetitor ? 'Deleting...' : 'Delete'}
            </button>
            <button
              onClick={() => {
                setShowDeleteConfirm(false);
                setCompetitorToDelete(null);
              }}
              className="flex-1 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg transition-colors duration-200 border border-gray-300"
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>
      </div>
    </div>
  );
};

export default Competitors;