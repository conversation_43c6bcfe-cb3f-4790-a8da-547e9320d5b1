/**
 * NotificationItem Component
 * Individual notification item with advanced features
 */

import React, { useState } from 'react';
import {
  Bell,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Database,
  Settings,
  Upload,
  BarChart3,
  X,
  Archive,
  Clock,
  Trash2,
  MoreHorizontal
} from 'lucide-react';
import { Notification as AppNotification } from '../types/notification';
import { formatRelativeTime, getNotificationTypeConfig, getPriorityConfig } from '../utils/notificationUtils';

interface NotificationItemProps {
  notification: AppNotification;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  onArchive: (id: string) => void;
  onClick?: (notification: AppNotification) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onDelete,
  onArchive,
  onClick
}) => {
  const [showActions, setShowActions] = useState(false);
  const typeConfig = getNotificationTypeConfig(notification.type);
  const priorityConfig = getPriorityConfig(notification.priority);

  const getIcon = () => {
    const iconProps = { size: 16, className: typeConfig.color };
    
    switch (notification.type) {
      case 'success':
        return <CheckCircle {...iconProps} />;
      case 'error':
        return <XCircle {...iconProps} />;
      case 'warning':
        return <AlertTriangle {...iconProps} />;
      case 'info':
        return <Info {...iconProps} />;
      case 'data_update':
        return <Database {...iconProps} />;
      case 'system':
        return <Settings {...iconProps} />;
      case 'file_upload':
        return <Upload {...iconProps} />;
      case 'analysis_complete':
        return <BarChart3 {...iconProps} />;
      default:
        return <Bell {...iconProps} />;
    }
  };

  const handleClick = () => {
    if (notification.status === 'unread') {
      onMarkAsRead(notification.id);
    }
    onClick?.(notification);
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkAsRead(notification.id);
    setShowActions(false);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(notification.id);
    setShowActions(false);
  };

  const handleArchive = (e: React.MouseEvent) => {
    e.stopPropagation();
    onArchive(notification.id);
    setShowActions(false);
  };

  const isUnread = notification.status === 'unread';
  const isUrgent = notification.priority === 'urgent';

  return (
    <div
      className={`
        relative px-4 py-3 border-b border-gray-100/80 last:border-b-0 cursor-pointer group
        transition-all duration-200 hover:bg-primary-50/50
        ${isUnread ? 'bg-blue-50/30' : ''}
        ${isUrgent ? 'border-l-4 border-l-red-500' : ''}
      `}
      onClick={handleClick}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Unread indicator */}
      {isUnread && (
        <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full"></div>
      )}

      <div className="flex items-start space-x-3 ml-2">
        {/* Icon */}
        <div className={`
          flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mt-0.5
          ${typeConfig.bgColor} ${typeConfig.borderColor} border
        `}>
          {getIcon()}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h4 className={`
                text-sm font-medium mb-1 group-hover:text-primary-600 transition-colors duration-200
                ${isUnread ? 'text-gray-900' : 'text-gray-700'}
              `}>
                {notification.title}
              </h4>
              <p className={`
                text-sm mb-2 line-clamp-2
                ${isUnread ? 'text-gray-800' : 'text-gray-600'}
              `}>
                {notification.message}
              </p>
              
              {/* Metadata */}
              <div className="flex items-center space-x-3 text-xs text-gray-500">
                <div className="flex items-center space-x-1">
                  <Clock size={12} />
                  <span>{formatRelativeTime(notification.createdAt)}</span>
                </div>
                
                {notification.category && (
                  <span className="px-2 py-0.5 bg-gray-100 rounded-full">
                    {notification.category}
                  </span>
                )}
                
                {notification.priority !== 'medium' && (
                  <span className={`
                    px-2 py-0.5 rounded-full text-xs font-medium
                    ${priorityConfig.bgColor} ${priorityConfig.color}
                  `}>
                    {priorityConfig.label}
                  </span>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className={`
              flex items-center space-x-1 ml-2 transition-opacity duration-200
              ${showActions ? 'opacity-100' : 'opacity-0'}
            `}>
              {isUnread && (
                <button
                  onClick={handleMarkAsRead}
                  className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-all duration-200"
                  title="Mark as read"
                >
                  <CheckCircle size={14} />
                </button>
              )}
              
              <button
                onClick={handleArchive}
                className="p-1 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded transition-all duration-200"
                title="Archive"
              >
                <Archive size={14} />
              </button>
              
              <button
                onClick={handleDelete}
                className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200"
                title="Delete"
              >
                <Trash2 size={14} />
              </button>
            </div>
          </div>

          {/* Actions for notification */}
          {notification.actions && notification.actions.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-2">
              {notification.actions.map((action) => (
                <button
                  key={action.id}
                  onClick={(e) => {
                    e.stopPropagation();
                    action.action();
                  }}
                  className={`
                    px-3 py-1 text-xs font-medium rounded-md transition-all duration-200
                    ${action.variant === 'primary'
                      ? 'bg-primary-500 text-white hover:bg-primary-600'
                      : action.variant === 'danger'
                      ? 'bg-red-500 text-white hover:bg-red-600'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }
                  `}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationItem;
