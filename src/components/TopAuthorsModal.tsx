import React, { useState, useMemo, useEffect, useRef } from 'react';
import { Crown, ChevronDown, ChevronUp, Search } from 'lucide-react';
import Modal from './Modal';

interface AuthorData {
  author: string;
  count: number;
  points: number;
  rank: number;
}

interface DetailedAuthorData {
  author: string;
  total_points: number;
  rank: number;
  page_distribution: { [key: string]: number };
}

interface TopAuthorsModalProps {
  isOpen: boolean;
  onClose: () => void;
  topAuthors: <AUTHORS>
  expandedAuthor: string | null;
  onAuthorClick: (authorName: string) => void;
  authorDetails: { [key: string]: DetailedAuthorData };
  loadingAuthorDetails: { [key: string]: boolean };
  renderAuthorDetails: (authorName: string) => React.ReactNode;
}

const TopAuthorsModal: React.FC<TopAuthorsModalProps> = React.memo(({
  isOpen,
  onClose,
  topAuthors,
  expandedAuthor,
  onAuthorClick,
  authorDetails,
  loadingAuthorDetails,
  renderAuthorDetails
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'rank' | 'points' | 'name'>('rank');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const tableBodyRef = useRef<HTMLDivElement>(null);
  const expandedRowRef = useRef<HTMLDivElement>(null);

  // Filter and sort authors
  const filteredAndSortedAuthors = useMemo(() => {
    let filtered = topAuthors.filter(author =>
      author.author.toLowerCase().includes(searchTerm.toLowerCase())
    );

    filtered.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'rank':
          comparison = a.rank - b.rank;
          break;
        case 'points':
          comparison = b.points - a.points; // Higher points first
          break;
        case 'name':
          comparison = a.author.localeCompare(b.author);
          break;
      }
      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [topAuthors, searchTerm, sortBy, sortOrder]);



  const handleSort = (field: 'rank' | 'points' | 'name') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder(field === 'points' ? 'desc' : 'asc');
    }
  };

  const getSortIcon = (field: 'rank' | 'points' | 'name') => {
    if (sortBy !== field) return null;
    return sortOrder === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  // Enhanced author click handler with improved scroll
  const handleAuthorClickWithScroll = (authorName: string) => {
    console.log('Author clicked:', authorName);

    // Call the original onAuthorClick
    onAuthorClick(authorName);

    // Improved scroll logic with proper timing
    setTimeout(() => {
      console.log('Attempting to scroll to expanded content...');

      if (tableBodyRef.current) {
        // Find the expanded content by author name
        const authorRows = tableBodyRef.current.querySelectorAll('[data-author]');
        let targetElement = null;
        let authorRowElement = null;

        authorRows.forEach((row) => {
          const authorAttr = row.getAttribute('data-author');
          if (authorAttr === authorName) {
            authorRowElement = row;
            // Look for expanded content within this row
            const expandedContent = row.querySelector('[data-expanded-content]');
            if (expandedContent) {
              targetElement = expandedContent;
            }
          }
        });

        if (targetElement && authorRowElement) {
          console.log('Found target element, calculating optimal scroll position...');

          const tableContainer = tableBodyRef.current;
          const containerRect = tableContainer.getBoundingClientRect();
          const targetRect = targetElement.getBoundingClientRect();
          const authorRowRect = authorRowElement.getBoundingClientRect();

          // Calculate optimal scroll position
          // Start from author row position, not center of expanded content
          const authorRowTop = authorRowRect.top - containerRect.top + tableContainer.scrollTop;
          const targetScrollTop = authorRowTop - 60; // 60px buffer from top

          console.log('Scrolling to optimal position:', targetScrollTop);

          // Smooth scroll to calculated position
          tableContainer.scrollTo({
            top: Math.max(0, targetScrollTop),
            behavior: 'smooth'
          });

        } else if (authorRowElement) {
          console.log('Expanded content not found, scrolling to author row...');
          // Fallback: scroll to author row
          const tableContainer = tableBodyRef.current;
          const containerRect = tableContainer.getBoundingClientRect();
          const authorRowRect = authorRowElement.getBoundingClientRect();

          const authorRowTop = authorRowRect.top - containerRect.top + tableContainer.scrollTop;
          const targetScrollTop = authorRowTop - 60;

          tableContainer.scrollTo({
            top: Math.max(0, targetScrollTop),
            behavior: 'smooth'
          });

        } else {
          console.log('Author row not found, using basic fallback scroll');
          // Basic fallback: scroll down moderately
          const tableContainer = tableBodyRef.current;
          const currentScroll = tableContainer.scrollTop;
          tableContainer.scrollTo({
            top: currentScroll + 150,
            behavior: 'smooth'
          });
        }
      }
    }, 700); // Increased delay to sync with modal expansion + content animation
  };

  // Footer content
  const footerContent = (
    <div className="flex items-center justify-between text-sm text-gray-600">
      <div className="flex items-center gap-4">
        <span>
          Showing {filteredAndSortedAuthors.length} of {topAuthors.length} authors
        </span>
        {searchTerm && (
          <span className="text-[#9CEE69] font-medium">
            Filtered by: "{searchTerm}"
          </span>
        )}
      </div>
      <div className="flex items-center gap-2">
        <span>Sorted by:</span>
        <span className="font-medium capitalize text-gray-800">
          {sortBy === 'rank' ? 'Rank' : sortBy === 'points' ? 'Points' : 'Name'}
        </span>
        <span className="text-gray-400">
          ({sortOrder === 'asc' ? '↑' : '↓'})
        </span>
      </div>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Top Authors"
      footer={footerContent}
    >
        <div className="space-y-6 modal-dynamic-height content-expansion flex flex-col h-full">

        {/* Search and Controls */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search authors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#9CEE69] focus:border-transparent"
            />
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => handleSort('rank')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1 ${
                sortBy === 'rank' ? 'bg-[#9CEE69] text-[#1A1919]' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <span>Rank</span>
              {getSortIcon('rank')}
            </button>
            <button
              onClick={() => handleSort('points')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1 ${
                sortBy === 'points' ? 'bg-[#9CEE69] text-[#1A1919]' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <span>Points</span>
              {getSortIcon('points')}
            </button>
            <button
              onClick={() => handleSort('name')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1 ${
                sortBy === 'name' ? 'bg-[#9CEE69] text-[#1A1919]' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <span>Name</span>
              {getSortIcon('name')}
            </button>
          </div>
        </div>

        {/* Authors Table */}
        <div className="flex-1 authors-table-container">
          <div className="bg-white rounded-xl border border-gray-200 overflow-hidden shadow-sm h-full flex flex-col">
            {/* Table Header */}
            <div className="bg-gray-50/50 border-b border-gray-200 grid grid-cols-12 text-sm font-semibold text-gray-700 py-3">
              <div className="col-span-2 px-4 text-center">Rank</div>
              <div className="col-span-6 px-4">Author</div>
              <div className="col-span-4 px-4 text-center">Points</div>
            </div>

            {/* Table Body */}
            <div
              ref={tableBodyRef}
              className="flex-1 overflow-y-auto modal-scroll-smooth modal-scrollbar authors-table-body transition-all duration-500 ease-in-out max-h-96"
              style={{ minHeight: '300px', maxHeight: '400px' }}
            >
              {filteredAndSortedAuthors.length === 0 ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Crown size={48} className="mx-auto mb-4 text-gray-300" />
                    <p className="text-gray-500">
                      {searchTerm ? 'No authors found matching your search' : 'No authors data available'}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredAndSortedAuthors.map((author, index) => (
                    <div key={author.author} data-author={author.author}>
                      {/* Main Author Row */}
                      <div
                        className="grid grid-cols-12 hover:bg-[#9CEE69]/20 transition-all duration-300 text-sm cursor-pointer py-3 group"
                        onClick={() => handleAuthorClickWithScroll(author.author)}
                      >
                        {/* Rank Column */}
                        <div className="col-span-2 px-4 text-center flex items-center justify-center">
                          {author.rank <= 3 ? (
                            <Crown
                              size={16}
                              className={`${
                                author.rank === 1 ? 'text-yellow-500' :
                                author.rank === 2 ? 'text-gray-400' :
                                'text-orange-400'
                              }`}
                            />
                          ) : (
                            <span className="font-medium text-gray-600">{author.rank}</span>
                          )}
                        </div>

                        {/* Author Column */}
                        <div className="col-span-6 px-4 flex items-center justify-between">
                          <span className="font-medium text-[#1A1919] truncate group-hover:text-[#1A1919] transition-colors duration-300">
                            {author.author}
                          </span>
                          <div className="flex items-center ml-2">
                            <div className={`transform transition-all duration-300 ease-in-out ${
                              expandedAuthor === author.author ? 'rotate-180' : 'rotate-0'
                            }`}>
                              <ChevronDown size={16} className="text-gray-400 group-hover:text-[#9CEE69] transition-colors duration-300" />
                            </div>
                          </div>
                        </div>

                        {/* Points Column */}
                        <div className="col-span-4 px-4 text-center flex items-center justify-center">
                          <span className="font-bold text-[#9CEE69]">
                            {author.points.toLocaleString()}
                          </span>
                        </div>
                      </div>

                      {/* Dropdown Details with Smooth Animation */}
                      <div className={`overflow-hidden transition-all duration-500 ease-in-out ${
                        expandedAuthor === author.author
                          ? 'max-h-[500px] opacity-100'
                          : 'max-h-0 opacity-0'
                      }`}>
                        <div className={`transform transition-all duration-500 ease-in-out ${
                          expandedAuthor === author.author
                            ? 'translate-y-0 scale-100'
                            : '-translate-y-4 scale-95'
                        }`}>
                          {expandedAuthor === author.author && (
                            <div
                              ref={expandedRowRef}
                              data-expanded-content="true"
                              className="animate-in fade-in slide-in-from-top-2 duration-300"
                            >
                              {renderAuthorDetails(author.author)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-center items-center text-sm text-gray-500 pt-4 border-t border-gray-200">
          <span>Showing {filteredAndSortedAuthors.length} of {topAuthors.length} authors</span>
        </div>
      </div>
    </Modal>
  );
});

export default TopAuthorsModal;
