import React, { useState, useEffect } from 'react';
import { Crown } from 'lucide-react';

interface FloatingTopAuthorsButtonProps {
  onClick: () => void;
  isVisible?: boolean;
}

const FloatingTopAuthorsButton: React.FC<FloatingTopAuthorsButtonProps> = ({
  onClick,
  isVisible = true
}) => {
  const [screenWidth, setScreenWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  // Effect to handle window resize
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Show button only on target screen sizes (1024px - 1600px)
  const shouldShow = isVisible && screenWidth >= 1024 && screenWidth <= 1600;

  if (!shouldShow) return null;

  return (
    <>
      {/* Floating Button */}
      <div className="fixed bottom-6 right-6 z-40">
        <button
          onClick={onClick}
          className="group relative flex items-center justify-center w-14 h-14 bg-[#9CEE69] hover:bg-[#8BD85A] text-[#1A1919] rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-[#9CEE69]/30"
          title="View Top Authors"
        >
          {/* Crown Icon */}
          <Crown size={24} className="transition-transform duration-300 group-hover:scale-110" />

          {/* Pulse animation */}
          <div className="absolute inset-0 rounded-full bg-[#9CEE69] animate-ping opacity-20"></div>
        </button>


      </div>

      {/* Backdrop for mobile touch (optional) */}
      {screenWidth < 1024 && (
        <div 
          className="fixed inset-0 z-30 bg-black/10 backdrop-blur-sm opacity-0 pointer-events-none"
          style={{ display: 'none' }}
        />
      )}
    </>
  );
};

export default FloatingTopAuthorsButton;
