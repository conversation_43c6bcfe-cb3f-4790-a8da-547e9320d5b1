/**
 * Navigation Menu Component
 * 
 * Responsive navigation menu with role-based access control
 */

import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  BarChart3,
  FileText,
  Settings,
  Menu,
  X,
  Database,
  Shield,
  Home,
} from 'lucide-react';
// Authentication removed

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  description?: string;
  badge?: string;
  external?: boolean;
}

interface NavigationMenuProps {
  className?: string;
}

const NavigationMenu: React.FC<NavigationMenuProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Define navigation items - no authentication required
  const navItems: NavigationItem[] = [
    {
      id: 'home',
      label: 'Home',
      path: '/',
      icon: Home,
      description: 'Go to homepage',
    },
    {
      id: 'analysis',
      label: 'Analysis',
      path: '/analysis',
      icon: BarChart3,
      description: 'View analytics dashboard',
    },
    {
      id: 'files',
      label: 'Files',
      path: '/files',
      icon: FileText,
      description: 'Manage files and documents',
    },
    {
      id: 'settings',
      label: 'Settings',
      path: '/settings',
      icon: Settings,
      description: 'User preferences and settings',
    },
    {
      id: 'admin',
      label: 'Admin',
      path: '/admin',
      icon: Shield,
      description: 'Admin system settings',
    },
    {
      id: 'dashboard',
      label: 'Backend',
      path: '/dashboard',
      icon: Database,
      description: 'Backend dashboard',
    },
  ];

  const handleNavigation = (path: string, external = false) => {
    if (external) {
      window.open(path, '_blank');
    } else {
      navigate(path);
    }
    setIsMobileMenuOpen(false);
  };

  const isActiveRoute = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const renderNavItem = (item: NavigationItem) => {
    const isActive = isActiveRoute(item.path);
    const Icon = item.icon;

    return (
      <button
        key={item.id}
        onClick={() => handleNavigation(item.path, item.external)}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 
          hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500/50
          ${isActive 
            ? 'bg-primary-600/20 border-primary-500/40 text-primary-300' 
            : 'bg-theme-jet/70 border-gray-500/40 text-gray-200 hover:bg-primary-900/30 hover:border-primary-500/40 hover:text-primary-300'
          }
          backdrop-blur-sm border shadow-sm
        `}
        title={item.description}
      >
        <Icon size={16} className="flex-shrink-0" />
        <span className="text-xs font-medium">{item.label}</span>
        {item.badge && (
          <span className="px-1.5 py-0.5 text-xs font-medium bg-primary-500/20 text-primary-400 rounded border border-primary-500/30">
            {item.badge}
          </span>
        )}
      </button>
    );
  };

  const renderMobileNavItem = (item: NavigationItem) => {
    const isActive = isActiveRoute(item.path);
    const Icon = item.icon;

    return (
      <button
        key={item.id}
        onClick={() => handleNavigation(item.path, item.external)}
        className={`
          w-full flex items-center space-x-3 px-4 py-3 transition-colors duration-200
          ${isActive 
            ? 'bg-primary-600/20 text-primary-300 border-l-2 border-primary-500' 
            : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
          }
        `}
      >
        <Icon size={18} />
        <div className="flex-1 text-left">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">{item.label}</span>
            {item.badge && (
              <span className="px-1.5 py-0.5 text-xs font-medium bg-primary-500/20 text-primary-400 rounded border border-primary-500/30">
                {item.badge}
              </span>
            )}
          </div>
          {item.description && (
            <p className="text-xs text-gray-400 mt-0.5">{item.description}</p>
          )}
        </div>
      </button>
    );
  };

  return (
    <>
      {/* Desktop Navigation */}
      <nav className={`hidden md:flex items-center space-x-2 ${className}`}>
        {navItems.map(renderNavItem)}
      </nav>

      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="md:hidden flex items-center justify-center w-10 h-10 rounded-lg bg-theme-jet/70 backdrop-blur-sm border border-gray-500/40 text-gray-200 hover:text-primary-300 hover:bg-primary-900/30 hover:border-primary-500/40 transition-all duration-200"
        aria-label="Toggle mobile menu"
      >
        {isMobileMenuOpen ? <X size={18} /> : <Menu size={18} />}
      </button>

      {/* Mobile Navigation Overlay */}
      {isMobileMenuOpen && (
        <div className="md:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
          <div className="fixed right-0 top-0 h-full w-80 max-w-[90vw] bg-theme-eerie-black/95 backdrop-blur-sm border-l border-gray-500/40 shadow-xl">
            {/* Mobile Menu Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-500/30">
              <h2 className="text-white font-medium">Navigation</h2>
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className="w-8 h-8 rounded-lg bg-gray-700/50 text-gray-300 hover:text-white hover:bg-gray-600/50 transition-colors duration-200 flex items-center justify-center"
                aria-label="Close menu"
              >
                <X size={16} />
              </button>
            </div>

            {/* Mobile Menu Items */}
            <div className="py-4">
              <div className="mb-4">
                <h3 className="px-4 text-xs font-medium text-gray-400 uppercase tracking-wider mb-2">
                  Navigation
                </h3>
                {navItems.map(renderMobileNavItem)}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default NavigationMenu;
