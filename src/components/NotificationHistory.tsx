/**
 * NotificationHistory Component
 * Advanced notification management with history, filtering, and bulk operations
 */

import React, { useState, useMemo } from 'react';
import {
  Bell,
  Search,
  Filter,
  Calendar,
  Download,
  Trash2,
  Archive,
  CheckCircle2,
  X,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Eye,
  EyeOff
} from 'lucide-react';
import { useNotifications } from '../contexts/NotificationContext';
import { Notification as AppNotification, NotificationType, NotificationPriority } from '../types/notification';
import NotificationItem from './NotificationItem';
import { 
  filterNotifications, 
  sortNotifications, 
  groupNotificationsByDate,
  formatRelativeTime 
} from '../utils/notificationUtils';

interface NotificationHistoryProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationHistory: React.FC<NotificationHistoryProps> = ({
  isOpen,
  onClose
}) => {
  const {
    notifications,
    stats,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    archiveNotification,
    bulkMarkAsRead,
    bulkDelete,
    bulkArchive,
    isLoading,
    error
  } = useNotifications();

  // State for filtering and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<NotificationType[]>([]);
  const [selectedPriorities, setSelectedPriorities] = useState<NotificationPriority[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({ start: '', end: '' });
  const [sortBy, setSortBy] = useState<'date' | 'priority' | 'type'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');

  // Filter and sort notifications
  const filteredNotifications = useMemo(() => {
    let filtered = filterNotifications(
      notifications,
      searchTerm,
      selectedTypes,
      selectedPriorities,
      selectedStatuses
    );

    // Apply date range filter
    if (dateRange.start || dateRange.end) {
      filtered = filtered.filter(notification => {
        const notificationDate = new Date(notification.createdAt);
        const startDate = dateRange.start ? new Date(dateRange.start) : null;
        const endDate = dateRange.end ? new Date(dateRange.end) : null;

        if (startDate && notificationDate < startDate) return false;
        if (endDate && notificationDate > endDate) return false;
        return true;
      });
    }

    return sortNotifications(filtered, sortBy, sortOrder);
  }, [notifications, searchTerm, selectedTypes, selectedPriorities, selectedStatuses, dateRange, sortBy, sortOrder]);

  // Pagination
  const totalPages = Math.ceil(filteredNotifications.length / itemsPerPage);
  const paginatedNotifications = filteredNotifications.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Group notifications by date for display
  const groupedNotifications = groupNotificationsByDate(paginatedNotifications);

  const handleSelectNotification = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedNotifications(prev => [...prev, id]);
    } else {
      setSelectedNotifications(prev => prev.filter(nId => nId !== id));
    }
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === paginatedNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(paginatedNotifications.map(n => n.id));
    }
  };

  const handleBulkAction = async (action: 'read' | 'delete' | 'archive') => {
    if (selectedNotifications.length === 0) return;

    switch (action) {
      case 'read':
        await bulkMarkAsRead(selectedNotifications);
        break;
      case 'delete':
        await bulkDelete(selectedNotifications);
        break;
      case 'archive':
        await bulkArchive(selectedNotifications);
        break;
    }
    setSelectedNotifications([]);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedTypes([]);
    setSelectedPriorities([]);
    setSelectedStatuses([]);
    setDateRange({ start: '', end: '' });
    setCurrentPage(1);
  };

  const exportNotifications = () => {
    const csvContent = [
      ['ID', 'Type', 'Priority', 'Status', 'Title', 'Message', 'Category', 'Created At', 'Read At'].join(','),
      ...filteredNotifications.map(n => [
        n.id,
        n.type,
        n.priority,
        n.status,
        `"${n.title}"`,
        `"${n.message}"`,
        n.category || '',
        n.createdAt.toISOString(),
        n.readAt?.toISOString() || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `notifications-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const hasActiveFilters = searchTerm || selectedTypes.length > 0 || selectedPriorities.length > 0 || 
                          selectedStatuses.length > 0 || dateRange.start || dateRange.end;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-orange-jasper/5 to-orange-400/5">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Bell size={24} className="text-orange-jasper" />
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Notification History</h2>
                <p className="text-sm text-gray-600">
                  {stats.total} total notifications • {stats.unread} unread
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200"
            >
              <X size={20} />
            </button>
          </div>

          {/* Search and Controls */}
          <div className="mt-4 flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search notifications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-jasper/30 focus:border-orange-jasper"
              />
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`
                  flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200
                  ${showFilters ? 'bg-orange-100 text-orange-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}
                `}
              >
                <Filter size={16} />
                <span>Filters</span>
                {hasActiveFilters && (
                  <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                )}
              </button>

              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newSortOrder] = e.target.value.split('-') as ['date' | 'priority' | 'type', 'asc' | 'desc'];
                  setSortBy(newSortBy);
                  setSortOrder(newSortOrder);
                }}
                className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-jasper/30"
              >
                <option value="date-desc">Newest First</option>
                <option value="date-asc">Oldest First</option>
                <option value="priority-desc">High Priority First</option>
                <option value="priority-asc">Low Priority First</option>
                <option value="type-asc">Type A-Z</option>
                <option value="type-desc">Type Z-A</option>
              </select>

              <button
                onClick={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
                className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
                title={`Switch to ${viewMode === 'list' ? 'grid' : 'list'} view`}
              >
                {viewMode === 'list' ? <Eye size={16} /> : <EyeOff size={16} />}
              </button>

              <button
                onClick={exportNotifications}
                className="flex items-center space-x-2 px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-all duration-200"
              >
                <Download size={16} />
                <span>Export</span>
              </button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {['success', 'error', 'warning', 'info', 'data_update', 'system', 'file_upload', 'analysis_complete'].map(type => (
                      <label key={type} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedTypes.includes(type as NotificationType)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedTypes(prev => [...prev, type as NotificationType]);
                            } else {
                              setSelectedTypes(prev => prev.filter(t => t !== type));
                            }
                          }}
                          className="mr-2 text-orange-jasper border-gray-300 rounded focus:ring-orange-jasper/30"
                        />
                        <span className="text-sm text-gray-700 capitalize">{type.replace('_', ' ')}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Priority Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                  <div className="space-y-1">
                    {['low', 'medium', 'high', 'urgent'].map(priority => (
                      <label key={priority} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedPriorities.includes(priority as NotificationPriority)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedPriorities(prev => [...prev, priority as NotificationPriority]);
                            } else {
                              setSelectedPriorities(prev => prev.filter(p => p !== priority));
                            }
                          }}
                          className="mr-2 text-orange-jasper border-gray-300 rounded focus:ring-orange-jasper/30"
                        />
                        <span className="text-sm text-gray-700 capitalize">{priority}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <div className="space-y-1">
                    {['unread', 'read', 'archived'].map(status => (
                      <label key={status} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedStatuses.includes(status)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedStatuses(prev => [...prev, status]);
                            } else {
                              setSelectedStatuses(prev => prev.filter(s => s !== status));
                            }
                          }}
                          className="mr-2 text-orange-jasper border-gray-300 rounded focus:ring-orange-jasper/30"
                        />
                        <span className="text-sm text-gray-700 capitalize">{status}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Date Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                  <div className="space-y-2">
                    <input
                      type="date"
                      value={dateRange.start}
                      onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                      className="w-full px-3 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-orange-jasper/30"
                      placeholder="Start date"
                    />
                    <input
                      type="date"
                      value={dateRange.end}
                      onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                      className="w-full px-3 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-orange-jasper/30"
                      placeholder="End date"
                    />
                  </div>
                </div>
              </div>

              {hasActiveFilters && (
                <div className="flex justify-end">
                  <button
                    onClick={clearFilters}
                    className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-200"
                  >
                    Clear All Filters
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Bulk Actions */}
        {selectedNotifications.length > 0 && (
          <div className="px-6 py-3 bg-blue-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700">
                {selectedNotifications.length} notification{selectedNotifications.length > 1 ? 's' : ''} selected
              </span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleBulkAction('read')}
                  className="flex items-center space-x-1 px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-all duration-200"
                >
                  <CheckCircle2 size={14} />
                  <span>Mark as Read</span>
                </button>
                <button
                  onClick={() => handleBulkAction('archive')}
                  className="flex items-center space-x-1 px-3 py-1.5 text-sm bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-all duration-200"
                >
                  <Archive size={14} />
                  <span>Archive</span>
                </button>
                <button
                  onClick={() => handleBulkAction('delete')}
                  className="flex items-center space-x-1 px-3 py-1.5 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-all duration-200"
                >
                  <Trash2 size={14} />
                  <span>Delete</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin w-8 h-8 border-2 border-orange-jasper border-t-transparent rounded-full"></div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <p className="text-red-600 mb-2">Error loading notifications</p>
                <p className="text-sm text-gray-500">{error}</p>
              </div>
            </div>
          ) : filteredNotifications.length > 0 ? (
            <div className="h-full overflow-y-auto">
              {Object.entries(groupedNotifications).map(([dateGroup, groupNotifications]) => (
                <div key={dateGroup}>
                  <div className="sticky top-0 px-6 py-2 bg-gray-100 border-b border-gray-200 z-10">
                    <h3 className="text-sm font-medium text-gray-600 uppercase tracking-wide">
                      {dateGroup} ({groupNotifications.length})
                    </h3>
                  </div>
                  {groupNotifications.map((notification) => (
                    <div key={notification.id} className="relative border-b border-gray-100 last:border-b-0">
                      <div className="absolute left-4 top-4 z-10">
                        <input
                          type="checkbox"
                          checked={selectedNotifications.includes(notification.id)}
                          onChange={(e) => handleSelectNotification(notification.id, e.target.checked)}
                          className="w-4 h-4 text-orange-jasper border-gray-300 rounded focus:ring-orange-jasper/30"
                        />
                      </div>
                      <div className="ml-10">
                        <NotificationItem
                          notification={notification}
                          onMarkAsRead={markAsRead}
                          onDelete={deleteNotification}
                          onArchive={archiveNotification}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Bell size={48} className="mx-auto text-gray-400 mb-4" />
                <p className="text-lg text-gray-500 mb-2">
                  {hasActiveFilters ? 'No notifications match your filters' : 'No notifications found'}
                </p>
                {hasActiveFilters && (
                  <button
                    onClick={clearFilters}
                    className="text-orange-jasper hover:text-orange-600 font-medium"
                  >
                    Clear filters
                  </button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer with Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleSelectAll}
                  className="text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200"
                >
                  {selectedNotifications.length === paginatedNotifications.length ? 'Deselect All' : 'Select All'}
                </button>
                <span className="text-sm text-gray-500">
                  Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, filteredNotifications.length)} of {filteredNotifications.length}
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg hover:bg-gray-100 transition-all duration-200"
                >
                  <ChevronLeft size={16} />
                </button>
                
                <span className="px-3 py-1 text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                
                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg hover:bg-gray-100 transition-all duration-200"
                >
                  <ChevronRight size={16} />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationHistory;
