import React from 'react';
import {
  Upload,
  Search,
  Database,
  FileText
} from 'lucide-react';

interface FileData {
  id: string;
  name: string;
  type: 'category' | 'competitor';
  size: string;
  uploadDate: string;
  lastModified: string;
  author: string;
}

interface FileStatsProps {
  files: FileData[];
  filteredFiles: FileData[];
  searchQuery: string;
  filterType: 'all' | 'category' | 'competitor';
  loading: boolean;
  onSearchChange: (query: string) => void;
  onFilterChange: (filter: 'all' | 'category' | 'competitor') => void;
  onUpload: () => void;
}

const FileStats: React.FC<FileStatsProps> = ({
  files,
  filteredFiles,
  searchQuery,
  filterType,
  loading,
  onSearchChange,
  onFilterChange,
  onUpload
}) => {
  const totalFiles = files.length;
  const categoryFiles = files.filter(f => f.type === 'category').length;
  const competitorFiles = files.filter(f => f.type === 'competitor').length;

  // Calculate total size (simplified - in real app you'd parse the size strings)
  const totalSize = files.length > 0 ? `${files.length * 2.5}MB` : '0MB';

  return (
    <div className="space-y-1 md:space-y-2">
      {/* Upload Button */}
      <div className="flex items-center justify-end mb-1">
        <button
          onClick={onUpload}
          className="flex items-center space-x-1 px-2 py-1.5 md:px-3 md:py-2 bg-[#9CEE69] text-[#1A1919] rounded-md hover:bg-[#9CEE69]/80 transition-colors duration-200 text-xs md:text-sm"
        >
          <Upload size={12} className="md:w-4 md:h-4" />
          <span className="font-medium">Upload File</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-1 md:gap-2">
        <div className="bg-white p-2 md:p-3 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center space-x-2">
            <div className="p-1 md:p-1.5 bg-blue-100 rounded-md">
              <Database size={14} className="md:w-4 md:h-4 text-blue-600" />
            </div>
            <div>
              <p className="text-xs text-gray-600">Total Files</p>
              <p className="text-sm md:text-base font-semibold text-gray-900">{totalFiles}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-2 md:p-3 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center space-x-2">
            <div className="p-1 md:p-1.5 bg-green-100 rounded-md">
              <FileText size={14} className="md:w-4 md:h-4 text-green-600" />
            </div>
            <div>
              <p className="text-xs text-gray-600">Category Files</p>
              <p className="text-sm md:text-base font-semibold text-gray-900">{categoryFiles}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-2 md:p-3 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center space-x-2">
            <div className="p-1 md:p-1.5 bg-purple-100 rounded-md">
              <FileText size={14} className="md:w-4 md:h-4 text-purple-600" />
            </div>
            <div>
              <p className="text-xs text-gray-600">Competitor Files</p>
              <p className="text-sm md:text-base font-semibold text-gray-900">{competitorFiles}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-2 md:p-3 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center space-x-2">
            <div className="p-1 md:p-1.5 bg-[#9CEE69] rounded-md">
              <Database size={14} className="md:w-4 md:h-4 text-[#1A1919]" />
            </div>
            <div>
              <p className="text-xs text-gray-600">Total Size</p>
              <p className="text-sm md:text-base font-semibold text-gray-900">{totalSize}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Controls */}
      <div className="bg-white p-2 md:p-3 rounded-lg border border-gray-200 shadow-sm">
        <div className="flex flex-col sm:flex-row gap-2 md:gap-3">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search size={12} className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="w-full pl-8 pr-3 py-1.5 md:py-2 text-xs md:text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#9CEE69] focus:border-transparent"
              />
            </div>
          </div>

          {/* Filter */}
          <div className="sm:w-40 md:w-48">
            <select
              value={filterType}
              onChange={(e) => onFilterChange(e.target.value as 'all' | 'category' | 'competitor')}
              className="w-full px-2.5 py-1.5 md:py-2 text-xs md:text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#9CEE69] focus:border-transparent"
            >
              <option value="all">All Files ({totalFiles})</option>
              <option value="category">Category ({categoryFiles})</option>
              <option value="competitor">Competitor ({competitorFiles})</option>
            </select>
          </div>
        </div>

        {/* Results Summary */}
        {(searchQuery || filterType !== 'all') && (
          <div className="mt-2 pt-2 border-t border-gray-200">
            <p className="text-xs text-gray-600">
              Showing {filteredFiles.length} of {totalFiles} files
              {searchQuery && (
                <span> matching "{searchQuery}"</span>
              )}
              {filterType !== 'all' && (
                <span> in {filterType} category</span>
              )}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default FileStats;
