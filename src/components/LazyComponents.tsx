/**
 * Lazy Components - Code Splitting Implementation
 * 
 * This file implements lazy loading for heavy components to improve
 * initial page load performance through code splitting.
 */

import React, { Suspense } from 'react';

// Loading component for lazy-loaded components
const LoadingSpinner: React.FC<{ message?: string }> = ({ message = 'Loading...' }) => (
  <div className="flex items-center justify-center min-h-[200px] w-full">
    <div className="flex flex-col items-center space-y-3">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
      <p className="text-secondary-600 text-sm">{message}</p>
    </div>
  </div>
);

// Error boundary for lazy components
class LazyErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="flex items-center justify-center min-h-[200px] w-full">
          <div className="text-center">
            <p className="text-error-600 font-medium">Failed to load component</p>
            <button
              onClick={() => this.setState({ hasError: false })}
              className="mt-2 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Lazy load heavy components
export const LazyCategories = React.lazy(() => 
  import('./Categories').then(module => ({ default: module.default }))
);

export const LazyCompetitors = React.lazy(() => 
  import('./Competitors').then(module => ({ default: module.default }))
);

export const LazyFileManagement = React.lazy(() => 
  import('./FileManagement').then(module => ({ default: module.default }))
);

export const LazySystemSettings = React.lazy(() => 
  import('./SystemSettings').then(module => ({ default: module.default }))
);

export const LazyNotificationDropdown = React.lazy(() => 
  import('./NotificationDropdown').then(module => ({ default: module.default }))
);

export const LazyTopAuthorsModal = React.lazy(() => 
  import('./TopAuthorsModal').then(module => ({ default: module.default }))
);

// HOC for wrapping lazy components with Suspense and Error Boundary
export const withLazyLoading = <P extends object>(
  LazyComponent: React.LazyExoticComponent<React.ComponentType<P>>,
  loadingMessage?: string,
  fallback?: React.ReactNode
) => {
  const WrappedComponent: React.FC<P> = (props) => (
    <LazyErrorBoundary fallback={fallback}>
      <Suspense fallback={<LoadingSpinner message={loadingMessage} />}>
        <LazyComponent {...props} />
      </Suspense>
    </LazyErrorBoundary>
  );

  WrappedComponent.displayName = `withLazyLoading(${LazyComponent.displayName || 'Component'})`;
  
  return WrappedComponent;
};

// Pre-configured lazy components with appropriate loading messages
export const Categories = withLazyLoading(
  LazyCategories, 
  'Loading Categories Analysis...'
);

export const Competitors = withLazyLoading(
  LazyCompetitors, 
  'Loading Competitors Dashboard...'
);

export const FileManagement = withLazyLoading(
  LazyFileManagement, 
  'Loading File Management...'
);

export const SystemSettings = withLazyLoading(
  LazySystemSettings, 
  'Loading System Settings...'
);

export const NotificationDropdown = withLazyLoading(
  LazyNotificationDropdown, 
  'Loading Notifications...'
);

export const TopAuthorsModal = withLazyLoading(
  LazyTopAuthorsModal, 
  'Loading Authors Data...'
);

// Preload functions for better UX
export const preloadCategories = () => import('./Categories');
export const preloadCompetitors = () => import('./Competitors');
export const preloadFileManagement = () => import('./FileManagement');
export const preloadSystemSettings = () => import('./SystemSettings');

// Preload on hover/focus for better perceived performance
export const usePreloadOnHover = (preloadFn: () => Promise<any>) => {
  const handleMouseEnter = React.useCallback(() => {
    preloadFn().catch(console.error);
  }, [preloadFn]);

  const handleFocus = React.useCallback(() => {
    preloadFn().catch(console.error);
  }, [preloadFn]);

  return { onMouseEnter: handleMouseEnter, onFocus: handleFocus };
};

export default {
  Categories,
  Competitors,
  FileManagement,
  SystemSettings,
  NotificationDropdown,
  TopAuthorsModal,
  withLazyLoading,
  usePreloadOnHover,
  preloadCategories,
  preloadCompetitors,
  preloadFileManagement,
  preloadSystemSettings,
};
