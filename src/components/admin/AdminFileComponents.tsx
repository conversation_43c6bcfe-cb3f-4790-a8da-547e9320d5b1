import React, { useState } from 'react';
import { 
  CheckSquare, 
  Square, 
  Trash2, 
  Download, 
  Archive, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Folder,
  Settings,
  Calendar,
  Filter
} from 'lucide-react';
import { FileData } from '../../types/file';

interface BulkOperationsProps {
  files: FileData[];
  selectedFiles: string[];
  onSelectionChange: (fileIds: string[]) => void;
  onBulkDelete: (fileIds: string[]) => Promise<void>;
  onBulkDownload: (fileIds: string[]) => Promise<void>;
  onBulkArchive: (fileIds: string[]) => Promise<void>;
  loading: boolean;
}

export const BulkOperationsPanel: React.FC<BulkOperationsProps> = ({
  files,
  selectedFiles,
  onSelectionChange,
  onBulkDelete,
  onBulkDownload,
  onBulkArchive,
  loading
}) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [operationType, setOperationType] = useState<'delete' | 'archive' | null>(null);

  const handleSelectAll = () => {
    if (selectedFiles.length === files.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(files.map(f => f.id));
    }
  };

  const handleBulkOperation = async (type: 'delete' | 'download' | 'archive') => {
    if (selectedFiles.length === 0) return;

    if (type === 'download') {
      await onBulkDownload(selectedFiles);
      return;
    }

    // For destructive operations, show confirmation
    setOperationType(type);
    setShowConfirmDialog(true);
  };

  const confirmOperation = async () => {
    if (!operationType || selectedFiles.length === 0) return;

    try {
      if (operationType === 'delete') {
        await onBulkDelete(selectedFiles);
      } else if (operationType === 'archive') {
        await onBulkArchive(selectedFiles);
      }
      
      onSelectionChange([]); // Clear selection after operation
    } finally {
      setShowConfirmDialog(false);
      setOperationType(null);
    }
  };

  const isAllSelected = files.length > 0 && selectedFiles.length === files.length;
  const isPartiallySelected = selectedFiles.length > 0 && selectedFiles.length < files.length;

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <button
            onClick={handleSelectAll}
            className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-gray-900"
          >
            {isAllSelected ? (
              <CheckSquare size={16} className="text-primary-600" />
            ) : isPartiallySelected ? (
              <div className="w-4 h-4 bg-primary-600 rounded border-2 border-primary-600 flex items-center justify-center">
                <div className="w-2 h-0.5 bg-white"></div>
              </div>
            ) : (
              <Square size={16} className="text-gray-400" />
            )}
            <span>
              {selectedFiles.length > 0 
                ? `${selectedFiles.length} selected` 
                : 'Select all'
              }
            </span>
          </button>
          
          {selectedFiles.length > 0 && (
            <span className="text-xs text-gray-500">
              ({files.length} total files)
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleBulkOperation('download')}
            disabled={selectedFiles.length === 0 || loading}
            className="flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium hover:bg-blue-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Download size={12} />
            <span>Download</span>
          </button>
          
          <button
            onClick={() => handleBulkOperation('archive')}
            disabled={selectedFiles.length === 0 || loading}
            className="flex items-center space-x-1 px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium hover:bg-yellow-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Archive size={12} />
            <span>Archive</span>
          </button>
          
          <button
            onClick={() => handleBulkOperation('delete')}
            disabled={selectedFiles.length === 0 || loading}
            className="flex items-center space-x-1 px-3 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium hover:bg-red-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Trash2 size={12} />
            <span>Delete</span>
          </button>
        </div>
      </div>

      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <AlertTriangle size={20} className="text-red-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">
                Confirm {operationType === 'delete' ? 'Deletion' : 'Archive'}
              </h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              Are you sure you want to {operationType} {selectedFiles.length} selected file(s)? 
              {operationType === 'delete' && ' This action cannot be undone.'}
            </p>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConfirmDialog(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={confirmOperation}
                disabled={loading}
                className={`px-4 py-2 rounded-lg text-white font-medium transition-colors ${
                  operationType === 'delete'
                    ? 'bg-red-600 hover:bg-red-700'
                    : 'bg-yellow-600 hover:bg-yellow-700'
                } disabled:opacity-50`}
              >
                {loading ? (
                  <RefreshCw size={16} className="animate-spin" />
                ) : (
                  `${operationType === 'delete' ? 'Delete' : 'Archive'}`
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

interface FileVersioningProps {
  files: FileData[];
  onCreateBackup: (fileId: string) => Promise<void>;
  onRestoreVersion: (fileId: string, version: string) => Promise<void>;
  loading: boolean;
}

export const FileVersioningPanel: React.FC<FileVersioningProps> = ({
  files,
  onCreateBackup,
  onRestoreVersion,
  loading
}) => {
  const [selectedFile, setSelectedFile] = useState<string>('');

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center mb-4">
        <Clock size={20} className="mr-2 text-primary-600" />
        <h3 className="text-lg font-semibold text-[#1A1919]">File Versioning</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-2">Create Backup</h4>
          <p className="text-sm text-gray-600 mb-3">
            Create a backup version of a file before making changes
          </p>
          
          <select
            value={selectedFile}
            onChange={(e) => setSelectedFile(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm mb-3"
          >
            <option value="">Select a file...</option>
            {files.map(file => (
              <option key={file.id} value={file.id}>
                {file.name}
              </option>
            ))}
          </select>
          
          <button
            onClick={() => selectedFile && onCreateBackup(selectedFile)}
            disabled={!selectedFile || loading}
            className="w-full px-3 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            {loading ? (
              <RefreshCw size={14} className="animate-spin mx-auto" />
            ) : (
              'Create Backup'
            )}
          </button>
        </div>
        
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-2">Version History</h4>
          <p className="text-sm text-gray-600 mb-3">
            View and restore previous versions of files
          </p>
          
          <div className="space-y-2">
            <div className="text-xs text-gray-500 p-2 bg-white rounded border">
              No version history available
            </div>
          </div>
          
          <button
            disabled
            className="w-full px-3 py-2 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed text-sm mt-3"
          >
            Coming Soon
          </button>
        </div>
      </div>
    </div>
  );
};

interface AutomatedCleanupProps {
  onScheduleCleanup: (config: CleanupConfig) => Promise<void>;
  loading: boolean;
}

interface CleanupConfig {
  enabled: boolean;
  retentionDays: number;
  fileTypes: string[];
  schedule: string;
}

export const AutomatedCleanupPanel: React.FC<AutomatedCleanupProps> = ({
  onScheduleCleanup,
  loading
}) => {
  const [config, setConfig] = useState<CleanupConfig>({
    enabled: false,
    retentionDays: 30,
    fileTypes: ['category', 'competitor'],
    schedule: 'weekly'
  });

  const handleSaveConfig = async () => {
    await onScheduleCleanup(config);
  };

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center mb-4">
        <Settings size={20} className="mr-2 text-primary-600" />
        <h3 className="text-lg font-semibold text-[#1A1919]">Automated Cleanup</h3>
      </div>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700">
            Enable Automated Cleanup
          </label>
          <input
            type="checkbox"
            checked={config.enabled}
            onChange={(e) => setConfig(prev => ({ ...prev, enabled: e.target.checked }))}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Retention Period (days)
          </label>
          <input
            type="number"
            value={config.retentionDays}
            onChange={(e) => setConfig(prev => ({ ...prev, retentionDays: Number(e.target.value) }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
            min="1"
            max="365"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Cleanup Schedule
          </label>
          <select
            value={config.schedule}
            onChange={(e) => setConfig(prev => ({ ...prev, schedule: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
        
        <button
          onClick={handleSaveConfig}
          disabled={loading}
          className="w-full px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <RefreshCw size={16} className="animate-spin mx-auto" />
          ) : (
            'Save Configuration'
          )}
        </button>
      </div>
    </div>
  );
};
