import React, { useState, useEffect } from 'react';
import { FileData } from '../types/file';
import { apiService } from '../services/api';
import FileTable from './FileTable';
import FileStats from './FileStats';
import UploadModal from './UploadModal';
import RenameModal from './RenameModal';
import DeleteModal from './DeleteModal';
import Toast from './Toast';
import { createApiErrorNotification, createEmptyDataNotification, createErrorState, ErrorState } from '../utils/notificationUtils';
import { XCircle, Info, FileX } from 'lucide-react';
import { BulkOperationsPanel, FileVersioningPanel, AutomatedCleanupPanel } from './admin/AdminFileComponents';

interface ToastState {
  message: string;
  type: 'success' | 'error' | 'info';
  visible: boolean;
}

interface FileManagementProps {
  adminMode?: boolean; // NEW: Enable admin-specific features
  userRole?: 'user' | 'admin'; // NEW: User role for feature access
  className?: string; // NEW: Custom styling
}

const FileManagement: React.FC<FileManagementProps> = ({
  adminMode = false,
  userRole = 'user',
  className = ''
}) => {
  // File data state
  const [files, setFiles] = useState<FileData[]>([]);
  const [loading, setLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [errorState, setErrorState] = useState<ErrorState>({ hasError: false, errorType: 'unknown', title: '', message: '' });

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'category' | 'competitor'>('all');

  // Modal states
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileData | null>(null);

  // Upload state
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'validating' | 'uploading' | 'success' | 'error'>('idle');
  const [uploadMessage, setUploadMessage] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // Operation loading states
  const [operationLoading, setOperationLoading] = useState<Record<string, boolean>>({});
  const [operationErrors, setOperationErrors] = useState<Record<string, string>>({});

  // Admin-specific state
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [bulkOperationLoading, setBulkOperationLoading] = useState(false);

  // Toast state
  const [toast, setToast] = useState<ToastState>({
    message: '',
    type: 'info',
    visible: false
  });

  // Helper functions for operation states
  const setOperationLoadingState = (operation: string, loading: boolean) => {
    setOperationLoading(prev => ({ ...prev, [operation]: loading }));
  };

  const setOperationError = (operation: string, error: string | null) => {
    setOperationErrors(prev => ({ ...prev, [operation]: error || '' }));
  };

  const clearOperationError = (operation: string) => {
    setOperationError(operation, null);
  };

  // Toast helper functions
  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type, visible: true });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, visible: false }));
  };

  // Load files from API on component mount
  useEffect(() => {
    loadFiles();
  }, []);

  const loadFiles = async () => {
    try {
      setLoading(true);
      setApiError(null);
      setErrorState({ hasError: false, errorType: 'unknown', title: '', message: '' });

      const response = await apiService.getFiles();
      if (response.success && response.data) {
        setFiles(response.data.files || []);
      } else {
        throw new Error(response.error || 'Failed to load files');
      }
    } catch (error) {
      console.error('Failed to load files:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setApiError(errorMessage);

      // Create error state for UI display
      const newErrorState = createErrorState(errorMessage, 'file');
      setErrorState(newErrorState);

      // Clear files instead of using fallback
      setFiles([]);

      // Show notification
      const notification = createApiErrorNotification(errorMessage);
      showToast(notification.message, notification.type);
    } finally {
      setLoading(false);
    }
  };

  // Filter files based on search and type
  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = filterType === 'all' || file.type === filterType;
    return matchesSearch && matchesType;
  });

  // Upload handlers
  const handleFileUpload = async (file: File) => {
    try {
      setIsUploading(true);
      setUploadStatus('validating');
      setUploadMessage('Validating file...');
      setUploadProgress(10);

      // Validate file
      const validationResponse = await apiService.validateFile(file);
      if (!validationResponse.success) {
        throw new Error(validationResponse.error || 'File validation failed');
      }

      setUploadStatus('uploading');
      setUploadMessage('Uploading file...');
      setUploadProgress(50);

      // Upload file
      const uploadResponse = await apiService.uploadFile(file);
      if (!uploadResponse.success) {
        throw new Error(uploadResponse.error || 'Upload failed');
      }

      setUploadStatus('success');
      setUploadMessage('File uploaded successfully!');
      setUploadProgress(100);

      // Refresh file list
      await loadFiles();
      showToast('File uploaded successfully!', 'success');

      // Close modal after delay
      setTimeout(() => {
        setShowUploadModal(false);
        resetUploadState();
      }, 2000);

    } catch (error) {
      setUploadStatus('error');
      setUploadMessage(error instanceof Error ? error.message : 'Upload failed');
      setUploadProgress(0);
      showToast('Upload failed', 'error');
    } finally {
      setIsUploading(false);
    }
  };

  const resetUploadState = () => {
    setUploadStatus('idle');
    setUploadMessage('');
    setUploadProgress(0);
    setIsUploading(false);
  };

  // File operation handlers
  const handleDownload = async (file: FileData) => {
    const operationKey = `download-${file.id}`;

    try {
      setOperationLoadingState(operationKey, true);
      clearOperationError(operationKey);

      const response = await apiService.downloadFile(file.id);

      if (!response.success || !response.blob) {
        throw new Error(response.error || 'Download failed');
      }

      // Create download link
      const link = document.createElement('a');
      const url = URL.createObjectURL(response.blob);

      link.setAttribute('href', url);
      link.setAttribute('download', file.name);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      showToast('File downloaded successfully!', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Failed to download file:', error);
      setOperationError(operationKey, errorMessage);
      showToast(`Failed to download file: ${errorMessage}`, 'error');
    } finally {
      setOperationLoadingState(operationKey, false);
    }
  };

  const handleRenameClick = (file: FileData) => {
    setSelectedFile(file);
    setShowRenameModal(true);
  };

  const handleRename = async (newName: string) => {
    if (!selectedFile || !newName.trim()) return;

    const operationKey = `rename-${selectedFile.id}`;

    try {
      setOperationLoadingState(operationKey, true);
      clearOperationError(operationKey);

      const response = await apiService.renameFile(selectedFile.id, newName.trim());

      if (!response.success) {
        throw new Error(response.error || 'Rename failed');
      }

      // Refresh file list from server to get updated data
      await loadFiles();
      showToast('File renamed successfully!', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Failed to rename file:', error);
      setOperationError(operationKey, errorMessage);
      showToast(`Failed to rename file: ${errorMessage}`, 'error');
      throw error; // Re-throw to let modal handle it
    } finally {
      setOperationLoadingState(operationKey, false);
    }
  };

  const handleDeleteClick = (fileId: string) => {
    const fileToDelete = files.find(f => f.id === fileId);
    if (!fileToDelete) return;

    setSelectedFile(fileToDelete);
    setShowDeleteModal(true);
  };

  const handleDelete = async () => {
    if (!selectedFile) return;

    const operationKey = `delete-${selectedFile.id}`;

    try {
      setOperationLoadingState(operationKey, true);
      clearOperationError(operationKey);

      const response = await apiService.deleteFile(selectedFile.id);

      if (!response.success) {
        throw new Error(response.error || 'Delete failed');
      }

      // Refresh file list from server to ensure consistency
      await loadFiles();
      showToast('File deleted successfully!', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Failed to delete file:', error);
      setOperationError(operationKey, errorMessage);
      showToast(`Failed to delete file: ${errorMessage}`, 'error');
      throw error; // Re-throw to let modal handle it
    } finally {
      setOperationLoadingState(operationKey, false);
    }
  };

  // Admin-specific handlers
  const handleBulkDelete = async (fileIds: string[]) => {
    setBulkOperationLoading(true);
    try {
      for (const fileId of fileIds) {
        const response = await apiService.deleteFile(fileId);
        if (!response.success) {
          throw new Error(`Failed to delete ${fileId}: ${response.error}`);
        }
      }
      await loadFiles();
      showToast(`Successfully deleted ${fileIds.length} files`, 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Bulk delete failed';
      showToast(errorMessage, 'error');
      throw error;
    } finally {
      setBulkOperationLoading(false);
    }
  };

  const handleBulkDownload = async (fileIds: string[]) => {
    setBulkOperationLoading(true);
    try {
      for (const fileId of fileIds) {
        const response = await apiService.downloadFile(fileId);
        if (!response.success) {
          throw new Error(`Failed to download ${fileId}: ${response.error}`);
        }
      }
      showToast(`Successfully downloaded ${fileIds.length} files`, 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Bulk download failed';
      showToast(errorMessage, 'error');
      throw error;
    } finally {
      setBulkOperationLoading(false);
    }
  };

  const handleBulkArchive = async (fileIds: string[]) => {
    setBulkOperationLoading(true);
    try {
      // For now, we'll simulate archiving by creating backups
      for (const fileId of fileIds) {
        // This would call a backend archive endpoint when implemented
        console.log(`Archiving file: ${fileId}`);
      }
      showToast(`Successfully archived ${fileIds.length} files`, 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Bulk archive failed';
      showToast(errorMessage, 'error');
      throw error;
    } finally {
      setBulkOperationLoading(false);
    }
  };

  const handleCreateBackup = async (fileId: string) => {
    try {
      // This would call a backend backup endpoint when implemented
      console.log(`Creating backup for file: ${fileId}`);
      showToast('Backup created successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Backup creation failed';
      showToast(errorMessage, 'error');
      throw error;
    }
  };

  const handleRestoreVersion = async (fileId: string, version: string) => {
    try {
      // This would call a backend restore endpoint when implemented
      console.log(`Restoring file ${fileId} to version ${version}`);
      showToast('File restored successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'File restore failed';
      showToast(errorMessage, 'error');
      throw error;
    }
  };

  const handleScheduleCleanup = async (config: any) => {
    try {
      // This would call a backend cleanup configuration endpoint when implemented
      console.log('Cleanup configuration saved:', config);
      showToast('Cleanup configuration saved successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save cleanup configuration';
      showToast(errorMessage, 'error');
      throw error;
    }
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Main Layout - Mengikuti standar Categories.tsx */}
      <div className={adminMode ? "space-y-4" : "p-1 md:p-2 lg:p-3 pt-8 md:pt-10 lg:pt-12"}>
        {/* File Management Card */}
        <div className="bg-white border border-gray-200 rounded-lg p-2 md:p-3 lg:p-4 shadow-sm">
          {/* Header Section */}
          <div className="flex items-center justify-between mb-2 md:mb-3">
            <div>
              <h2 className="text-base md:text-lg font-semibold text-[#1A1919]">File Management</h2>
              <p className="text-xs text-gray-600">Upload, manage, and organize your CSV data files</p>
            </div>
          </div>

          {/* File Stats */}
          <div className="mb-1 md:mb-2">
            <FileStats
              files={files}
              filteredFiles={filteredFiles}
              searchQuery={searchQuery}
              filterType={filterType}
              loading={loading}
              onSearchChange={setSearchQuery}
              onFilterChange={setFilterType}
              onUpload={() => setShowUploadModal(true)}
            />
          </div>

          {/* Admin Features - Only show in admin mode */}
          {adminMode && userRole === 'admin' && (
            <div className="mb-4 space-y-4">
              {/* Bulk Operations */}
              <BulkOperationsPanel
                files={filteredFiles}
                selectedFiles={selectedFiles}
                onSelectionChange={setSelectedFiles}
                onBulkDelete={handleBulkDelete}
                onBulkDownload={handleBulkDownload}
                onBulkArchive={handleBulkArchive}
                loading={bulkOperationLoading}
              />

              {/* File Versioning */}
              <FileVersioningPanel
                files={filteredFiles}
                onCreateBackup={handleCreateBackup}
                onRestoreVersion={handleRestoreVersion}
                loading={bulkOperationLoading}
              />

              {/* Automated Cleanup */}
              <AutomatedCleanupPanel
                onScheduleCleanup={handleScheduleCleanup}
                loading={bulkOperationLoading}
              />
            </div>
          )}

          {/* File Table */}
          <div className="mt-1 md:mt-2">
            <div className="bg-white/40 backdrop-blur-sm rounded-xl border border-white/30 overflow-hidden shadow-lg">
              <FileTable
                files={filteredFiles}
                loading={loading}
                errorState={errorState}
                operationLoading={operationLoading}
                onDownload={handleDownload}
                onRename={handleRenameClick}
                onDelete={handleDeleteClick}
                onRetry={loadFiles}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <UploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleFileUpload}
        uploadStatus={uploadStatus}
        uploadMessage={uploadMessage}
        uploadProgress={uploadProgress}
        isUploading={isUploading}
        onReset={resetUploadState}
      />

      <RenameModal
        isOpen={showRenameModal}
        onClose={() => setShowRenameModal(false)}
        onRename={handleRename}
        currentName={selectedFile?.name || ''}
        loading={selectedFile ? operationLoading[`rename-${selectedFile.id}`] : false}
      />

      <DeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onDelete={handleDelete}
        fileName={selectedFile?.name || ''}
        loading={selectedFile ? operationLoading[`delete-${selectedFile.id}`] : false}
      />

      {/* Toast */}
      <Toast
        message={toast.message}
        type={toast.type}
        visible={toast.visible}
        onClose={hideToast}
      />
    </div>
  );
};

export default FileManagement;
