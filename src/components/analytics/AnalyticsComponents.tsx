import React, { useState, useEffect } from 'react';
import { 
  Bar<PERSON>hart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  FileText, 
  Activity,
  Clock,
  Target,
  Zap,
  Database,
  RefreshCw,
  Calendar,
  <PERSON><PERSON>hart,
  LineChart,
  AlertCircle
} from 'lucide-react';

// Types for analytics data
interface DataTrends {
  totalFiles: number;
  totalRecords: number;
  weeklyGrowth: number;
  monthlyGrowth: number;
  topCategories: Array<{
    name: string;
    count: number;
    growth: number;
  }>;
  recentActivity: Array<{
    date: string;
    uploads: number;
    processing: number;
  }>;
}

interface UsageStatistics {
  activeUsers: number;
  totalSessions: number;
  averageSessionDuration: number;
  topFeatures: Array<{
    feature: string;
    usage: number;
    trend: 'up' | 'down' | 'stable';
  }>;
  deviceBreakdown: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
}

interface PerformanceInsights {
  averageLoadTime: number;
  cacheHitRate: number;
  errorRate: number;
  systemHealth: 'excellent' | 'good' | 'fair' | 'poor';
  bottlenecks: Array<{
    component: string;
    impact: 'high' | 'medium' | 'low';
    description: string;
  }>;
}

// Data Trends Component
export const DataTrendsCard: React.FC<{ 
  loading: boolean; 
  error: string | null;
  onRefresh: () => void;
}> = ({ loading, error, onRefresh }) => {
  const [trends, setTrends] = useState<DataTrends | null>(null);

  // Simulate data fetching
  useEffect(() => {
    if (!loading && !error) {
      // Mock data - in real implementation, this would come from API
      setTrends({
        totalFiles: 1247,
        totalRecords: 89432,
        weeklyGrowth: 12.5,
        monthlyGrowth: 34.2,
        topCategories: [
          { name: 'WordPress Themes', count: 342, growth: 15.3 },
          { name: 'Graphics', count: 289, growth: 8.7 },
          { name: 'Code Scripts', count: 234, growth: -2.1 },
          { name: 'Audio', count: 156, growth: 22.4 },
          { name: 'Video', count: 134, growth: 5.8 }
        ],
        recentActivity: [
          { date: '2024-01-20', uploads: 23, processing: 45 },
          { date: '2024-01-21', uploads: 31, processing: 52 },
          { date: '2024-01-22', uploads: 28, processing: 38 },
          { date: '2024-01-23', uploads: 35, processing: 41 },
          { date: '2024-01-24', uploads: 42, processing: 48 }
        ]
      });
    }
  }, [loading, error]);

  if (loading) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <BarChart3 size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Data Trends</h3>
          <RefreshCw size={16} className="ml-2 animate-spin text-gray-400" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <BarChart3 size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Data Trends</h3>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertCircle size={16} className="text-red-600 mr-2" />
              <p className="text-sm text-red-800">Error loading data trends</p>
            </div>
            <button
              onClick={onRefresh}
              className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs font-medium hover:bg-red-200 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <BarChart3 size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Data Trends</h3>
        </div>
        <button
          onClick={onRefresh}
          className="flex items-center space-x-1 px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-xs font-medium hover:bg-primary-200 transition-colors"
        >
          <RefreshCw size={12} />
          <span>Refresh</span>
        </button>
      </div>
      
      {trends && (
        <div className="space-y-4">
          {/* Overview Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center mb-1">
                <FileText size={16} className="text-blue-600 mr-2" />
                <h4 className="font-medium text-blue-800 text-sm">Total Files</h4>
              </div>
              <p className="text-xl font-bold text-blue-600">{trends.totalFiles.toLocaleString()}</p>
            </div>
            
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="flex items-center mb-1">
                <Database size={16} className="text-green-600 mr-2" />
                <h4 className="font-medium text-green-800 text-sm">Total Records</h4>
              </div>
              <p className="text-xl font-bold text-green-600">{trends.totalRecords.toLocaleString()}</p>
            </div>
            
            <div className="bg-purple-50 p-3 rounded-lg">
              <div className="flex items-center mb-1">
                <TrendingUp size={16} className="text-purple-600 mr-2" />
                <h4 className="font-medium text-purple-800 text-sm">Weekly Growth</h4>
              </div>
              <p className="text-xl font-bold text-purple-600">+{trends.weeklyGrowth}%</p>
            </div>
            
            <div className="bg-orange-50 p-3 rounded-lg">
              <div className="flex items-center mb-1">
                <Calendar size={16} className="text-orange-600 mr-2" />
                <h4 className="font-medium text-orange-800 text-sm">Monthly Growth</h4>
              </div>
              <p className="text-xl font-bold text-orange-600">+{trends.monthlyGrowth}%</p>
            </div>
          </div>

          {/* Top Categories */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Top Categories</h4>
            <div className="space-y-2">
              {trends.topCategories.map((category, index) => (
                <div key={category.name} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="w-6 h-6 bg-primary-100 text-primary-800 rounded-full text-xs font-medium flex items-center justify-center mr-3">
                      {index + 1}
                    </span>
                    <span className="text-sm font-medium text-gray-800">{category.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{category.count}</span>
                    <div className={`flex items-center text-xs ${
                      category.growth > 0 ? 'text-green-600' : category.growth < 0 ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {category.growth > 0 ? (
                        <TrendingUp size={12} className="mr-1" />
                      ) : category.growth < 0 ? (
                        <TrendingDown size={12} className="mr-1" />
                      ) : null}
                      <span>{Math.abs(category.growth)}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Activity Chart Placeholder */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Recent Activity (Last 5 Days)</h4>
            <div className="grid grid-cols-5 gap-2">
              {trends.recentActivity.map((day, index) => (
                <div key={day.date} className="text-center">
                  <div className="text-xs text-gray-600 mb-1">
                    Day {index + 1}
                  </div>
                  <div className="bg-primary-100 rounded p-2">
                    <div className="text-xs text-primary-800">
                      ↑ {day.uploads}
                    </div>
                    <div className="text-xs text-primary-600">
                      ⚡ {day.processing}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Usage Statistics Component
export const UsageStatisticsCard: React.FC<{
  loading: boolean;
  error: string | null;
  onRefresh: () => void;
}> = ({ loading, error, onRefresh }) => {
  const [usage, setUsage] = useState<UsageStatistics | null>(null);

  useEffect(() => {
    if (!loading && !error) {
      // Mock data - in real implementation, this would come from API
      setUsage({
        activeUsers: 127,
        totalSessions: 1834,
        averageSessionDuration: 24.5,
        topFeatures: [
          { feature: 'Category Analysis', usage: 89, trend: 'up' },
          { feature: 'File Management', usage: 76, trend: 'up' },
          { feature: 'Data Export', usage: 64, trend: 'stable' },
          { feature: 'Monitoring', usage: 45, trend: 'up' },
          { feature: 'Settings', usage: 32, trend: 'down' }
        ],
        deviceBreakdown: {
          desktop: 68,
          mobile: 24,
          tablet: 8
        }
      });
    }
  }, [loading, error]);

  if (loading) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Users size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Usage Statistics</h3>
          <RefreshCw size={16} className="ml-2 animate-spin text-gray-400" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Users size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Usage Statistics</h3>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertCircle size={16} className="text-red-600 mr-2" />
              <p className="text-sm text-red-800">Error loading usage statistics</p>
            </div>
            <button
              onClick={onRefresh}
              className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs font-medium hover:bg-red-200 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Users size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Usage Statistics</h3>
        </div>
        <button
          onClick={onRefresh}
          className="flex items-center space-x-1 px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-xs font-medium hover:bg-primary-200 transition-colors"
        >
          <RefreshCw size={12} />
          <span>Refresh</span>
        </button>
      </div>

      {usage && (
        <div className="space-y-4">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center mb-1">
                <Users size={16} className="text-blue-600 mr-2" />
                <h4 className="font-medium text-blue-800 text-sm">Active Users</h4>
              </div>
              <p className="text-xl font-bold text-blue-600">{usage.activeUsers}</p>
              <p className="text-xs text-blue-600">Last 30 days</p>
            </div>

            <div className="bg-green-50 p-3 rounded-lg">
              <div className="flex items-center mb-1">
                <Activity size={16} className="text-green-600 mr-2" />
                <h4 className="font-medium text-green-800 text-sm">Total Sessions</h4>
              </div>
              <p className="text-xl font-bold text-green-600">{usage.totalSessions.toLocaleString()}</p>
              <p className="text-xs text-green-600">This month</p>
            </div>

            <div className="bg-purple-50 p-3 rounded-lg">
              <div className="flex items-center mb-1">
                <Clock size={16} className="text-purple-600 mr-2" />
                <h4 className="font-medium text-purple-800 text-sm">Avg. Session</h4>
              </div>
              <p className="text-xl font-bold text-purple-600">{usage.averageSessionDuration}m</p>
              <p className="text-xs text-purple-600">Duration</p>
            </div>
          </div>

          {/* Top Features */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Most Used Features</h4>
            <div className="space-y-2">
              {usage.topFeatures.map((feature, index) => (
                <div key={feature.feature} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="w-6 h-6 bg-primary-100 text-primary-800 rounded-full text-xs font-medium flex items-center justify-center mr-3">
                      {index + 1}
                    </span>
                    <span className="text-sm font-medium text-gray-800">{feature.feature}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{feature.usage}%</span>
                    <div className={`flex items-center text-xs ${
                      feature.trend === 'up' ? 'text-green-600' :
                      feature.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {feature.trend === 'up' ? (
                        <TrendingUp size={12} />
                      ) : feature.trend === 'down' ? (
                        <TrendingDown size={12} />
                      ) : (
                        <div className="w-3 h-0.5 bg-gray-400 rounded"></div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Device Breakdown */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Device Usage</h4>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-blue-600 font-bold text-sm">{usage.deviceBreakdown.desktop}%</span>
                </div>
                <p className="text-xs text-gray-600">Desktop</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-green-600 font-bold text-sm">{usage.deviceBreakdown.mobile}%</span>
                </div>
                <p className="text-xs text-gray-600">Mobile</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-purple-600 font-bold text-sm">{usage.deviceBreakdown.tablet}%</span>
                </div>
                <p className="text-xs text-gray-600">Tablet</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Performance Insights Component
export const PerformanceInsightsCard: React.FC<{
  loading: boolean;
  error: string | null;
  onRefresh: () => void;
}> = ({ loading, error, onRefresh }) => {
  const [performance, setPerformance] = useState<PerformanceInsights | null>(null);

  useEffect(() => {
    if (!loading && !error) {
      // Mock data - in real implementation, this would come from monitoring API
      setPerformance({
        averageLoadTime: 1.2,
        cacheHitRate: 87.5,
        errorRate: 0.3,
        systemHealth: 'good',
        bottlenecks: [
          { component: 'Database Queries', impact: 'medium', description: 'Some queries taking longer than expected' },
          { component: 'File Processing', impact: 'low', description: 'Large CSV files causing minor delays' },
          { component: 'Cache Warming', impact: 'low', description: 'Initial cache population takes time' }
        ]
      });
    }
  }, [loading, error]);

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'text-green-600 bg-green-50';
      case 'good': return 'text-blue-600 bg-blue-50';
      case 'fair': return 'text-yellow-600 bg-yellow-50';
      case 'poor': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Zap size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Performance Insights</h3>
          <RefreshCw size={16} className="ml-2 animate-spin text-gray-400" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Zap size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Performance Insights</h3>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertCircle size={16} className="text-red-600 mr-2" />
              <p className="text-sm text-red-800">Error loading performance insights</p>
            </div>
            <button
              onClick={onRefresh}
              className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs font-medium hover:bg-red-200 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Zap size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Performance Insights</h3>
        </div>
        <button
          onClick={onRefresh}
          className="flex items-center space-x-1 px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-xs font-medium hover:bg-primary-200 transition-colors"
        >
          <RefreshCw size={12} />
          <span>Refresh</span>
        </button>
      </div>

      {performance && (
        <div className="space-y-4">
          {/* Key Performance Metrics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center mb-1">
                <Clock size={16} className="text-blue-600 mr-2" />
                <h4 className="font-medium text-blue-800 text-sm">Load Time</h4>
              </div>
              <p className="text-xl font-bold text-blue-600">{performance.averageLoadTime}s</p>
              <p className="text-xs text-blue-600">Average</p>
            </div>

            <div className="bg-green-50 p-3 rounded-lg">
              <div className="flex items-center mb-1">
                <Target size={16} className="text-green-600 mr-2" />
                <h4 className="font-medium text-green-800 text-sm">Cache Hit</h4>
              </div>
              <p className="text-xl font-bold text-green-600">{performance.cacheHitRate}%</p>
              <p className="text-xs text-green-600">Rate</p>
            </div>

            <div className="bg-red-50 p-3 rounded-lg">
              <div className="flex items-center mb-1">
                <AlertCircle size={16} className="text-red-600 mr-2" />
                <h4 className="font-medium text-red-800 text-sm">Error Rate</h4>
              </div>
              <p className="text-xl font-bold text-red-600">{performance.errorRate}%</p>
              <p className="text-xs text-red-600">Last 24h</p>
            </div>

            <div className={`p-3 rounded-lg ${getHealthColor(performance.systemHealth)}`}>
              <div className="flex items-center mb-1">
                <Activity size={16} className="mr-2" />
                <h4 className="font-medium text-sm">System Health</h4>
              </div>
              <p className="text-xl font-bold capitalize">{performance.systemHealth}</p>
              <p className="text-xs">Overall</p>
            </div>
          </div>

          {/* Performance Bottlenecks */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Performance Bottlenecks</h4>
            {performance.bottlenecks.length > 0 ? (
              <div className="space-y-3">
                {performance.bottlenecks.map((bottleneck, index) => (
                  <div key={index} className="bg-white p-3 rounded-lg border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-gray-800 text-sm">{bottleneck.component}</h5>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getImpactColor(bottleneck.impact)}`}>
                        {bottleneck.impact} impact
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{bottleneck.description}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Zap size={20} className="text-green-600" />
                </div>
                <p className="text-sm text-gray-600">No performance issues detected</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
