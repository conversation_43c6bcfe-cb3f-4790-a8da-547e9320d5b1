import React, { useState } from 'react';
import { 
  Bar<PERSON>hart3, 
  RefreshCw, 
  Settings as SettingsIcon,
  Calendar,
  Download,
  Filter,
  TrendingUp
} from 'lucide-react';
import { 
  DataTrendsCard, 
  UsageStatisticsCard, 
  PerformanceInsightsCard 
} from './AnalyticsComponents';

interface AnalyticsDashboardProps {
  className?: string;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ className = '' }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(new Date());
  const [showSettings, setShowSettings] = useState(false);
  const [dateRange, setDateRange] = useState('7d');

  const handleRefresh = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setLastUpdated(new Date());
    } catch (err) {
      setError('Failed to refresh analytics data');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = () => {
    // Simulate export functionality
    console.log('Exporting analytics data...');
    // In real implementation, this would trigger data export
  };

  const formatLastUpdated = (date: Date | null): string => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Controls */}
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-[#1A1919] mb-1">
              Analytics Dashboard
            </h2>
            <p className="text-sm text-gray-600">
              Comprehensive insights and data trends analysis
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Last Updated */}
            <div className="flex items-center space-x-2 text-xs text-gray-600">
              <Calendar size={14} />
              <span>Updated: {formatLastUpdated(lastUpdated)}</span>
            </div>
            
            {/* Date Range Selector */}
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-full text-xs font-medium bg-white hover:bg-gray-50 transition-colors"
            >
              <option value="1d">Last 24 hours</option>
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
            </select>
            
            {/* Export Button */}
            <button
              onClick={handleExport}
              className="flex items-center space-x-1 px-3 py-1 bg-secondary-100 text-secondary-800 rounded-full text-xs font-medium hover:bg-secondary-200 transition-colors"
            >
              <Download size={12} />
              <span>Export</span>
            </button>
            
            {/* Refresh Button */}
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center space-x-1 px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-xs font-medium hover:bg-primary-200 transition-colors disabled:opacity-50"
            >
              <RefreshCw size={12} className={loading ? 'animate-spin' : ''} />
              <span>Refresh</span>
            </button>
            
            {/* Settings */}
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center space-x-1 px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium hover:bg-gray-200 transition-colors"
            >
              <SettingsIcon size={12} />
              <span>Settings</span>
            </button>
          </div>
        </div>
        
        {/* Settings Panel */}
        {showSettings && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Auto Refresh
                </label>
                <select className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:ring-1 focus:ring-primary-500 focus:border-transparent">
                  <option value="off">Off</option>
                  <option value="30s">30 seconds</option>
                  <option value="1m">1 minute</option>
                  <option value="5m">5 minutes</option>
                </select>
              </div>
              
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Chart Type
                </label>
                <select className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:ring-1 focus:ring-primary-500 focus:border-transparent">
                  <option value="line">Line Chart</option>
                  <option value="bar">Bar Chart</option>
                  <option value="area">Area Chart</option>
                </select>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="showTrends"
                  className="mr-2"
                  defaultChecked
                />
                <label htmlFor="showTrends" className="text-xs text-gray-700">
                  Show Trend Lines
                </label>
              </div>
              
              <div className="text-xs text-gray-600">
                <p>Data Points: {loading ? 'Loading...' : '1,247'}</p>
                <p>Coverage: {dateRange === '1d' ? '24h' : dateRange === '7d' ? '7 days' : dateRange === '30d' ? '30 days' : '90 days'}</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Analytics Cards Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Data Trends - Takes full width on smaller screens, 1 column on xl */}
        <div className="xl:col-span-1">
          <DataTrendsCard 
            loading={loading} 
            error={error} 
            onRefresh={handleRefresh} 
          />
        </div>
        
        {/* Usage Statistics */}
        <div className="xl:col-span-1">
          <UsageStatisticsCard 
            loading={loading} 
            error={error} 
            onRefresh={handleRefresh} 
          />
        </div>
        
        {/* Performance Insights */}
        <div className="xl:col-span-1">
          <PerformanceInsightsCard 
            loading={loading} 
            error={error} 
            onRefresh={handleRefresh} 
          />
        </div>
      </div>

      {/* Quick Insights Summary */}
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <TrendingUp size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Quick Insights</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-1">Data Growth</h4>
            <p className="text-2xl font-bold text-blue-600">+34.2%</p>
            <p className="text-xs text-blue-600">vs last month</p>
          </div>
          
          <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
            <h4 className="font-medium text-green-800 mb-1">User Engagement</h4>
            <p className="text-2xl font-bold text-green-600">87.5%</p>
            <p className="text-xs text-green-600">active sessions</p>
          </div>
          
          <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
            <h4 className="font-medium text-purple-800 mb-1">Performance</h4>
            <p className="text-2xl font-bold text-purple-600">1.2s</p>
            <p className="text-xs text-purple-600">avg load time</p>
          </div>
          
          <div className="bg-gradient-to-r from-orange-50 to-orange-100 p-4 rounded-lg">
            <h4 className="font-medium text-orange-800 mb-1">System Health</h4>
            <p className="text-2xl font-bold text-orange-600">Good</p>
            <p className="text-xs text-orange-600">overall status</p>
          </div>
        </div>
      </div>

      {/* Global Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-red-800">Analytics Error</h3>
              <p className="text-xs text-red-600 mt-1">{error}</p>
            </div>
            <button
              onClick={handleRefresh}
              className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs font-medium hover:bg-red-200 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsDashboard;
