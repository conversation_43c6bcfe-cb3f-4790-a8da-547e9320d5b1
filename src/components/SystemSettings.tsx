import React, { useState, useEffect } from 'react';
import {
  Settings,
  Database,
  Upload,
  Download,
  Trash2,
  RefreshCw,
  Server,
  HardDrive,
  Users,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3,
  Folder,
  User,
  Mail,
  Shield,
  XCircle,
  Info,
  Bell,
  Palette,
  Monitor,
  Globe,
  // NEW: Admin-specific icons
  Zap,
  Activity,
  Lock,
  Link,
  Archive
} from 'lucide-react';
import Modal from './Modal';
import NotificationSettings from './NotificationSettings';
import MonitoringDashboard from './monitoring/MonitoringDashboard';
import FileManagement from './FileManagement';
import AnalyticsDashboard from './analytics/AnalyticsDashboard';
import AutomationDashboard from './automation/AutomationDashboard';
import { createApiErrorNotification, createEmptyDataNotification, createErrorState, ErrorState } from '../utils/notificationUtils';

interface SystemInfo {
  version: string;
  uptime: string;
  dataFiles: number;
  totalSize: string;
  lastBackup: string | null;
  status: 'healthy' | 'warning' | 'error';
  memory?: {
    used: number;
    total: number;
    percent: number;
  };
  cpu?: {
    percent: number;
  };
  notifications?: {
    total: number;
  };
  timestamp?: string;
}

interface BackupInfo {
  filename: string;
  size: number;
  size_formatted: string;
  created_at: string;
  timestamp: string;
  files_count: number;
  version: string;
  includes: string[];
}

interface UserSettings {
  name: string;
  email: string;
  role: string;
  notifications: {
    email: boolean;
    browser: boolean;
    dataUpdates: boolean;
  };
  theme: 'light' | 'dark' | 'auto';
  language: string;
}

interface SystemSettingsProps {
  initialTab?: string;
  adminMode?: boolean; // NEW: Enable admin-specific features
  userRole?: 'user' | 'admin'; // NEW: User role for feature access
}

const SystemSettings: React.FC<SystemSettingsProps> = ({
  initialTab = 'overview',
  adminMode = false,
  userRole = 'user'
}) => {
  const [activeTab, setActiveTab] = useState(initialTab);

  // Update activeTab when initialTab changes
  useEffect(() => {
    setActiveTab(initialTab);
  }, [initialTab]);
  const [systemInfo, setSystemInfo] = useState<SystemInfo>({
    version: '1.0.0',
    uptime: 'Loading...',
    dataFiles: 0,
    totalSize: '0 B',
    lastBackup: null,
    status: 'healthy'
  });

  const [userSettings, setUserSettings] = useState<UserSettings>({
    name: 'Ariaseta Alam',
    email: '<EMAIL>',
    role: 'Administrator',
    notifications: {
      email: true,
      browser: true,
      dataUpdates: false
    },
    theme: 'light',
    language: 'en'
  });

  const [showBackupModal, setShowBackupModal] = useState(false);
  const [showClearDataModal, setShowClearDataModal] = useState(false);
  const [showBackupListModal, setShowBackupListModal] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [backupToDelete, setBackupToDelete] = useState<BackupInfo | null>(null);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isRefreshingBackups, setIsRefreshingBackups] = useState(false);
  const [isRefreshingStatus, setIsRefreshingStatus] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [errorState, setErrorState] = useState<ErrorState>({ hasError: false, errorType: 'unknown', title: '', message: '' });
  const [backupProgress, setBackupProgress] = useState({
    isCreating: false,
    step: '',
    success: false,
    error: '',
    filename: '',
    size: ''
  });
  const [backups, setBackups] = useState<BackupInfo[]>([]);
  const [backupStats, setBackupStats] = useState({
    total_count: 0,
    total_size_formatted: '0 B'
  });

  useEffect(() => {
    // Load system info from API
    fetchSystemInfo();

    // Auto-refresh every 60 seconds (increased from 30 to reduce load)
    const interval = setInterval(() => {
      // Only refresh if not currently performing backup operations
      if (!backupProgress.isCreating && !isDeleting && !isRefreshingBackups && !isRefreshingStatus) {
        fetchSystemInfo(false); // Auto-refresh without loading indicator
      }
    }, 60000); // Changed to 60 seconds

    return () => clearInterval(interval);
  }, [backupProgress.isCreating, isDeleting, isRefreshingBackups, isRefreshingStatus]);

  const fetchSystemInfo = async (showLoading = false) => {
    if (showLoading) {
      setIsRefreshingStatus(true);
    }

    try {
      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch('http://localhost:5001/api/system/metrics', {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        setSystemInfo({
          version: data.version || '1.0.0',
          uptime: data.uptime || 'Unknown',
          dataFiles: data.dataFiles || 0,
          totalSize: data.totalSize || '0 B',
          lastBackup: data.lastBackup || null,
          status: data.status || 'error',
          memory: data.memory,
          cpu: data.cpu,
          notifications: data.notifications,
          timestamp: data.timestamp
        });

        // Show success feedback for manual refresh
        if (showLoading) {
          const successMessage = document.createElement('div');
          successMessage.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 text-sm';
          successMessage.textContent = 'System status refreshed successfully';
          document.body.appendChild(successMessage);

          setTimeout(() => {
            if (document.body.contains(successMessage)) {
              document.body.removeChild(successMessage);
            }
          }, 2000);
        }
      } else {
        throw new Error(`HTTP ${response.status}: Failed to fetch system metrics`);
      }
    } catch (error) {
      console.error('Failed to fetch system info:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setApiError(errorMessage);

      // Create error state for UI display
      const newErrorState = createErrorState(errorMessage, 'sistem');
      setErrorState(newErrorState);

      // Set system status to error
      setSystemInfo(prev => ({ ...prev, status: 'error' }));

      // Show notification for manual refresh
      if (showLoading) {
        const notification = createApiErrorNotification(errorMessage);
        console.log(`[${notification.type.toUpperCase()}] ${notification.title}: ${notification.message}`);
      }
    } finally {
      if (showLoading) {
        setIsRefreshingStatus(false);
      }
    }
  };

  const handleBackupData = async () => {
    setIsLoading(true);
    setBackupProgress({
      isCreating: true,
      step: 'Initializing backup...',
      success: false,
      error: '',
      filename: '',
      size: ''
    });

    try {
      // Step 1: Preparing
      setBackupProgress(prev => ({ ...prev, step: 'Preparing backup files...' }));
      await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for UX

      // Step 2: Creating backup
      setBackupProgress(prev => ({ ...prev, step: 'Creating backup archive...' }));

      // Add timeout for backup operation
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch('http://localhost:5001/api/system/backup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (response.ok && data.success) {
        // Step 3: Finalizing
        setBackupProgress(prev => ({ ...prev, step: 'Finalizing backup...' }));
        await new Promise(resolve => setTimeout(resolve, 300));

        // Update system info
        setSystemInfo(prev => ({
          ...prev,
          lastBackup: new Date().toLocaleString()
        }));

        // Refresh backup list if modal is open
        if (showBackupListModal) {
          fetchBackups();
        }

        // Show success state
        setBackupProgress({
          isCreating: false,
          step: '',
          success: true,
          error: '',
          filename: data.backup_file,
          size: data.size_formatted
        });

        // Auto close after 2 seconds
        setTimeout(() => {
          setShowBackupModal(false);
          resetBackupProgress();
        }, 2000);

      } else {
        throw new Error(data.error || 'Backup failed');
      }

    } catch (error) {
      console.error('Backup failed:', error);
      setBackupProgress({
        isCreating: false,
        step: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        filename: '',
        size: ''
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('http://localhost:5001/api/system/cache/clear', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Show success message
        console.log(`Cache cleared successfully: ${data.count} items removed (${data.size_cleared_formatted} freed)`);
      } else {
        throw new Error(data.error || 'Cache clear failed');
      }

      setShowClearDataModal(false);
    } catch (error) {
      console.error('Clear cache failed:', error);
      // You can add error notification here
      alert(`Clear cache failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchBackups = async () => {
    setIsRefreshingBackups(true);
    try {
      // Add timeout for backup list fetch
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch('http://localhost:5001/api/system/backups', {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Smooth transition to new data
          setBackups(data.backups || []);
          setBackupStats({
            total_count: data.total_count || 0,
            total_size_formatted: data.total_size_formatted || '0 B'
          });

          return data.backups || []; // Return for chaining if needed
        }
      }
      return []; // Return empty array if failed
    } catch (error) {
      if (error.name === 'AbortError') {
        console.error('Backup fetch request timed out');
      } else {
        console.error('Failed to fetch backups:', error);
      }
      return []; // Return empty array on error
    } finally {
      setIsRefreshingBackups(false);
    }
  };

  const handleDownloadBackup = async (filename: string) => {
    try {
      const response = await fetch(`http://localhost:5001/api/system/backups/${filename}/download`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        throw new Error('Download failed');
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert(`Download failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleDeleteBackup = async (backup: BackupInfo) => {
    setBackupToDelete(backup);
    setDeleteConfirmText(''); // Reset confirmation text
    setShowDeleteConfirmModal(true);
  };

  const confirmDeleteBackup = async () => {
    if (!backupToDelete) {
      console.error('No backup selected for deletion');
      return;
    }

    if (deleteConfirmText !== backupToDelete.filename) {
      console.error('Confirmation text does not match filename');
      return;
    }

    setIsDeleting(true);
    try {
      // Add timeout for delete operation
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

      const response = await fetch(`http://localhost:5001/api/system/backups/${backupToDelete.filename}`, {
        method: 'DELETE',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (response.ok && data.success) {
        console.log(`Backup deleted: ${data.size_freed_formatted} freed`);

        // Close delete confirmation modal and reset state
        setShowDeleteConfirmModal(false);
        setBackupToDelete(null);
        setDeleteConfirmText('');

        // Smooth reload - refresh backup list without closing main modal
        await fetchBackups();

        // Show brief success feedback
        // Create a temporary success indicator
        const successMessage = document.createElement('div');
        successMessage.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 text-sm';
        successMessage.textContent = `Backup "${backupToDelete.filename}" deleted successfully`;
        document.body.appendChild(successMessage);

        // Remove success message after 3 seconds
        setTimeout(() => {
          if (document.body.contains(successMessage)) {
            document.body.removeChild(successMessage);
          }
        }, 3000);

      } else {
        throw new Error(data.error || 'Delete failed');
      }
    } catch (error) {
      console.error('Delete failed:', error);

      // Enhanced error handling - show error in modal instead of alert
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      // You can replace this with a proper error modal or toast notification
      alert(`Failed to delete backup: ${errorMessage}\n\nPlease try again or contact support if the problem persists.`);

      // Keep the delete confirmation modal open so user can try again
      // Don't close the modal on error
    } finally {
      setIsDeleting(false);
    }
  };

  const cancelDeleteBackup = () => {
    setShowDeleteConfirmModal(false);
    setBackupToDelete(null);
    setDeleteConfirmText(''); // Reset confirmation text
  };

  const resetBackupProgress = () => {
    setBackupProgress({
      isCreating: false,
      step: '',
      success: false,
      error: '',
      filename: '',
      size: ''
    });
  };

  const handleCloseBackupModal = () => {
    if (!backupProgress.isCreating) {
      setShowBackupModal(false);
      resetBackupProgress();
    }
  };

  const updateUserSettings = (key: keyof UserSettings, value: any) => {
    setUserSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updateNotificationSettings = (key: keyof UserSettings['notifications'], value: boolean) => {
    setUserSettings(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: value
      }
    }));
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle size={20} className="text-green-500" />;
      case 'warning':
        return <AlertCircle size={20} className="text-yellow-500" />;
      case 'error':
        return <AlertCircle size={20} className="text-red-500" />;
      default:
        return <Clock size={20} className="text-gray-500" />;
    }
  };

  // Base tabs available to all users
  const baseTabs = [
    { id: 'overview', label: 'System Overview', icon: Monitor },
    { id: 'data', label: 'Data Management', icon: Database },
    { id: 'user', label: 'User Settings', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'advanced', label: 'Advanced', icon: Settings }
  ];

  // Admin-only tabs
  const adminTabs = [
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'automation', label: 'Automation', icon: Zap },
    { id: 'monitoring', label: 'Monitoring', icon: Activity },
    { id: 'security', label: 'Security', icon: Lock },
    { id: 'logs', label: 'System Logs', icon: FileText },
    { id: 'integrations', label: 'Integrations', icon: Link },
    { id: 'backup-management', label: 'Backup Management', icon: Archive }
  ];

  // Combine tabs based on admin mode
  const tabs = adminMode && userRole === 'admin'
    ? [...baseTabs, ...adminTabs]
    : baseTabs;

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-3 md:space-y-4">
            {/* System Status */}
            <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-3 md:p-4">
              <div className="flex items-center justify-between mb-2 md:mb-3">
                <h3 className="text-base md:text-lg font-semibold text-[#1A1919] flex items-center">
                  <Server size={16} className="mr-2 text-primary-600" />
                  System Status
                </h3>
                {getStatusIcon(systemInfo.status)}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-2 md:gap-3">
                <div className="bg-white/30 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs text-gray-600">Version</p>
                      <p className="text-sm md:text-base font-semibold text-[#1A1919]">{systemInfo.version}</p>
                    </div>
                    <FileText size={20} className="text-primary-600" />
                  </div>
                </div>

                <div className="bg-white/30 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs text-gray-600">Uptime</p>
                      <p className="text-sm md:text-base font-semibold text-[#1A1919]">{systemInfo.uptime}</p>
                    </div>
                    <Clock size={20} className="text-green-500" />
                  </div>
                </div>

                <div className="bg-white/30 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs text-gray-600">Data Files</p>
                      <p className="text-sm md:text-base font-semibold text-[#1A1919]">{systemInfo.dataFiles}</p>
                    </div>
                    <Folder size={20} className="text-blue-500" />
                  </div>
                </div>

                <div className="bg-white/30 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs text-gray-600">Total Size</p>
                      <p className="text-sm md:text-base font-semibold text-[#1A1919]">{systemInfo.totalSize}</p>
                    </div>
                    <HardDrive size={20} className="text-purple-500" />
                  </div>
                </div>

                {/* Memory Usage Card */}
                {systemInfo.memory && (
                  <div className="bg-white/30 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <p className="text-xs text-gray-600">Memory</p>
                        <p className="text-sm md:text-base font-semibold text-[#1A1919]">{systemInfo.memory.percent}%</p>
                      </div>
                      <Monitor size={20} className="text-orange-500" />
                    </div>
                    <div className="bg-gray-200 rounded-full h-1.5">
                      <div
                        className={`h-1.5 rounded-full transition-all duration-300 ${
                          systemInfo.memory.percent > 80 ? 'bg-red-500' :
                          systemInfo.memory.percent > 60 ? 'bg-orange-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${systemInfo.memory.percent}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* CPU Usage Card */}
                {systemInfo.cpu && (
                  <div className="bg-white/30 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <p className="text-xs text-gray-600">CPU</p>
                        <p className="text-sm md:text-base font-semibold text-[#1A1919]">{systemInfo.cpu.percent}%</p>
                      </div>
                      <Settings size={20} className="text-blue-500" />
                    </div>
                    <div className="bg-gray-200 rounded-full h-1.5">
                      <div
                        className={`h-1.5 rounded-full transition-all duration-300 ${
                          systemInfo.cpu.percent > 80 ? 'bg-red-500' :
                          systemInfo.cpu.percent > 60 ? 'bg-orange-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${systemInfo.cpu.percent}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-3 md:p-4">
              <h3 className="text-base md:text-lg font-semibold text-[#1A1919] mb-2 md:mb-3 flex items-center">
                <BarChart3 size={16} className="mr-2 text-primary-600" />
                Quick Actions
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-2 md:gap-3">
                {/* Backup Data Button - Primary Theme */}
                <button
                  onClick={() => setShowBackupModal(true)}
                  disabled={backupProgress.isCreating}
                  className={`flex items-center justify-center space-x-2 p-3 rounded-lg transition-all duration-200 ${
                    backupProgress.isCreating
                      ? 'bg-primary-300 text-primary-600 cursor-not-allowed opacity-60'
                      : 'bg-primary-500 text-white hover:bg-primary-600 hover:shadow-md active:bg-primary-700'
                  }`}
                >
                  {backupProgress.isCreating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                      <span className="text-sm font-medium">Creating...</span>
                    </>
                  ) : (
                    <>
                      <Download size={16} />
                      <span className="text-sm font-medium">Backup Data</span>
                    </>
                  )}
                </button>

                {/* Refresh Status Button - Secondary Theme */}
                <button
                  onClick={() => fetchSystemInfo(true)}
                  disabled={isRefreshingStatus}
                  className={`flex items-center justify-center space-x-2 p-3 rounded-lg transition-all duration-200 ${
                    isRefreshingStatus
                      ? 'bg-secondary-300 text-secondary-600 cursor-not-allowed opacity-60'
                      : 'bg-secondary-500 text-white hover:bg-secondary-600 hover:shadow-md active:bg-secondary-700'
                  }`}
                >
                  {isRefreshingStatus ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-secondary-600"></div>
                      <span className="text-sm font-medium">Refreshing...</span>
                    </>
                  ) : (
                    <>
                      <RefreshCw size={16} />
                      <span className="text-sm font-medium">Refresh Status</span>
                    </>
                  )}
                </button>

                {/* Clear Cache Button - Warning/Danger Style */}
                <button
                  onClick={() => setShowClearDataModal(true)}
                  className="flex items-center justify-center space-x-2 p-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 hover:shadow-md active:bg-orange-700 transition-all duration-200"
                >
                  <Trash2 size={16} />
                  <span className="text-sm font-medium">Clear Cache</span>
                </button>
              </div>
            </div>
          </div>
        );

      case 'data':
        return (
          <FileManagement
            adminMode={adminMode}
            userRole={userRole}
            className="space-y-4"
          />
        );

      case 'user':
        return (
          <div className="space-y-3 md:space-y-4">
            <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-3 md:p-4">
              <h3 className="text-base md:text-lg font-semibold text-[#1A1919] mb-2 md:mb-3 flex items-center">
                <User size={16} className="mr-2 text-primary-600" />
                User Profile
              </h3>

              <div className="space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Full Name</label>
                    <input
                      type="text"
                      value={userSettings.name}
                      onChange={(e) => updateUserSettings('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Email</label>
                    <input
                      type="email"
                      value={userSettings.email}
                      onChange={(e) => updateUserSettings('email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Role</label>
                  <select
                    value={userSettings.role}
                    onChange={(e) => updateUserSettings('role', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  >
                    <option value="Administrator">Administrator</option>
                    <option value="Analyst">Analyst</option>
                    <option value="Viewer">Viewer</option>
                  </select>
                </div>

                <div className="pt-2">
                  <button className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 hover:shadow-md active:bg-primary-700 transition-all duration-200 text-sm font-medium">
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      case 'notifications':
        return <NotificationSettings />;

      case 'appearance':
        return (
          <div className="space-y-3 md:space-y-4">
            <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-3 md:p-4">
              <h3 className="text-base md:text-lg font-semibold text-[#1A1919] mb-2 md:mb-3 flex items-center">
                <Palette size={16} className="mr-2 text-primary-600" />
                Appearance Settings
              </h3>

              <div className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Theme</label>
                  <select
                    value={userSettings.theme}
                    onChange={(e) => updateUserSettings('theme', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  >
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="auto">Auto (System)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Language</label>
                  <select
                    value={userSettings.language}
                    onChange={(e) => updateUserSettings('language', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  >
                    <option value="en">English</option>
                    <option value="id">Bahasa Indonesia</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        );

      case 'advanced':
        return (
          <div className="space-y-3 md:space-y-4">
            <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-3 md:p-4">
              <h3 className="text-base md:text-lg font-semibold text-[#1A1919] mb-2 md:mb-3 flex items-center">
                <Settings size={16} className="mr-2 text-primary-600" />
                Advanced Settings
              </h3>

              <div className="space-y-3">
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center">
                    <AlertCircle size={16} className="text-yellow-600 mr-2" />
                    <p className="text-xs text-yellow-800">
                      Advanced settings can affect system performance. Change with caution.
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-white/30 rounded-lg">
                  <div>
                    <h4 className="font-medium text-[#1A1919] text-sm">Debug Mode</h4>
                    <p className="text-xs text-gray-600">Enable detailed logging for troubleshooting</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-500"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between p-3 bg-white/30 rounded-lg">
                  <div>
                    <h4 className="font-medium text-[#1A1919] text-sm">API Rate Limiting</h4>
                    <p className="text-xs text-gray-600">Limit API requests per minute</p>
                  </div>
                  <input
                    type="number"
                    defaultValue={100}
                    className="w-20 px-2 py-1 border border-gray-300 rounded text-center text-sm"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      // Admin-only tab cases
      case 'analytics':
        return adminMode && userRole === 'admin' ? (
          <AnalyticsDashboard />
        ) : null;

      case 'automation':
        return adminMode && userRole === 'admin' ? (
          <AutomationDashboard />
        ) : null;

      case 'monitoring':
        return adminMode && userRole === 'admin' ? (
          <MonitoringDashboard />
        ) : null;

      case 'security':
        return adminMode && userRole === 'admin' ? (
          <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-3 md:p-4">
            <div className="flex items-center mb-4">
              <Lock size={20} className="mr-2 text-primary-600" />
              <h3 className="text-lg font-semibold text-[#1A1919]">Security Management</h3>
            </div>
            <p className="text-gray-600 text-sm">Security settings and access control will be implemented here.</p>
            <div className="mt-4 space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-800">Access Control</h4>
                <p className="text-sm text-gray-600 mt-1">Coming soon...</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-800">Audit Logs</h4>
                <p className="text-sm text-gray-600 mt-1">Coming soon...</p>
              </div>
            </div>
          </div>
        ) : null;

      case 'logs':
        return adminMode && userRole === 'admin' ? (
          <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-3 md:p-4">
            <div className="flex items-center mb-4">
              <FileText size={20} className="mr-2 text-primary-600" />
              <h3 className="text-lg font-semibold text-[#1A1919]">System Logs</h3>
            </div>
            <p className="text-gray-600 text-sm">System logs and activity tracking will be implemented here.</p>
            <div className="mt-4 bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-800">Log Viewer</h4>
              <p className="text-sm text-gray-600 mt-1">Coming soon...</p>
            </div>
          </div>
        ) : null;

      case 'integrations':
        return adminMode && userRole === 'admin' ? (
          <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-3 md:p-4">
            <div className="flex items-center mb-4">
              <Link size={20} className="mr-2 text-primary-600" />
              <h3 className="text-lg font-semibold text-[#1A1919]">External Integrations</h3>
            </div>
            <p className="text-gray-600 text-sm">External system integrations will be implemented here.</p>
            <div className="mt-4 bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-800">API Connections</h4>
              <p className="text-sm text-gray-600 mt-1">Coming soon...</p>
            </div>
          </div>
        ) : null;

      case 'backup-management':
        return adminMode && userRole === 'admin' ? (
          <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-3 md:p-4">
            <div className="flex items-center mb-4">
              <Archive size={20} className="mr-2 text-primary-600" />
              <h3 className="text-lg font-semibold text-[#1A1919]">Advanced Backup Management</h3>
            </div>
            <p className="text-gray-600 text-sm">Advanced backup features and automation will be implemented here.</p>
            <div className="mt-4 space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-800">Automated Backups</h4>
                <p className="text-sm text-gray-600 mt-1">Coming soon...</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-800">Backup Policies</h4>
                <p className="text-sm text-gray-600 mt-1">Coming soon...</p>
              </div>
            </div>
          </div>
        ) : null;

      default:
        return (
          <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-3 md:p-4">
            <p className="text-gray-600 text-sm">Content for {activeTab} tab will be implemented here.</p>
          </div>
        );
    }
  };

  return (
    <div className="w-full">
      {/* Main Layout - Dengan proper spacing dari header */}
      <div className="p-1 md:p-2 lg:p-3 pt-8 md:pt-10 lg:pt-12 space-y-1 md:space-y-2 lg:space-y-3">
        {/* Header */}
        <div className="bg-white border border-gray-200 rounded-lg p-2 md:p-3 lg:p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-base md:text-lg font-semibold text-[#1A1919] flex items-center">
                <Settings size={20} className="mr-2 text-primary-600" />
                System Settings
                {adminMode && userRole === 'admin' && (
                  <span className="ml-2 px-2 py-1 bg-primary-500 text-white text-xs rounded-full">
                    Admin
                  </span>
                )}
              </h1>
              <p className="text-xs text-gray-600">
                {adminMode && userRole === 'admin'
                  ? 'Advanced system administration and management'
                  : 'Manage system configuration and preferences'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white border border-gray-200 rounded-lg p-2 md:p-3 lg:p-4 shadow-sm">
          <div className="bg-gray-100/50 p-1 rounded-lg">
            <div className="flex flex-wrap gap-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-2 md:px-3 py-1.5 md:py-2 rounded-md transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-[#9CEE69] text-[#1A1919] shadow-sm'
                        : 'text-[#1A1919] hover:text-white hover:bg-[#9CEE69]/80'
                    }`}
                  >
                    <Icon size={16} />
                    <span className="text-xs md:text-sm font-medium">{tab.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white border border-gray-200 rounded-lg p-2 md:p-3 lg:p-4 shadow-sm">
          {renderTabContent()}
        </div>

      {/* Backup Modal */}
      <Modal
        isOpen={showBackupModal}
        onClose={handleCloseBackupModal}
        title="Create Data Backup"
      >
        <div className="space-y-4">
          {/* Progress State */}
          {backupProgress.isCreating && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <div>
                  <p className="text-sm font-medium text-blue-800">Creating Backup...</p>
                  <p className="text-xs text-blue-600">{backupProgress.step}</p>
                </div>
              </div>
              <div className="mt-3 bg-blue-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
              </div>
            </div>
          )}

          {/* Success State */}
          {backupProgress.success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <CheckCircle size={24} className="text-green-600" />
                <div>
                  <p className="text-sm font-medium text-green-800">Backup Created Successfully!</p>
                  <p className="text-xs text-green-600">File: {backupProgress.filename}</p>
                  <p className="text-xs text-green-600">Size: {backupProgress.size}</p>
                </div>
              </div>
            </div>
          )}

          {/* Error State */}
          {backupProgress.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <AlertCircle size={24} className="text-red-600" />
                <div>
                  <p className="text-sm font-medium text-red-800">Backup Failed</p>
                  <p className="text-xs text-red-600">{backupProgress.error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Default State */}
          {!backupProgress.isCreating && !backupProgress.success && !backupProgress.error && (
            <div className="space-y-3">
              <div className="bg-gray-50 rounded-lg p-3">
                <h4 className="text-sm font-medium text-gray-800 mb-2">Backup will include:</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>• All CSV data files (categories, competitors, analysis)</li>
                  <li>• Notification database</li>
                  <li>• System configuration</li>
                  <li>• Backup manifest with metadata</li>
                </ul>
              </div>
              <p className="text-gray-600 text-sm">
                This will create a complete backup of all your data. The process may take a few moments.
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            {!backupProgress.success && (
              <button
                onClick={handleCloseBackupModal}
                disabled={backupProgress.isCreating}
                className="px-3 py-2 text-theme-jet border border-gray-300 rounded-lg hover:bg-theme-seashell hover:shadow-sm active:bg-gray-100 transition-all duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {backupProgress.isCreating ? 'Please wait...' : 'Cancel'}
              </button>
            )}

            {!backupProgress.success && !backupProgress.isCreating && (
              <button
                onClick={handleBackupData}
                disabled={backupProgress.isCreating}
                className={`px-3 py-2 rounded-lg transition-all duration-200 text-sm font-medium flex items-center space-x-2 ${
                  backupProgress.isCreating
                    ? 'bg-primary-300 text-primary-600 cursor-not-allowed opacity-60'
                    : 'bg-primary-500 text-white hover:bg-primary-600 hover:shadow-md active:bg-primary-700'
                }`}
              >
                {backupProgress.isCreating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <Download size={16} />
                    <span>Create Backup</span>
                  </>
                )}
              </button>
            )}

            {backupProgress.error && (
              <button
                onClick={resetBackupProgress}
                className="px-3 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 hover:shadow-md active:bg-primary-700 transition-all duration-200 text-sm font-medium"
              >
                Try Again
              </button>
            )}
          </div>
        </div>
      </Modal>

      {/* Clear Data Modal */}
      <Modal
        isOpen={showClearDataModal}
        onClose={() => setShowClearDataModal(false)}
        title="Clear Cache Data"
      >
        <div className="space-y-3">
          <p className="text-gray-600 text-sm">
            This will clear all cached data and temporary files. Your main data will not be affected.
          </p>
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setShowClearDataModal(false)}
              className="px-3 py-2 text-theme-jet border border-gray-300 rounded-lg hover:bg-theme-seashell hover:shadow-sm active:bg-gray-100 transition-all duration-200 text-sm font-medium"
            >
              Cancel
            </button>
            <button
              onClick={handleClearData}
              disabled={isLoading}
              className={`px-3 py-2 rounded-lg transition-all duration-200 text-sm font-medium ${
                isLoading
                  ? 'bg-orange-300 text-orange-600 cursor-not-allowed opacity-60'
                  : 'bg-orange-500 text-white hover:bg-orange-600 hover:shadow-md active:bg-orange-700'
              }`}
            >
              {isLoading ? 'Clearing...' : 'Clear Cache'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Backup List Modal */}
      <Modal
        isOpen={showBackupListModal}
        onClose={() => setShowBackupListModal(false)}
        title="Backup Management"
      >
        <div className="space-y-4">
          {/* Backup Stats */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-800">Total Backups</p>
                <p className="text-xs text-gray-600">{backupStats.total_count} files</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-800">Total Size</p>
                <p className="text-xs text-gray-600">{backupStats.total_size_formatted}</p>
              </div>
            </div>
          </div>

          {/* Backup List */}
          <div className="max-h-96 overflow-y-auto space-y-2 relative">
            {/* Loading Overlay */}
            {isRefreshingBackups && (
              <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-10 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <p className="text-sm text-blue-600">Refreshing backup list...</p>
                </div>
              </div>
            )}

            {backups.length === 0 && !isRefreshingBackups ? (
              <div className="text-center py-8 text-gray-500">
                <p className="text-sm">No backups found</p>
                <p className="text-xs">Create your first backup to get started</p>
              </div>
            ) : (
              backups.map((backup) => (
                <div key={backup.filename} className="border border-gray-200 rounded-lg p-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm text-gray-800">{backup.filename}</h4>
                      <div className="grid grid-cols-2 gap-2 mt-1 text-xs text-gray-600">
                        <p>Created: {backup.created_at}</p>
                        <p>Size: {backup.size_formatted}</p>
                        <p>Files: {backup.files_count}</p>
                        <p>Version: {backup.version}</p>
                      </div>
                      {backup.includes.length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs text-gray-500">Includes:</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {backup.includes.map((item, index) => (
                              <span
                                key={index}
                                className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                              >
                                {item}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="flex space-x-1 ml-3">
                      <button
                        onClick={() => handleDownloadBackup(backup.filename)}
                        className="px-2 py-1 bg-secondary-500 text-white rounded text-xs hover:bg-secondary-600 hover:shadow-sm active:bg-secondary-700 transition-all duration-200"
                        title="Download"
                      >
                        <Download size={12} />
                      </button>
                      <button
                        onClick={() => handleDeleteBackup(backup)}
                        disabled={isDeleting}
                        className={`px-2 py-1 rounded text-xs transition-all duration-200 ${
                          isDeleting
                            ? 'bg-red-300 text-red-600 cursor-not-allowed opacity-60'
                            : 'bg-red-500 text-white hover:bg-red-600 hover:shadow-sm active:bg-red-700'
                        }`}
                        title="Delete"
                      >
                        {isDeleting ? (
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600"></div>
                        ) : (
                          <Trash2 size={12} />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Modal Actions */}
          <div className="flex justify-between">
            <button
              onClick={() => setShowBackupModal(true)}
              disabled={backupProgress.isCreating}
              className={`px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium flex items-center space-x-2 ${
                backupProgress.isCreating
                  ? 'bg-primary-300 text-primary-600 cursor-not-allowed opacity-60'
                  : 'bg-primary-500 text-white hover:bg-primary-600 hover:shadow-md active:bg-primary-700'
              }`}
            >
              {backupProgress.isCreating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <Download size={16} />
                  <span>Create New Backup</span>
                </>
              )}
            </button>
            <button
              onClick={() => setShowBackupListModal(false)}
              className="px-4 py-2 text-theme-jet border border-gray-300 rounded-lg hover:bg-theme-seashell hover:shadow-sm active:bg-gray-100 transition-all duration-200 text-sm font-medium"
            >
              Close
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteConfirmModal}
        onClose={cancelDeleteBackup}
        title="Delete Backup Confirmation"
      >
        <div className="space-y-4">
          {/* Warning Header */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <AlertCircle size={24} className="text-red-600" />
              <div>
                <h4 className="text-sm font-medium text-red-800">Permanent Deletion Warning</h4>
                <p className="text-xs text-red-600">This action cannot be undone</p>
              </div>
            </div>
          </div>

          {/* Backup Details */}
          {backupToDelete && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-800 mb-3">Backup Details:</h4>
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div>
                  <p className="text-gray-600">Filename:</p>
                  <p className="font-medium text-gray-800">{backupToDelete.filename}</p>
                </div>
                <div>
                  <p className="text-gray-600">Size:</p>
                  <p className="font-medium text-gray-800">{backupToDelete.size_formatted}</p>
                </div>
                <div>
                  <p className="text-gray-600">Created:</p>
                  <p className="font-medium text-gray-800">{backupToDelete.created_at}</p>
                </div>
                <div>
                  <p className="text-gray-600">Files Count:</p>
                  <p className="font-medium text-gray-800">{backupToDelete.files_count} files</p>
                </div>
              </div>

              {backupToDelete.includes.length > 0 && (
                <div className="mt-3">
                  <p className="text-xs text-gray-600 mb-2">Contains:</p>
                  <div className="flex flex-wrap gap-1">
                    {backupToDelete.includes.map((item, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                      >
                        {item}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Consequences Warning */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <h4 className="text-sm font-medium text-yellow-800 mb-2">What will happen:</h4>
            <ul className="text-xs text-yellow-700 space-y-1">
              <li>• The backup file will be permanently deleted from the server</li>
              <li>• All data contained in this backup will be lost forever</li>
              <li>• This action cannot be reversed or undone</li>
              <li>• You will need to create a new backup to replace this one</li>
            </ul>
          </div>

          {/* Confirmation Text */}
          <div className="text-center">
            <p className="text-sm text-gray-600">
              Are you absolutely sure you want to delete this backup?
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Type the backup filename to confirm deletion
            </p>
            {deleteConfirmText && deleteConfirmText !== backupToDelete?.filename && (
              <p className="text-xs text-red-500 mt-1">
                Filename does not match. Please type exactly: {backupToDelete?.filename}
              </p>
            )}
            {deleteConfirmText === backupToDelete?.filename && (
              <p className="text-xs text-green-600 mt-1">
                ✓ Filename confirmed. You can now delete the backup.
              </p>
            )}
          </div>

          {/* Confirmation Input */}
          <div>
            <input
              type="text"
              value={deleteConfirmText}
              placeholder={`Type "${backupToDelete?.filename}" to confirm`}
              className={`w-full px-3 py-2 border rounded-lg text-sm transition-colors ${
                isDeleting
                  ? 'border-gray-300 bg-gray-100 cursor-not-allowed'
                  : deleteConfirmText === backupToDelete?.filename
                    ? 'border-green-500 focus:ring-2 focus:ring-green-500 focus:border-transparent'
                    : deleteConfirmText && deleteConfirmText !== backupToDelete?.filename
                      ? 'border-red-500 focus:ring-2 focus:ring-red-500 focus:border-transparent'
                      : 'border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-transparent'
              }`}
              onChange={(e) => setDeleteConfirmText(e.target.value)}
              disabled={isDeleting}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={cancelDeleteBackup}
              disabled={isDeleting}
              className="px-4 py-2 text-theme-jet border border-gray-300 rounded-lg hover:bg-theme-seashell hover:shadow-sm active:bg-gray-100 transition-all duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              onClick={confirmDeleteBackup}
              disabled={deleteConfirmText !== backupToDelete?.filename || isDeleting}
              className={`px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium flex items-center space-x-2 ${
                deleteConfirmText !== backupToDelete?.filename || isDeleting
                  ? 'bg-red-300 text-red-600 cursor-not-allowed opacity-60'
                  : 'bg-red-500 text-white hover:bg-red-600 hover:shadow-md active:bg-red-700'
              }`}
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                  <span>Deleting...</span>
                </>
              ) : (
                <>
                  <Trash2 size={16} />
                  <span>Delete Permanently</span>
                </>
              )}
            </button>
          </div>
        </div>
      </Modal>
      </div>
    </div>
  );
};

export default SystemSettings;
