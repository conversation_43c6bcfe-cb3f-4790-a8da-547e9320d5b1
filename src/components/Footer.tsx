import React from 'react';
import { Heart, Github, Mail, ExternalLink } from 'lucide-react';

interface FooterProps {
  className?: string;
}

const Footer: React.FC<FooterProps> = ({ className = '' }) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer 
      className={`
        bg-theme-eerie-black border-t border-theme-jet/50 backdrop-blur-sm
        ${className}
      `}
      role="contentinfo"
      aria-label="Site footer"
    >
      <div className="max-w-7xl mx-auto px-4 lg:px-8 py-6">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">

          {/* Brand Section */}
          <div className="space-y-3 md:col-span-2">
            <div className="flex items-center space-x-2">
              <img
                src="https://assets.elements.envato.com/apps/storefront/EnvatoLogoLight-b794a434513b3b975d91.svg"
                alt="Progress Dashboard - Analytics Platform"
                className="h-5 sm:h-6 w-auto transition-opacity duration-200 flex-shrink-0"
              />
              <h3 className="text-white font-medium text-base">
                Analytics Dashboard
              </h3>
            </div>
            <p className="text-gray-400 text-sm leading-relaxed">
              Advanced analytics platform for data-driven insights and competitive analysis.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-3 md:col-span-1">
            <h4 className="text-white font-medium text-sm">Quick Links</h4>
            <nav className="space-y-2" aria-label="Footer navigation">
              <a
                href="/analysis"
                className="block text-gray-400 hover:text-theme-light-green text-sm transition-colors duration-200 focus:outline-none focus:text-theme-light-green"
              >
                Categories Analysis
              </a>
              <a
                href="/analysis"
                className="block text-gray-400 hover:text-theme-light-green text-sm transition-colors duration-200 focus:outline-none focus:text-theme-light-green"
              >
                Competitors Tracking
              </a>
              <a
                href="/files"
                className="block text-gray-400 hover:text-theme-light-green text-sm transition-colors duration-200 focus:outline-none focus:text-theme-light-green"
              >
                File Management
              </a>
            </nav>
          </div>

          {/* Contact & Social */}
          <div className="space-y-3 md:col-span-1">
            <h4 className="text-white font-medium text-sm">Connect</h4>
            <div className="flex items-center space-x-3">
              <a
                href="mailto:<EMAIL>"
                className="p-2 text-gray-400 hover:text-theme-light-green hover:bg-theme-jet/40 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-theme-light-green focus:ring-offset-2 focus:ring-offset-theme-eerie-black"
                aria-label="Send email"
                title="Contact us via email"
              >
                <Mail size={16} />
              </a>
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 text-gray-400 hover:text-theme-light-green hover:bg-theme-jet/40 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-theme-light-green focus:ring-offset-2 focus:ring-offset-theme-eerie-black"
                aria-label="View on GitHub"
                title="View source code on GitHub"
              >
                <Github size={16} />
                <ExternalLink size={10} className="ml-1 opacity-60" />
              </a>
            </div>
            <p className="text-gray-500 text-xs">
              Open source analytics platform
            </p>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="pt-4 border-t border-theme-jet/30">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
            <div className="flex items-center space-x-1 text-gray-400 text-xs">
              <span>© {currentYear} Analytics Dashboard.</span>
              <span>Made with</span>
              <Heart size={12} className="text-red-400 fill-current" aria-label="love" />
              <span>for data insights.</span>
            </div>
            
            <div className="flex items-center space-x-4 text-xs">
              <a 
                href="#privacy" 
                className="text-gray-400 hover:text-theme-light-green transition-colors duration-200 focus:outline-none focus:text-theme-light-green"
              >
                Privacy Policy
              </a>
              <a 
                href="#terms" 
                className="text-gray-400 hover:text-theme-light-green transition-colors duration-200 focus:outline-none focus:text-theme-light-green"
              >
                Terms of Service
              </a>
              <span className="text-gray-500">v1.0.0</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
