/**
 * NotificationDropdown Component
 * Advanced notification dropdown with filtering, search, and bulk operations
 */

import React, { useState, useMemo } from 'react';
import {
  Bell,
  Search,
  Filter,
  CheckCircle2,
  Archive,
  Trash2,
  Settings,
  X,
  ChevronDown,
  MoreHorizontal,
  History,
  ExternalLink
} from 'lucide-react';
import { Notification as AppNotification, NotificationType, NotificationPriority } from '../types/notification';
import { useNotifications } from '../contexts/NotificationContext';
import NotificationItem from './NotificationItem';
import NotificationHistory from './NotificationHistory';
import { filterNotifications, sortNotifications, groupNotificationsByDate } from '../utils/notificationUtils';

interface NotificationDropdownProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  isOpen,
  onClose
}) => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    archiveNotification,
    bulkMarkAsRead,
    bulkDelete,
    bulkArchive,
    isLoading,
    error
  } = useNotifications();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<NotificationType[]>([]);
  const [selectedPriorities, setSelectedPriorities] = useState<NotificationPriority[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'date' | 'priority' | 'type'>('date');
  const [groupByDate, setGroupByDate] = useState(true);
  const [showHistory, setShowHistory] = useState(false);

  // Filter and sort notifications
  const filteredNotifications = useMemo(() => {
    let filtered = filterNotifications(
      notifications,
      searchTerm,
      selectedTypes,
      selectedPriorities,
      selectedStatuses
    );

    filtered = sortNotifications(filtered, sortBy, 'desc');

    return filtered;
  }, [notifications, searchTerm, selectedTypes, selectedPriorities, selectedStatuses, sortBy]);

  // Group notifications by date if enabled
  const groupedNotifications = useMemo(() => {
    if (groupByDate) {
      return groupNotificationsByDate(filteredNotifications);
    }
    return { 'All': filteredNotifications };
  }, [filteredNotifications, groupByDate]);

  const handleSelectNotification = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedNotifications(prev => [...prev, id]);
    } else {
      setSelectedNotifications(prev => prev.filter(nId => nId !== id));
    }
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n.id));
    }
  };

  const handleBulkMarkAsRead = async () => {
    if (selectedNotifications.length > 0) {
      await bulkMarkAsRead(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedNotifications.length > 0) {
      await bulkDelete(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const handleBulkArchive = async () => {
    if (selectedNotifications.length > 0) {
      await bulkArchive(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedTypes([]);
    setSelectedPriorities([]);
    setSelectedStatuses([]);
  };

  const hasActiveFilters = searchTerm || selectedTypes.length > 0 || selectedPriorities.length > 0 || selectedStatuses.length > 0;

  if (!isOpen) return null;

  return (
    <div className="absolute right-0 top-full mt-3 w-96 max-w-[calc(100vw-2rem)] bg-theme-seashell/95 backdrop-blur-sm rounded-xl shadow-xl border border-gray-200/80 z-[60] animate-fade-in overflow-hidden
      sm:right-0 sm:w-96
      max-sm:right-2 max-sm:left-2 max-sm:w-auto
      max-h-[calc(100vh-5rem)] overflow-y-auto">
      {/* Header */}
      <div className="p-4 border-b border-gray-200/80 bg-gradient-to-r from-primary-500/5 to-secondary-500/5">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell size={18} className="text-primary-600" />
            <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
            {unreadCount > 0 && (
              <span className="px-2 py-0.5 bg-blue-500 text-white text-xs font-bold rounded-full">
                {unreadCount}
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 rounded transition-colors duration-200"
          >
            <X size={16} />
          </button>
        </div>

        {/* Search and Filters */}
        <div className="mt-3 space-y-2">
          <div className="relative">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search notifications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-jasper/30 focus:border-orange-jasper"
            />
          </div>

          <div className="flex items-center justify-between">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`
                flex items-center space-x-1 px-3 py-1.5 text-sm rounded-lg transition-all duration-200
                ${showFilters ? 'bg-orange-100 text-orange-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}
              `}
            >
              <Filter size={14} />
              <span>Filters</span>
              {hasActiveFilters && (
                <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
              )}
            </button>

            <div className="flex items-center space-x-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'date' | 'priority' | 'type')}
                className="text-xs border border-gray-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-orange-jasper/30"
              >
                <option value="date">Sort by Date</option>
                <option value="priority">Sort by Priority</option>
                <option value="type">Sort by Type</option>
              </select>

              <button
                onClick={() => setGroupByDate(!groupByDate)}
                className={`
                  px-2 py-1 text-xs rounded transition-all duration-200
                  ${groupByDate ? 'bg-orange-100 text-orange-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}
                `}
              >
                Group
              </button>
            </div>
          </div>

          {/* Filter Options */}
          {showFilters && (
            <div className="p-3 bg-gray-50/50 rounded-lg space-y-3">
              {/* Type Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Type</label>
                <div className="flex flex-wrap gap-1">
                  {['success', 'error', 'warning', 'info', 'data_update', 'system', 'file_upload', 'analysis_complete'].map(type => (
                    <button
                      key={type}
                      onClick={() => {
                        if (selectedTypes.includes(type as NotificationType)) {
                          setSelectedTypes(prev => prev.filter(t => t !== type));
                        } else {
                          setSelectedTypes(prev => [...prev, type as NotificationType]);
                        }
                      }}
                      className={`
                        px-2 py-1 text-xs rounded transition-all duration-200
                        ${selectedTypes.includes(type as NotificationType)
                          ? 'bg-orange-jasper text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-100'
                        }
                      `}
                    >
                      {type.replace('_', ' ')}
                    </button>
                  ))}
                </div>
              </div>

              {/* Priority Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Priority</label>
                <div className="flex gap-1">
                  {['low', 'medium', 'high', 'urgent'].map(priority => (
                    <button
                      key={priority}
                      onClick={() => {
                        if (selectedPriorities.includes(priority as NotificationPriority)) {
                          setSelectedPriorities(prev => prev.filter(p => p !== priority));
                        } else {
                          setSelectedPriorities(prev => [...prev, priority as NotificationPriority]);
                        }
                      }}
                      className={`
                        px-2 py-1 text-xs rounded transition-all duration-200
                        ${selectedPriorities.includes(priority as NotificationPriority)
                          ? 'bg-orange-jasper text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-100'
                        }
                      `}
                    >
                      {priority}
                    </button>
                  ))}
                </div>
              </div>

              {/* Status Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Status</label>
                <div className="flex gap-1">
                  {['unread', 'read', 'archived'].map(status => (
                    <button
                      key={status}
                      onClick={() => {
                        if (selectedStatuses.includes(status)) {
                          setSelectedStatuses(prev => prev.filter(s => s !== status));
                        } else {
                          setSelectedStatuses(prev => [...prev, status]);
                        }
                      }}
                      className={`
                        px-2 py-1 text-xs rounded transition-all duration-200
                        ${selectedStatuses.includes(status)
                          ? 'bg-orange-jasper text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-100'
                        }
                      `}
                    >
                      {status}
                    </button>
                  ))}
                </div>
              </div>

              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="w-full px-3 py-1.5 text-xs text-gray-600 hover:text-gray-800 border border-gray-200 rounded hover:bg-gray-50 transition-all duration-200"
                >
                  Clear Filters
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedNotifications.length > 0 && (
        <div className="px-4 py-2 bg-blue-50/50 border-b border-gray-200/80">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">
              {selectedNotifications.length} selected
            </span>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleBulkMarkAsRead}
                className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-all duration-200"
                title="Mark as read"
              >
                <CheckCircle2 size={16} />
              </button>
              <button
                onClick={handleBulkArchive}
                className="p-1 text-yellow-600 hover:bg-yellow-100 rounded transition-all duration-200"
                title="Archive"
              >
                <Archive size={16} />
              </button>
              <button
                onClick={handleBulkDelete}
                className="p-1 text-red-600 hover:bg-red-100 rounded transition-all duration-200"
                title="Delete"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Notifications List */}
      <div className="max-h-96 sm:max-h-96 max-sm:max-h-[50vh] overflow-y-auto">
        {isLoading ? (
          <div className="p-6 text-center">
            <div className="animate-spin w-6 h-6 border-2 border-orange-jasper border-t-transparent rounded-full mx-auto mb-2"></div>
            <p className="text-sm text-gray-500">Loading notifications...</p>
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <p className="text-sm text-red-600 mb-2">Error loading notifications</p>
            <p className="text-xs text-gray-500">{error}</p>
          </div>
        ) : filteredNotifications.length > 0 ? (
          <div>
            {Object.entries(groupedNotifications).map(([dateGroup, groupNotifications]) => (
              <div key={dateGroup}>
                {groupByDate && (
                  <div className="px-4 py-2 bg-gray-50/50 border-b border-gray-100/80">
                    <h4 className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                      {dateGroup}
                    </h4>
                  </div>
                )}
                {groupNotifications.map((notification) => (
                  <div key={notification.id} className="relative">
                    <input
                      type="checkbox"
                      checked={selectedNotifications.includes(notification.id)}
                      onChange={(e) => handleSelectNotification(notification.id, e.target.checked)}
                      className="absolute left-2 top-4 z-10 w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500/30"
                    />
                    <div className="ml-6">
                      <NotificationItem
                        notification={notification}
                        onMarkAsRead={markAsRead}
                        onDelete={deleteNotification}
                        onArchive={archiveNotification}
                      />
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        ) : (
          <div className="p-6 text-center">
            <Bell size={24} className="mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-gray-500">
              {hasActiveFilters ? 'No notifications match your filters' : 'No notifications'}
            </p>
          </div>
        )}
      </div>

      {/* Footer */}
      {filteredNotifications.length > 0 && (
        <div className="p-3 border-t border-gray-200/80 bg-gray-50/50">
          <div className="flex items-center justify-between">
            <button
              onClick={handleSelectAll}
              className="text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200"
            >
              {selectedNotifications.length === filteredNotifications.length ? 'Deselect All' : 'Select All'}
            </button>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowHistory(true)}
                className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200"
              >
                <History size={14} />
                <span>View All</span>
              </button>

              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-sm text-primary-600 hover:text-primary-700 font-medium transition-all duration-200 hover:scale-105"
                >
                  Mark all as read
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Notification History Modal */}
      <NotificationHistory
        isOpen={showHistory}
        onClose={() => setShowHistory(false)}
      />
    </div>
  );
};

export default NotificationDropdown;
