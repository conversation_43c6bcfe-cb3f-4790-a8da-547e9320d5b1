import React, { useState, useEffect } from 'react';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

/**
 * React Query DevTools Wrapper Component
 * 
 * Provides controlled visibility for React Query DevTools with multiple trigger methods:
 * - Environment variable control
 * - Keyboard shortcut (Ctrl/Cmd + Shift + Q)
 * - Manual toggle function
 * - Completely hidden in production
 * 
 * Features:
 * - Hidden by default for cleaner UI
 * - Multiple activation methods
 * - Production safety (never shows in production)
 * - Persistent state across sessions (localStorage)
 * - Visual feedback when toggling
 */

interface ReactQueryDevtoolsWrapperProps {
  /**
   * Force show DevTools regardless of other settings
   * Useful for debugging specific scenarios
   */
  forceShow?: boolean;
  
  /**
   * Custom keyboard shortcut
   * Default: 'ctrl+shift+q' or 'cmd+shift+q' on Mac
   */
  keyboardShortcut?: string;
  
  /**
   * Environment variable name to check for enabling DevTools
   * Default: 'VITE_ENABLE_REACT_QUERY_DEVTOOLS'
   */
  envVariable?: string;
  
  /**
   * Whether to persist DevTools visibility state across sessions
   * Default: true
   */
  persistState?: boolean;
}

const ReactQueryDevtoolsWrapper: React.FC<ReactQueryDevtoolsWrapperProps> = ({
  forceShow = false,
  keyboardShortcut = 'ctrl+shift+q',
  envVariable = 'VITE_ENABLE_REACT_QUERY_DEVTOOLS',
  persistState = true
}) => {
  // Check if we're in production
  const isProduction = import.meta.env.PROD;
  
  // Check environment variable
  const envEnabled = import.meta.env[envVariable] === 'true';
  
  // Get initial state from localStorage if persistence is enabled
  const getInitialState = (): boolean => {
    if (isProduction) return false; // Never show in production
    if (forceShow) return true;
    if (envEnabled) return true;
    
    if (persistState) {
      const stored = localStorage.getItem('reactQueryDevtoolsVisible');
      return stored === 'true';
    }
    
    return false; // Hidden by default
  };

  const [isVisible, setIsVisible] = useState<boolean>(getInitialState);
  const [showToggleMessage, setShowToggleMessage] = useState<boolean>(false);

  // Save state to localStorage when it changes
  useEffect(() => {
    if (persistState && !isProduction) {
      localStorage.setItem('reactQueryDevtoolsVisible', isVisible.toString());
    }
  }, [isVisible, persistState, isProduction]);

  // Toggle function
  const toggleDevtools = () => {
    if (isProduction) {
      console.warn('React Query DevTools are disabled in production');
      return;
    }
    
    setIsVisible(prev => !prev);
    setShowToggleMessage(true);
    
    // Hide toggle message after 2 seconds
    setTimeout(() => setShowToggleMessage(false), 2000);
    
    console.log(`React Query DevTools ${!isVisible ? 'enabled' : 'disabled'}`);
  };

  // Keyboard shortcut handler
  useEffect(() => {
    if (isProduction) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
      const ctrlKey = isMac ? event.metaKey : event.ctrlKey;
      
      // Parse keyboard shortcut
      const shortcut = keyboardShortcut.toLowerCase();
      const needsCtrl = shortcut.includes('ctrl') || shortcut.includes('cmd');
      const needsShift = shortcut.includes('shift');
      const needsAlt = shortcut.includes('alt');
      const key = shortcut.split('+').pop() || 'q';
      
      if (
        event.key.toLowerCase() === key &&
        (!needsCtrl || ctrlKey) &&
        (!needsShift || event.shiftKey) &&
        (!needsAlt || event.altKey)
      ) {
        event.preventDefault();
        toggleDevtools();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [keyboardShortcut, isProduction, isVisible]);

  // Expose toggle function globally for manual control
  useEffect(() => {
    if (!isProduction) {
      // Make toggle function available globally
      (window as any).toggleReactQueryDevtools = toggleDevtools;
      
      // Add helpful console message on first load
      if (!sessionStorage.getItem('reactQueryDevtoolsHelpShown')) {
        console.log(
          '%c🔧 React Query DevTools Controls:',
          'color: #00d8ff; font-weight: bold; font-size: 14px;'
        );
        console.log(
          `%c• Keyboard: ${keyboardShortcut.toUpperCase()}`,
          'color: #888; font-size: 12px;'
        );
        console.log(
          '%c• Console: toggleReactQueryDevtools()',
          'color: #888; font-size: 12px;'
        );
        console.log(
          `%c• Environment: Set ${envVariable}=true`,
          'color: #888; font-size: 12px;'
        );
        console.log(
          `%c• Current state: ${isVisible ? 'Visible' : 'Hidden'}`,
          `color: ${isVisible ? '#4ade80' : '#f87171'}; font-size: 12px;`
        );
        
        sessionStorage.setItem('reactQueryDevtoolsHelpShown', 'true');
      }
    }

    // Cleanup
    return () => {
      if (!isProduction) {
        delete (window as any).toggleReactQueryDevtools;
      }
    };
  }, [keyboardShortcut, envVariable, isVisible, isProduction]);

  // Don't render anything in production
  if (isProduction) {
    return null;
  }

  return (
    <>
      {/* Toggle message */}
      {showToggleMessage && (
        <div
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            backgroundColor: isVisible ? '#4ade80' : '#f87171',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '6px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: 9999,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            animation: 'fadeInOut 2s ease-in-out'
          }}
        >
          React Query DevTools {isVisible ? 'Enabled' : 'Disabled'}
        </div>
      )}

      {/* DevTools component */}
      {isVisible && (
        <ReactQueryDevtools
          initialIsOpen={false}
          buttonPosition="bottom-left"
          position="bottom"
          toggleButtonProps={{
            style: {
              backgroundColor: '#00d8ff',
              border: 'none',
              borderRadius: '6px',
              color: 'white',
              fontSize: '12px',
              fontWeight: '500',
              padding: '8px 12px',
              cursor: 'pointer',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
              transition: 'all 0.2s ease',
            }
          }}
        />
      )}

      {/* CSS for fade animation */}
      <style>{`
        @keyframes fadeInOut {
          0% { opacity: 0; transform: translateY(-10px); }
          20% { opacity: 1; transform: translateY(0); }
          80% { opacity: 1; transform: translateY(0); }
          100% { opacity: 0; transform: translateY(-10px); }
        }
      `}</style>
    </>
  );
};

/**
 * Hook for programmatic DevTools control
 */
export const useReactQueryDevtools = () => {
  const isProduction = import.meta.env.PROD;
  
  const toggle = () => {
    if (isProduction) {
      console.warn('React Query DevTools are disabled in production');
      return false;
    }
    
    if ((window as any).toggleReactQueryDevtools) {
      (window as any).toggleReactQueryDevtools();
      return true;
    }
    
    console.warn('React Query DevTools toggle function not available');
    return false;
  };

  const show = () => {
    if (isProduction) return false;
    
    const current = localStorage.getItem('reactQueryDevtoolsVisible') === 'true';
    if (!current) {
      toggle();
    }
    return true;
  };

  const hide = () => {
    if (isProduction) return false;
    
    const current = localStorage.getItem('reactQueryDevtoolsVisible') === 'true';
    if (current) {
      toggle();
    }
    return true;
  };

  return {
    toggle,
    show,
    hide,
    isProduction
  };
};

export default ReactQueryDevtoolsWrapper;
