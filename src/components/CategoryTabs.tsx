import React, { useRef } from 'react';
import { Chevron<PERSON>eft, ChevronRight, Bar<PERSON>hart3, CheckCircle } from 'lucide-react';

interface CategoryStats {
  totalItems: number;
  upItems: number;
  downItems: number;
  sameItems: number;
  uniqueAuthors: <AUTHORS>
}

interface CategoryTabsProps {
  // Core data
  categories: string[];
  selectedCategory: string;
  
  // Navigation state
  tabStartIndex: number;
  tabsPerView: number;
  
  // Dropdown state
  showCategoryDropdown: string | null;
  isDropdownAnimating: boolean;
  
  // Stats
  categoryStats: CategoryStats | null;
  getCategoryStats: (category: string) => CategoryStats;
  
  // Event handlers
  onCategoryClick: (category: string) => void;
  onCategoryDropdownToggle: (category: string, event: React.MouseEvent) => void;
  onPrevTabs: () => void;
  onNextTabs: () => void;
}

/**
 * CategoryTabs Component
 * 
 * Extracted from Categories.tsx for better modularity and performance.
 * Handles category navigation, tabs display, and category info dropdown.
 * 
 * Features:
 * - Smart navigation with prev/next buttons
 * - Responsive tab display (1-3 tabs based on screen size)
 * - Category info dropdown with statistics
 * - Smooth animations and transitions
 * - Consistent styling with theme colors
 */
const CategoryTabs: React.FC<CategoryTabsProps> = React.memo(({
  categories,
  selectedCategory,
  tabStartIndex,
  tabsPerView,
  showCategoryDropdown,
  isDropdownAnimating,
  categoryStats,
  getCategoryStats,
  onCategoryClick,
  onCategoryDropdownToggle,
  onPrevTabs,
  onNextTabs
}) => {
  const categoryDropdownRef = useRef<HTMLDivElement>(null);

  // Calculate navigation state
  const canGoPrev = tabStartIndex > 0;
  const canGoNext = tabStartIndex + tabsPerView < categories.length;
  const visibleCategories = categories.slice(tabStartIndex, tabStartIndex + tabsPerView);
  const needsNavigation = categories.length > tabsPerView;

  return (
    <>
      {/* Category Tabs with Smart Navigation */}
      <div className="relative flex items-center mb-1 md:mb-2 gap-1 w-full overflow-hidden">
        {/* Previous Button - Only show when there are hidden categories on the left */}
        {needsNavigation && canGoPrev && (
          <button
            onClick={onPrevTabs}
            className="flex-shrink-0 p-1.5 md:p-2 rounded-md transition-all duration-200 text-[#1A1919] hover:text-white hover:bg-[#9CEE69] bg-white shadow-sm border border-gray-200"
            title="Previous categories"
          >
            <ChevronLeft size={14} className="md:w-4 md:h-4" />
          </button>
        )}

        {/* Tabs Container */}
        <div className="flex-1 bg-gray-100/50 p-1 rounded-lg min-w-0" ref={categoryDropdownRef}>
          <div className="flex gap-1 w-full overflow-hidden">
            {visibleCategories.map((category) => {
              const isActive = selectedCategory === category;
              const isDropdownOpen = showCategoryDropdown === category;

              return (
                <div key={category} className="flex-1">
                  <button
                    onClick={(e) => {
                      if (isActive) {
                        // If already active, toggle dropdown
                        onCategoryDropdownToggle(category, e);
                      } else {
                        // If not active, switch category
                        onCategoryClick(category);
                      }
                    }}
                    className={`w-full flex items-center justify-center gap-1 px-1 md:px-2 py-1.5 md:py-2 rounded-md transition-all duration-200 min-w-0 overflow-hidden cursor-pointer ${
                      isActive
                        ? 'bg-[#9CEE69] shadow-sm text-[#1A1919] border border-[#9CEE69]'
                        : 'text-[#1A1919] hover:text-white hover:bg-[#9CEE69]/80'
                    }`}
                    title={category.replace(/_/g, ' ')}
                  >
                    <BarChart3 size={12} className="flex-shrink-0" />
                    <span className="font-medium text-xs truncate min-w-0">
                      {category.replace(/_/g, ' ').toUpperCase()}
                    </span>
                  </button>
                </div>
              );
            })}
          </div>
        </div>

        {/* Next Button - Only show when there are hidden categories on the right */}
        {needsNavigation && canGoNext && (
          <button
            onClick={onNextTabs}
            className="flex-shrink-0 p-1.5 md:p-2 rounded-md transition-all duration-200 text-[#1A1919] hover:text-white hover:bg-[#9CEE69] bg-white shadow-sm border border-gray-200"
            title="Next categories"
          >
            <ChevronRight size={14} className="md:w-4 md:h-4" />
          </button>
        )}
      </div>

      {/* Category Info Dropdown - Clean & Minimal */}
      {showCategoryDropdown && (
        <div className={`w-full mb-3 bg-gray-50/80 border border-gray-200/60 rounded-lg p-3 ${
          isDropdownAnimating ? 'animate-slide-up' : 'animate-slide-down'
        }`}>
          {(() => {
            // Use memoized stats for selected category, calculate for others
            const stats = showCategoryDropdown === selectedCategory && categoryStats
              ? categoryStats
              : getCategoryStats(showCategoryDropdown);
            return (
              <div className="space-y-3">
                {/* Header with Data Verified Badge */}
                <div className="flex items-center justify-between">
                  <div className="text-xs font-medium text-gray-600 uppercase tracking-wider">
                    {showCategoryDropdown.replace(/_/g, ' ')} Overview
                  </div>
                  <div className="flex items-center space-x-1 px-2 py-0.5 bg-[#9CEE69]/10 border border-[#9CEE69]/30 rounded-md">
                    <CheckCircle size={10} className="text-[#608F44]" />
                    <span className="text-xs text-[#1A1919] font-medium">Verified</span>
                  </div>
                </div>

                {/* Main Stats */}
                <div className="grid grid-cols-4 gap-2">
                  <div className="text-center">
                    <div className="text-lg font-bold text-[#1A1919]">{stats.totalItems}</div>
                    <div className="text-xs text-gray-500">Total</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">{stats.upItems}</div>
                    <div className="text-xs text-gray-500">Up</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-red-600">{stats.downItems}</div>
                    <div className="text-xs text-gray-500">Down</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-600">{stats.sameItems}</div>
                    <div className="text-xs text-gray-500">Same</div>
                  </div>
                </div>

                {/* Additional Stats */}
                <div className="pt-2 border-t border-gray-200/60">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">Unique Authors: <AUTHORS>
                    <span className="font-medium text-[#1A1919]">{stats.uniqueAuthors}</span>
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </>
  );
});

CategoryTabs.displayName = 'CategoryTabs';

export default CategoryTabs;
