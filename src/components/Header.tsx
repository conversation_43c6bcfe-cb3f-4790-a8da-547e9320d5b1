import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  User,
  LogOut,
  LogIn,
  Shield,
  ChevronDown
} from 'lucide-react';
import NavigationMenu from './navigation/NavigationMenu';
import { useAuth } from '../contexts/AuthContext';
import { Button } from './ui';

interface HeaderProps {
  // No props needed
}

const Header: React.FC<HeaderProps> = () => {
  const navigate = useNavigate();
  const { authState, logout } = useAuth();
  const [showUserMenu, setShowUserMenu] = useState(false);

  return (
    <header
      className="fixed top-0 left-0 right-0 z-50 bg-theme-eerie-black shadow-lg border-b border-theme-jet/50 backdrop-blur-sm"
      role="banner"
      aria-label="Main navigation"
    >
      <div className="flex items-center justify-between px-4 lg:px-8 py-3 min-h-[3.5rem]">
        {/* Logo/Brand Section */}
        <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
          <div className="flex items-center space-x-1 sm:space-x-2 min-w-0">
            <img
              src="https://assets.elements.envato.com/apps/storefront/EnvatoLogoLight-b794a434513b3b975d91.svg"
              alt="Progress Dashboard - Analytics Platform"
              className="h-5 sm:h-6 w-auto transition-opacity duration-200 flex-shrink-0"
              onError={(e) => {
                // Fallback to text logo if image fails to load
                e.currentTarget.style.display = 'none';
                const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                if (fallback) fallback.style.display = 'block';
              }}
            />
            <div
              className="hidden w-6 h-6 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg items-center justify-center"
              style={{ display: 'none' }}
              role="img"
              aria-label="Dashboard logo fallback"
            >
              <span className="text-white font-bold text-xs" aria-hidden="true">D</span>
            </div>
            <div className="hidden sm:block min-w-0">
              <h1 className="text-white font-medium text-sm sm:text-base tracking-tight truncate">
                Analytics Dashboard
              </h1>
            </div>
          </div>
        </div>

        {/* Navigation and User Section */}
        <div className="flex items-center space-x-3 flex-shrink-0">
          {/* Navigation Menu - Hidden for clean header */}
          <div className="hidden">
            <NavigationMenu />
          </div>

          {/* Authentication Section */}
          {authState.isAuthenticated ? (
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="text-white hover:bg-white/10 flex items-center space-x-2"
              >
                <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                  <User size={14} className="text-white" />
                </div>
                <span className="hidden sm:block text-sm">
                  {authState.user?.email?.split('@')[0]}
                </span>
                <ChevronDown size={14} />
              </Button>

              {/* User Dropdown Menu */}
              {showUserMenu && (
                <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                  <div className="px-4 py-2 border-b border-gray-200">
                    <p className="text-sm font-medium text-gray-900">
                      {authState.user?.email}
                    </p>
                    <p className="text-xs text-gray-500">
                      Authenticated
                    </p>
                  </div>

                  <div className="py-1">
                    <button
                      onClick={() => {
                        navigate('/admin');
                        setShowUserMenu(false);
                      }}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                    >
                      <Shield size={14} />
                      <span>Admin Panel</span>
                    </button>

                    <button
                      onClick={async () => {
                        await logout();
                        setShowUserMenu(false);
                        navigate('/');
                      }}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                    >
                      <LogOut size={14} />
                      <span>Logout</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/login')}
              className="text-white border-white/20 hover:bg-white/10 flex items-center space-x-2"
            >
              <LogIn size={14} />
              <span className="hidden sm:block">Login</span>
            </Button>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;