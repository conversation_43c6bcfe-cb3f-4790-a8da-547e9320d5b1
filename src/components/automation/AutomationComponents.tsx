import React, { useState, useEffect } from 'react';
import { 
  Zap, 
  Clock, 
  Play, 
  Pause, 
  Setting<PERSON>, 
  Flag,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar,
  Target,
  Activity,
  ToggleLeft,
  ToggleRight
} from 'lucide-react';

// Types for automation data
interface ScheduledTask {
  id: string;
  name: string;
  description: string;
  schedule: string;
  status: 'active' | 'paused' | 'error';
  lastRun: string;
  nextRun: string;
  successRate: number;
}

interface FeatureFlag {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  rolloutPercentage: number;
  environment: 'development' | 'staging' | 'production';
  lastModified: string;
}

interface AutomationStats {
  totalTasks: number;
  activeTasks: number;
  successfulRuns: number;
  failedRuns: number;
  averageExecutionTime: number;
}

// Scheduled Tasks Component
export const ScheduledTasksPanel: React.FC<{ 
  loading: boolean; 
  error: string | null;
  onRefresh: () => void;
}> = ({ loading, error, onRefresh }) => {
  const [tasks, setTasks] = useState<ScheduledTask[]>([]);
  const [stats, setStats] = useState<AutomationStats | null>(null);

  useEffect(() => {
    if (!loading && !error) {
      // Mock data - in real implementation, this would come from API
      setTasks([
        {
          id: '1',
          name: 'Data Backup',
          description: 'Automated daily backup of all CSV data',
          schedule: 'Daily at 2:00 AM',
          status: 'active',
          lastRun: '2024-01-24 02:00:00',
          nextRun: '2024-01-25 02:00:00',
          successRate: 98.5
        },
        {
          id: '2',
          name: 'Cache Cleanup',
          description: 'Clear expired cache entries',
          schedule: 'Every 6 hours',
          status: 'active',
          lastRun: '2024-01-24 18:00:00',
          nextRun: '2024-01-25 00:00:00',
          successRate: 100
        },
        {
          id: '3',
          name: 'Performance Report',
          description: 'Generate weekly performance analytics',
          schedule: 'Weekly on Sunday',
          status: 'paused',
          lastRun: '2024-01-21 09:00:00',
          nextRun: '2024-01-28 09:00:00',
          successRate: 95.2
        },
        {
          id: '4',
          name: 'File Validation',
          description: 'Validate uploaded CSV files for integrity',
          schedule: 'Every 30 minutes',
          status: 'error',
          lastRun: '2024-01-24 19:30:00',
          nextRun: '2024-01-24 20:00:00',
          successRate: 87.3
        }
      ]);

      setStats({
        totalTasks: 4,
        activeTasks: 2,
        successfulRuns: 1247,
        failedRuns: 23,
        averageExecutionTime: 45.2
      });
    }
  }, [loading, error]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle size={16} className="text-green-600" />;
      case 'paused': return <Pause size={16} className="text-yellow-600" />;
      case 'error': return <XCircle size={16} className="text-red-600" />;
      default: return <AlertTriangle size={16} className="text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50';
      case 'paused': return 'text-yellow-600 bg-yellow-50';
      case 'error': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Clock size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Scheduled Tasks</h3>
          <RefreshCw size={16} className="ml-2 animate-spin text-gray-400" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Clock size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Scheduled Tasks</h3>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <XCircle size={16} className="text-red-600 mr-2" />
              <p className="text-sm text-red-800">Error loading scheduled tasks</p>
            </div>
            <button
              onClick={onRefresh}
              className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs font-medium hover:bg-red-200 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Clock size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Scheduled Tasks</h3>
        </div>
        <button
          onClick={onRefresh}
          className="flex items-center space-x-1 px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-xs font-medium hover:bg-primary-200 transition-colors"
        >
          <RefreshCw size={12} />
          <span>Refresh</span>
        </button>
      </div>
      
      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center mb-1">
              <Target size={16} className="text-blue-600 mr-2" />
              <h4 className="font-medium text-blue-800 text-sm">Total Tasks</h4>
            </div>
            <p className="text-xl font-bold text-blue-600">{stats.totalTasks}</p>
          </div>
          
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="flex items-center mb-1">
              <Play size={16} className="text-green-600 mr-2" />
              <h4 className="font-medium text-green-800 text-sm">Active</h4>
            </div>
            <p className="text-xl font-bold text-green-600">{stats.activeTasks}</p>
          </div>
          
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="flex items-center mb-1">
              <CheckCircle size={16} className="text-purple-600 mr-2" />
              <h4 className="font-medium text-purple-800 text-sm">Success</h4>
            </div>
            <p className="text-xl font-bold text-purple-600">{stats.successfulRuns}</p>
          </div>
          
          <div className="bg-red-50 p-3 rounded-lg">
            <div className="flex items-center mb-1">
              <XCircle size={16} className="text-red-600 mr-2" />
              <h4 className="font-medium text-red-800 text-sm">Failed</h4>
            </div>
            <p className="text-xl font-bold text-red-600">{stats.failedRuns}</p>
          </div>
          
          <div className="bg-orange-50 p-3 rounded-lg">
            <div className="flex items-center mb-1">
              <Activity size={16} className="text-orange-600 mr-2" />
              <h4 className="font-medium text-orange-800 text-sm">Avg Time</h4>
            </div>
            <p className="text-xl font-bold text-orange-600">{stats.averageExecutionTime}s</p>
          </div>
        </div>
      )}

      {/* Tasks List */}
      <div className="space-y-3">
        {tasks.map((task) => (
          <div key={task.id} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                {getStatusIcon(task.status)}
                <h4 className="font-medium text-gray-800 ml-2">{task.name}</h4>
                <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                  {task.status}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <button className="p-1 text-gray-600 hover:text-gray-800 transition-colors">
                  <Settings size={14} />
                </button>
                <button className="p-1 text-gray-600 hover:text-gray-800 transition-colors">
                  {task.status === 'active' ? <Pause size={14} /> : <Play size={14} />}
                </button>
              </div>
            </div>
            
            <p className="text-sm text-gray-600 mb-3">{task.description}</p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
              <div>
                <span className="text-gray-500">Schedule:</span>
                <p className="font-medium text-gray-800">{task.schedule}</p>
              </div>
              <div>
                <span className="text-gray-500">Last Run:</span>
                <p className="font-medium text-gray-800">{new Date(task.lastRun).toLocaleString()}</p>
              </div>
              <div>
                <span className="text-gray-500">Next Run:</span>
                <p className="font-medium text-gray-800">{new Date(task.nextRun).toLocaleString()}</p>
              </div>
              <div>
                <span className="text-gray-500">Success Rate:</span>
                <p className="font-medium text-gray-800">{task.successRate}%</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Feature Flags Component
export const FeatureFlagsPanel: React.FC<{
  loading: boolean;
  error: string | null;
  onRefresh: () => void;
}> = ({ loading, error, onRefresh }) => {
  const [flags, setFlags] = useState<FeatureFlag[]>([]);

  useEffect(() => {
    if (!loading && !error) {
      // Mock data - in real implementation, this would come from feature API
      setFlags([
        {
          id: 'optimizations',
          name: 'Performance Optimizations',
          description: 'Enable advanced caching and optimization features',
          enabled: true,
          rolloutPercentage: 100,
          environment: 'production',
          lastModified: '2024-01-24 14:30:00'
        },
        {
          id: 'new_analytics',
          name: 'Enhanced Analytics',
          description: 'New analytics dashboard with advanced insights',
          enabled: true,
          rolloutPercentage: 75,
          environment: 'production',
          lastModified: '2024-01-23 16:45:00'
        },
        {
          id: 'bulk_operations',
          name: 'Bulk File Operations',
          description: 'Enable bulk file management operations',
          enabled: false,
          rolloutPercentage: 0,
          environment: 'development',
          lastModified: '2024-01-22 10:15:00'
        },
        {
          id: 'real_time_sync',
          name: 'Real-time Data Sync',
          description: 'Enable real-time synchronization of data changes',
          enabled: false,
          rolloutPercentage: 25,
          environment: 'staging',
          lastModified: '2024-01-21 09:20:00'
        }
      ]);
    }
  }, [loading, error]);

  const handleToggleFlag = (flagId: string) => {
    setFlags(prev => prev.map(flag =>
      flag.id === flagId
        ? { ...flag, enabled: !flag.enabled, lastModified: new Date().toISOString() }
        : flag
    ));
  };

  const getEnvironmentColor = (env: string) => {
    switch (env) {
      case 'production': return 'text-green-600 bg-green-100';
      case 'staging': return 'text-yellow-600 bg-yellow-100';
      case 'development': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Flag size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Feature Flags</h3>
          <RefreshCw size={16} className="ml-2 animate-spin text-gray-400" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Flag size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Feature Flags</h3>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <XCircle size={16} className="text-red-600 mr-2" />
              <p className="text-sm text-red-800">Error loading feature flags</p>
            </div>
            <button
              onClick={onRefresh}
              className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs font-medium hover:bg-red-200 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Flag size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Feature Flags</h3>
        </div>
        <button
          onClick={onRefresh}
          className="flex items-center space-x-1 px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-xs font-medium hover:bg-primary-200 transition-colors"
        >
          <RefreshCw size={12} />
          <span>Refresh</span>
        </button>
      </div>

      <div className="space-y-3">
        {flags.map((flag) => (
          <div key={flag.id} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <h4 className="font-medium text-gray-800">{flag.name}</h4>
                <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium ${getEnvironmentColor(flag.environment)}`}>
                  {flag.environment}
                </span>
              </div>
              <button
                onClick={() => handleToggleFlag(flag.id)}
                className="flex items-center space-x-2"
              >
                {flag.enabled ? (
                  <ToggleRight size={24} className="text-green-600" />
                ) : (
                  <ToggleLeft size={24} className="text-gray-400" />
                )}
              </button>
            </div>

            <p className="text-sm text-gray-600 mb-3">{flag.description}</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
              <div>
                <span className="text-gray-500">Status:</span>
                <p className={`font-medium ${flag.enabled ? 'text-green-600' : 'text-gray-600'}`}>
                  {flag.enabled ? 'Enabled' : 'Disabled'}
                </p>
              </div>
              <div>
                <span className="text-gray-500">Rollout:</span>
                <p className="font-medium text-gray-800">{flag.rolloutPercentage}%</p>
              </div>
              <div>
                <span className="text-gray-500">Last Modified:</span>
                <p className="font-medium text-gray-800">{new Date(flag.lastModified).toLocaleString()}</p>
              </div>
            </div>

            {/* Rollout Progress Bar */}
            <div className="mt-3">
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs text-gray-500">Rollout Progress</span>
                <span className="text-xs text-gray-600">{flag.rolloutPercentage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    flag.enabled ? 'bg-green-500' : 'bg-gray-400'
                  }`}
                  style={{ width: `${flag.rolloutPercentage}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
