import React, { useState } from 'react';
import { 
  Zap, 
  RefreshCw, 
  Settings as SettingsIcon,
  Play,
  Pause,
  Plus,
  Activity,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { 
  ScheduledTasksPanel, 
  FeatureFlagsPanel 
} from './AutomationComponents';

interface AutomationDashboardProps {
  className?: string;
}

const AutomationDashboard: React.FC<AutomationDashboardProps> = ({ className = '' }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(new Date());
  const [showSettings, setShowSettings] = useState(false);
  const [automationEnabled, setAutomationEnabled] = useState(true);

  const handleRefresh = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setLastUpdated(new Date());
    } catch (err) {
      setError('Failed to refresh automation data');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleAutomation = () => {
    setAutomationEnabled(!automationEnabled);
    // In real implementation, this would call API to enable/disable automation
    console.log('Automation toggled:', !automationEnabled);
  };

  const handleCreateTask = () => {
    // Simulate task creation
    console.log('Creating new scheduled task...');
    // In real implementation, this would open a modal or navigate to task creation
  };

  const formatLastUpdated = (date: Date | null): string => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Controls */}
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-[#1A1919] mb-1">
              Automation Management
            </h2>
            <p className="text-sm text-gray-600">
              Manage scheduled tasks, feature flags, and automated workflows
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Automation Status */}
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${automationEnabled ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-xs text-gray-600">
                {automationEnabled ? 'Active' : 'Disabled'}
              </span>
            </div>
            
            {/* Last Updated */}
            <div className="flex items-center space-x-2 text-xs text-gray-600">
              <Clock size={14} />
              <span>Updated: {formatLastUpdated(lastUpdated)}</span>
            </div>
            
            {/* Master Toggle */}
            <button
              onClick={handleToggleAutomation}
              className={`flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                automationEnabled
                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                  : 'bg-red-100 text-red-800 hover:bg-red-200'
              }`}
            >
              {automationEnabled ? (
                <Pause size={12} />
              ) : (
                <Play size={12} />
              )}
              <span>{automationEnabled ? 'Disable' : 'Enable'}</span>
            </button>
            
            {/* Create Task */}
            <button
              onClick={handleCreateTask}
              className="flex items-center space-x-1 px-3 py-1 bg-secondary-100 text-secondary-800 rounded-full text-xs font-medium hover:bg-secondary-200 transition-colors"
            >
              <Plus size={12} />
              <span>New Task</span>
            </button>
            
            {/* Refresh */}
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center space-x-1 px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-xs font-medium hover:bg-primary-200 transition-colors disabled:opacity-50"
            >
              <RefreshCw size={12} className={loading ? 'animate-spin' : ''} />
              <span>Refresh</span>
            </button>
            
            {/* Settings */}
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center space-x-1 px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium hover:bg-gray-200 transition-colors"
            >
              <SettingsIcon size={12} />
              <span>Settings</span>
            </button>
          </div>
        </div>
        
        {/* Settings Panel */}
        {showSettings && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Task Execution
                </label>
                <select className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:ring-1 focus:ring-primary-500 focus:border-transparent">
                  <option value="parallel">Parallel</option>
                  <option value="sequential">Sequential</option>
                  <option value="priority">Priority Based</option>
                </select>
              </div>
              
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Retry Policy
                </label>
                <select className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:ring-1 focus:ring-primary-500 focus:border-transparent">
                  <option value="exponential">Exponential Backoff</option>
                  <option value="linear">Linear Retry</option>
                  <option value="none">No Retry</option>
                </select>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableNotifications"
                  className="mr-2"
                  defaultChecked
                />
                <label htmlFor="enableNotifications" className="text-xs text-gray-700">
                  Task Notifications
                </label>
              </div>
              
              <div className="text-xs text-gray-600">
                <p>Active Tasks: {loading ? 'Loading...' : '2'}</p>
                <p>Success Rate: 98.5%</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Automation Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <Activity size={16} className="text-blue-600 mr-2" />
            <h4 className="font-medium text-blue-800">Active Tasks</h4>
          </div>
          <p className="text-2xl font-bold text-blue-600">2</p>
          <p className="text-xs text-blue-600">Running now</p>
        </div>
        
        <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <Clock size={16} className="text-green-600 mr-2" />
            <h4 className="font-medium text-green-800">Scheduled</h4>
          </div>
          <p className="text-2xl font-bold text-green-600">4</p>
          <p className="text-xs text-green-600">Total tasks</p>
        </div>
        
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <Zap size={16} className="text-purple-600 mr-2" />
            <h4 className="font-medium text-purple-800">Features</h4>
          </div>
          <p className="text-2xl font-bold text-purple-600">4</p>
          <p className="text-xs text-purple-600">Feature flags</p>
        </div>
        
        <div className="bg-gradient-to-r from-orange-50 to-orange-100 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <AlertTriangle size={16} className="text-orange-600 mr-2" />
            <h4 className="font-medium text-orange-800">Issues</h4>
          </div>
          <p className="text-2xl font-bold text-orange-600">1</p>
          <p className="text-xs text-orange-600">Needs attention</p>
        </div>
      </div>

      {/* Automation Panels Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Scheduled Tasks */}
        <ScheduledTasksPanel 
          loading={loading} 
          error={error} 
          onRefresh={handleRefresh} 
        />
        
        {/* Feature Flags */}
        <FeatureFlagsPanel 
          loading={loading} 
          error={error} 
          onRefresh={handleRefresh} 
        />
      </div>

      {/* Global Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-red-800">Automation Error</h3>
              <p className="text-xs text-red-600 mt-1">{error}</p>
            </div>
            <button
              onClick={handleRefresh}
              className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs font-medium hover:bg-red-200 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Automation Disabled Warning */}
      {!automationEnabled && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle size={16} className="text-yellow-600 mr-2" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800">Automation Disabled</h3>
                <p className="text-xs text-yellow-600 mt-1">
                  Scheduled tasks and automated workflows are currently disabled.
                </p>
              </div>
            </div>
            <button
              onClick={handleToggleAutomation}
              className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded text-xs font-medium hover:bg-yellow-200 transition-colors"
            >
              Enable
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AutomationDashboard;
