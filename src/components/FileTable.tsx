import React from 'react';
import {
  Download,
  Edit3,
  Trash2,
  FileText,
  Calendar,
  User,
  RefreshCw,
  XCircle,
  Info,
  FileX
} from 'lucide-react';
import DataTable from './DataTable';
import { ErrorState } from '../utils/notificationUtils';

interface FileData {
  id: string;
  name: string;
  type: 'category' | 'competitor';
  size: string;
  uploadDate: string;
  lastModified: string;
  author: string;
}

interface FileTableProps {
  files: FileData[];
  loading?: boolean;
  errorState?: ErrorState;
  onDownload: (file: FileData) => Promise<void>;
  onRename: (file: FileData) => void;
  onDelete: (fileId: string) => void;
  onRetry?: () => void;
  operationLoading: { [key: string]: boolean };
}

const FileTable: React.FC<FileTableProps> = ({
  files,
  loading = false,
  errorState,
  onDownload,
  onRename,
  onDelete,
  onRetry,
  operationLoading
}) => {
  const columns = [
    {
      key: 'name',
      label: (
        <div className="flex items-center justify-start">
          <FileText size={12} className="text-gray-500 mr-1" />
          <span className="text-xs font-medium">FILE NAME</span>
        </div>
      ),
      width: 'flex-1',
      cellClass: 'text-left px-3 py-3 align-top min-w-0',
      render: (value: string, row: FileData) => (
        <div className="w-full min-w-0">
          <div className="font-medium text-gray-900 break-words leading-tight mb-1 text-sm">{value}</div>
          <div className="flex items-center">
            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${
              row.type === 'category'
                ? 'bg-theme-nav-green/10 text-theme-eerie-black border-theme-nav-green/30'
                : 'bg-theme-light-green/10 text-theme-eerie-black border-theme-light-green/30'
            }`}>
              {row.type}
            </span>
          </div>
        </div>
      )
    },
    {
      key: 'size',
      label: (
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium">SIZE</span>
        </div>
      ),
      width: 'w-20',
      cellClass: 'text-center px-2 py-3 align-middle',
      render: (value: string) => (
        <div className="flex items-center justify-center">
          <span className="font-medium text-sm text-gray-900 whitespace-nowrap">{value}</span>
        </div>
      )
    },
    {
      key: 'uploadDate',
      label: (
        <div className="flex items-center justify-center space-x-1">
          <Calendar size={12} className="text-gray-500" />
          <span className="text-xs font-medium">UPLOADED</span>
        </div>
      ),
      width: 'w-28',
      cellClass: 'text-center px-2 py-3 align-middle',
      render: (value: string) => (
        <div className="flex items-center justify-center">
          <span className="font-medium text-sm text-gray-900 whitespace-nowrap">{value}</span>
        </div>
      )
    },
    {
      key: 'lastModified',
      label: (
        <div className="flex items-center justify-center space-x-1">
          <Calendar size={12} className="text-gray-500" />
          <span className="text-xs font-medium">MODIFIED</span>
        </div>
      ),
      width: 'w-28',
      cellClass: 'text-center px-2 py-3 align-middle',
      render: (value: string) => (
        <div className="flex items-center justify-center">
          <span className="font-medium text-sm text-gray-900 whitespace-nowrap">{value}</span>
        </div>
      )
    },
    {
      key: 'author',
      label: (
        <div className="flex items-center justify-center space-x-1">
          <User size={12} className="text-gray-500" />
          <span className="text-xs font-medium">AUTHOR</span>
        </div>
      ),
      width: 'w-24',
      cellClass: 'text-center px-2 py-3 align-middle',
      render: (value: string) => (
        <div className="flex items-center justify-center">
          <span className="font-medium text-sm text-gray-900 truncate" title={value}>{value}</span>
        </div>
      )
    },
    {
      key: 'actions',
      label: (
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium">ACTIONS</span>
        </div>
      ),
      width: 'w-36',
      cellClass: 'text-center px-2 py-3 align-middle',
      render: (_, row: FileData) => (
        <div className="flex items-center justify-center space-x-1">
          {/* Download Button */}
          <button
            onClick={() => onDownload(row)}
            disabled={operationLoading[`download-${row.id}`]}
            className="group flex items-center justify-center w-7 h-7 text-theme-eerie-black hover:text-white hover:bg-theme-nav-green rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed border border-theme-nav-green/30 hover:border-theme-nav-green hover:shadow-sm flex-shrink-0"
            title="Download file"
          >
            {operationLoading[`download-${row.id}`] ? (
              <RefreshCw size={13} className="animate-spin" />
            ) : (
              <Download size={13} className="group-hover:scale-110 transition-transform duration-200" />
            )}
          </button>

          {/* Rename Button */}
          <button
            onClick={() => onRename(row)}
            disabled={operationLoading[`rename-${row.id}`]}
            className="group flex items-center justify-center w-7 h-7 text-theme-eerie-black hover:text-white hover:bg-theme-light-green rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed border border-theme-light-green/30 hover:border-theme-light-green hover:shadow-sm flex-shrink-0"
            title="Rename file"
          >
            {operationLoading[`rename-${row.id}`] ? (
              <RefreshCw size={13} className="animate-spin" />
            ) : (
              <Edit3 size={13} className="group-hover:scale-110 transition-transform duration-200" />
            )}
          </button>

          {/* Delete Button */}
          <button
            onClick={() => onDelete(row.id)}
            disabled={operationLoading[`delete-${row.id}`]}
            className="group flex items-center justify-center w-7 h-7 text-red-600 hover:text-white hover:bg-red-500 rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed border border-red-300 hover:border-red-500 hover:shadow-sm flex-shrink-0"
            title="Delete file"
          >
            {operationLoading[`delete-${row.id}`] ? (
              <RefreshCw size={13} className="animate-spin" />
            ) : (
              <Trash2 size={13} className="group-hover:scale-110 transition-transform duration-200" />
            )}
          </button>
        </div>
      )
    }
  ];

  if (loading) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#9CEE69]"></div>
          <span className="ml-3 text-[#1A1919]">Loading files...</span>
        </div>
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="w-full">
        {errorState?.hasError ? (
          <div className="flex flex-col items-center justify-center py-12 flex-1">
            <div className="text-center max-w-md">
              {errorState.errorType === 'connection' ? (
                <div className="text-red-500 mb-4">
                  <XCircle size={48} className="mx-auto mb-3" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{errorState.title}</h3>
                  <p className="text-sm text-gray-600 mb-4">{errorState.message}</p>
                  {errorState.suggestion && (
                    <p className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                      💡 {errorState.suggestion}
                    </p>
                  )}
                </div>
              ) : (
                <div className="text-yellow-500 mb-4">
                  <Info size={48} className="mx-auto mb-3" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{errorState.title}</h3>
                  <p className="text-sm text-gray-600 mb-4">{errorState.message}</p>
                  {errorState.suggestion && (
                    <p className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                      💡 {errorState.suggestion}
                    </p>
                  )}
                </div>
              )}
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="mt-4 px-4 py-2 bg-[#9CEE69] text-[#1A1919] rounded-lg hover:bg-[#9CEE69]/80 transition-all duration-200"
                >
                  Coba Lagi
                </button>
              )}
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center py-12 text-gray-500">
            <div className="text-center">
              <FileX size={48} className="mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak Ada File</h3>
              <p className="text-sm text-gray-600 mb-4">Tidak ada file yang ditemukan.</p>
              <p className="text-xs text-gray-500">Upload file CSV pertama Anda untuk memulai.</p>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        <div className="min-w-[800px]">
          <DataTable data={files} columns={columns} />
        </div>
      </div>
    </div>
  );
};

export default FileTable;
