import React from 'react';
import { Categories, Competitors } from './LazyComponents';

interface AnalysisLayoutProps {
  title: string;
  subtitle: string;
  activeTab: 'categories' | 'competitors';
}

const AnalysisLayout: React.FC<AnalysisLayoutProps> = ({ activeTab }) => {

  return (
    <div className="min-h-screen bg-gradient-to-br from-theme-seashell via-white to-primary-50/30">
      {/* Content Area - Proper spacing untuk fixed header */}
      <div className="w-full" role="main" aria-label={`${activeTab} analysis dashboard`}>
        {activeTab === 'categories' && (
          <div id="categories-panel" role="tabpanel" aria-labelledby="categories-tab">
            <Categories />
          </div>
        )}
        {activeTab === 'competitors' && (
          <div id="competitors-panel" role="tabpanel" aria-labelledby="competitors-tab">
            <Competitors />
          </div>
        )}
      </div>


    </div>
  );
};

export default AnalysisLayout;
