import React from 'react';
import { Trophy, Medal, Award, User, TrendingUp } from 'lucide-react';
import { AuthorData } from '../services/api';

/**
 * Top Author Interface - Compatible with AuthorData
 */
export interface TopAuthor extends AuthorData {
  points: number; // Make points required for our calculations
}

/**
 * Top Authors Panel Props Interface
 */
export interface TopAuthorsPanelProps {
  topAuthors: <AUTHORS>
  onAuthorClick: (author: string) => void;
  className?: string;
}

/**
 * TopAuthorsPanel Component
 * 
 * Extracts top authors panel logic from Categories.tsx component.
 * Provides reusable top authors display with ranking, points, and interactive features.
 * 
 * Features:
 * - Ranking display with trophy icons for top 3
 * - Points and count display for each author
 * - Interactive author selection
 * - Responsive design with proper spacing
 * - Visual hierarchy with different styling for ranks
 * - Empty state handling
 * - Reusable across components
 * 
 * @param topAuthors - Array of top authors with ranking information
 * @param onAuthorClick - Callback function when an author is clicked
 * @param className - Optional additional CSS classes
 * @returns TopAuthorsPanel JSX element
 */
export const TopAuthorsPanel: React.FC<TopAuthorsPanelProps> = ({
  topAuthors,
  onAuthorClick,
  className = ''
}) => {
  // Get appropriate icon for rank
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="w-5 h-5 text-yellow-500" />;
      case 2:
        return <Medal className="w-5 h-5 text-gray-400" />;
      case 3:
        return <Award className="w-5 h-5 text-amber-600" />;
      default:
        return <User className="w-4 h-4 text-gray-400" />;
    }
  };

  // Get appropriate styling for rank
  const getRankStyling = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200 hover:from-yellow-100 hover:to-yellow-200';
      case 2:
        return 'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200 hover:from-gray-100 hover:to-gray-200';
      case 3:
        return 'bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200 hover:from-amber-100 hover:to-amber-200';
      default:
        return 'bg-white border-gray-200 hover:bg-gray-50';
    }
  };

  // Handle empty state
  if (!topAuthors || topAuthors.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="flex items-center space-x-2 mb-4">
          <TrendingUp className="w-5 h-5 text-[#9CEE69]" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Top Authors</h3>
        </div>
        <div className="text-center py-8">
          <User className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <p className="text-gray-500">No authors data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <TrendingUp className="w-5 h-5 text-[#9CEE69]" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Top Authors</h3>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          Ranked by points and contribution count
        </p>
      </div>

      {/* Authors List */}
      <div className="p-6">
        <div className="space-y-3">
          {topAuthors.slice(0, 10).map((author, index) => (
            <div
              key={author.author}
              onClick={() => onAuthorClick(author.author)}
              className={`p-4 rounded-lg border transition-all duration-200 cursor-pointer ${getRankStyling(author.rank)}`}
            >
              <div className="flex items-center justify-between">
                {/* Left side - Rank and Author */}
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8">
                    {getRankIcon(author.rank)}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-[#1A1919] text-sm">
                        {author.author}
                      </span>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        #{author.rank}
                      </span>
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {author.count} contribution{author.count !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>

                {/* Right side - Points */}
                <div className="text-right">
                  <div className="text-lg font-bold text-[#1A1919]">
                    {author.points}
                  </div>
                  <div className="text-xs text-gray-500">
                    points
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Show more indicator */}
        {topAuthors.length > 10 && (
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-500">
              Showing top 10 of {topAuthors.length} authors
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * getTopAuthorsDisplayData - Utility function for top authors data processing
 * 
 * Processes top authors data for display purposes.
 * Used for cases where component rendering isn't needed.
 * 
 * @param topAuthors - Array of top authors
 * @param limit - Maximum number of authors to return (default: 10)
 * @returns Processed top authors data
 */
export const getTopAuthorsDisplayData = (
  topAuthors: <AUTHORS>
  limit: number = 10
) => {
  if (!topAuthors || topAuthors.length === 0) {
    return {
      authors: [],
      totalCount: 0,
      hasMore: false
    };
  }

  const limitedAuthors = topAuthors.slice(0, limit);
  
  return {
    authors: limitedAuthors,
    totalCount: topAuthors.length,
    hasMore: topAuthors.length > limit
  };
};

export default TopAuthorsPanel;
