import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Plus, Clock, TrendingUp, TrendingDown, ArrowUpDown, CheckCircle, XCircle, Minus, BarChart3, <PERSON>tings, Edit2, Trash2, ExternalLink, Crown, ChevronLeft, ChevronRight, ChevronDown, ChevronUp, Info } from 'lucide-react';
import Modal from './Modal';
import DataTable from './DataTable';
import FloatingTopAuthorsButton from './FloatingTopAuthorsButton';
import TopAuthorsModal from './TopAuthorsModal';
import PaginationControls from './PaginationControls';
import { api } from '../utils/apiClient';
import { AuthorData, CategoryData, DetailedAuthorData } from '../services/api';
import { createApiErrorNotification, createEmptyDataNotification, createErrorState, ErrorState } from '../utils/notificationUtils';



const Categories: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [categories, setCategories] = useState<string[]>([]);
  const [categoryData, setCategoryData] = useState<CategoryData[]>([]);
  const [loading, setLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isApiAvailable, setIsApiAvailable] = useState(true);
  const [apiError, setApiError] = useState<string | null>(null);
  const [errorState, setErrorState] = useState<ErrorState>({ hasError: false, errorType: 'unknown', title: '', message: '' });
  const [showAddModal, setShowAddModal] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [addCategoryError, setAddCategoryError] = useState('');
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editCategoryName, setEditCategoryName] = useState('');
  const [editCategoryError, setEditCategoryError] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<string | null>(null);

  // Top Authors state
  const [topAuthors, setTopAuthors] = useState<AuthorData[]>([]);
  const [expandedAuthor, setExpandedAuthor] = useState<string | null>(null);
  const [authorDetails, setAuthorDetails] = useState<{ [key: string]: DetailedAuthorData }>({});
  const [loadingAuthorDetails, setLoadingAuthorDetails] = useState<{ [key: string]: boolean }>({});
  const [showTopAuthorsModal, setShowTopAuthorsModal] = useState(false);

  // Filter state
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: { start: '', end: '' },
    year: '',
    categories: [] as string[]
  });
  const [hasActiveFilters, setHasActiveFilters] = useState(false);
  const filterDropdownRef = useRef<HTMLDivElement>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 25;

  // Screen width state for responsive behavior
  const [screenWidth, setScreenWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  // Tab navigation state
  const [tabStartIndex, setTabStartIndex] = useState(0);
  const [tabsPerView, setTabsPerView] = useState(4); // Number of tabs visible at once

  // Category dropdown state
  const [showCategoryDropdown, setShowCategoryDropdown] = useState<string | null>(null);
  const [isDropdownAnimating, setIsDropdownAnimating] = useState(false);
  const categoryDropdownRef = useRef<HTMLDivElement>(null);

  // Responsive tabs per view - optimized to prevent overflow
  useEffect(() => {
    const updateTabsPerView = () => {
      const width = window.innerWidth;
      // Very conservative approach to prevent any overflow
      if (width < 640) { // sm - mobile
        setTabsPerView(1); // Show only 1 tab
      } else if (width < 768) { // md - small tablet
        setTabsPerView(1); // Keep 1 tab for safety
      } else if (width < 1024) { // lg - tablet
        setTabsPerView(2); // Show 2 tabs
      } else if (width < 1280) { // xl - desktop
        setTabsPerView(2); // Keep 2 tabs to prevent overflow
      } else { // 2xl and above - large desktop
        setTabsPerView(3); // Maximum 3 tabs to ensure no overflow
      }
    };

    updateTabsPerView();
    window.addEventListener('resize', updateTabsPerView);
    return () => window.removeEventListener('resize', updateTabsPerView);
  }, []);

  // Reset tab navigation when categories change
  useEffect(() => {
    setTabStartIndex(0);
  }, [categories]);



  // Pagination functions
  const getPaginatedData = (data: CategoryData[]) => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
  };

  const handleNextPage = () => {
    const totalPages = Math.ceil(categoryData.length / itemsPerPage);
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const loadCategoryData = useCallback(async (category: string) => {
    setLoading(true);
    setCurrentPage(1); // Reset to first page when category changes
    setApiError(null);
    setErrorState({ hasError: false, errorType: 'unknown', title: '', message: '' });

    try {
      const response = await api.get(`/api/categories/${encodeURIComponent(category)}/data`);
      if (response.success && response.data) {
        setCategoryData(response.data.data);
        setApiError(null);
        setErrorState({ hasError: false, errorType: 'unknown', title: '', message: '' });
        setIsApiAvailable(true);
      } else {
        throw new Error(response.error || 'Failed to load category data');
      }
    } catch (error) {
      console.error('Error loading category data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setApiError(errorMessage);
      setIsApiAvailable(false);

      // Create error state for UI display
      const newErrorState = createErrorState(errorMessage, 'kategori');
      setErrorState(newErrorState);

      // Clear category data instead of showing mock data
      setCategoryData([]);

      // Show notification
      const notification = createApiErrorNotification(errorMessage);
      console.log(`[${notification.type.toUpperCase()}] ${notification.title}: ${notification.message}`);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
    loadCategoryData(category);

    // Ensure selected category is visible
    const categoryIndex = categories.indexOf(category);
    if (categoryIndex !== -1) {
      const newStartIndex = Math.floor(categoryIndex / tabsPerView) * tabsPerView;
      setTabStartIndex(newStartIndex);
    }
  };

  // Handle category dropdown toggle with smooth animation
  const handleCategoryDropdownToggle = (category: string, event: React.MouseEvent) => {
    event.stopPropagation();

    if (showCategoryDropdown === category) {
      // Closing dropdown
      setIsDropdownAnimating(true);
      setTimeout(() => {
        setShowCategoryDropdown(null);
        setIsDropdownAnimating(false);
      }, 200); // Match animation duration
    } else {
      // Opening dropdown
      setShowCategoryDropdown(category);
      setIsDropdownAnimating(false);
    }
  };

  // Get category statistics
  const getCategoryStats = (category: string) => {
    const data = category === selectedCategory ? categoryData : getMockDataForCategory(category);
    const totalItems = data.length;
    const upItems = data.filter(item => item.Status === 'Up').length;
    const downItems = data.filter(item => item.Status === 'Down').length;
    const sameItems = data.filter(item => item.Status === 'Same').length;
    const uniqueAuthors = new Set(data.map(item => item.Author)).size;

    return {
      totalItems,
      upItems,
      downItems,
      sameItems,
      uniqueAuthors
    };
  };

  // Tab navigation functions
  const handlePrevTabs = () => {
    setTabStartIndex(Math.max(0, tabStartIndex - tabsPerView));
  };

  const handleNextTabs = () => {
    const maxStartIndex = Math.max(0, categories.length - tabsPerView);
    setTabStartIndex(Math.min(maxStartIndex, tabStartIndex + tabsPerView));
  };

  const canGoPrev = tabStartIndex > 0;
  const canGoNext = tabStartIndex + tabsPerView < categories.length;
  const visibleCategories = categories.slice(tabStartIndex, tabStartIndex + tabsPerView);
  const needsNavigation = categories.length > tabsPerView;

  // Debug logging for navigation state (commented out for production)
  // console.log('Navigation Debug:', {
  //   totalCategories: categories.length,
  //   tabsPerView,
  //   tabStartIndex,
  //   needsNavigation,
  //   canGoPrev,
  //   canGoNext,
  //   visibleCategories: visibleCategories.length
  // });

  const handleAddCategory = () => {
    const trimmedName = newCategoryName.trim();
    setAddCategoryError('');

    if (!trimmedName) {
      setAddCategoryError('Category name is required');
      return;
    }

    if (trimmedName.length < 2) {
      setAddCategoryError('Category name must be at least 2 characters');
      return;
    }

    // Convert to consistent format (lowercase with underscores)
    const categoryKey = trimmedName.toLowerCase().replace(/\s+/g, '_');

    // Check for duplicates
    if (categories.includes(categoryKey)) {
      setAddCategoryError('Category already exists!');
      return;
    }

    setCategories([...categories, categoryKey]);
    setNewCategoryName('');
    setAddCategoryError('');
    setShowAddModal(false);
  };

  const handleDeleteCategory = (category: string) => {
    setCategories(categories.filter(c => c !== category));
    if (selectedCategory === category) {
      const remainingCategories = categories.filter(c => c !== category);
      if (remainingCategories.length > 0) {
        setSelectedCategory(remainingCategories[0]);
        loadCategoryData(remainingCategories[0]);
      } else {
        setSelectedCategory('');
        setCategoryData([]);
      }
    }
  };

  const handleEditCategory = (category: string) => {
    setEditingCategory(category);
    setEditCategoryName(category.replace(/_/g, ' '));
    setShowSettingsModal(false);
  };

  const handleSaveEdit = () => {
    const trimmedName = editCategoryName.trim();
    setEditCategoryError('');

    if (!trimmedName) {
      setEditCategoryError('Category name is required');
      return;
    }

    if (trimmedName.length < 2) {
      setEditCategoryError('Category name must be at least 2 characters');
      return;
    }

    if (editingCategory) {
      const newCategoryKey = trimmedName.toLowerCase().replace(/\s+/g, '_');

      // Check if new name is same as current (no change needed)
      if (newCategoryKey === editingCategory) {
        setEditingCategory(null);
        setEditCategoryName('');
        setEditCategoryError('');
        return;
      }

      // Check for duplicates
      if (categories.includes(newCategoryKey)) {
        setEditCategoryError('Category name already exists!');
        return;
      }

      const updatedCategories = categories.map(cat =>
        cat === editingCategory ? newCategoryKey : cat
      );
      setCategories(updatedCategories);

      if (selectedCategory === editingCategory) {
        setSelectedCategory(newCategoryKey);
      }

      setEditingCategory(null);
      setEditCategoryName('');
      setEditCategoryError('');
    }
  };

  const handleConfirmDelete = (category: string) => {
    setCategoryToDelete(category);
    setShowDeleteConfirm(true);
    setShowSettingsModal(false);
  };

  const handleDuplicateCategory = (category: string) => {
    const duplicateName = `${category}_copy`;
    setCategories([...categories, duplicateName]);
    setShowSettingsModal(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Up':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'Down':
        return <XCircle size={16} className="text-red-500" />;
      default:
        return <Minus size={16} className="text-gray-500" />;
    }
  };

  const getAuthorBadgeColor = (author: string) => {
    // Generate consistent colors based on author name using app theme
    const colors = [
      'bg-theme-seashell/80 text-theme-eerie-black border-theme-nav-green/30 backdrop-blur-sm',
      'bg-theme-nav-green/20 text-theme-eerie-black border-theme-nav-green/50 backdrop-blur-sm',
      'bg-gray-50 text-gray-700 border-gray-200 backdrop-blur-sm',
      'bg-theme-seashell/60 text-theme-jet border-theme-asparagus/40 backdrop-blur-sm',
      'bg-theme-nav-green/15 text-theme-asparagus border-theme-asparagus/30 backdrop-blur-sm',
      'bg-gray-100/80 text-theme-eerie-black border-gray-300/50 backdrop-blur-sm',
      'bg-theme-seashell/90 text-theme-asparagus border-theme-nav-green/20 backdrop-blur-sm',
      'bg-theme-nav-green/10 text-theme-jet border-theme-jet/20 backdrop-blur-sm'
    ];

    // Simple hash function to get consistent color for same author
    let hash = 0;
    for (let i = 0; i < author.length; i++) {
      hash = author.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  };

  // Render author details dropdown
  const renderAuthorDetails = (authorName: string) => {
    const details = authorDetails[authorName];
    const isLoading = loadingAuthorDetails[authorName];

    if (isLoading) {
      return (
        <div className="px-2 py-3 bg-gray-50/30 border-t border-gray-200">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#9CEE69]"></div>
            <span className="ml-2 text-xs text-[#1A1919]">Loading details...</span>
          </div>
        </div>
      );
    }

    if (!details) {
      return (
        <div className="px-2 py-3 bg-gray-50/30 border-t border-gray-200">
          <div className="text-center text-xs text-gray-500">
            No details available
          </div>
        </div>
      );
    }

    // Sort pages by page number
    const sortedPages = Object.entries(details.page_distribution)
      .map(([pageKey, count]) => ({
        page: parseInt(pageKey.replace('page_', '')),
        count
      }))
      .sort((a, b) => a.page - b.page);

    // Calculate points per page
    const maxPage = Math.max(...sortedPages.map(p => p.page));
    const pagesWithPoints = sortedPages.map(({ page, count }) => ({
      page,
      count,
      pointsPerItem: maxPage - page + 1,
      totalPoints: count * (maxPage - page + 1)
    }));

    return (
      <div className="px-2 py-3 bg-gray-50/30 border-t border-gray-200">
        <div className="space-y-2">
          {/* Summary */}
          <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-200">
            <div className="flex items-center space-x-1">
              <Info size={10} className="text-blue-500" />
              <span className="text-xs font-medium text-gray-700">Points Breakdown</span>
            </div>
            <span className="text-xs font-bold text-[#9CEE69]">
              {details.total_points} pts
            </span>
          </div>

          {/* Page distribution */}
          <div className="space-y-1">
            {pagesWithPoints.map(({ page, count, pointsPerItem, totalPoints }) => (
              <div key={page} className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2">
                  <span className="text-gray-600">Page {page}:</span>
                  <span className="font-medium text-gray-800">{count} items</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="text-gray-500">×{pointsPerItem}</span>
                  <span className="font-medium text-[#9CEE69]">{totalPoints}pts</span>
                </div>
              </div>
            ))}
          </div>

          {/* Total items */}
          <div className="pt-2 border-t border-gray-200">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Total Items:</span>
              <span className="font-medium text-gray-800">
                {Object.values(details.page_distribution).reduce((sum, count) => sum + count, 0)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const columns = [
    {
      key: 'Title',
      label: (
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium">TITLE</span>
        </div>
      ),
      width: 'flex-1',
      cellClass: 'text-sm whitespace-normal break-words leading-tight pr-2 border-r border-gray-200 align-top',
      render: (value: string, row: CategoryData) => (
        <div className="w-full py-0.5">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="font-medium text-gray-900 break-words leading-snug text-sm flex-shrink-0">{value}</span>
            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border flex-shrink-0 transition-all duration-200 ${getAuthorBadgeColor(row.Author)}`}>
              {row.Author}
            </span>
          </div>
        </div>
      )
    },
    {
      key: 'Page New',
      label: (
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium">PAGE NEW</span>
        </div>
      ),
      width: 'w-24',
      cellClass: 'text-sm text-center px-2 border-r border-gray-200 align-middle',
      render: (value: number) => (
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-1 bg-green-50 px-1.5 py-0.5 rounded">
            <Clock size={10} className="text-green-500" />
            <span className="font-semibold text-green-600 text-xs">{value}</span>
          </div>
        </div>
      )
    },
    {
      key: 'Page Old',
      label: (
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium">PAGE OLD</span>
        </div>
      ),
      width: 'w-24',
      cellClass: 'text-sm text-center px-2 border-r border-gray-200 align-middle',
      render: (value: number) => (
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-1 bg-gray-50 px-1.5 py-0.5 rounded">
            <Clock size={10} className="text-gray-500" />
            <span className="font-semibold text-gray-600 text-xs">{value}</span>
          </div>
        </div>
      )
    },
    {
      key: 'Order Old',
      label: (
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium">ORDER OLD</span>
        </div>
      ),
      width: 'w-24',
      cellClass: 'text-sm text-center px-2 border-r border-gray-200 align-middle',
      render: (value: number) => (
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-1 bg-gray-50 px-1.5 py-0.5 rounded">
            <TrendingDown size={10} className="text-gray-500" />
            <span className="font-semibold text-gray-600 text-xs">{value}</span>
          </div>
        </div>
      )
    },
    {
      key: 'Order New',
      label: (
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium">ORDER NEW</span>
        </div>
      ),
      width: 'w-24',
      cellClass: 'text-sm text-center px-2 border-r border-gray-200 align-middle',
      render: (value: number) => (
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-1 bg-blue-50 px-1.5 py-0.5 rounded">
            <TrendingUp size={10} className="text-blue-500" />
            <span className="font-semibold text-blue-600 text-xs">{value}</span>
          </div>
        </div>
      )
    },
    {
      key: 'Change',
      label: (
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium">CHANGE</span>
        </div>
      ),
      width: 'w-20',
      cellClass: 'text-sm text-center px-2 border-r border-gray-200 align-middle',
      render: (value: number) => (
        <div className="flex items-center justify-center">
          <div className={`flex items-center space-x-1 px-1.5 py-0.5 rounded ${
            value > 0 ? 'bg-green-50' :
            value < 0 ? 'bg-red-50' :
            'bg-gray-50'
          }`}>
            {value > 0 ? (
              <TrendingUp size={10} className="text-green-500" />
            ) : value < 0 ? (
              <TrendingDown size={10} className="text-red-500" />
            ) : (
              <Minus size={10} className="text-gray-500" />
            )}
            <span className={`font-semibold text-xs ${
              value > 0 ? 'text-green-600' :
              value < 0 ? 'text-red-600' :
              'text-gray-600'
            }`}>
              {value > 0 ? '+' : ''}{value}
            </span>
          </div>
        </div>
      )
    },
    {
      key: 'Status',
      label: (
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium">STATUS</span>
        </div>
      ),
      width: 'w-20',
      cellClass: 'text-sm text-center px-2 border-r border-gray-200 align-middle',
      render: (value: string) => (
        <div className="flex items-center justify-center space-x-1">
          {getStatusIcon(value)}
          <span className={`font-medium text-xs ${
            value === 'Up' ? 'text-green-600' :
            value === 'Down' ? 'text-red-600' :
            'text-gray-600'
          }`}>
            {value}
          </span>
        </div>
      )
    },
    {
      key: 'View',
      label: (
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium">VIEW</span>
        </div>
      ),
      width: 'w-12',
      cellClass: 'text-center px-2 align-middle',
      render: (value: any, row: CategoryData) => (
        <div className="w-full py-0.5">
          <div className="flex items-center justify-center">
            <button
              onClick={() => row.Link && window.open(row.Link, '_blank')}
              className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border backdrop-blur-sm flex-shrink-0 transition-all duration-200 cursor-pointer hover:bg-theme-nav-green hover:text-theme-eerie-black hover:border-theme-nav-green bg-gray-50 text-gray-700 border-gray-200"
              style={{ minHeight: 'auto', height: 'auto', lineHeight: '1' }}
              title="View Item"
              disabled={!row.Link}
            >
              <ExternalLink size={10} className="mr-1" />
              <span>View</span>
            </button>
          </div>
        </div>
      )
    }
  ];

  // Load categories from API
  const loadCategories = async () => {
    try {
      console.log('🔄 Loading categories from API...');
      const response = await api.getCategories();
      console.log('📡 API Response:', response);

      if (response.success && response.data) {
        console.log('✅ Categories loaded successfully:', response.data.categories);
        setCategories(response.data.categories);
        setApiError(null);
        setIsApiAvailable(true);
        setErrorState({ hasError: false, errorType: 'unknown', title: '', message: '' });
        return response.data.categories;
      } else {
        throw new Error(response.error || 'Failed to load categories');
      }
    } catch (error) {
      console.error('❌ Error loading categories:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setApiError(errorMessage);
      setIsApiAvailable(false);

      // Create error state for UI display
      const newErrorState = createErrorState(errorMessage, 'kategori');
      setErrorState(newErrorState);

      // Clear categories instead of using fallback
      setCategories([]);

      // Show notification
      const notification = createApiErrorNotification(errorMessage);
      console.log(`[${notification.type.toUpperCase()}] ${notification.title}: ${notification.message}`);

      return [];
    }
  };

  // Load categories on component mount
  useEffect(() => {
    loadCategories();
  }, []);

  // Auto-select first category on component mount
  useEffect(() => {
    // Only run when categories are loaded and component is not yet initialized
    if (categories.length > 0 && !isInitialized && !selectedCategory) {
      const firstCategory = categories[0];
      setSelectedCategory(firstCategory);
      loadCategoryData(firstCategory);
      setIsInitialized(true);
    }
  }, [categories, isInitialized, selectedCategory, loadCategoryData]); // Fixed: Include all dependencies properly

  // Handle clicks outside filter dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filterDropdownRef.current && !filterDropdownRef.current.contains(event.target as Node)) {
        setShowFilterDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle clicks outside category dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (categoryDropdownRef.current && !categoryDropdownRef.current.contains(event.target as Node)) {
        setShowCategoryDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Effect to handle window resize for responsive behavior
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update top authors when category data changes
  useEffect(() => {
    if (categoryData && categoryData.length > 0) {
      const authorsFromCategory = calculateTopAuthorsFromCategory(categoryData);
      setTopAuthors(authorsFromCategory);
    } else {
      setTopAuthors([]);
    }
    // Reset expanded author when category changes
    setExpandedAuthor(null);
    setAuthorDetails({});
    setLoadingAuthorDetails({});
  }, [categoryData]);

  // Update active filters indicator
  useEffect(() => {
    const hasFilters = Boolean(
      filters.dateRange.start ||
      filters.dateRange.end ||
      filters.year ||
      filters.categories.length > 0
    );
    setHasActiveFilters(hasFilters);
  }, [filters]);

  // Filter functions
  const handleFilterChange = (filterType: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleCategoryFilterChange = (category: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      categories: checked
        ? [...prev.categories, category]
        : prev.categories.filter(c => c !== category)
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      dateRange: { start: '', end: '' },
      year: '',
      categories: []
    });
  };

  const applyFilters = () => {
    // Filter logic will be applied to the data
    setShowFilterDropdown(false);
  };

  // Calculate top authors from current category data
  const calculateTopAuthorsFromCategory = (data: CategoryData[]) => {
    if (!data || data.length === 0) return [];

    // Find max page number for points calculation
    const maxPage = Math.max(...data.map(item => item['Page New'] || 1));

    // Group by author and calculate points
    const authorStats: { [key: string]: { count: number; totalPoints: number } } = {};

    data.forEach(item => {
      const author = item.Author;
      const pageNew = item['Page New'] || 1;
      const points = maxPage - pageNew + 1; // Same calculation as backend

      if (!authorStats[author]) {
        authorStats[author] = { count: 0, totalPoints: 0 };
      }

      authorStats[author].count += 1;
      authorStats[author].totalPoints += points;
    });

    // Convert to array and sort by total points
    const authorsArray = Object.entries(authorStats)
      .map(([author, stats], index) => ({
        author,
        count: stats.count,
        points: stats.totalPoints,
        rank: index + 1
      }))
      .filter(item => item.author && item.author !== 'Unknown Author' && item.author.trim() !== '')
      .sort((a, b) => b.points - a.points)
      .map((item, index) => ({ ...item, rank: index + 1 }));

    return authorsArray;
  };

  // Calculate detailed author data from current category data
  const calculateAuthorDetailsFromCategory = (data: CategoryData[], authorName: string): DetailedAuthorData => {
    const authorItems = data.filter(item => item.Author === authorName);
    const maxPage = Math.max(...data.map(item => item['Page New'] || 1));

    // Calculate page distribution
    const pageDistribution: Record<string, number> = {};
    let totalPoints = 0;

    authorItems.forEach(item => {
      const pageNew = item['Page New'] || 1;
      const points = maxPage - pageNew + 1;
      totalPoints += points;

      const pageKey = `page_${pageNew}`;
      pageDistribution[pageKey] = (pageDistribution[pageKey] || 0) + 1;
    });

    return {
      author: authorName,
      total_points: totalPoints,
      rank: topAuthors.find(a => a.author === authorName)?.rank || 0,
      page_distribution: pageDistribution
    };
  };

  // Handle author click to toggle dropdown
  const handleAuthorClick = async (authorName: string) => {
    if (expandedAuthor === authorName) {
      // Close if already expanded
      setExpandedAuthor(null);
      return;
    }

    setExpandedAuthor(authorName);

    // Check if we already have details for this author
    if (authorDetails[authorName]) {
      return;
    }

    // Set loading state
    setLoadingAuthorDetails(prev => ({ ...prev, [authorName]: true }));

    try {
      // Try to get detailed data from API first
      const response = await api.get('/api/authors/detailed?limit=20');
      if (response.success && response.data) {
        const authorDetail = response.data.data.find(author => author.author === authorName);
        if (authorDetail) {
          setAuthorDetails(prev => ({ ...prev, [authorName]: authorDetail }));
        } else {
          // Fallback to calculate from current category data
          const calculatedDetail = calculateAuthorDetailsFromCategory(categoryData, authorName);
          setAuthorDetails(prev => ({ ...prev, [authorName]: calculatedDetail }));
        }
      } else {
        // Fallback to calculate from current category data
        const calculatedDetail = calculateAuthorDetailsFromCategory(categoryData, authorName);
        setAuthorDetails(prev => ({ ...prev, [authorName]: calculatedDetail }));
      }
    } catch (error) {
      console.error('Error fetching author details:', error);
      // Fallback to calculate from current category data
      const calculatedDetail = calculateAuthorDetailsFromCategory(categoryData, authorName);
      setAuthorDetails(prev => ({ ...prev, [authorName]: calculatedDetail }));
    } finally {
      setLoadingAuthorDetails(prev => ({ ...prev, [authorName]: false }));
    }
  };

  return (
    <div className="w-full">
      {/* Main Grid Layout - Dengan proper spacing dari header - Responsive grid */}
      <div className={`grid grid-cols-1 gap-1 md:gap-2 lg:gap-3 p-1 md:p-2 lg:p-3 pt-8 md:pt-10 lg:pt-12 ${
        (screenWidth < 1024 || screenWidth > 1600) ? 'lg:grid-cols-4' : 'lg:grid-cols-1'
      }`}>
        {/* Categories Analysis Overview - Responsive width */}
        <div className={`bg-white border border-gray-200 rounded-lg p-2 md:p-3 lg:p-4 shadow-sm ${
          (screenWidth < 1024 || screenWidth > 1600) ? 'lg:col-span-3' : 'lg:col-span-1'
        }`}>
          {/* Header - Simple & Clean */}
          <div className="flex items-center justify-between mb-3 md:mb-4">
            <div className="flex items-center gap-2.5">
              <div className="flex items-center justify-center w-8 h-8 bg-[#9CEE69]/10 rounded-lg">
                <BarChart3 size={16} className="text-[#1A1919]" />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h2 className="text-base md:text-lg font-semibold text-[#1A1919]">Categories Analysis</h2>
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-[#9CEE69]/20 text-[#1A1919] border border-[#9CEE69]/30">
                    Standard
                  </span>
                </div>
                <p className="text-xs text-gray-600">Performance data overview</p>
              </div>
            </div>

            <button
              onClick={() => setShowSettingsModal(true)}
              className="flex items-center justify-center w-8 h-8 md:w-9 md:h-9 text-gray-500 hover:text-[#1A1919] hover:bg-[#9CEE69]/10 rounded-lg transition-all duration-200 border border-gray-200 hover:border-[#9CEE69]/30"
              title="Category Settings"
            >
              <Settings size={16} className="md:w-[17px] md:h-[17px]" />
            </button>
          </div>

          {/* Category Tabs with Smart Navigation */}
          <div className="flex items-center mb-1 md:mb-2 gap-1 w-full overflow-hidden">
            {categories.length === 0 ? (
              <div className="flex-1 bg-gray-100/50 p-4 rounded-lg text-center">
                <div className="text-gray-500">
                  <BarChart3 size={24} className="mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">Tidak ada kategori tersedia</p>
                  {errorState.hasError && (
                    <p className="text-xs text-gray-400 mt-1">
                      {errorState.errorType === 'connection' ? 'Backend tidak dapat diakses' : 'Gagal memuat kategori'}
                    </p>
                  )}
                </div>
              </div>
            ) : (
              <>
                {/* Previous Button - Only show when there are hidden categories on the left */}
                {needsNavigation && canGoPrev && (
                  <button
                    onClick={handlePrevTabs}
                    className="flex-shrink-0 p-1.5 md:p-2 rounded-md transition-all duration-200 text-[#1A1919] hover:text-white hover:bg-[#9CEE69] bg-white shadow-sm border border-gray-200"
                    title={`Previous categories`}
                  >
                    <ChevronLeft size={14} className="md:w-4 md:h-4" />
                  </button>
                )}

                {/* Tabs Container */}
                <div className="flex-1 bg-gray-100/50 p-1 rounded-lg min-w-0" ref={categoryDropdownRef}>
                  <div className="flex gap-1 w-full overflow-hidden">
                    {visibleCategories.map((category) => {
                  const isActive = selectedCategory === category;
                  const isDropdownOpen = showCategoryDropdown === category;

                  return (
                    <div key={category} className="flex-1">
                      <button
                        onClick={(e) => {
                          if (isActive) {
                            // If already active, toggle dropdown
                            handleCategoryDropdownToggle(category, e);
                          } else {
                            // If not active, switch category
                            handleCategoryClick(category);
                          }
                        }}
                        className={`w-full flex items-center justify-center gap-1 px-1 md:px-2 py-1.5 md:py-2 rounded-md transition-all duration-200 min-w-0 overflow-hidden cursor-pointer ${
                          isActive
                            ? 'bg-[#9CEE69] shadow-sm text-[#1A1919] border border-[#9CEE69]'
                            : 'text-[#1A1919] hover:text-white hover:bg-[#9CEE69]/80'
                        }`}
                        title={category.replace(/_/g, ' ')}
                      >
                        <BarChart3 size={12} className="flex-shrink-0" />
                        <span className="font-medium text-xs truncate min-w-0">
                          {category.replace(/_/g, ' ').toUpperCase()}
                        </span>
                      </button>
                    </div>
                  );
                })}
              </div>
            </div>

                {/* Next Button - Only show when there are hidden categories on the right */}
                {needsNavigation && canGoNext && (
                  <button
                    onClick={handleNextTabs}
                    className="flex-shrink-0 p-1.5 md:p-2 rounded-md transition-all duration-200 text-[#1A1919] hover:text-white hover:bg-[#9CEE69] bg-white shadow-sm border border-gray-200"
                    title={`Next categories`}
                  >
                    <ChevronRight size={14} className="md:w-4 md:h-4" />
                  </button>
                )}
              </>
            )}
          </div>

          {/* Category Info Dropdown - Clean & Minimal */}
          {showCategoryDropdown && (
            <div className={`w-full mb-3 bg-gray-50/80 border border-gray-200/60 rounded-lg p-3 ${
              isDropdownAnimating ? 'animate-slide-up' : 'animate-slide-down'
            }`}>
              {(() => {
                const stats = getCategoryStats(showCategoryDropdown);
                return (
                  <div className="space-y-3">
                    {/* Header with Data Verified Badge */}
                    <div className="flex items-center justify-between">
                      <div className="text-xs font-medium text-gray-600 uppercase tracking-wider">
                        {showCategoryDropdown.replace(/_/g, ' ')} Overview
                      </div>
                      <div className="flex items-center space-x-1 px-2 py-0.5 bg-[#9CEE69]/10 border border-[#9CEE69]/30 rounded-md">
                        <CheckCircle size={10} className="text-[#608F44]" />
                        <span className="text-xs text-[#1A1919] font-medium">Verified</span>
                      </div>
                    </div>

                    {/* Main Stats */}
                    <div className="grid grid-cols-4 gap-2">
                      <div className="text-center">
                        <div className="text-lg font-bold text-[#1A1919]">{stats.totalItems}</div>
                        <div className="text-xs text-gray-500">Total</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">{stats.upItems}</div>
                        <div className="text-xs text-gray-500">Up</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-red-600">{stats.downItems}</div>
                        <div className="text-xs text-gray-500">Down</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-600">{stats.sameItems}</div>
                        <div className="text-xs text-gray-500">Same</div>
                      </div>
                    </div>

                    {/* Authors */}
                    <div className="flex justify-between items-center pt-2 border-t border-gray-200/60">
                      <span className="text-xs text-gray-600">Authors</span>
                      <span className="text-sm font-semibold text-[#1A1919]">{stats.uniqueAuthors}</span>
                    </div>
                  </div>
                );
              })()}
            </div>
          )}

          {/* Data Table */}
          <div className="mt-1 md:mt-2">
            {selectedCategory ? (
              <>


                {loading ? (
                  <div className="flex items-center justify-center py-12 flex-1">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#9CEE69]"></div>
                    <span className="ml-3 text-[#1A1919]">Loading data...</span>
                  </div>
                ) : errorState.hasError ? (
                  <div className="flex flex-col items-center justify-center py-12 flex-1">
                    <div className="text-center max-w-md">
                      {errorState.errorType === 'connection' ? (
                        <div className="text-red-500 mb-4">
                          <XCircle size={48} className="mx-auto mb-3" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">{errorState.title}</h3>
                          <p className="text-sm text-gray-600 mb-4">{errorState.message}</p>
                          {errorState.suggestion && (
                            <p className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                              💡 {errorState.suggestion}
                            </p>
                          )}
                        </div>
                      ) : (
                        <div className="text-yellow-500 mb-4">
                          <Info size={48} className="mx-auto mb-3" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">{errorState.title}</h3>
                          <p className="text-sm text-gray-600 mb-4">{errorState.message}</p>
                          {errorState.suggestion && (
                            <p className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                              💡 {errorState.suggestion}
                            </p>
                          )}
                        </div>
                      )}
                      <button
                        onClick={() => loadCategories()}
                        className="mt-4 px-4 py-2 bg-[#9CEE69] text-[#1A1919] rounded-lg hover:bg-[#9CEE69]/80 transition-all duration-200"
                      >
                        Coba Lagi
                      </button>
                    </div>
                  </div>
                ) : categoryData.length > 0 ? (
                  <div className="bg-white/40 backdrop-blur-sm rounded-xl border border-white/30 overflow-hidden shadow-lg">
                    <DataTable data={getPaginatedData(categoryData)} columns={columns} tableLayout="fixed" />

                    {/* Pagination Controls */}
                    <PaginationControls
                      currentPage={currentPage}
                      totalItems={categoryData.length}
                      itemsPerPage={itemsPerPage}
                      onNextPage={handleNextPage}
                      onPrevPage={handlePrevPage}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-12 text-gray-500 flex-1">
                    <div className="text-center max-w-md">
                      <BarChart3 size={48} className="mx-auto mb-4 text-gray-300" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak Ada Data</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Tidak ada data yang ditemukan untuk kategori {selectedCategory.replace(/_/g, ' ')}.
                      </p>
                      {errorState.hasError ? (
                        <p className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                          💡 {errorState.errorType === 'connection'
                            ? 'Pastikan backend server berjalan dan coba lagi'
                            : 'Periksa koneksi atau coba kategori lain'}
                        </p>
                      ) : (
                        <p className="text-xs text-gray-500">
                          Data mungkin belum tersedia atau sedang diproses.
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </>
            ) : !isInitialized ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#9CEE69]"></div>
                <span className="ml-3 text-[#1A1919]">Initializing...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center py-12 text-gray-500">
                <div className="text-center">
                  <BarChart3 size={48} className="mx-auto mb-4 text-gray-300" />
                  <p>Select a category above to view its data</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Top Authors Card - Right Side (1/4 width) - Responsive visibility */}
        {(screenWidth < 1024 || screenWidth > 1600) && (
          <div className="lg:col-span-1 bg-white border border-gray-200 rounded-lg p-2 md:p-3 lg:p-4 shadow-sm">
            <div className="flex items-center justify-between mb-1 md:mb-2">
            <div className="flex items-center space-x-2 md:space-x-3">
              <div className="p-1.5 md:p-2 bg-[#9CEE69] rounded-lg">
                <Crown className="h-4 w-4 md:h-5 md:w-5 text-[#1A1919]" />
              </div>
              <div>
                <h2 className="text-base md:text-lg font-semibold text-[#1A1919]">Top Authors</h2>
                <p className="text-xs text-gray-600">Active creators</p>
              </div>
            </div>
          </div>



          {/* Authors Table - Excel Style */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-6 w-6 md:h-8 md:w-8 border-b-2 border-[#9CEE69]"></div>
              <span className="ml-3 text-[#1A1919] text-sm md:text-base">Loading authors...</span>
            </div>
          ) : topAuthors.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <span className="text-gray-500 text-sm">No authors data available</span>
            </div>
          ) : (
            <div className="bg-white rounded-xl border border-gray-200 overflow-hidden shadow-lg">
              <div>
                {/* Table Header */}
                <div className="bg-gray-50/50 border-b border-gray-200 grid grid-cols-12 text-xs font-semibold text-gray-700">
                  <div className="col-span-2 px-1 py-1 border-r border-gray-200 text-center">#</div>
                  <div className="col-span-6 px-1 py-1 border-r border-gray-200">Author</div>
                  <div className="col-span-4 px-1 py-1 text-center">Points</div>
                </div>

                {/* Table Body */}
                <div className="divide-y divide-gray-200">
                  {topAuthors.slice(0, 10).map((author, index) => (
                    <div key={author.author}>
                      {/* Main Author Row */}
                      <div
                        className="grid grid-cols-12 hover:bg-[#9CEE69]/20 transition-all duration-200 text-xs cursor-pointer"
                        onClick={() => handleAuthorClick(author.author)}
                      >
                        {/* Rank Column */}
                        <div className="col-span-2 px-1 py-1 border-r border-gray-200 text-center flex items-center justify-center">
                          {index < 3 ? (
                            <Crown
                              size={10}
                              className={`${
                                index === 0 ? 'text-yellow-500' :
                                index === 1 ? 'text-gray-400' :
                                'text-orange-400'
                              }`}
                            />
                          ) : (
                            <span className="font-medium text-gray-600 text-xs">{index + 1}</span>
                          )}
                        </div>

                        {/* Author Column */}
                        <div className="col-span-6 px-1 py-1 border-r border-gray-200 flex items-center justify-between">
                          <span className="font-medium text-[#1A1919] truncate text-xs">
                            {author.author}
                          </span>
                          <div className="flex items-center ml-1">
                            {expandedAuthor === author.author ? (
                              <ChevronUp size={8} className="text-gray-400" />
                            ) : (
                              <ChevronDown size={8} className="text-gray-400" />
                            )}
                          </div>
                        </div>

                        {/* Points Column */}
                        <div className="col-span-4 px-1 py-1 text-center flex items-center justify-center">
                          <span className="font-bold text-[#9CEE69] text-xs">
                            {author.points}
                          </span>
                        </div>
                      </div>

                      {/* Dropdown Details */}
                      {expandedAuthor === author.author && renderAuthorDetails(author.author)}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
        )}
      </div>

      {/* Add Category Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => {
          setShowAddModal(false);
          setNewCategoryName('');
          setAddCategoryError('');
        }}
        title="Add New Category"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Name
            </label>
            <input
              type="text"
              value={newCategoryName}
              onChange={(e) => {
                setNewCategoryName(e.target.value);
                setAddCategoryError(''); // Clear error on input change
              }}
              placeholder="Enter category name"
              className={`w-full px-3 py-2 bg-white border rounded-lg focus:outline-none focus:ring-2 ${
                addCategoryError
                  ? 'border-red-300 focus:ring-red-500'
                  : 'border-gray-300 focus:ring-[#9CEE69]'
              }`}
            />
            {addCategoryError && (
              <p className="mt-1 text-sm text-red-600">{addCategoryError}</p>
            )}
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => {
                setShowAddModal(false);
                setNewCategoryName('');
                setAddCategoryError('');
              }}
              className="px-4 py-2 text-theme-eerie-black hover:text-theme-jet border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleAddCategory}
              className="px-4 py-2 bg-[#9CEE69] text-[#1A1919] rounded-lg hover:bg-[#9CEE69]/80 transition-all duration-200"
            >
              Add Category
            </button>
          </div>
        </div>
      </Modal>

      {/* Settings Modal */}
      <Modal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        title="Category Settings"
      >
        <div className="space-y-6">
          {/* Header with Add Button */}
          <div className="flex items-center justify-between pb-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Manage Categories</h3>
            <button
              onClick={() => {
                setShowSettingsModal(false);
                setShowAddModal(true);
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-[#9CEE69] text-[#1A1919] rounded-md hover:bg-[#9CEE69]/80 transition-colors duration-200"
            >
              <Plus size={16} />
              <span>Add New Category</span>
            </button>
          </div>

          {/* Categories List */}
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {categories.length > 0 ? (
              categories.map((category, index) => {
                return (
                  <div
                    key={category}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#9CEE69] rounded-full flex items-center justify-center text-[#1A1919] text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {category.replace(/_/g, ' ').toUpperCase()}
                        </p>
                        <p className="text-sm text-gray-500">
                          {category}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditCategory(category)}
                        className="flex items-center space-x-1 px-3 py-1.5 text-sm text-theme-eerie-black hover:text-white hover:bg-theme-nav-green rounded-md transition-colors duration-200 border border-theme-nav-green/30"
                        title="Edit"
                      >
                        <Edit2 size={14} />
                        <span>Edit</span>
                      </button>
                      <button
                        onClick={() => handleConfirmDelete(category)}
                        className="flex items-center space-x-1 px-3 py-1.5 text-sm text-red-600 hover:text-white hover:bg-red-500 rounded-md transition-colors duration-200 border border-red-300"
                        title="Delete"
                      >
                        <Trash2 size={14} />
                        <span>Delete</span>
                      </button>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-8 text-gray-500">
                <BarChart3 size={48} className="mx-auto mb-4 text-gray-300" />
                <p>No categories added yet</p>
                <p className="text-sm mt-1">Click "Add New Category" to get started</p>
              </div>
            )}
          </div>
        </div>
      </Modal>

      {/* Edit Category Modal */}
      <Modal
        isOpen={editingCategory !== null}
        onClose={() => {
          setEditingCategory(null);
          setEditCategoryName('');
          setEditCategoryError('');
        }}
        title="Edit Category"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Name
            </label>
            <input
              type="text"
              value={editCategoryName}
              onChange={(e) => {
                setEditCategoryName(e.target.value);
                setEditCategoryError(''); // Clear error on input change
              }}
              placeholder="Enter category name"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                editCategoryError
                  ? 'border-red-300 focus:ring-red-500'
                  : 'border-gray-300 focus:ring-[#9CEE69]'
              }`}
            />
            {editCategoryError && (
              <p className="mt-1 text-sm text-red-600">{editCategoryError}</p>
            )}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleSaveEdit}
              className="flex-1 px-4 py-2 bg-[#9CEE69] text-[#1A1919] rounded-md hover:bg-[#9CEE69]/80 transition-colors duration-200"
            >
              Save Changes
            </button>
            <button
              onClick={() => {
                setEditingCategory(null);
                setEditCategoryName('');
                setEditCategoryError('');
              }}
              className="flex-1 px-4 py-2 bg-theme-seashell text-theme-eerie-black border border-gray-300 rounded-md hover:bg-gray-100 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={() => {
          setShowDeleteConfirm(false);
          setCategoryToDelete(null);
        }}
        title="Delete Category"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200">
            <div className="flex-shrink-0">
              <Trash2 size={24} className="text-red-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-red-800">
                Are you sure you want to delete this category?
              </p>
              <p className="text-sm text-red-600 mt-1">
                <strong>{categoryToDelete?.replace(/_/g, ' ').toUpperCase()}</strong>
              </p>
              <p className="text-xs text-red-500 mt-2">
                This action cannot be undone. All data for this category will be removed.
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => {
                if (categoryToDelete) {
                  handleDeleteCategory(categoryToDelete);
                }
                setShowDeleteConfirm(false);
                setCategoryToDelete(null);
              }}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200"
            >
              Delete
            </button>
            <button
              onClick={() => {
                setShowDeleteConfirm(false);
                setCategoryToDelete(null);
              }}
              className="flex-1 px-4 py-2 bg-theme-seashell text-theme-eerie-black border border-gray-300 rounded-md hover:bg-gray-100 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>

      {/* Floating Top Authors Button - Responsive visibility */}
      <FloatingTopAuthorsButton
        onClick={() => setShowTopAuthorsModal(true)}
        isVisible={screenWidth >= 1024 && screenWidth <= 1600}
      />

      {/* Top Authors Modal */}
      <TopAuthorsModal
        isOpen={showTopAuthorsModal}
        onClose={() => setShowTopAuthorsModal(false)}
        topAuthors={topAuthors.map(author => ({
          author: author.author,
          count: author.count || 0,
          points: author.points || 0,
          rank: author.rank || 0
        }))}
        onAuthorClick={handleAuthorClick}
        expandedAuthor={expandedAuthor}
        authorDetails={authorDetails}
        loadingAuthorDetails={loadingAuthorDetails}
        renderAuthorDetails={renderAuthorDetails}
      />
    </div>
  );
};

export default Categories;