import React, { useState } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { 
  BarChart3, 
  Database, 
  Settings, 
  Users, 
  TrendingUp, 
  Zap,
  Monitor,
  HardDrive,
  Activity,
  FileText,
  CheckSquare,
  GitBranch,
  ChevronDown,
  ChevronRight,
  Menu,
  X,
  Home,
  User,
  LogOut
} from 'lucide-react';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  path?: string;
  children?: SidebarItem[];
}

interface BackendLayoutProps {
  children?: React.ReactNode;
}

const BackendLayout: React.FC<BackendLayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>(['data-mgmt', 'system']);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  // Sidebar navigation structure
  const sidebarItems: SidebarItem[] = [
    {
      id: 'overview',
      label: 'Overview',
      icon: BarChart3,
      path: '/dashboard'
    },
    {
      id: 'data-mgmt',
      label: 'Data Management',
      icon: Database,
      children: [
        {
          id: 'files',
          label: 'Files',
          icon: FileText,
          path: '/dashboard/data/files'
        },
        {
          id: 'quality',
          label: 'Quality',
          icon: CheckSquare,
          path: '/dashboard/data/quality'
        },
        {
          id: 'pipeline',
          label: 'Pipeline',
          icon: GitBranch,
          path: '/dashboard/data/pipeline'
        }
      ]
    },
    {
      id: 'system',
      label: 'System',
      icon: Settings,
      children: [
        {
          id: 'monitor',
          label: 'Monitor',
          icon: Monitor,
          path: '/dashboard/system/monitor'
        },
        {
          id: 'cache',
          label: 'Cache',
          icon: HardDrive,
          path: '/dashboard/system/cache'
        },
        {
          id: 'health',
          label: 'Health',
          icon: Activity,
          path: '/dashboard/system/health'
        }
      ]
    },
    {
      id: 'users',
      label: 'Users',
      icon: Users,
      path: '/dashboard/users'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: TrendingUp,
      path: '/dashboard/analytics'
    },
    {
      id: 'automation',
      label: 'Automation',
      icon: Zap,
      path: '/dashboard/automation'
    },
    {
      id: 'config',
      label: 'Configuration',
      icon: Settings,
      path: '/dashboard/config'
    }
  ];

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const isParentActive = (item: SidebarItem) => {
    if (item.path && isActive(item.path)) return true;
    if (item.children) {
      return item.children.some(child => child.path && isActive(child.path));
    }
    return false;
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    setMobileMenuOpen(false);
  };

  const generateBreadcrumb = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [];
    
    // Always start with Dashboard
    breadcrumbs.push({ label: 'Dashboard', path: '/dashboard' });
    
    // Generate breadcrumb based on current path
    if (pathSegments.length > 1) {
      const section = pathSegments[1];
      const subsection = pathSegments[2];
      
      // Find the main section
      const mainItem = sidebarItems.find(item => 
        item.path?.includes(section) || 
        item.children?.some(child => child.path?.includes(section))
      );
      
      if (mainItem) {
        if (mainItem.children && subsection) {
          // Parent section
          breadcrumbs.push({ 
            label: mainItem.label, 
            path: `/dashboard/${section}` 
          });
          
          // Child section
          const childItem = mainItem.children.find(child => 
            child.path?.includes(subsection)
          );
          if (childItem) {
            breadcrumbs.push({ 
              label: childItem.label, 
              path: childItem.path 
            });
          }
        } else {
          // Direct section
          breadcrumbs.push({ 
            label: mainItem.label, 
            path: mainItem.path || `/dashboard/${section}` 
          });
        }
      }
    }
    
    return breadcrumbs;
  };

  const renderSidebarItem = (item: SidebarItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = isParentActive(item);
    const directActive = item.path && isActive(item.path);

    return (
      <div key={item.id} className="mb-1">
        <button
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.id);
            } else if (item.path) {
              handleNavigation(item.path);
            }
          }}
          className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
            directActive
              ? 'bg-primary-100 text-primary-800 border border-primary-200'
              : active
              ? 'bg-primary-50 text-primary-700'
              : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
          } ${level > 0 ? 'ml-4' : ''}`}
        >
          <div className="flex items-center">
            <item.icon size={18} className="mr-3 flex-shrink-0" />
            {!sidebarCollapsed && (
              <span className="truncate">{item.label}</span>
            )}
          </div>
          {!sidebarCollapsed && hasChildren && (
            <div className="flex-shrink-0">
              {isExpanded ? (
                <ChevronDown size={16} />
              ) : (
                <ChevronRight size={16} />
              )}
            </div>
          )}
        </button>
        
        {!sidebarCollapsed && hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map(child => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Menu Overlay */}
      {mobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside className={`fixed top-0 left-0 z-50 h-full bg-white border-r border-gray-200 transition-all duration-300 ${
        sidebarCollapsed ? 'w-16' : 'w-64'
      } ${mobileMenuOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`}>
        
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          {!sidebarCollapsed && (
            <div className="flex items-center">
              <BarChart3 size={24} className="text-primary-600 mr-2" />
              <h1 className="text-lg font-semibold text-gray-900">Backend Dashboard</h1>
            </div>
          )}
          <button
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="p-1 rounded-lg hover:bg-gray-100 transition-colors hidden lg:block"
          >
            <Menu size={18} className="text-gray-600" />
          </button>
          <button
            onClick={() => setMobileMenuOpen(false)}
            className="p-1 rounded-lg hover:bg-gray-100 transition-colors lg:hidden"
          >
            <X size={18} className="text-gray-600" />
          </button>
        </div>

        {/* Sidebar Navigation */}
        <nav className="p-4 space-y-2 flex-1 overflow-y-auto">
          {sidebarItems.map(item => renderSidebarItem(item))}
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <User size={16} className="text-primary-600" />
            </div>
            {!sidebarCollapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">Admin User</p>
                <p className="text-xs text-gray-500 truncate"><EMAIL></p>
              </div>
            )}
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <div className={`transition-all duration-300 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}`}>
        
        {/* Top Header */}
        <header className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            
            {/* Mobile Menu Button & Breadcrumb */}
            <div className="flex items-center">
              <button
                onClick={() => setMobileMenuOpen(true)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors lg:hidden mr-3"
              >
                <Menu size={20} className="text-gray-600" />
              </button>
              
              {/* Breadcrumb */}
              <nav className="flex items-center space-x-2 text-sm">
                {generateBreadcrumb().map((crumb, index, array) => (
                  <div key={crumb.path} className="flex items-center">
                    <button
                      onClick={() => handleNavigation(crumb.path)}
                      className={`hover:text-primary-600 transition-colors ${
                        index === array.length - 1 
                          ? 'text-gray-900 font-medium' 
                          : 'text-gray-500'
                      }`}
                    >
                      {crumb.label}
                    </button>
                    {index < array.length - 1 && (
                      <ChevronRight size={16} className="mx-2 text-gray-400" />
                    )}
                  </div>
                ))}
              </nav>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-3">
              <button
                onClick={() => navigate('/analysis')}
                className="flex items-center space-x-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Home size={16} />
                <span className="hidden sm:inline">Frontend</span>
              </button>
              
              <button className="flex items-center space-x-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <LogOut size={16} />
                <span className="hidden sm:inline">Logout</span>
              </button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="p-6">
          {children || <Outlet />}
        </main>
      </div>
    </div>
  );
};

export default BackendLayout;
