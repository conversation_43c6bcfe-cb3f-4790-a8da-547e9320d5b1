import React from 'react';
import { 
  BarChart3, 
  Database, 
  Users, 
  Activity,
  TrendingUp,
  TrendingDown,
  Server,
  HardDrive,
  Cpu,
  Zap,
  CheckCircle,
  AlertTriangle,
  Clock,
  FileText
} from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  icon: React.ComponentType<{ size?: number; className?: string }>;
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red';
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, change, icon: Icon, color }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600 border-blue-200',
    green: 'bg-green-50 text-green-600 border-green-200',
    purple: 'bg-purple-50 text-purple-600 border-purple-200',
    orange: 'bg-orange-50 text-orange-600 border-orange-200',
    red: 'bg-red-50 text-red-600 border-red-200'
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          <Icon size={24} />
        </div>
        {change && (
          <div className={`flex items-center text-sm ${
            change.type === 'increase' ? 'text-green-600' : 'text-red-600'
          }`}>
            {change.type === 'increase' ? (
              <TrendingUp size={16} className="mr-1" />
            ) : (
              <TrendingDown size={16} className="mr-1" />
            )}
            <span>{Math.abs(change.value)}%</span>
          </div>
        )}
      </div>
      
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-1">{value}</h3>
        <p className="text-sm text-gray-600">{title}</p>
        {change && (
          <p className="text-xs text-gray-500 mt-1">vs {change.period}</p>
        )}
      </div>
    </div>
  );
};

interface StatusItemProps {
  label: string;
  status: 'healthy' | 'warning' | 'error';
  value?: string;
  description?: string;
}

const StatusItem: React.FC<StatusItemProps> = ({ label, status, value, description }) => {
  const statusConfig = {
    healthy: { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-50' },
    warning: { icon: AlertTriangle, color: 'text-yellow-600', bg: 'bg-yellow-50' },
    error: { icon: AlertTriangle, color: 'text-red-600', bg: 'bg-red-50' }
  };

  const config = statusConfig[status];
  const StatusIcon = config.icon;

  return (
    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
      <div className="flex items-center">
        <div className={`p-2 rounded-full ${config.bg} mr-3`}>
          <StatusIcon size={16} className={config.color} />
        </div>
        <div>
          <h4 className="font-medium text-gray-900">{label}</h4>
          {description && (
            <p className="text-sm text-gray-600">{description}</p>
          )}
        </div>
      </div>
      {value && (
        <span className="text-sm font-medium text-gray-700">{value}</span>
      )}
    </div>
  );
};

const BackendOverview: React.FC = () => {
  // Mock data - in real implementation, this would come from APIs
  const metrics = [
    {
      title: 'Total Files',
      value: '1,247',
      change: { value: 12.5, type: 'increase' as const, period: 'last week' },
      icon: FileText,
      color: 'blue' as const
    },
    {
      title: 'Active Users',
      value: '127',
      change: { value: 8.3, type: 'increase' as const, period: 'last month' },
      icon: Users,
      color: 'green' as const
    },
    {
      title: 'System Load',
      value: '23%',
      change: { value: 5.2, type: 'decrease' as const, period: 'last hour' },
      icon: Cpu,
      color: 'purple' as const
    },
    {
      title: 'Cache Hit Rate',
      value: '87.5%',
      change: { value: 2.1, type: 'increase' as const, period: 'last day' },
      icon: Zap,
      color: 'orange' as const
    }
  ];

  const systemStatus = [
    {
      label: 'Database',
      status: 'healthy' as const,
      value: 'Online',
      description: 'All connections stable'
    },
    {
      label: 'Cache Server',
      status: 'healthy' as const,
      value: 'Online',
      description: 'Redis running normally'
    },
    {
      label: 'File Storage',
      status: 'warning' as const,
      value: '78% Full',
      description: 'Consider cleanup soon'
    },
    {
      label: 'Background Jobs',
      status: 'healthy' as const,
      value: '4 Running',
      description: 'All tasks processing'
    }
  ];

  const recentActivity = [
    {
      time: '2 minutes ago',
      action: 'File uploaded',
      details: 'categories_data.csv (2.3 MB)',
      user: '<EMAIL>'
    },
    {
      time: '15 minutes ago',
      action: 'Cache cleared',
      details: 'Manual cache invalidation',
      user: '<EMAIL>'
    },
    {
      time: '1 hour ago',
      action: 'Backup completed',
      details: 'Automated daily backup',
      user: 'system'
    },
    {
      time: '3 hours ago',
      action: 'User login',
      details: 'Admin panel access',
      user: '<EMAIL>'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Backend Overview</h1>
        <p className="text-gray-600">
          Monitor system health, performance metrics, and recent activity
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </div>

      {/* System Status & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* System Status */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <Server size={20} className="text-gray-700 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">System Status</h2>
          </div>
          
          <div className="space-y-3">
            {systemStatus.map((item, index) => (
              <StatusItem key={index} {...item} />
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <Activity size={20} className="text-gray-700 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
          </div>
          
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <span className="text-xs text-gray-500">{activity.time}</span>
                  </div>
                  <p className="text-sm text-gray-600">{activity.details}</p>
                  <p className="text-xs text-gray-500">by {activity.user}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Database size={20} className="text-primary-600 mr-3" />
            <span className="font-medium text-gray-900">Manage Files</span>
          </button>
          
          <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <BarChart3 size={20} className="text-primary-600 mr-3" />
            <span className="font-medium text-gray-900">View Analytics</span>
          </button>
          
          <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <HardDrive size={20} className="text-primary-600 mr-3" />
            <span className="font-medium text-gray-900">System Monitor</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default BackendOverview;
