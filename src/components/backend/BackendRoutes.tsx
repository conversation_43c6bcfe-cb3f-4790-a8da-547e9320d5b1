import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import BackendLayout from './BackendLayout';
import BackendOverview from './BackendOverview';
// Import existing components for integration
import MonitoringDashboard from '../monitoring/MonitoringDashboard';
import AnalyticsDashboard from '../analytics/AnalyticsDashboard';
import AutomationDashboard from '../automation/AutomationDashboard';
import FileManagement from '../FileManagement';

// Wrapper components for consistent backend styling and admin mode
const BackendPageWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="space-y-6">
    {children}
  </div>
);

// Integrated component pages
const BackendMonitoringPage: React.FC = () => (
  <BackendPageWrapper>
    <MonitoringDashboard className="backend-monitoring" />
  </BackendPageWrapper>
);

const BackendAnalyticsPage: React.FC = () => (
  <BackendPageWrapper>
    <AnalyticsDashboard className="backend-analytics" />
  </BackendPageWrapper>
);

const BackendAutomationPage: React.FC = () => (
  <BackendPageWrapper>
    <AutomationDashboard className="backend-automation" />
  </BackendPageWrapper>
);

const BackendFileManagementPage: React.FC = () => (
  <BackendPageWrapper>
    <FileManagement
      adminMode={true}
      userRole="admin"
      className="backend-file-management"
    />
  </BackendPageWrapper>
);

// Placeholder components for routes not yet implemented
const PlaceholderPage: React.FC<{ title: string; description: string }> = ({ title, description }) => (
  <div className="space-y-6">
    <div>
      <h1 className="text-2xl font-bold text-gray-900 mb-2">{title}</h1>
      <p className="text-gray-600">{description}</p>
    </div>

    <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <div className="w-8 h-8 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">Coming Soon</h3>
      <p className="text-gray-600 mb-4">
        This section will be implemented in future phases
      </p>
      <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-800 rounded-lg text-sm font-medium">
        Future Implementation
      </div>
    </div>
  </div>
);

// Data Management Pages
const DataFilesPage: React.FC = () => <BackendFileManagementPage />;

const DataQualityPage: React.FC = () => (
  <PlaceholderPage
    title="Data Quality"
    description="Monitor data quality, validation rules, and integrity checks"
  />
);

const DataPipelinePage: React.FC = () => (
  <PlaceholderPage
    title="Data Pipeline"
    description="Manage data processing workflows and automation pipelines"
  />
);

// System Management Pages
const SystemMonitorPage: React.FC = () => <BackendMonitoringPage />;

const SystemCachePage: React.FC = () => (
  <PlaceholderPage
    title="Cache Management"
    description="Monitor and manage Redis cache, performance optimization"
  />
);

const SystemHealthPage: React.FC = () => (
  <PlaceholderPage
    title="System Health"
    description="Overall system health status, alerts, and diagnostics"
  />
);

// Other Pages
const UsersPage: React.FC = () => (
  <PlaceholderPage
    title="User Management"
    description="Manage user accounts, permissions, and access control"
  />
);

const AnalyticsPage: React.FC = () => <BackendAnalyticsPage />;

const AutomationPage: React.FC = () => <BackendAutomationPage />;

const ConfigPage: React.FC = () => (
  <PlaceholderPage 
    title="Configuration" 
    description="System configuration, settings, and environment management"
  />
);

const BackendRoutes: React.FC = () => {
  return (
    <BackendLayout>
      <Routes>
        {/* Default route - redirect to overview */}
        <Route index element={<BackendOverview />} />

        {/* Data Management Routes */}
        <Route path="data/files" element={<DataFilesPage />} />
        <Route path="data/quality" element={<DataQualityPage />} />
        <Route path="data/pipeline" element={<DataPipelinePage />} />

        {/* System Management Routes */}
        <Route path="system/monitor" element={<SystemMonitorPage />} />
        <Route path="system/cache" element={<SystemCachePage />} />
        <Route path="system/health" element={<SystemHealthPage />} />

        {/* Direct Routes */}
        <Route path="users" element={<UsersPage />} />
        <Route path="analytics" element={<AnalyticsPage />} />
        <Route path="automation" element={<AutomationPage />} />
        <Route path="config" element={<ConfigPage />} />

        {/* Catch-all redirect */}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </BackendLayout>
  );
};

export default BackendRoutes;
