/**
 * NotificationSettings Component
 * Advanced notification settings management
 */

import React, { useState } from 'react';
import {
  Bell,
  Mail,
  Monitor,
  Volume2,
  VolumeX,
  Clock,
  Shield,
  Smartphone,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Save,
  RotateCcw
} from 'lucide-react';
import { useNotifications } from '../contexts/NotificationContext';
import { NotificationSettings as NotificationSettingsType } from '../types/notification';

const NotificationSettings: React.FC = () => {
  const { settings, updateSettings, isLoading } = useNotifications();
  const [localSettings, setLocalSettings] = useState<NotificationSettingsType>(settings);
  const [hasChanges, setHasChanges] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleSettingChange = (key: keyof NotificationSettingsType, value: any) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };

  const handlePriorityChange = (priority: keyof NotificationSettingsType['priorities'], value: boolean) => {
    setLocalSettings(prev => ({
      ...prev,
      priorities: {
        ...prev.priorities,
        [priority]: value
      }
    }));
    setHasChanges(true);
  };

  const handleQuietHoursChange = (key: keyof NotificationSettingsType['quietHours'], value: any) => {
    setLocalSettings(prev => ({
      ...prev,
      quietHours: {
        ...prev.quietHours,
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      await updateSettings(localSettings);
      setHasChanges(false);
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setLocalSettings(settings);
    setHasChanges(false);
  };

  const ToggleSwitch: React.FC<{ 
    checked: boolean; 
    onChange: (checked: boolean) => void; 
    disabled?: boolean;
  }> = ({ checked, onChange, disabled = false }) => (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        className="sr-only peer"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        disabled={disabled}
      />
      <div className={`
        relative w-11 h-6 rounded-full peer transition-colors duration-200
        ${disabled 
          ? 'bg-gray-200 cursor-not-allowed' 
          : checked 
            ? 'bg-orange-jasper peer-focus:ring-4 peer-focus:ring-orange-jasper/20' 
            : 'bg-gray-200 peer-focus:ring-4 peer-focus:ring-gray-300/20'
        }
      `}>
        <div className={`
          absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-5 w-5 
          transition-transform duration-200 peer-checked:translate-x-full peer-checked:border-white
          ${disabled ? 'opacity-50' : ''}
        `}></div>
      </div>
    </label>
  );

  return (
    <div className="space-y-6">
      {/* General Settings */}
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Bell size={20} className="mr-2 text-orange-jasper" />
          General Notification Settings
        </h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-800">Enable Notifications</h4>
              <p className="text-sm text-gray-600">Turn all notifications on or off</p>
            </div>
            <ToggleSwitch
              checked={localSettings.enabled}
              onChange={(checked) => handleSettingChange('enabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div className="flex items-center">
              <Mail size={16} className="mr-2 text-blue-500" />
              <div>
                <h4 className="font-medium text-gray-800">Email Notifications</h4>
                <p className="text-sm text-gray-600">Receive notifications via email</p>
              </div>
            </div>
            <ToggleSwitch
              checked={localSettings.email}
              onChange={(checked) => handleSettingChange('email', checked)}
              disabled={!localSettings.enabled}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div className="flex items-center">
              <Monitor size={16} className="mr-2 text-green-500" />
              <div>
                <h4 className="font-medium text-gray-800">Browser Notifications</h4>
                <p className="text-sm text-gray-600">Show notifications in browser</p>
              </div>
            </div>
            <ToggleSwitch
              checked={localSettings.browser}
              onChange={(checked) => handleSettingChange('browser', checked)}
              disabled={!localSettings.enabled}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div className="flex items-center">
              <Volume2 size={16} className="mr-2 text-purple-500" />
              <div>
                <h4 className="font-medium text-gray-800">Sound Notifications</h4>
                <p className="text-sm text-gray-600">Play sound for notifications</p>
              </div>
            </div>
            <ToggleSwitch
              checked={localSettings.sound}
              onChange={(checked) => handleSettingChange('sound', checked)}
              disabled={!localSettings.enabled}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div className="flex items-center">
              <Smartphone size={16} className="mr-2 text-indigo-500" />
              <div>
                <h4 className="font-medium text-gray-800">Desktop Notifications</h4>
                <p className="text-sm text-gray-600">Show system desktop notifications</p>
              </div>
            </div>
            <ToggleSwitch
              checked={localSettings.desktop}
              onChange={(checked) => handleSettingChange('desktop', checked)}
              disabled={!localSettings.enabled}
            />
          </div>
        </div>
      </div>

      {/* Notification Types */}
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Shield size={20} className="mr-2 text-orange-jasper" />
          Notification Types
        </h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-800">Data Updates</h4>
              <p className="text-sm text-gray-600">Notifications for data processing and updates</p>
            </div>
            <ToggleSwitch
              checked={localSettings.dataUpdates}
              onChange={(checked) => handleSettingChange('dataUpdates', checked)}
              disabled={!localSettings.enabled}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-800">System Alerts</h4>
              <p className="text-sm text-gray-600">System maintenance and status notifications</p>
            </div>
            <ToggleSwitch
              checked={localSettings.systemAlerts}
              onChange={(checked) => handleSettingChange('systemAlerts', checked)}
              disabled={!localSettings.enabled}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-800">Analysis Complete</h4>
              <p className="text-sm text-gray-600">Notifications when analysis tasks finish</p>
            </div>
            <ToggleSwitch
              checked={localSettings.analysisComplete}
              onChange={(checked) => handleSettingChange('analysisComplete', checked)}
              disabled={!localSettings.enabled}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-800">File Operations</h4>
              <p className="text-sm text-gray-600">File upload, download, and processing notifications</p>
            </div>
            <ToggleSwitch
              checked={localSettings.fileOperations}
              onChange={(checked) => handleSettingChange('fileOperations', checked)}
              disabled={!localSettings.enabled}
            />
          </div>
        </div>
      </div>

      {/* Priority Settings */}
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <AlertTriangle size={20} className="mr-2 text-orange-jasper" />
          Priority Levels
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-gray-400 rounded-full mr-3"></div>
              <div>
                <h4 className="font-medium text-gray-800">Low Priority</h4>
                <p className="text-sm text-gray-600">General information</p>
              </div>
            </div>
            <ToggleSwitch
              checked={localSettings.priorities.low}
              onChange={(checked) => handlePriorityChange('low', checked)}
              disabled={!localSettings.enabled}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
              <div>
                <h4 className="font-medium text-gray-800">Medium Priority</h4>
                <p className="text-sm text-gray-600">Standard notifications</p>
              </div>
            </div>
            <ToggleSwitch
              checked={localSettings.priorities.medium}
              onChange={(checked) => handlePriorityChange('medium', checked)}
              disabled={!localSettings.enabled}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
              <div>
                <h4 className="font-medium text-gray-800">High Priority</h4>
                <p className="text-sm text-gray-600">Important notifications</p>
              </div>
            </div>
            <ToggleSwitch
              checked={localSettings.priorities.high}
              onChange={(checked) => handlePriorityChange('high', checked)}
              disabled={!localSettings.enabled}
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
              <div>
                <h4 className="font-medium text-gray-800">Urgent Priority</h4>
                <p className="text-sm text-gray-600">Critical alerts</p>
              </div>
            </div>
            <ToggleSwitch
              checked={localSettings.priorities.urgent}
              onChange={(checked) => handlePriorityChange('urgent', checked)}
              disabled={!localSettings.enabled}
            />
          </div>
        </div>
      </div>

      {/* Quiet Hours */}
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Clock size={20} className="mr-2 text-orange-jasper" />
          Quiet Hours
        </h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-white/30 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-800">Enable Quiet Hours</h4>
              <p className="text-sm text-gray-600">Suppress notifications during specified hours</p>
            </div>
            <ToggleSwitch
              checked={localSettings.quietHours.enabled}
              onChange={(checked) => handleQuietHoursChange('enabled', checked)}
              disabled={!localSettings.enabled}
            />
          </div>

          {localSettings.quietHours.enabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-white/30 rounded-lg">
                <label className="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                <input
                  type="time"
                  value={localSettings.quietHours.start}
                  onChange={(e) => handleQuietHoursChange('start', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-jasper/30 focus:border-orange-jasper"
                />
              </div>

              <div className="p-4 bg-white/30 rounded-lg">
                <label className="block text-sm font-medium text-gray-700 mb-2">End Time</label>
                <input
                  type="time"
                  value={localSettings.quietHours.end}
                  onChange={(e) => handleQuietHoursChange('end', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-jasper/30 focus:border-orange-jasper"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Save/Reset Actions */}
      {hasChanges && (
        <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center text-amber-600">
              <Info size={16} className="mr-2" />
              <span className="text-sm">You have unsaved changes</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={handleReset}
                disabled={saving}
                className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RotateCcw size={16} />
                <span>Reset</span>
              </button>
              
              <button
                onClick={handleSave}
                disabled={saving}
                className="flex items-center space-x-2 px-4 py-2 bg-orange-jasper text-white rounded-lg hover:bg-orange-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? (
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                ) : (
                  <Save size={16} />
                )}
                <span>{saving ? 'Saving...' : 'Save Changes'}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationSettings;
