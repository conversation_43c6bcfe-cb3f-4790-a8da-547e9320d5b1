import React, { useState, useEffect } from 'react';
import { 
  Activity, 
  Server, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock,
  Cpu,
  HardDrive,
  Database,
  RefreshCw,
  TrendingUp,
  AlertCircle
} from 'lucide-react';

// Types for monitoring data
interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime_seconds: number;
  components: {
    [key: string]: {
      status: 'healthy' | 'degraded' | 'unhealthy' | 'disabled';
      [key: string]: any;
    };
  };
  alerts: string[];
}

interface PerformanceMetrics {
  cache_performance?: {
    hit_rate: number;
    total_requests: number;
  };
  system_performance?: {
    cpu: { percent: number };
    memory: { percent: number };
    error?: string;
  };
  timestamp: string;
}

interface AlertsData {
  alerts: string[];
  status: string;
  timestamp: string;
}

// Utility functions
const formatUptime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (hours > 24) {
    const days = Math.floor(hours / 24);
    return `${days}d ${hours % 24}h`;
  }
  
  return `${hours}h ${minutes}m`;
};

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'healthy': return 'text-green-600';
    case 'degraded': return 'text-yellow-600';
    case 'unhealthy': return 'text-red-600';
    case 'disabled': return 'text-gray-500';
    default: return 'text-gray-600';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'healthy': return <CheckCircle size={16} className="text-green-600" />;
    case 'degraded': return <AlertTriangle size={16} className="text-yellow-600" />;
    case 'unhealthy': return <XCircle size={16} className="text-red-600" />;
    case 'disabled': return <XCircle size={16} className="text-gray-500" />;
    default: return <AlertCircle size={16} className="text-gray-600" />;
  }
};

// System Status Component
export const SystemStatusCard: React.FC<{ health: SystemHealth | null; loading: boolean; error: string | null }> = ({ 
  health, 
  loading, 
  error 
}) => {
  if (loading) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Server size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">System Status</h3>
          <RefreshCw size={16} className="ml-2 animate-spin text-gray-400" />
        </div>
        <div className="text-center py-8">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Server size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">System Status</h3>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircle size={16} className="text-red-600 mr-2" />
            <p className="text-sm text-red-800">Error: {error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!health) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Server size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">System Status</h3>
        </div>
        <p className="text-gray-600 text-sm">No data available</p>
      </div>
    );
  }

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center mb-4">
        <Server size={20} className="mr-2 text-primary-600" />
        <h3 className="text-lg font-semibold text-[#1A1919]">System Status</h3>
        {getStatusIcon(health.status)}
      </div>
      
      <div className="space-y-4">
        {/* Overall Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div>
            <h4 className="font-medium text-gray-800">Overall Status</h4>
            <p className={`text-sm font-semibold ${getStatusColor(health.status)}`}>
              {health.status.toUpperCase()}
            </p>
          </div>
          <div className="text-right">
            <p className="text-xs text-gray-600">Uptime</p>
            <p className="text-sm font-medium">{formatUptime(health.uptime_seconds)}</p>
          </div>
        </div>

        {/* Components Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {Object.entries(health.components).map(([component, info]) => (
            <div key={component} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                {getStatusIcon(info.status)}
                <span className="ml-2 text-sm font-medium capitalize">
                  {component.replace('_', ' ')}
                </span>
              </div>
              <span className={`text-xs font-medium ${getStatusColor(info.status)}`}>
                {info.status}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Performance Metrics Component
export const PerformanceMetricsCard: React.FC<{ 
  performance: PerformanceMetrics | null; 
  loading: boolean; 
  error: string | null 
}> = ({ performance, loading, error }) => {
  if (loading) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <TrendingUp size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Performance Metrics</h3>
          <RefreshCw size={16} className="ml-2 animate-spin text-gray-400" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <TrendingUp size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Performance Metrics</h3>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircle size={16} className="text-red-600 mr-2" />
            <p className="text-sm text-red-800">Error: {error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center mb-4">
        <TrendingUp size={20} className="mr-2 text-primary-600" />
        <h3 className="text-lg font-semibold text-[#1A1919]">Performance Metrics</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Cache Performance */}
        {performance?.cache_performance && (
          <>
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <Database size={16} className="text-blue-600 mr-2" />
                <h4 className="font-medium text-blue-800">Cache Hit Rate</h4>
              </div>
              <p className="text-2xl font-bold text-blue-600">
                {performance.cache_performance.hit_rate.toFixed(1)}%
              </p>
            </div>
            
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <Activity size={16} className="text-green-600 mr-2" />
                <h4 className="font-medium text-green-800">Total Requests</h4>
              </div>
              <p className="text-2xl font-bold text-green-600">
                {performance.cache_performance.total_requests.toLocaleString()}
              </p>
            </div>
          </>
        )}

        {/* System Performance */}
        {performance?.system_performance && !performance.system_performance.error && (
          <>
            <div className="bg-orange-50 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <Cpu size={16} className="text-orange-600 mr-2" />
                <h4 className="font-medium text-orange-800">CPU Usage</h4>
              </div>
              <p className="text-2xl font-bold text-orange-600">
                {performance.system_performance.cpu.percent.toFixed(1)}%
              </p>
            </div>
            
            <div className="bg-purple-50 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <HardDrive size={16} className="text-purple-600 mr-2" />
                <h4 className="font-medium text-purple-800">Memory Usage</h4>
              </div>
              <p className="text-2xl font-bold text-purple-600">
                {performance.system_performance.memory.percent.toFixed(1)}%
              </p>
            </div>
          </>
        )}
      </div>

      {(!performance?.cache_performance && !performance?.system_performance) && (
        <div className="text-center py-8">
          <p className="text-gray-600 text-sm">No performance data available</p>
        </div>
      )}
    </div>
  );
};

// Alerts Component
export const AlertsCard: React.FC<{
  alerts: AlertsData | null;
  loading: boolean;
  error: string | null
}> = ({ alerts, loading, error }) => {
  if (loading) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <AlertTriangle size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">System Alerts</h3>
          <RefreshCw size={16} className="ml-2 animate-spin text-gray-400" />
        </div>
        <div className="space-y-2">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <AlertTriangle size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">System Alerts</h3>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircle size={16} className="text-red-600 mr-2" />
            <p className="text-sm text-red-800">Error: {error}</p>
          </div>
        </div>
      </div>
    );
  }

  const hasAlerts = alerts?.alerts && alerts.alerts.length > 0;

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center mb-4">
        <AlertTriangle size={20} className="mr-2 text-primary-600" />
        <h3 className="text-lg font-semibold text-[#1A1919]">System Alerts</h3>
        {hasAlerts && (
          <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
            {alerts.alerts.length}
          </span>
        )}
      </div>

      {hasAlerts ? (
        <div className="space-y-2">
          {alerts.alerts.map((alert, index) => (
            <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-start">
                <AlertTriangle size={16} className="text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-red-800">{alert}</p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle size={16} className="text-green-600 mr-2" />
            <p className="text-sm text-green-800">No alerts - All systems operational</p>
          </div>
        </div>
      )}

      {alerts?.timestamp && (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            Last updated: {new Date(alerts.timestamp).toLocaleTimeString()}
          </p>
        </div>
      )}
    </div>
  );
};

// Configuration Status Component
export const ConfigurationCard: React.FC<{
  health: SystemHealth | null;
  loading: boolean;
  error: string | null
}> = ({ health, loading, error }) => {
  if (loading) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Server size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Configuration</h3>
          <RefreshCw size={16} className="ml-2 animate-spin text-gray-400" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center mb-4">
          <Server size={20} className="mr-2 text-primary-600" />
          <h3 className="text-lg font-semibold text-[#1A1919]">Configuration</h3>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircle size={16} className="text-red-600 mr-2" />
            <p className="text-sm text-red-800">Error: {error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
      <div className="flex items-center mb-4">
        <Server size={20} className="mr-2 text-primary-600" />
        <h3 className="text-lg font-semibold text-[#1A1919]">Configuration</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-50 p-3 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-1">Optimizations</h4>
          <p className={`text-sm font-medium ${
            health?.components?.redis?.status !== 'disabled' ? 'text-green-600' : 'text-gray-600'
          }`}>
            {health?.components?.redis?.status !== 'disabled' ? 'Enabled' : 'Disabled'}
          </p>
        </div>

        <div className="bg-gray-50 p-3 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-1">Caching</h4>
          <p className={`text-sm font-medium ${getStatusColor(health?.components?.redis?.status || 'unknown')}`}>
            {health?.components?.redis?.status || 'Unknown'}
          </p>
        </div>

        <div className="bg-gray-50 p-3 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-1">File Monitoring</h4>
          <p className={`text-sm font-medium ${getStatusColor(health?.components?.file_monitor?.status || 'unknown')}`}>
            {health?.components?.file_monitor?.status || 'Unknown'}
          </p>
        </div>
      </div>
    </div>
  );
};
