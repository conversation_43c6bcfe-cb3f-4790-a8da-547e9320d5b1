import React, { useState } from 'react';
import { 
  RefreshCw, 
  Settings as SettingsIcon, 
  Play, 
  Pause,
  Clock,
  Wifi,
  WifiOff
} from 'lucide-react';
import { 
  SystemStatusCard, 
  PerformanceMetricsCard, 
  AlertsCard, 
  ConfigurationCard 
} from './MonitoringComponents';
import { useMonitoringDashboard, useMonitoringConfig } from '../../hooks/useMonitoring';

interface MonitoringDashboardProps {
  className?: string;
}

const MonitoringDashboard: React.FC<MonitoringDashboardProps> = ({ className = '' }) => {
  const { config, updateConfig } = useMonitoringConfig();
  const { 
    health, 
    performance, 
    alerts, 
    refreshAll, 
    isLoading, 
    hasError, 
    lastUpdated 
  } = useMonitoringDashboard(config.autoRefresh);

  const [showSettings, setShowSettings] = useState(false);

  const handleRefresh = () => {
    refreshAll();
  };

  const toggleAutoRefresh = () => {
    updateConfig({ autoRefresh: !config.autoRefresh });
  };

  const formatLastUpdated = (date: Date | null): string => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    
    if (diffSeconds < 60) return `${diffSeconds}s ago`;
    if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}m ago`;
    return date.toLocaleTimeString();
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Controls */}
      <div className="bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-[#1A1919] mb-1">
              System Monitoring Dashboard
            </h2>
            <p className="text-sm text-gray-600">
              Real-time system health and performance monitoring
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Connection Status */}
            <div className="flex items-center space-x-2">
              {hasError ? (
                <WifiOff size={16} className="text-red-500" />
              ) : (
                <Wifi size={16} className="text-green-500" />
              )}
              <span className="text-xs text-gray-600">
                {hasError ? 'Disconnected' : 'Connected'}
              </span>
            </div>
            
            {/* Last Updated */}
            <div className="flex items-center space-x-2 text-xs text-gray-600">
              <Clock size={14} />
              <span>Updated: {formatLastUpdated(lastUpdated)}</span>
            </div>
            
            {/* Auto Refresh Toggle */}
            <button
              onClick={toggleAutoRefresh}
              className={`flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                config.autoRefresh
                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              {config.autoRefresh ? (
                <Play size={12} />
              ) : (
                <Pause size={12} />
              )}
              <span>{config.autoRefresh ? 'Auto' : 'Manual'}</span>
            </button>
            
            {/* Manual Refresh */}
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="flex items-center space-x-1 px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-xs font-medium hover:bg-primary-200 transition-colors disabled:opacity-50"
            >
              <RefreshCw size={12} className={isLoading ? 'animate-spin' : ''} />
              <span>Refresh</span>
            </button>
            
            {/* Settings */}
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center space-x-1 px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium hover:bg-gray-200 transition-colors"
            >
              <SettingsIcon size={12} />
              <span>Settings</span>
            </button>
          </div>
        </div>
        
        {/* Settings Panel */}
        {showSettings && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Refresh Interval
                </label>
                <select
                  value={config.refreshInterval}
                  onChange={(e) => updateConfig({ refreshInterval: Number(e.target.value) })}
                  className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:ring-1 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value={5000}>5 seconds</option>
                  <option value={15000}>15 seconds</option>
                  <option value={30000}>30 seconds</option>
                  <option value={60000}>1 minute</option>
                  <option value={300000}>5 minutes</option>
                </select>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableNotifications"
                  checked={config.enableNotifications}
                  onChange={(e) => updateConfig({ enableNotifications: e.target.checked })}
                  className="mr-2"
                />
                <label htmlFor="enableNotifications" className="text-xs text-gray-700">
                  Enable Notifications
                </label>
              </div>
              
              <div className="text-xs text-gray-600">
                <p>Status: {hasError ? 'Error' : 'Operational'}</p>
                <p>Components: {Object.keys(health.data?.components || {}).length}</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Monitoring Cards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Status */}
        <SystemStatusCard 
          health={health.data} 
          loading={health.loading} 
          error={health.error} 
        />
        
        {/* Performance Metrics */}
        <PerformanceMetricsCard 
          performance={performance.data} 
          loading={performance.loading} 
          error={performance.error} 
        />
        
        {/* System Alerts */}
        <AlertsCard 
          alerts={alerts.data} 
          loading={alerts.loading} 
          error={alerts.error} 
        />
        
        {/* Configuration */}
        <ConfigurationCard 
          health={health.data} 
          loading={health.loading} 
          error={health.error} 
        />
      </div>

      {/* Global Error State */}
      {hasError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-red-800">Connection Issues Detected</h3>
              <p className="text-xs text-red-600 mt-1">
                Some monitoring data may be unavailable. Check your backend connection.
              </p>
            </div>
            <button
              onClick={handleRefresh}
              className="px-3 py-1 bg-red-100 text-red-800 rounded text-xs font-medium hover:bg-red-200 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MonitoringDashboard;
