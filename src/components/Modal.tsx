import React from 'react';
import { X } from 'lucide-react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, footer, size = 'lg' }) => {
  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="w-full h-full flex items-center justify-center px-4 py-16 max-h-screen">
        {/* Backdrop */}
        <div
          className="fixed inset-0 transition-opacity bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Modal */}
        <div className={`relative bg-white rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all w-full ${sizeClasses[size]} max-h-[calc(100vh-8rem)] animate-scale-in flex flex-col border border-gray-100`}>
          <div className="bg-white px-6 py-5 border-b border-gray-100 flex-shrink-0">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold text-[#1A1919]">{title}</h3>
              <button
                onClick={onClose}
                className="flex items-center justify-center w-10 h-10 rounded-xl hover:bg-gray-100 transition-all duration-200 text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>
          </div>
          <div className="px-6 py-6 flex-1 overflow-hidden">
            {children}
          </div>

          {/* Footer */}
          {footer && (
            <div className="bg-gray-50/50 border-t border-gray-200 px-6 py-3 flex-shrink-0">
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Modal;