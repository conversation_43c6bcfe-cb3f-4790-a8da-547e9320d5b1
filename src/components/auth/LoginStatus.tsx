/**
 * Login Status Component
 * 
 * Displays current authentication status and user information
 */

import React, { useState, useEffect } from 'react';
import { 
  User, 
  LogOut, 
  Clock, 
  Shield, 
  AlertCircle, 
  CheckCircle,
  RefreshCw,
  Settings
} from 'lucide-react';
import { Button } from '../ui';
import { useAuth } from '../../contexts/AuthContext';
import { useExtension } from '../../hooks/useExtension';

interface LoginStatusProps {
  showDetails?: boolean;
  showExtensionStatus?: boolean;
  showSessionTimer?: boolean;
  className?: string;
  onLogout?: () => void;
}

const LoginStatus: React.FC<LoginStatusProps> = ({
  showDetails = true,
  showExtensionStatus = true,
  showSessionTimer = true,
  className = '',
  onLogout
}) => {
  // State
  const [timeUntilExpiry, setTimeUntilExpiry] = useState(0);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);

  // Hooks
  const { 
    authState, 
    logout, 
    logoutAll, 
    refreshSession, 
    getTimeUntilExpiry 
  } = useAuth();
  const { status: extensionStatus, isExtensionReady } = useExtension();

  // Update session timer
  useEffect(() => {
    if (authState.isAuthenticated && showSessionTimer) {
      const interval = setInterval(() => {
        const remaining = getTimeUntilExpiry();
        setTimeUntilExpiry(remaining);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [authState.isAuthenticated, showSessionTimer, getTimeUntilExpiry]);

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      onLogout?.();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setShowLogoutConfirm(false);
    }
  };

  // Handle logout all
  const handleLogoutAll = async () => {
    try {
      await logoutAll();
      onLogout?.();
    } catch (error) {
      console.error('Logout all error:', error);
    } finally {
      setShowLogoutConfirm(false);
    }
  };

  // Handle refresh session
  const handleRefreshSession = async () => {
    try {
      await refreshSession();
    } catch (error) {
      console.error('Refresh session error:', error);
    }
  };

  // Format time display
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  // Get session status
  const getSessionStatus = () => {
    if (timeUntilExpiry <= 0) {
      return { type: 'expired', color: 'red' };
    } else if (timeUntilExpiry < 3600) { // Less than 1 hour
      return { type: 'expiring', color: 'amber' };
    } else {
      return { type: 'active', color: 'green' };
    }
  };

  if (!authState.isAuthenticated) {
    return (
      <div className={`p-4 bg-gray-50 border border-gray-200 rounded-lg ${className}`}>
        <div className="flex items-center space-x-2">
          <AlertCircle size={16} className="text-gray-500" />
          <span className="text-sm text-gray-600">Not authenticated</span>
        </div>
      </div>
    );
  }

  const sessionStatus = getSessionStatus();

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
              <User size={20} className="text-primary-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">
                {authState.user?.email}
              </h3>
              <p className="text-sm text-gray-500">
                Authenticated
              </p>
            </div>
          </div>
          
          {/* Status Indicator */}
          <div className={`w-3 h-3 rounded-full ${
            sessionStatus.color === 'green' ? 'bg-green-500' :
            sessionStatus.color === 'amber' ? 'bg-amber-500' : 'bg-red-500'
          }`} />
        </div>
      </div>

      {/* Details */}
      {showDetails && (
        <div className="p-4 space-y-4">
          {/* Session Timer */}
          {showSessionTimer && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock size={16} className="text-gray-500" />
                <span className="text-sm text-gray-600">Session expires in:</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`text-sm font-mono ${
                  sessionStatus.color === 'green' ? 'text-green-600' :
                  sessionStatus.color === 'amber' ? 'text-amber-600' : 'text-red-600'
                }`}>
                  {formatTime(timeUntilExpiry)}
                </span>
                {sessionStatus.type === 'expiring' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRefreshSession}
                    leftIcon={<RefreshCw size={14} />}
                  >
                    Refresh
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Extension Status */}
          {showExtensionStatus && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Shield size={16} className="text-gray-500" />
                <span className="text-sm text-gray-600">Chrome Extension:</span>
              </div>
              <div className="flex items-center space-x-2">
                {isExtensionReady() ? (
                  <>
                    <CheckCircle size={14} className="text-green-500" />
                    <span className="text-sm text-green-600">Connected</span>
                  </>
                ) : (
                  <>
                    <AlertCircle size={14} className="text-red-500" />
                    <span className="text-sm text-red-600">Not Available</span>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Session Info */}
          <div className="text-xs text-gray-500 space-y-1">
            <div>
              Authenticated: {new Date(authState.user?.authenticated_at || '').toLocaleString()}
            </div>
            {authState.user?.last_activity && (
              <div>
                Last activity: {new Date(authState.user.last_activity).toLocaleString()}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="p-4 border-t border-gray-200">
        {!showLogoutConfirm ? (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowLogoutConfirm(true)}
              leftIcon={<LogOut size={14} />}
              className="flex-1"
            >
              Logout
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {/* Settings modal */}}
              leftIcon={<Settings size={14} />}
            >
              Settings
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            <p className="text-sm text-gray-600 text-center">
              Are you sure you want to logout?
            </p>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex-1"
              >
                This Device
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleLogoutAll}
                className="flex-1"
              >
                All Devices
              </Button>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowLogoutConfirm(false)}
              className="w-full"
            >
              Cancel
            </Button>
          </div>
        )}
      </div>

      {/* Session Warning */}
      {sessionStatus.type === 'expiring' && (
        <div className="p-3 bg-amber-50 border-t border-amber-200">
          <div className="flex items-center space-x-2">
            <AlertCircle size={14} className="text-amber-600" />
            <span className="text-sm text-amber-700">
              Your session will expire soon. Click refresh to extend it.
            </span>
          </div>
        </div>
      )}

      {/* Session Expired */}
      {sessionStatus.type === 'expired' && (
        <div className="p-3 bg-red-50 border-t border-red-200">
          <div className="flex items-center space-x-2">
            <AlertCircle size={14} className="text-red-600" />
            <span className="text-sm text-red-700">
              Your session has expired. Please log in again.
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default LoginStatus;
