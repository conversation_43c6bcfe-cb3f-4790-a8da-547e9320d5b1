/**
 * Protected Route Component
 * 
 * Wrapper component that protects routes requiring authentication
 */

import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Loader2, Lock, AlertCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { Button } from '../ui';
import type { ProtectedRouteProps } from '../../types/auth';

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  fallback,
  requireAuth = true,
  redirectTo = '/login'
}) => {
  const { authState, checkAuthStatus, logout } = useAuth();
  const location = useLocation();

  // Check auth status on mount and when location changes
  useEffect(() => {
    if (requireAuth && authState.sessionToken) {
      checkAuthStatus();
    }
  }, [requireAuth, authState.sessionToken, checkAuthStatus, location.pathname]);

  // If authentication is not required, render children
  if (!requireAuth) {
    return <>{children}</>;
  }

  // Show loading state while checking authentication
  if (authState.isLoading) {
    return fallback || <LoadingFallback />;
  }

  // If not authenticated, redirect to login or show fallback
  if (!authState.isAuthenticated) {
    if (fallback) {
      return <>{fallback}</>;
    }
    
    // Redirect to login with return URL
    return (
      <Navigate 
        to={redirectTo} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // If session is expired, show session expired message
  if (authState.user && !authState.isLoading) {
    const timeUntilExpiry = authState.user.expires_at 
      ? Math.max(0, Math.floor((new Date(authState.user.expires_at).getTime() - Date.now()) / 1000))
      : 0;
    
    if (timeUntilExpiry === 0) {
      return <SessionExpiredFallback onLogout={logout} />;
    }
  }

  // If there's an authentication error, show error fallback
  if (authState.error) {
    return <AuthErrorFallback error={authState.error} onRetry={checkAuthStatus} />;
  }

  // User is authenticated, render protected content
  return <>{children}</>;
};

// Loading fallback component
const LoadingFallback: React.FC = () => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-theme-seashell via-white to-primary-50/30">
    <div className="text-center">
      <div className="w-16 h-16 bg-white rounded-full shadow-lg flex items-center justify-center mx-auto mb-4">
        <Loader2 size={32} className="text-primary-600 animate-spin" />
      </div>
      <h2 className="text-xl font-semibold text-secondary-900 mb-2">
        Checking Authentication
      </h2>
      <p className="text-secondary-600">
        Please wait while we verify your session...
      </p>
    </div>
  </div>
);

// Session expired fallback component
interface SessionExpiredFallbackProps {
  onLogout: () => void;
}

const SessionExpiredFallback: React.FC<SessionExpiredFallbackProps> = ({ onLogout }) => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-theme-seashell via-white to-primary-50/30">
    <div className="max-w-md mx-auto text-center p-8">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <Lock size={32} className="text-red-600" />
      </div>
      <h2 className="text-xl font-semibold text-secondary-900 mb-2">
        Session Expired
      </h2>
      <p className="text-secondary-600 mb-6">
        Your session has expired for security reasons. Please log in again to continue.
      </p>
      <Button
        variant="primary"
        size="lg"
        onClick={onLogout}
        className="w-full"
      >
        Log In Again
      </Button>
    </div>
  </div>
);

// Auth error fallback component
interface AuthErrorFallbackProps {
  error: string;
  onRetry: () => void;
}

const AuthErrorFallback: React.FC<AuthErrorFallbackProps> = ({ error, onRetry }) => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-theme-seashell via-white to-primary-50/30">
    <div className="max-w-md mx-auto text-center p-8">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <AlertCircle size={32} className="text-red-600" />
      </div>
      <h2 className="text-xl font-semibold text-secondary-900 mb-2">
        Authentication Error
      </h2>
      <p className="text-secondary-600 mb-6">
        {error}
      </p>
      <div className="space-y-3">
        <Button
          variant="primary"
          size="lg"
          onClick={onRetry}
          className="w-full"
        >
          Try Again
        </Button>
        <Button
          variant="outline"
          size="md"
          onClick={() => window.location.href = '/login'}
          className="w-full"
        >
          Go to Login
        </Button>
      </div>
    </div>
  </div>
);

// Higher-order component for protecting components
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'> = {}
) => {
  const WrappedComponent: React.FC<P> = (props) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  );
  
  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Hook for checking if current route is protected
export const useProtectedRoute = () => {
  const { authState } = useAuth();
  const location = useLocation();
  
  return {
    isProtected: true, // This would be determined by route configuration
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    user: authState.user,
    currentPath: location.pathname
  };
};

export default ProtectedRoute;
