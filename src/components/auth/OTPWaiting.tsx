/**
 * OTP Waiting Component
 * 
 * Shows waiting state while user approves OTP in Chrome Extension
 */

import React, { useEffect, useState } from 'react';
import { 
  Clock, 
  RefreshCw, 
  X, 
  AlertCircle, 
  CheckCircle, 
  Chrome,
  Timer
} from 'lucide-react';
import { Button } from '../ui';
import { useOTP } from '../../hooks/useOTP';
import { useExtension } from '../../hooks/useExtension';
import type { OTPWaitingState } from '../../types/auth';

interface OTPWaitingProps {
  waitingState: OTPWaitingState;
  onCancel?: () => void;
  onResend?: () => void;
  className?: string;
}

const OTPWaiting: React.FC<OTPWaitingProps> = ({
  waitingState,
  onCancel,
  onResend,
  className = ''
}) => {
  // State
  const [timeRemaining, setTimeRemaining] = useState(waitingState.time_remaining);
  const [resendCooldown, setResendCooldown] = useState(0);

  // Hooks
  const { canResend, getResendCooldown } = useOTP();
  const { status: extensionStatus, isExtensionReady } = useExtension();

  // Update time remaining
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const expiresAt = new Date(waitingState.expires_at).getTime();
      const remaining = Math.max(0, Math.floor((expiresAt - now) / 1000));
      
      setTimeRemaining(remaining);
      
      if (remaining === 0) {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [waitingState.expires_at]);

  // Update resend cooldown
  useEffect(() => {
    const interval = setInterval(() => {
      const cooldown = getResendCooldown();
      setResendCooldown(Math.ceil(cooldown));
    }, 1000);

    return () => clearInterval(interval);
  }, [getResendCooldown]);

  // Format time display
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Get progress percentage
  const getProgress = () => {
    const totalTime = 300; // 5 minutes in seconds
    return ((totalTime - timeRemaining) / totalTime) * 100;
  };

  // Handle resend
  const handleResend = () => {
    if (canResend() && onResend) {
      onResend();
    }
  };

  return (
    <div className={`w-full max-w-md mx-auto ${className}`}>
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Chrome size={32} className="text-blue-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Waiting for Approval
          </h2>
          <p className="text-gray-600 text-sm">
            Check your Chrome Extension for the OTP notification
          </p>
        </div>

        {/* Email Display */}
        <div className="bg-gray-50 rounded-lg p-3 mb-6">
          <div className="text-center">
            <p className="text-sm text-gray-600">OTP sent to:</p>
            <p className="font-medium text-gray-900">{waitingState.email}</p>
          </div>
        </div>

        {/* Timer and Progress */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">Time remaining:</span>
            <div className="flex items-center space-x-1">
              <Timer size={14} className="text-gray-500" />
              <span className="text-sm font-mono text-gray-900">
                {formatTime(timeRemaining)}
              </span>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-1000"
              style={{ width: `${getProgress()}%` }}
            />
          </div>
        </div>

        {/* Extension Status */}
        <div className="mb-6">
          <div className={`flex items-center space-x-2 p-3 rounded-lg ${
            isExtensionReady() 
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200'
          }`}>
            {isExtensionReady() ? (
              <>
                <CheckCircle size={16} className="text-green-600 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-green-800">
                    Extension Connected
                  </p>
                  <p className="text-xs text-green-600">
                    Waiting for your approval...
                  </p>
                </div>
              </>
            ) : (
              <>
                <AlertCircle size={16} className="text-red-600 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-red-800">
                    Extension Not Available
                  </p>
                  <p className="text-xs text-red-600">
                    Please install or enable the Chrome Extension
                  </p>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h4 className="text-sm font-medium text-blue-800 mb-2">
            How to approve:
          </h4>
          <ol className="text-sm text-blue-700 space-y-1">
            <li>1. Look for the Chrome Extension notification</li>
            <li>2. Click on the Progress Dashboard extension icon</li>
            <li>3. Click "Login" to approve the request</li>
            <li>4. You'll be automatically logged in</li>
          </ol>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          {/* Resend Button */}
          <Button
            variant="outline"
            size="md"
            onClick={handleResend}
            disabled={!canResend() || timeRemaining === 0}
            className="flex-1"
            leftIcon={<RefreshCw size={16} />}
          >
            {resendCooldown > 0 ? `Resend (${resendCooldown}s)` : 'Resend OTP'}
          </Button>

          {/* Cancel Button */}
          <Button
            variant="ghost"
            size="md"
            onClick={onCancel}
            className="flex-1"
            leftIcon={<X size={16} />}
          >
            Cancel
          </Button>
        </div>

        {/* Expired State */}
        {timeRemaining === 0 && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertCircle size={16} className="text-red-600 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium text-red-800">
                  OTP Expired
                </p>
                <p className="text-xs text-red-600">
                  Please request a new OTP to continue
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Development Mode Notice */}
        {import.meta.env.DEV && (
          <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <p className="text-xs text-gray-600 text-center">
              <strong>Dev Mode:</strong> OTP will be auto-approved in 2 seconds
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OTPWaiting;
