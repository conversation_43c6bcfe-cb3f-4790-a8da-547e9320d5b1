/**
 * Login Form Component
 * 
 * Email input form that starts the OTP authentication flow
 */

import React, { useState, useEffect } from 'react';
import { Mail, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { Button, Input } from '../ui';
import { useAuth } from '../../contexts/AuthContext';
import { useOTP } from '../../hooks/useOTP';
import type { LoginFormData } from '../../types/auth';
import { AUTH_STORAGE_KEYS } from '../../types/auth';

interface LoginFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  className?: string;
  showTitle?: boolean;
  autoFocus?: boolean;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onSuccess,
  onError,
  className = '',
  showTitle = true,
  autoFocus = true
}) => {
  // State
  const [formData, setFormData] = useState<LoginFormData>({ email: '' });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [localError, setLocalError] = useState<string>('');
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false);
  const [showRejectionAnimation, setShowRejectionAnimation] = useState(false);

  // Hooks
  const { authState } = useAuth();
  const {
    flowState,
    error: otpError,
    generateOTP,
    resetFlow
  } = useOTP({
    onSuccess: () => {
      onSuccess?.();
    },
    onError: (error) => {
      onError?.(error);
    }
  });

  // Load last used email on mount
  useEffect(() => {
    const lastEmail = localStorage.getItem(AUTH_STORAGE_KEYS.LAST_EMAIL);
    if (lastEmail) {
      setFormData({ email: lastEmail });
    }
  }, []);

  // Handle flow state changes for animations
  useEffect(() => {
    if (flowState === 'success') {
      setShowSuccessAnimation(true);
      // Auto-hide success animation after 2 seconds
      const timer = setTimeout(() => {
        setShowSuccessAnimation(false);
      }, 2000);
      return () => clearTimeout(timer);
    }

    if (flowState === 'rejected') {
      setShowRejectionAnimation(true);
      // Auto-hide rejection animation after 3 seconds
      const timer = setTimeout(() => {
        setShowRejectionAnimation(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [flowState]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setLocalError('');

    // Validate email is not empty
    if (!formData.email.trim()) {
      setLocalError('Email address is required');
      // Add visual feedback with focus
      const emailInput = document.querySelector('input[type="email"]') as HTMLInputElement;
      if (emailInput) {
        emailInput.focus();
        emailInput.classList.add('shake-animation');
        setTimeout(() => emailInput.classList.remove('shake-animation'), 500);
      }
      onError?.('Email address is required');
      return;
    }

    // Validate email format
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(formData.email.trim())) {
      setLocalError('Please enter a valid email address');
      // Add visual feedback with focus
      const emailInput = document.querySelector('input[type="email"]') as HTMLInputElement;
      if (emailInput) {
        emailInput.focus();
        emailInput.select(); // Select all text for easy correction
        emailInput.classList.add('shake-animation');
        setTimeout(() => emailInput.classList.remove('shake-animation'), 500);
      }
      onError?.('Please enter a valid email address');
      return;
    }

    try {
      setIsSubmitting(true);
      await generateOTP(formData.email.trim().toLowerCase());
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setLocalError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle email input change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ email: e.target.value });

    // Clear local error when user starts typing
    if (localError) {
      setLocalError('');
    }

    // Reset flow if there was an error
    if (flowState === 'error') {
      resetFlow();
    }
  };

  // Get current status
  const getStatus = () => {
    if (authState.isAuthenticated) {
      return {
        type: 'success' as const,
        message: `Welcome back, ${authState.user?.email}!`
      };
    }

    if (flowState === 'generating') {
      return {
        type: 'loading' as const,
        message: 'Generating OTP...'
      };
    }

    if (flowState === 'waiting') {
      return {
        type: 'info' as const,
        message: 'Check your Chrome Extension for OTP approval'
      };
    }

    if (flowState === 'validating') {
      return {
        type: 'loading' as const,
        message: 'Validating OTP...'
      };
    }

    if (flowState === 'success') {
      return {
        type: 'success' as const,
        message: 'Authentication successful!'
      };
    }

    if (flowState === 'rejected') {
      return {
        type: 'error' as const,
        message: 'Authentication was rejected'
      };
    }

    if (flowState === 'expired') {
      return {
        type: 'error' as const,
        message: 'OTP has expired'
      };
    }

    if (flowState === 'error' || otpError) {
      return {
        type: 'error' as const,
        message: otpError || 'Authentication failed'
      };
    }

    return null;
  };

  const status = getStatus();
  const isLoading = isSubmitting || flowState === 'generating' || flowState === 'validating';
  const isDisabled = isLoading || authState.isAuthenticated || flowState === 'waiting';

  return (
    <>
      {/* Add CSS for animations */}
      <style>{`
        .shake-animation {
          animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
          0%, 100% { transform: translateX(0); }
          10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
          20%, 40%, 60%, 80% { transform: translateX(4px); }
        }

        .success-animation {
          animation: successPulse 2s ease-in-out;
        }

        @keyframes successPulse {
          0% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.05); opacity: 0.9; }
          100% { transform: scale(1); opacity: 1; }
        }

        .rejection-animation {
          animation: rejectionShake 0.6s ease-in-out;
        }

        @keyframes rejectionShake {
          0%, 100% { transform: translateX(0) scale(1); }
          10%, 30%, 50%, 70%, 90% { transform: translateX(-8px) scale(0.98); }
          20%, 40%, 60%, 80% { transform: translateX(8px) scale(0.98); }
        }

        .loading-dots {
          animation: loadingDots 1.5s infinite;
        }

        @keyframes loadingDots {
          0%, 20% { opacity: 0; }
          50% { opacity: 1; }
          100% { opacity: 0; }
        }

        .loading-dots:nth-child(2) { animation-delay: 0.2s; }
        .loading-dots:nth-child(3) { animation-delay: 0.4s; }

        .fade-in {
          animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .slide-up {
          animation: slideUp 0.4s ease-out;
        }

        @keyframes slideUp {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }
      `}</style>

      <div className={className}>
        {showTitle && (
        <div className="text-center mb-8">
          {/* Icon Container */}
          <div className="flex items-center justify-center w-12 h-12 bg-theme-nav-green/10 rounded-lg mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-theme-eerie-black">
              <path d="M9 12l2 2 4-4"/>
              <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1"/>
              <path d="M3 12v6c0 .552.448 1 1 1h16c.552 0 1-.448 1-1v-6"/>
            </svg>
          </div>

          <h1 className="text-2xl font-bold text-theme-eerie-black mb-2">
            Welcome Back
          </h1>
          <p className="text-secondary-600 text-sm">
            Enter your email to receive an OTP
          </p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Email Input */}
        <Input
          type="email"
          label="Email Address"
          placeholder="Enter your email address"
          value={formData.email}
          onChange={handleEmailChange}
          leftIcon={<Mail size={16} />}
          error={localError || (status?.type === 'error' ? status.message : undefined)}
          success={status?.type === 'success' ? status.message : undefined}
          disabled={isDisabled}
          required
          autoFocus={autoFocus}
          autoComplete="email"
        />

        {/* Dynamic Status Messages with Animations */}
        {status && status.type !== 'error' && status.type !== 'success' && (
          <div className={`flex items-center space-x-2 p-3 rounded-lg fade-in ${
            status.type === 'loading'
              ? 'bg-blue-50 text-blue-700 border border-blue-200'
              : 'bg-amber-50 text-amber-700 border border-amber-200'
          }`}>
            {status.type === 'loading' ? (
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full loading-dots"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full loading-dots"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full loading-dots"></div>
              </div>
            ) : (
              <AlertCircle size={16} className="flex-shrink-0" />
            )}
            <span className="text-sm font-medium">{status.message}</span>
          </div>
        )}

        {/* Success Animation */}
        {(showSuccessAnimation || flowState === 'success') && (
          <div className="success-animation bg-green-50 border border-green-200 rounded-lg p-4 slide-up">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle size={20} className="text-green-600" />
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-green-800">
                  OTP Approved Successfully!
                </h4>
                <p className="text-sm text-green-600">
                  Redirecting to dashboard...
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Rejection Animation */}
        {(showRejectionAnimation || flowState === 'rejected') && (
          <div className="rejection-animation bg-red-50 border border-red-200 rounded-lg p-4 slide-up">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircle size={20} className="text-red-600" />
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-800">
                  OTP Request Rejected
                </h4>
                <p className="text-sm text-red-600">
                  Please try again or check your Chrome Extension
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Demo Mode - Development Only */}
        {import.meta.env.DEV && (
          <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <p className="text-xs text-gray-600 text-center mb-2">
              <strong>Demo Mode:</strong> Test Animations
            </p>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={() => {
                  setShowSuccessAnimation(true);
                  setTimeout(() => setShowSuccessAnimation(false), 2000);
                }}
                className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200"
              >
                Test Success
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowRejectionAnimation(true);
                  setTimeout(() => setShowRejectionAnimation(false), 3000);
                }}
                className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
              >
                Test Rejection
              </button>
            </div>
          </div>
        )}

        {/* Submit Button with Dynamic States */}
        <Button
          type="submit"
          variant="primary"
          size="lg"
          className={`w-full bg-theme-nav-green hover:bg-theme-nav-green/80 text-theme-eerie-black font-medium transition-all duration-200 focus:ring-theme-nav-green/50 border border-theme-nav-green/30 ${
            showSuccessAnimation ? 'success-animation' : ''
          } ${
            showRejectionAnimation ? 'rejection-animation' : ''
          }`}
          loading={isLoading}
          disabled={isDisabled}
        >
          {flowState === 'generating' && 'Generating OTP...'}
          {flowState === 'waiting' && 'Waiting for Approval...'}
          {flowState === 'validating' && 'Validating...'}
          {flowState === 'success' && '✓ Success!'}
          {flowState === 'rejected' && '✗ Rejected'}
          {!flowState || flowState === 'idle' || flowState === 'error' ? 'Send OTP' : ''}
        </Button>

        {/* Extension Status */}
        {flowState === 'waiting' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-blue-800 mb-1">
                  Waiting for Chrome Extension
                </h4>
                <p className="text-sm text-blue-600">
                  Please check your Chrome Extension notification and click "Login" to approve the authentication request.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Extension Not Available */}
        {flowState === 'error' && otpError?.includes('Extension') && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle size={16} className="text-amber-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-amber-800 mb-1">
                  Chrome Extension Required
                </h4>
                <p className="text-sm text-amber-600 mb-2">
                  This application requires the Progress Dashboard Chrome Extension for OTP authentication.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // In Phase 3, this will link to Chrome Web Store
                    window.open('#', '_blank');
                  }}
                >
                  Install Extension
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Success State */}
        {authState.isAuthenticated && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <CheckCircle size={16} className="text-green-600 flex-shrink-0" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-green-800">
                  Authentication Successful
                </h4>
                <p className="text-sm text-green-600">
                  Welcome back, {authState.user?.email}!
                </p>
              </div>
            </div>
          </div>
        )}
      </form>
      </div>
    </>
  );
};

export default LoginForm;
