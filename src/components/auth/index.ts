/**
 * Authentication Components Export
 * 
 * Centralized exports for all authentication-related components
 */

export { default as LoginForm } from './LoginForm';
export { default as OTPWaiting } from './OTPWaiting';
export { default as LoginStatus } from './LoginStatus';
export { default as ProtectedRoute, withAuth, useProtectedRoute } from './ProtectedRoute';

// Re-export types for convenience
export type {
  LoginFormData,
  LoginFormState,
  OTPWaitingState,
  ProtectedRouteProps,
  LoginPageProps
} from '../../types/auth';
