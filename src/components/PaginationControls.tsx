import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

/**
 * Pagination Controls Props Interface
 */
export interface PaginationControlsProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  onNextPage: () => void;
  onPrevPage: () => void;
  className?: string;
}

/**
 * PaginationControls Component
 * 
 * Extracts pagination controls logic from Categories.tsx component.
 * Provides reusable pagination UI with navigation controls and item count display.
 * 
 * Features:
 * - Clean pagination UI with prev/next buttons
 * - Item count display with range information
 * - Disabled state handling for boundary conditions
 * - Responsive design with proper spacing
 * - Accessible button states and hover effects
 * - Reusable across components
 * 
 * @param currentPage - Current active page number (1-based)
 * @param totalItems - Total number of items across all pages
 * @param itemsPerPage - Number of items displayed per page
 * @param onNextPage - Callback function for next page navigation
 * @param onPrevPage - Callback function for previous page navigation
 * @param className - Optional additional CSS classes
 * @returns PaginationControls JSX element
 */
export const PaginationControls: React.FC<PaginationControlsProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  onNextPage,
  onPrevPage,
  className = ''
}) => {
  // Calculate pagination values
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startItem = ((currentPage - 1) * itemsPerPage) + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);
  
  // Don't render if there's only one page or no items
  if (totalItems <= itemsPerPage) {
    return null;
  }

  return (
    <div className={`flex items-center justify-between px-3 py-2 bg-gray-50/50 ${className}`}>
      {/* Item Count Display */}
      <div className="flex items-center space-x-2 text-sm text-gray-600">
        <span>
          Showing {startItem} to {endItem} of {totalItems} entries
        </span>
      </div>
      
      {/* Navigation Controls */}
      <div className="flex items-center space-x-2">
        {/* Previous Page Button */}
        <button
          onClick={onPrevPage}
          disabled={currentPage === 1}
          className={`px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200 ${
            currentPage === 1
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-white text-[#1A1919] hover:bg-[#9CEE69] hover:text-white border border-gray-300'
          }`}
          aria-label="Previous page"
        >
          <ChevronLeft size={16} />
        </button>
        
        {/* Page Info */}
        <span className="px-3 py-1 text-sm text-gray-600">
          Page {currentPage} of {totalPages}
        </span>
        
        {/* Next Page Button */}
        <button
          onClick={onNextPage}
          disabled={currentPage >= totalPages}
          className={`px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200 ${
            currentPage >= totalPages
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-white text-[#1A1919] hover:bg-[#9CEE69] hover:text-white border border-gray-300'
          }`}
          aria-label="Next page"
        >
          <ChevronRight size={16} />
        </button>
      </div>
    </div>
  );
};

/**
 * getPaginationInfo - Utility function for pagination calculations
 * 
 * Calculates pagination information for any given dataset.
 * Used for cases where component rendering isn't needed.
 * 
 * @param currentPage - Current active page number (1-based)
 * @param totalItems - Total number of items across all pages
 * @param itemsPerPage - Number of items displayed per page
 * @returns Pagination information object
 */
export const getPaginationInfo = (
  currentPage: number,
  totalItems: number,
  itemsPerPage: number
) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startItem = ((currentPage - 1) * itemsPerPage) + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);
  
  return {
    totalPages,
    startItem,
    endItem,
    hasNextPage: currentPage < totalPages,
    hasPrevPage: currentPage > 1,
    isFirstPage: currentPage === 1,
    isLastPage: currentPage >= totalPages
  };
};

export default PaginationControls;
