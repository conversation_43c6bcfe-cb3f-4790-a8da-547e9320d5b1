import React, { useState } from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';

interface Column {
  key: string;
  label: React.ReactNode;
  render?: (value: any, row: any) => React.ReactNode;
  width?: string;
  headerClass?: string;
  cellClass?: string;
  className?: string;
  sortable?: boolean;
}

interface DataTableProps {
  data: any[];
  columns: Column[];
  pageSize?: number;
  tableLayout?: 'auto' | 'fixed';
}

const DataTable: React.FC<DataTableProps> = React.memo(({ data, columns }) => {
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // State for responsive grid updates
  const [screenWidth, setScreenWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

  // Effect to handle window resize
  React.useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Sort data
  const sortedData = React.useMemo(() => {
    if (!sortConfig) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [data, sortConfig]);

  const displayData = sortedData;

  const handleSort = (key: string) => {
    if (sortConfig?.key === key) {
      setSortConfig({
        key,
        direction: sortConfig.direction === 'asc' ? 'desc' : 'asc'
      });
    } else {
      setSortConfig({ key, direction: 'asc' });
    }
  };

  const getSortIcon = (key: string) => {
    if (sortConfig?.key !== key) return null;
    return sortConfig.direction === 'asc' ?
      <ChevronUp size={16} className="text-primary-600" /> :
      <ChevronDown size={16} className="text-primary-600" />;
  };

  // Helper function to get responsive grid template columns - Optimized for Mac 13" (1440px) & 1024px
  const getGridTemplateColumns = () => {
    // Filter columns based on screen width for better mobile experience
    const visibleColumns = columns.filter(column => {
      if (screenWidth < 480) {
        // Very small screens - only show Title and Link/View
        return column.key === 'Title' || column.key === 'Link' || column.key === 'View';
      } else if (screenWidth < 640) {
        // Small screens - hide less important columns
        return !(column.key === 'Page Old' || column.key === 'Order Old');
      }
      return true;
    });

    // Calculate grid template with specific optimizations for Mac 13" and 1024px
    const gridCols = visibleColumns.map(column => {
      if (column.width === 'flex-1') {
        // Optimized Title column widths for Mac screens
        if (screenWidth < 480) {
          return 'minmax(140px, 1fr)'; // Very small screens - more space for title
        } else if (screenWidth < 640) {
          return 'minmax(160px, 1fr)'; // Small screens
        } else if (screenWidth < 768) {
          return 'minmax(180px, 1fr)'; // Medium screens
        } else if (screenWidth >= 768 && screenWidth < 1024) {
          return 'minmax(220px, 1fr)'; // Tablet screens
        } else if (screenWidth >= 1024 && screenWidth < 1200) {
          return 'minmax(250px, 1fr)'; // 1024px - Reduced for better fit
        } else if (screenWidth >= 1200 && screenWidth < 1600) {
          return 'minmax(300px, 1fr)'; // 1440px Mac 13" - Optimal title width
        } else if (screenWidth >= 1600) {
          return 'minmax(350px, 1fr)'; // Large screens
        }
        return 'minmax(220px, 1fr)'; // Default fallback
      } else if (column.width?.includes('w-20')) {
        // Page, Rank columns
        if (screenWidth < 480) {
          return 'minmax(50px, 60px)'; // Very small screens
        } else if (screenWidth < 640) {
          return 'minmax(55px, 65px)'; // Small screens
        } else if (screenWidth < 768) {
          return 'minmax(60px, 70px)'; // Medium screens
        } else if (screenWidth >= 768 && screenWidth < 1024) {
          return 'minmax(65px, 75px)'; // Tablet screens
        } else if (screenWidth >= 1024 && screenWidth < 1200) {
          return 'minmax(70px, 80px)'; // 1024px - Compact but readable
        } else if (screenWidth >= 1200 && screenWidth < 1600) {
          return 'minmax(75px, 85px)'; // 1440px Mac 13" - Comfortable size
        } else if (screenWidth >= 1600) {
          return 'minmax(80px, 90px)'; // Large screens
        }
        return 'minmax(65px, 75px)'; // Default fallback
      } else if (column.width?.includes('w-24')) {
        // Medium columns
        if (screenWidth >= 1024 && screenWidth < 1200) {
          return 'minmax(85px, 95px)'; // 1024px
        } else if (screenWidth >= 1200 && screenWidth < 1600) {
          return 'minmax(95px, 105px)'; // 1440px Mac 13"
        } else if (screenWidth >= 1600) {
          return 'minmax(105px, 115px)'; // Large screens
        }
        return screenWidth < 640 ? 'minmax(50px, 70px)' : 'minmax(80px, 96px)';
      } else if (column.width?.includes('w-28')) {
        // Order Old, Order New columns
        if (screenWidth >= 1024 && screenWidth < 1200) {
          return 'minmax(95px, 105px)'; // 1024px
        } else if (screenWidth >= 1200 && screenWidth < 1600) {
          return 'minmax(105px, 115px)'; // 1440px Mac 13" - Perfect for order columns
        } else if (screenWidth >= 1600) {
          return 'minmax(115px, 125px)'; // Large screens
        }
        return screenWidth < 640 ? 'minmax(60px, 80px)' : 'minmax(90px, 112px)';
      } else if (column.width?.includes('w-12')) {
        // View column - Badge-style button with icon + "View" text
        if (screenWidth >= 1024 && screenWidth < 1200) {
          return 'minmax(60px, 70px)'; // 1024px - Accommodate icon + "View" text
        } else if (screenWidth >= 1200 && screenWidth < 1600) {
          return 'minmax(65px, 75px)'; // 1440px Mac 13" - Comfortable for text
        } else if (screenWidth >= 1600) {
          return 'minmax(70px, 80px)'; // Large screens - Maximum comfort
        }
        return screenWidth < 640 ? 'minmax(55px, 65px)' : 'minmax(60px, 70px)'; // Adequate for icon + text
      } else if (column.width?.includes('w-14')) {
        // View column medium size - Updated for 35px button
        if (screenWidth >= 1024 && screenWidth < 1200) {
          return 'minmax(60px, 70px)'; // 1024px - Comfortable for 35px button
        } else if (screenWidth >= 1200 && screenWidth < 1600) {
          return 'minmax(65px, 75px)'; // 1440px Mac 13" - Optimal spacing
        } else if (screenWidth >= 1600) {
          return 'minmax(70px, 80px)'; // Large screens - Maximum comfort
        }
        return screenWidth < 640 ? 'minmax(55px, 65px)' : 'minmax(60px, 70px)';
      } else if (column.width?.includes('w-16')) {
        // Link column - Optimized for button interaction
        if (screenWidth < 480) {
          return 'minmax(50px, 60px)'; // Very small screens
        } else if (screenWidth < 640) {
          return 'minmax(55px, 65px)'; // Small screens
        } else if (screenWidth < 768) {
          return 'minmax(60px, 70px)'; // Medium screens
        } else if (screenWidth >= 768 && screenWidth < 1024) {
          return 'minmax(65px, 75px)'; // Tablet screens
        } else if (screenWidth >= 1024 && screenWidth < 1200) {
          return 'minmax(65px, 75px)'; // 1024px - Comfortable for button
        } else if (screenWidth >= 1200 && screenWidth < 1600) {
          return 'minmax(70px, 80px)'; // 1440px Mac 13" - Optimal for touch interaction
        } else if (screenWidth >= 1600) {
          return 'minmax(75px, 85px)'; // Large screens - Maximum comfort
        }
        return 'minmax(65px, 75px)'; // Default fallback
      } else {
        // Default columns
        if (screenWidth >= 1024 && screenWidth < 1200) {
          return 'minmax(80px, 90px)'; // 1024px
        } else if (screenWidth >= 1200 && screenWidth < 1600) {
          return 'minmax(90px, 100px)'; // 1440px Mac 13"
        } else if (screenWidth >= 1600) {
          return 'minmax(100px, 110px)'; // Large screens
        }
        return screenWidth < 640 ? 'minmax(50px, 1fr)' : 'minmax(80px, 1fr)';
      }
    }).join(' ');

    return gridCols;
  };

  if (data.length === 0) {
    return (
      <div className="text-center py-16">
        <p className="text-lg font-semibold text-gray-600">No data available</p>
        <p className="text-sm text-gray-500 mt-2">Data will appear here when available</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Table - Using CSS Grid for better responsive support */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
        <div
          className="w-full min-w-0"
          style={{
            display: 'grid',
            gridTemplateColumns: getGridTemplateColumns(),
            minWidth: 'max-content',
            width: '100%'
          }}
        >
          {/* Header */}
          <div className="contents">
            {columns.map((column, index) => {
              const isSortable = column.sortable !== false;
              const isLastColumn = index === columns.length - 1;

              // Hide certain columns on very small screens for better mobile experience
              if (screenWidth < 480 && !(column.key === 'Title' || column.key === 'Link' || column.key === 'View')) {
                return null; // Only show Title and Link/View columns on very small screens
              } else if (screenWidth < 640 && (column.key === 'Page Old' || column.key === 'Order Old')) {
                return null; // Hide less important columns on small screens
              }

              // Build header class optimized for grid with Mac-specific spacing
              let headerClass = 'bg-gray-50 text-xs font-semibold text-[#1A1919] transition-all duration-200 text-center border-b border-gray-200';

              // Optimized padding for Mac screens - Compact version
              if (screenWidth >= 1024 && screenWidth < 1200) {
                headerClass += ' px-2 py-2'; // 1024px - Compact padding
              } else if (screenWidth >= 1200 && screenWidth < 1600) {
                headerClass += ' px-3 py-2.5'; // 1440px Mac 13" - Slightly more space
              } else if (screenWidth >= 1600) {
                headerClass += ' px-4 py-3'; // Large screens - Still compact
              } else {
                headerClass += ' px-1 sm:px-2 py-2'; // Default responsive - Compact
              }

              // Add interactive styling for sortable columns
              if (isSortable) {
                headerClass += " cursor-pointer hover:text-[#1A1919] hover:bg-[#9CEE69]/10";
              }

              // Add separator for all columns except the last one
              if (!isLastColumn) {
                headerClass += " border-r border-gray-200";
              }

              // Special handling for VIEW column to ensure visibility
              if (column.key === 'View') {
                headerClass += " relative z-10";
              }

              return (
                <div
                  key={column.key}
                  className={headerClass}
                  onClick={isSortable ? () => handleSort(column.key) : undefined}
                >
                  <div className="flex items-center justify-center space-x-1">
                    <span className="truncate">{column.label}</span>
                    {isSortable && (
                      <div className="opacity-60 hover:opacity-100 transition-opacity duration-200 flex-shrink-0">
                        {getSortIcon(column.key)}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Body */}
          {displayData.map((row, index) => (
            <div key={index} className="contents group">
              {columns.map((column, colIndex) => {
                const isLastColumn = colIndex === columns.length - 1;

                // Hide certain columns on very small screens (same as header)
                if (screenWidth < 480 && !(column.key === 'Title' || column.key === 'Link' || column.key === 'View')) {
                  return null; // Only show Title and Link/View columns on very small screens
                } else if (screenWidth < 640 && (column.key === 'Page Old' || column.key === 'Order Old')) {
                  return null; // Hide less important columns on small screens
                }

                // Build cell class optimized for grid with Mac-specific spacing
                let cellClass = 'text-[#1A1919] text-center border-b border-gray-100 group-hover:bg-[#9CEE69]/10 transition-all duration-200 text-xs';

                // Optimized padding for Mac screens - Compact version
                if (screenWidth >= 1024 && screenWidth < 1200) {
                  cellClass += ' px-2 py-1.5'; // 1024px - Compact cell padding
                } else if (screenWidth >= 1200 && screenWidth < 1600) {
                  cellClass += ' px-3 py-2'; // 1440px Mac 13" - Compact but readable
                } else if (screenWidth >= 1600) {
                  cellClass += ' px-4 py-2.5'; // Large screens - Still compact
                } else {
                  cellClass += ' px-1 sm:px-2 py-1.5'; // Default responsive - Compact
                }

                // Add border separator for all columns except the last one
                if (!isLastColumn) {
                  cellClass += " border-r border-gray-200";
                }

                // Special handling for VIEW column to ensure visibility
                if (column.key === 'View') {
                  cellClass += " relative z-10 min-w-0";
                }

                return (
                  <div key={column.key} className={cellClass}>
                    {column.render ? (
                      // Use custom render function - no additional wrapping
                      column.render(row[column.key], row)
                    ) : (
                      // Default: Show value with proper alignment based on column
                      <div className={`flex items-center truncate ${
                        column.key === 'Title' ? 'justify-start' : 'justify-center'
                      }`} title={String(row[column.key])}>
                        {row[column.key]}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

export default DataTable;
