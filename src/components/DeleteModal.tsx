import React from 'react';
import { RefreshCw, AlertCircle, Trash2, FileText } from 'lucide-react';
import Modal from './Modal';

interface FileData {
  id: string;
  name: string;
  type: 'category' | 'competitor';
  size: string;
  uploadDate: string;
  lastModified: string;
  author: string;
}

interface DeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => Promise<void>;
  selectedFile: FileData | null;
  isLoading: boolean;
  error: string | null;
}

const DeleteModal: React.FC<DeleteModalProps> = ({
  isOpen,
  onClose,
  onDelete,
  selectedFile,
  isLoading,
  error
}) => {
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFile || isLoading) return;

    try {
      await onDelete();
      onClose();
    } catch (error) {
      // Error handling is managed by parent component
      console.error('Delete failed:', error);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Confirm File Deletion"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {selectedFile && (
          <>
            {/* Warning Banner */}
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-red-100 rounded-md">
                  <AlertCircle size={20} className="text-red-600" />
                </div>
                <div>
                  <h4 className="font-medium text-red-800">Warning: This action cannot be undone</h4>
                  <p className="text-xs text-red-700 mt-1">
                    The file will be permanently removed from the system.
                  </p>
                </div>
              </div>
            </div>

            {/* File Information */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="p-2 bg-gray-100 rounded-md">
                  <FileText size={20} className="text-gray-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-800 mb-2">File to be deleted:</h4>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                    <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
                      <div>
                        <span className="font-medium">Type:</span> {selectedFile.type}
                      </div>
                      <div>
                        <span className="font-medium">Size:</span> {selectedFile.size}
                      </div>
                      <div>
                        <span className="font-medium">Uploaded:</span> {selectedFile.uploadDate}
                      </div>
                      <div>
                        <span className="font-medium">Author:</span> {selectedFile.author}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Consequences Warning */}
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <h4 className="font-medium text-amber-800 mb-2">What happens when you delete this file?</h4>
              <ul className="text-sm text-amber-700 space-y-1">
                <li>• The file will be permanently removed from storage</li>
                <li>• All associated data and analysis will be lost</li>
                <li>• Any reports or dashboards using this data may be affected</li>
                <li>• This action cannot be reversed</li>
              </ul>
            </div>

            {/* Error Display */}
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <AlertCircle size={16} className="text-red-600 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-red-800">Deletion Failed</h4>
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Confirmation Input */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <p className="text-sm text-gray-700 mb-3">
                To confirm deletion, please type the file name exactly as shown:
              </p>
              <p className="text-sm font-mono bg-white border border-gray-300 rounded px-2 py-1 text-gray-800">
                {selectedFile.name}
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-[#1A1919] border border-gray-300 rounded-md hover:bg-gray-50 transition-colors font-medium text-sm"
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 font-medium text-sm"
              >
                {isLoading && (
                  <RefreshCw size={14} className="animate-spin" />
                )}
                <Trash2 size={14} />
                <span>Delete File</span>
              </button>
            </div>
          </>
        )}
      </form>
    </Modal>
  );
};

export default DeleteModal;
