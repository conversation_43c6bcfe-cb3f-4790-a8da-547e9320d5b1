import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../utils/cn';

// Button variants using class-variance-authority for type-safe variants
const buttonVariants = cva(
  // Base styles - common to all buttons
  [
    'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white',
    'disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed',
    'active:scale-95 transform',
  ],
  {
    variants: {
      // Visual variants
      variant: {
        primary: [
          'bg-primary-500 text-white shadow-sm',
          'hover:bg-primary-600 hover:shadow-md',
          'focus:ring-primary-500',
          'active:bg-primary-700',
        ],
        secondary: [
          'bg-secondary-100 text-secondary-900 border border-secondary-200',
          'hover:bg-secondary-200 hover:border-secondary-300',
          'focus:ring-secondary-500',
          'active:bg-secondary-300',
        ],
        outline: [
          'border border-primary-500 text-primary-600 bg-transparent',
          'hover:bg-primary-50 hover:text-primary-700',
          'focus:ring-primary-500',
          'active:bg-primary-100',
        ],
        ghost: [
          'text-secondary-600 bg-transparent',
          'hover:bg-secondary-100 hover:text-secondary-900',
          'focus:ring-secondary-500',
          'active:bg-secondary-200',
        ],
        danger: [
          'bg-error-500 text-white shadow-sm',
          'hover:bg-error-600 hover:shadow-md',
          'focus:ring-error-500',
          'active:bg-error-700',
        ],
        success: [
          'bg-success-500 text-white shadow-sm',
          'hover:bg-success-600 hover:shadow-md',
          'focus:ring-success-500',
          'active:bg-success-700',
        ],
        warning: [
          'bg-warning-500 text-white shadow-sm',
          'hover:bg-warning-600 hover:shadow-md',
          'focus:ring-warning-500',
          'active:bg-warning-700',
        ],
        glass: [
          'bg-glass-light backdrop-blur-md border border-white/20 text-secondary-900',
          'hover:bg-glass-medium hover:border-white/30',
          'focus:ring-primary-500',
          'active:bg-glass-heavy',
        ],
      },
      
      // Size variants
      size: {
        xs: 'h-7 px-2 text-xs gap-1',
        sm: 'h-8 px-3 text-sm gap-1.5',
        md: 'h-10 px-4 text-base gap-2',
        lg: 'h-12 px-6 text-lg gap-2.5',
        xl: 'h-14 px-8 text-xl gap-3',
      },
      
      // Shape variants
      shape: {
        default: 'rounded-lg',
        rounded: 'rounded-full',
        square: 'rounded-none',
        pill: 'rounded-full px-6',
      },
      
      // Width variants
      width: {
        auto: 'w-auto',
        full: 'w-full',
        fit: 'w-fit',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      shape: 'default',
      width: 'auto',
    },
  }
);

// Button props interface
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  /** Loading state */
  loading?: boolean;
  /** Icon to display before text */
  leftIcon?: React.ReactNode;
  /** Icon to display after text */
  rightIcon?: React.ReactNode;
  /** Icon only button (no text) */
  iconOnly?: boolean;
  /** Custom class name */
  className?: string;
  /** Children content */
  children?: React.ReactNode;
}

// Button component with forwardRef for proper ref handling
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      shape,
      width,
      loading = false,
      leftIcon,
      rightIcon,
      iconOnly = false,
      disabled,
      children,
      ...props
    },
    ref
  ) => {
    // Determine if button is disabled (either explicitly or due to loading)
    const isDisabled = disabled || loading;
    
    // Loading spinner component
    const LoadingSpinner = () => (
      <svg
        className="animate-spin h-4 w-4"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    );

    return (
      <button
        className={cn(
          buttonVariants({ variant, size, shape, width }),
          iconOnly && 'aspect-square p-0',
          className
        )}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {/* Left icon or loading spinner */}
        {loading ? (
          <LoadingSpinner />
        ) : leftIcon ? (
          <span className="flex-shrink-0">{leftIcon}</span>
        ) : null}
        
        {/* Button text content */}
        {!iconOnly && children && (
          <span className={loading ? 'opacity-0' : ''}>{children}</span>
        )}
        
        {/* Icon only content */}
        {iconOnly && !loading && children}
        
        {/* Right icon */}
        {!loading && rightIcon && (
          <span className="flex-shrink-0">{rightIcon}</span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
export default Button;
