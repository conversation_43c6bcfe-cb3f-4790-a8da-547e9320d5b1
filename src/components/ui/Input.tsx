import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../utils/cn';

// Input variants
const inputVariants = cva(
  [
    'w-full rounded-lg border transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-0',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-secondary-50',
    'placeholder:text-secondary-400',
  ],
  {
    variants: {
      // Size variants
      size: {
        sm: 'px-3 py-1.5 text-sm h-8',
        md: 'px-4 py-2 text-base h-10',
        lg: 'px-6 py-3 text-lg h-12',
      },
      
      // State variants
      state: {
        default: [
          'border-secondary-300 bg-white text-secondary-900',
          'hover:border-secondary-400',
          'focus:border-primary-500 focus:ring-primary-500/20',
        ],
        error: [
          'border-error-500 bg-white text-secondary-900',
          'hover:border-error-600',
          'focus:border-error-500 focus:ring-error-500/20',
        ],
        success: [
          'border-success-500 bg-white text-secondary-900',
          'hover:border-success-600',
          'focus:border-success-500 focus:ring-success-500/20',
        ],
        warning: [
          'border-warning-500 bg-white text-secondary-900',
          'hover:border-warning-600',
          'focus:border-warning-500 focus:ring-warning-500/20',
        ],
      },
    },
    defaultVariants: {
      size: 'md',
      state: 'default',
    },
  }
);

// Input props interface
export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  /** Label for the input */
  label?: string;
  /** Helper text */
  helperText?: string;
  /** Error message */
  error?: string;
  /** Success message */
  success?: string;
  /** Warning message */
  warning?: string;
  /** Icon to display before input */
  leftIcon?: React.ReactNode;
  /** Icon to display after input */
  rightIcon?: React.ReactNode;
  /** Custom class name */
  className?: string;
  /** Custom class for input wrapper */
  wrapperClassName?: string;
  /** Custom class for label */
  labelClassName?: string;
  /** Whether the field is required */
  required?: boolean;
}

// Input component with forwardRef
const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      wrapperClassName,
      labelClassName,
      size,
      state,
      label,
      helperText,
      error,
      success,
      warning,
      leftIcon,
      rightIcon,
      required,
      id,
      ...props
    },
    ref
  ) => {
    // Determine the current state based on props
    const currentState = error ? 'error' : success ? 'success' : warning ? 'warning' : state;
    
    // Generate unique ID if not provided
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    
    // Determine message to show
    const message = error || success || warning || helperText;
    const messageColor = error 
      ? 'text-error-600' 
      : success 
      ? 'text-success-600' 
      : warning 
      ? 'text-warning-600' 
      : 'text-secondary-600';

    return (
      <div className={cn('w-full', wrapperClassName)}>
        {/* Label */}
        {label && (
          <label
            htmlFor={inputId}
            className={cn(
              'block text-sm font-medium text-secondary-700 mb-1.5',
              labelClassName
            )}
          >
            {label}
            {required && <span className="text-error-500 ml-1">*</span>}
          </label>
        )}

        {/* Input wrapper */}
        <div className="relative">
          {/* Left icon */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400 pointer-events-none">
              {leftIcon}
            </div>
          )}

          {/* Input field */}
          <input
            ref={ref}
            id={inputId}
            className={cn(
              inputVariants({ size, state: currentState }),
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              className
            )}
            {...props}
          />

          {/* Right icon */}
          {rightIcon && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 pointer-events-none">
              {rightIcon}
            </div>
          )}
        </div>

        {/* Helper text / Error / Success / Warning message */}
        {message && (
          <p className={cn('mt-1.5 text-sm', messageColor)}>
            {message}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input, inputVariants };
export default Input;
