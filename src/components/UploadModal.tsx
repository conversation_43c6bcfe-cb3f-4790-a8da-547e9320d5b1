import React, { useState } from 'react';
import { Upload, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import Modal from './Modal';

interface UploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (file: File) => Promise<void>;
  isUploading: boolean;
  uploadStatus: 'idle' | 'validating' | 'uploading' | 'success' | 'error';
  uploadMessage: string;
  uploadProgress: number;
  onReset: () => void;
}

const UploadModal: React.FC<UploadModalProps> = ({
  isOpen,
  onClose,
  onUpload,
  isUploading,
  uploadStatus,
  uploadMessage,
  uploadProgress,
  onReset
}) => {
  const [dragActive, setDragActive] = useState(false);

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onUpload(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    const csvFile = files.find(file => file.name.endsWith('.csv'));

    if (csvFile) {
      onUpload(csvFile);
    } else {
      // Handle error - could be passed as prop or handled here
      console.error('Please drop a CSV file');
    }
  };

  const handleClose = () => {
    if (!isUploading) {
      onClose();
      onReset();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Upload CSV File"
    >
      <div className="space-y-6">
        {/* Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 ${
            dragActive
              ? 'border-[#9CEE69] bg-[#9CEE69]/10'
              : uploadStatus === 'error'
                ? 'border-red-300 bg-red-50'
                : uploadStatus === 'success'
                  ? 'border-green-300 bg-green-50'
                  : 'border-gray-300 hover:border-[#9CEE69] hover:bg-[#9CEE69]/10'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {uploadStatus === 'idle' && (
            <>
              <Upload size={40} className={`mx-auto mb-4 ${dragActive ? 'text-[#9CEE69]' : 'text-gray-400'}`} />
              <p className="text-[#1A1919] mb-4">
                {dragActive ? 'Drop your CSV file here' : 'Drag & drop your CSV file here, or click to select'}
              </p>
              <input
                type="file"
                accept=".csv"
                onChange={handleFileInputChange}
                className="hidden"
                id="file-upload"
                disabled={isUploading}
              />
              <label
                htmlFor="file-upload"
                className="inline-flex items-center px-4 py-2 bg-[#9CEE69] text-[#1A1919] rounded-md cursor-pointer hover:bg-[#9CEE69]/80 transition-colors duration-200 font-medium text-sm"
              >
                Select File
              </label>
            </>
          )}

          {uploadStatus === 'validating' && (
            <>
              <RefreshCw size={40} className="mx-auto text-blue-500 mb-4 animate-spin" />
              <p className="text-blue-600 font-medium">{uploadMessage}</p>
            </>
          )}

          {uploadStatus === 'uploading' && (
            <>
              <RefreshCw size={40} className="mx-auto text-blue-500 mb-4 animate-spin" />
              <p className="text-blue-600 font-medium mb-4">{uploadMessage}</p>
              <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <div
                  className="bg-[#9CEE69] h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600 mt-2">{uploadProgress}% complete</p>
            </>
          )}

          {uploadStatus === 'success' && (
            <>
              <CheckCircle size={40} className="mx-auto text-green-500 mb-4" />
              <p className="text-green-600 font-medium">{uploadMessage}</p>
            </>
          )}

          {uploadStatus === 'error' && (
            <>
              <AlertCircle size={40} className="mx-auto text-red-500 mb-4" />
              <p className="text-red-600 font-medium mb-4">{uploadMessage}</p>
              <button
                onClick={onReset}
                className="px-4 py-2 bg-[#9CEE69] text-[#1A1919] rounded-md hover:bg-[#9CEE69]/80 transition-colors font-medium text-sm"
              >
                Try Again
              </button>
            </>
          )}
        </div>

        {/* File Format Info */}
        {uploadStatus === 'idle' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">Supported File Format</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• CSV files only (.csv)</li>
              <li>• Maximum file size: 10MB</li>
              <li>• First row should contain column headers</li>
              <li>• UTF-8 encoding recommended</li>
            </ul>
          </div>
        )}

        {/* Action Buttons */}
        {uploadStatus !== 'success' && uploadStatus !== 'uploading' && uploadStatus !== 'validating' && (
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-[#1A1919] border border-gray-300 rounded-md hover:bg-gray-50 transition-colors font-medium text-sm"
              disabled={isUploading}
            >
              Cancel
            </button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default UploadModal;
