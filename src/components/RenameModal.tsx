import React, { useState, useEffect } from 'react';
import { RefreshCw, Edit3, AlertCircle } from 'lucide-react';
import Modal from './Modal';

interface FileData {
  id: string;
  name: string;
  type: 'category' | 'competitor';
  size: string;
  uploadDate: string;
  lastModified: string;
  author: string;
}

interface RenameModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRename: (newName: string) => Promise<void>;
  selectedFile: FileData | null;
  isLoading: boolean;
  error: string | null;
}

const RenameModal: React.FC<RenameModalProps> = ({
  isOpen,
  onClose,
  onRename,
  selectedFile,
  isLoading,
  error
}) => {
  const [newFileName, setNewFileName] = useState('');

  // Update filename when selectedFile changes
  useEffect(() => {
    if (selectedFile) {
      setNewFileName(selectedFile.name);
    }
  }, [selectedFile]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newFileName.trim() || !selectedFile) return;

    try {
      await onRename(newFileName.trim());
      handleClose();
    } catch (error) {
      // Error handling is managed by parent component
      console.error('Rename failed:', error);
    }
  };

  const handleClose = () => {
    onClose();
    setNewFileName('');
  };

  const isValidName = newFileName.trim().length > 0;
  const isNameChanged = newFileName !== selectedFile?.name;
  const canSubmit = isValidName && isNameChanged && !isLoading;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Rename File"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {selectedFile && (
          <>
            {/* Current File Info */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-md">
                  <Edit3 size={16} className="text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-800">Current File</h4>
                  <p className="text-sm text-gray-600">{selectedFile.name}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    Type: {selectedFile.type} • Size: {selectedFile.size}
                  </p>
                </div>
              </div>
            </div>

            {/* New Name Input */}
            <div className="space-y-2">
              <label htmlFor="newFileName" className="block text-sm font-medium text-gray-700">
                New File Name
              </label>
              <input
                type="text"
                id="newFileName"
                value={newFileName}
                onChange={(e) => setNewFileName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#9CEE69] focus:border-transparent"
                placeholder="Enter new file name"
                disabled={isLoading}
                autoFocus
              />
              
              {/* Validation Messages */}
              {!isValidName && newFileName.length > 0 && (
                <p className="text-sm text-red-600">File name cannot be empty</p>
              )}
              
              {isValidName && !isNameChanged && (
                <p className="text-sm text-amber-600">Please enter a different name</p>
              )}
              
              {isValidName && isNameChanged && (
                <p className="text-sm text-green-600">✓ Valid file name</p>
              )}
            </div>

            {/* Error Display */}
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <AlertCircle size={16} className="text-red-600 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-red-800">Rename Failed</h4>
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* File Naming Guidelines */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">File Naming Guidelines</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Use descriptive names for easy identification</li>
                <li>• Avoid special characters (/, \, :, *, ?, ", &lt;, &gt;, |)</li>
                <li>• Keep names under 255 characters</li>
                <li>• File extension will be preserved automatically</li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-[#1A1919] border border-gray-300 rounded-md hover:bg-gray-50 transition-colors font-medium text-sm"
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!canSubmit}
                className="px-4 py-2 bg-[#9CEE69] text-[#1A1919] rounded-md hover:bg-[#9CEE69]/80 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 font-medium text-sm"
              >
                {isLoading && (
                  <RefreshCw size={14} className="animate-spin" />
                )}
                <span>Rename File</span>
              </button>
            </div>
          </>
        )}
      </form>
    </Modal>
  );
};

export default RenameModal;
