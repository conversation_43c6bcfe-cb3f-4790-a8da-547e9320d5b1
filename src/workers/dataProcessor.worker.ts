/**
 * Web Worker untuk Heavy Data Processing
 * 
 * Memindahkan calculations yang berat ke background thread untuk mencegah
 * blocking UI dan meningkatkan user experience.
 * 
 * Features:
 * - Top authors calculation dengan complex point system
 * - Category statistics processing
 * - Large dataset transformations
 * - Parallel processing untuk multiple operations
 * - Progress reporting untuk long-running tasks
 */

import { CategoryData } from '../services/api';

// Types untuk worker communication
export interface WorkerMessage {
  id: string;
  type: 'CALCULATE_TOP_AUTHORS' | 'CALCULATE_CATEGORY_STATS' | 'PROCESS_LARGE_DATASET' | 'BATCH_PROCESS';
  payload: any;
}

export interface WorkerResponse {
  id: string;
  type: 'SUCCESS' | 'ERROR' | 'PROGRESS';
  payload: any;
  error?: string;
}

export interface TopAuthor {
  author: string;
  count: number;
  points: number;
  rank: number;
}

export interface CategoryStats {
  totalItems: number;
  upItems: number;
  downItems: number;
  sameItems: number;
  uniqueAuthors: <AUTHORS>
}

/**
 * Calculate top authors dengan advanced point system
 */
const calculateTopAuthors = (data: CategoryData[]): TopAuthor[] => {
  if (!data || data.length === 0) return [];

  // Advanced point system dengan multiple factors
  const authorStats = data.reduce((acc, item) => {
    const author = item.Author;
    if (!author || author === 'Unknown Author' || author.trim() === '') {
      return acc;
    }

    if (!acc[author]) {
      acc[author] = { 
        count: 0, 
        totalPoints: 0,
        upCount: 0,
        downCount: 0,
        sameCount: 0,
        pageImprovements: 0
      };
    }

    acc[author].count += 1;

    // Enhanced point system
    switch (item.Status) {
      case 'Up':
        acc[author].totalPoints += 3;
        acc[author].upCount += 1;
        break;
      case 'Down':
        acc[author].totalPoints -= 1;
        acc[author].downCount += 1;
        break;
      case 'Same':
        acc[author].totalPoints += 1;
        acc[author].sameCount += 1;
        break;
    }

    // Bonus points untuk page improvements
    const pageImprovement = (item['Page Old'] || 0) - (item['Page New'] || 0);
    if (pageImprovement > 0) {
      acc[author].totalPoints += Math.min(pageImprovement * 0.1, 2); // Max 2 bonus points
      acc[author].pageImprovements += pageImprovement;
    }

    return acc;
  }, {} as Record<string, any>);

  // Convert to array dan sort dengan advanced ranking
  const authorsArray = Object.entries(authorStats)
    .map(([author, stats]) => ({
      author,
      count: stats.count,
      points: Math.round(stats.totalPoints * 100) / 100, // Round to 2 decimal places
      rank: 0, // Will be assigned after sorting
      upCount: stats.upCount,
      downCount: stats.downCount,
      sameCount: stats.sameCount,
      pageImprovements: stats.pageImprovements,
      consistency: stats.upCount / (stats.upCount + stats.downCount + stats.sameCount) // Consistency score
    }))
    .filter(item => item.author && item.author !== 'Unknown Author' && item.author.trim() !== '')
    .sort((a, b) => {
      // Primary sort: points
      if (b.points !== a.points) return b.points - a.points;
      // Secondary sort: consistency
      if (b.consistency !== a.consistency) return b.consistency - a.consistency;
      // Tertiary sort: count
      return b.count - a.count;
    })
    .map((item, index) => ({ ...item, rank: index + 1 }));

  return authorsArray;
};

/**
 * Calculate category statistics dengan detailed breakdown
 */
const calculateCategoryStats = (data: CategoryData[]): CategoryStats & { 
  averageChange: number;
  topPerformers: number;
  underPerformers: number;
  stableItems: number;
} => {
  const totalItems = data.length;
  const upItems = data.filter(item => item.Status === 'Up').length;
  const downItems = data.filter(item => item.Status === 'Down').length;
  const sameItems = data.filter(item => item.Status === 'Same').length;
  const uniqueAuthors = new Set(data.map(item => item.Author)).size;

  // Advanced metrics
  const changes = data.map(item => item.Change || 0);
  const averageChange = changes.reduce((sum, change) => sum + change, 0) / changes.length;
  
  const topPerformers = data.filter(item => (item.Change || 0) > 5).length;
  const underPerformers = data.filter(item => (item.Change || 0) < -5).length;
  const stableItems = data.filter(item => Math.abs(item.Change || 0) <= 2).length;

  return {
    totalItems,
    upItems,
    downItems,
    sameItems,
    uniqueAuthors,
    averageChange: Math.round(averageChange * 100) / 100,
    topPerformers,
    underPerformers,
    stableItems
  };
};

/**
 * Process large dataset dengan chunking dan progress reporting
 */
const processLargeDataset = (
  data: CategoryData[], 
  chunkSize: number = 1000,
  onProgress?: (progress: number) => void
): Promise<{ processedData: CategoryData[]; metadata: any }> => {
  return new Promise((resolve) => {
    const chunks = [];
    for (let i = 0; i < data.length; i += chunkSize) {
      chunks.push(data.slice(i, i + chunkSize));
    }

    const processedChunks: CategoryData[][] = [];
    let processedCount = 0;

    const processChunk = (chunkIndex: number) => {
      if (chunkIndex >= chunks.length) {
        const processedData = processedChunks.flat();
        const metadata = {
          totalItems: processedData.length,
          chunksProcessed: chunks.length,
          processingTime: Date.now()
        };
        resolve({ processedData, metadata });
        return;
      }

      const chunk = chunks[chunkIndex];
      
      // Simulate heavy processing
      const processedChunk = chunk.map(item => ({
        ...item,
        processed: true,
        processingTimestamp: Date.now(),
        // Add computed fields
        performanceScore: calculatePerformanceScore(item),
        trend: calculateTrend(item)
      }));

      processedChunks.push(processedChunk);
      processedCount += chunk.length;

      // Report progress
      const progress = (processedCount / data.length) * 100;
      if (onProgress) {
        onProgress(progress);
      }

      // Process next chunk asynchronously
      setTimeout(() => processChunk(chunkIndex + 1), 0);
    };

    processChunk(0);
  });
};

/**
 * Calculate performance score untuk item
 */
const calculatePerformanceScore = (item: CategoryData): number => {
  let score = 50; // Base score

  // Status impact
  switch (item.Status) {
    case 'Up': score += 30; break;
    case 'Down': score -= 20; break;
    case 'Same': score += 10; break;
  }

  // Page improvement impact
  const pageImprovement = (item['Page Old'] || 0) - (item['Page New'] || 0);
  score += Math.min(pageImprovement * 2, 20);

  // Order improvement impact
  const orderImprovement = (item['Order Old'] || 0) - (item['Order New'] || 0);
  score += Math.min(orderImprovement * 0.5, 10);

  return Math.max(0, Math.min(100, Math.round(score)));
};

/**
 * Calculate trend untuk item
 */
const calculateTrend = (item: CategoryData): 'improving' | 'declining' | 'stable' => {
  const change = item.Change || 0;
  if (change > 2) return 'improving';
  if (change < -2) return 'declining';
  return 'stable';
};

/**
 * Batch processing untuk multiple operations
 */
const batchProcess = async (operations: Array<{ type: string; data: any }>): Promise<any[]> => {
  const results = [];
  
  for (const operation of operations) {
    switch (operation.type) {
      case 'TOP_AUTHORS':
        results.push(calculateTopAuthors(operation.data));
        break;
      case 'CATEGORY_STATS':
        results.push(calculateCategoryStats(operation.data));
        break;
      case 'LARGE_DATASET':
        results.push(await processLargeDataset(operation.data));
        break;
      default:
        results.push(null);
    }
  }
  
  return results;
};

// Worker message handler
self.onmessage = async (event: MessageEvent<WorkerMessage>) => {
  const { id, type, payload } = event.data;

  try {
    let result;

    switch (type) {
      case 'CALCULATE_TOP_AUTHORS':
        result = calculateTopAuthors(payload.data);
        break;

      case 'CALCULATE_CATEGORY_STATS':
        result = calculateCategoryStats(payload.data);
        break;

      case 'PROCESS_LARGE_DATASET':
        result = await processLargeDataset(
          payload.data,
          payload.chunkSize,
          (progress) => {
            // Send progress updates
            self.postMessage({
              id,
              type: 'PROGRESS',
              payload: { progress }
            } as WorkerResponse);
          }
        );
        break;

      case 'BATCH_PROCESS':
        result = await batchProcess(payload.operations);
        break;

      default:
        throw new Error(`Unknown operation type: ${type}`);
    }

    // Send success response
    self.postMessage({
      id,
      type: 'SUCCESS',
      payload: result
    } as WorkerResponse);

  } catch (error) {
    // Send error response
    self.postMessage({
      id,
      type: 'ERROR',
      payload: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    } as WorkerResponse);
  }
};

export default null; // Required untuk TypeScript
