/**
 * Notification System Types and Interfaces
 * Comprehensive type definitions for advanced notification management
 */

export type NotificationType = 
  | 'success' 
  | 'error' 
  | 'warning' 
  | 'info' 
  | 'data_update' 
  | 'system' 
  | 'file_upload' 
  | 'analysis_complete';

export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

export type NotificationStatus = 'unread' | 'read' | 'archived';

export interface NotificationAction {
  id: string;
  label: string;
  action: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
}

export interface Notification {
  id: string;
  type: NotificationType;
  priority: NotificationPriority;
  status: NotificationStatus;
  title: string;
  message: string;
  timestamp: string;
  createdAt: Date;
  readAt?: Date;
  expiresAt?: Date;
  category?: string;
  source?: string;
  metadata?: Record<string, any>;
  actions?: NotificationAction[];
  persistent?: boolean;
  dismissible?: boolean;
}

export interface NotificationFilter {
  type?: NotificationType[];
  priority?: NotificationPriority[];
  status?: NotificationStatus[];
  category?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  search?: string;
}

export interface NotificationSettings {
  enabled: boolean;
  email: boolean;
  browser: boolean;
  sound: boolean;
  desktop: boolean;
  dataUpdates: boolean;
  systemAlerts: boolean;
  analysisComplete: boolean;
  fileOperations: boolean;
  priorities: {
    low: boolean;
    medium: boolean;
    high: boolean;
    urgent: boolean;
  };
  quietHours: {
    enabled: boolean;
    start: string; // HH:mm format
    end: string;   // HH:mm format
  };
}

export interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<NotificationType, number>;
  byPriority: Record<NotificationPriority, number>;
  todayCount: number;
  weekCount: number;
}

export interface CreateNotificationRequest {
  type: NotificationType;
  priority?: NotificationPriority;
  title: string;
  message: string;
  category?: string;
  source?: string;
  metadata?: Record<string, any>;
  actions?: Omit<NotificationAction, 'action'>[];
  persistent?: boolean;
  dismissible?: boolean;
  expiresAt?: Date;
}

export interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  stats: NotificationStats;
  settings: NotificationSettings;
  filter: NotificationFilter;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  createNotification: (notification: CreateNotificationRequest) => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  archiveNotification: (id: string) => Promise<void>;
  updateSettings: (settings: Partial<NotificationSettings>) => Promise<void>;
  setFilter: (filter: Partial<NotificationFilter>) => void;
  clearFilter: () => void;
  refreshNotifications: () => Promise<void>;
  
  // Bulk operations
  bulkMarkAsRead: (ids: string[]) => Promise<void>;
  bulkDelete: (ids: string[]) => Promise<void>;
  bulkArchive: (ids: string[]) => Promise<void>;
}

export interface NotificationApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface NotificationListResponse {
  notifications: Notification[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Utility types for notification creation
export type SystemNotification = {
  type: 'system';
  category: 'maintenance' | 'update' | 'security' | 'performance';
};

export type DataNotification = {
  type: 'data_update';
  category: 'scraping' | 'processing' | 'analysis' | 'export';
  metadata: {
    competitor?: string;
    category?: string;
    recordCount?: number;
    processingTime?: number;
  };
};

export type FileNotification = {
  type: 'file_upload';
  category: 'upload' | 'processing' | 'validation' | 'error';
  metadata: {
    filename: string;
    fileSize?: number;
    fileType?: string;
    uploadTime?: number;
  };
};

export type AnalysisNotification = {
  type: 'analysis_complete';
  category: 'competitor' | 'category' | 'author' | 'trend';
  metadata: {
    analysisType: string;
    duration?: number;
    resultCount?: number;
    competitor?: string;
  };
};

// Event types for real-time updates
export interface NotificationEvent {
  type: 'notification_created' | 'notification_updated' | 'notification_deleted' | 'settings_updated';
  data: any;
  timestamp: string;
}

// Sound notification types
export type NotificationSound = 'default' | 'success' | 'error' | 'warning' | 'urgent' | 'none';

export interface SoundSettings {
  enabled: boolean;
  volume: number; // 0-1
  sounds: Record<NotificationType, NotificationSound>;
}
