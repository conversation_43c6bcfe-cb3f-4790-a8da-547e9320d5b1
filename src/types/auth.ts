/**
 * Authentication Types
 * 
 * TypeScript interfaces and types for OTP authentication system
 */

// User interface
export interface User {
  email: string;
  authenticated_at: string;
  session_token?: string;
  last_activity?: string;
  expires_at?: string;
}

// Authentication state
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  sessionToken: string | null;
  refreshToken: string | null;
}

// OTP flow states
export type OTPFlowState = 
  | 'idle'           // Initial state
  | 'generating'     // Generating OTP
  | 'waiting'        // Waiting for Chrome Extension response
  | 'validating'     // Validating OTP
  | 'success'        // Authentication successful
  | 'error'          // Error occurred
  | 'expired'        // OTP expired
  | 'rejected';      // User rejected OTP

// OTP data interface
export interface OTPData {
  email: string;
  otp_key: string;
  otp_code: string;
  expires_in: number;
  created_at: string;
}

// Chrome Extension message types
export type ExtensionMessageType = 
  | 'OTP_REQUEST'
  | 'OTP_RESPONSE'
  | 'OTP_REJECTED'
  | 'EXTENSION_READY'
  | 'EXTENSION_ERROR';

// Chrome Extension message interface
export interface ExtensionMessage {
  type: ExtensionMessageType;
  data?: any;
  timestamp: number;
  origin?: string;
}

// OTP request to Chrome Extension
export interface OTPRequest extends ExtensionMessage {
  type: 'OTP_REQUEST';
  data: {
    email: string;
    otp_code: string;
    otp_key: string;
    expires_in: number;
    website: string;
  };
}

// OTP response from Chrome Extension
export interface OTPResponse extends ExtensionMessage {
  type: 'OTP_RESPONSE' | 'OTP_REJECTED';
  data: {
    action: 'APPROVE' | 'REJECT';
    email: string;
    otp_code?: string;
    otp_key?: string;
  };
}

// API response interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  message?: string;
}

// Generate OTP API response
export interface GenerateOTPResponse {
  success: boolean;
  message: string;
  email: string;
  otp_key: string;
  expires_in: number;
  created_at: string;
  otp_code: string; // For Chrome Extension
}

// Validate OTP API response
export interface ValidateOTPResponse {
  success: boolean;
  message: string;
  session_token: string;
  refresh_token: string;
  expires_in: number;
  refresh_expires_in: number;
  user: User;
}

// Session info API response
export interface SessionInfoResponse {
  success: boolean;
  user: User;
}

// Login form data
export interface LoginFormData {
  email: string;
}

// Login form state
export interface LoginFormState {
  email: string;
  isLoading: boolean;
  error: string | null;
  isValid: boolean;
}

// OTP waiting state
export interface OTPWaitingState {
  email: string;
  otp_key: string;
  expires_at: string;
  time_remaining: number;
  status: OTPFlowState;
  can_resend: boolean;
  resend_cooldown: number;
}

// Authentication context interface
export interface AuthContextType {
  // State
  authState: AuthState;
  otpFlowState: OTPFlowState;
  
  // Actions
  login: (email: string) => Promise<void>;
  logout: () => Promise<void>;
  logoutAll: () => Promise<void>;
  refreshSession: () => Promise<void>;
  
  // OTP flow
  generateOTP: (email: string) => Promise<void>;
  validateOTP: (email: string, otpCode: string, otpKey?: string) => Promise<void>;
  resetOTPFlow: () => void;
  
  // Utilities
  isSessionValid: () => boolean;
  getTimeUntilExpiry: () => number;
  checkAuthStatus: () => Promise<void>;
}

// Chrome Extension service interface
export interface ExtensionServiceType {
  // Connection
  isExtensionAvailable: () => boolean;
  checkExtensionConnection: () => Promise<boolean>;
  
  // Communication
  sendOTPRequest: (otpData: OTPData) => Promise<OTPResponse>;
  listenForResponses: (callback: (response: OTPResponse) => void) => void;
  stopListening: () => void;
  
  // Utilities
  getExtensionInfo: () => Promise<any>;
}

// Auth service interface
export interface AuthServiceType {
  // OTP operations
  generateOTP: (email: string) => Promise<ApiResponse<GenerateOTPResponse>>;
  validateOTP: (email: string, otpCode: string, otpKey?: string) => Promise<ApiResponse<ValidateOTPResponse>>;
  
  // Session operations
  refreshSession: (refreshToken: string) => Promise<ApiResponse<ValidateOTPResponse>>;
  logout: (sessionToken: string) => Promise<ApiResponse>;
  logoutAll: (sessionToken: string) => Promise<ApiResponse>;
  getSessionInfo: (sessionToken: string) => Promise<ApiResponse<SessionInfoResponse>>;
  
  // Health check
  healthCheck: () => Promise<ApiResponse>;
}

// Protected route props
export interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

// Login page props
export interface LoginPageProps {
  onSuccess?: (user: User) => void;
  redirectTo?: string;
  showHeader?: boolean;
  showFooter?: boolean;
}

// Error types
export type AuthErrorCode = 
  | 'EMAIL_REQUIRED'
  | 'INVALID_EMAIL'
  | 'RATE_LIMIT_EXCEEDED'
  | 'OTP_NOT_FOUND'
  | 'OTP_EXPIRED'
  | 'INVALID_OTP'
  | 'MAX_ATTEMPTS_EXCEEDED'
  | 'SESSION_NOT_FOUND'
  | 'SESSION_EXPIRED'
  | 'EXTENSION_NOT_AVAILABLE'
  | 'EXTENSION_ERROR'
  | 'NETWORK_ERROR'
  | 'INTERNAL_ERROR';

// Auth error interface
export interface AuthError {
  code: AuthErrorCode;
  message: string;
  details?: any;
}

// Local storage keys
export const AUTH_STORAGE_KEYS = {
  SESSION_TOKEN: 'auth_session_token',
  REFRESH_TOKEN: 'auth_refresh_token',
  USER_DATA: 'auth_user_data',
  LAST_EMAIL: 'auth_last_email'
} as const;

// Configuration constants
export const AUTH_CONFIG = {
  OTP_VALIDITY_SECONDS: 300,        // 5 minutes
  SESSION_DURATION: 24 * 60 * 60,   // 24 hours
  REFRESH_DURATION: 7 * 24 * 60 * 60, // 7 days
  RESEND_COOLDOWN: 60,               // 1 minute
  MAX_OTP_ATTEMPTS: 3,
  EXTENSION_TIMEOUT: 30000,          // 30 seconds
  API_TIMEOUT: 10000                 // 10 seconds
} as const;

// Export all types
export type {
  User,
  AuthState,
  OTPFlowState,
  OTPData,
  ExtensionMessage,
  OTPRequest,
  OTPResponse,
  ApiResponse,
  GenerateOTPResponse,
  ValidateOTPResponse,
  SessionInfoResponse,
  LoginFormData,
  LoginFormState,
  OTPWaitingState,
  AuthContextType,
  ExtensionServiceType,
  AuthServiceType,
  ProtectedRouteProps,
  LoginPageProps,
  AuthError,
  AuthErrorCode
};
