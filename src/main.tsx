import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import App from './App.tsx';
import './index.css';
import queryClient from './config/queryClient';
import ReactQueryDevtoolsWrapper from './components/ReactQueryDevtools';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <App />
      </BrowserRouter>
      {/* React Query DevTools - Hidden by default, multiple activation methods */}
      <ReactQueryDevtoolsWrapper />
    </QueryClientProvider>
  </StrictMode>
);