import { QueryClient } from '@tanstack/react-query';

/**
 * React Query Client Configuration
 * 
 * Optimal configuration untuk Categories data dengan focus pada:
 * - Performance optimization
 * - Intelligent caching strategies
 * - Error handling dan retry logic
 * - Background refetching
 * - Memory management
 */

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Caching Strategy
      staleTime: 5 * 60 * 1000, // 5 minutes - data considered fresh
      gcTime: 10 * 60 * 1000, // 10 minutes - cache time (formerly cacheTime)
      
      // Retry Logic
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors (client errors)
        if (error && typeof error === 'object' && 'status' in error) {
          const status = (error as any).status;
          if (status >= 400 && status < 500) {
            return false;
          }
        }
        
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      
      // Background Refetching
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
      refetchOnMount: true,
      
      // Network Mode
      networkMode: 'online',
      
      // Error handling
      throwOnError: false,
      
      // Placeholder data
      placeholderData: (previousData) => previousData,
    },
    mutations: {
      // Retry mutations once
      retry: 1,
      retryDelay: 1000,
      
      // Network mode
      networkMode: 'online',
    },
  },
});

/**
 * Query Client dengan Development Tools
 * Hanya untuk development environment
 */
export const createQueryClientWithDevtools = () => {
  const client = new QueryClient({
    ...queryClient.getDefaultOptions(),
    defaultOptions: {
      ...queryClient.getDefaultOptions(),
      queries: {
        ...queryClient.getDefaultOptions().queries,
        // Development-specific settings
        staleTime: 1 * 60 * 1000, // 1 minute in dev for faster testing
        gcTime: 5 * 60 * 1000, // 5 minutes in dev
      },
    },
  });

  return client;
};

/**
 * Production Query Client
 * Optimized untuk production environment
 */
export const createProductionQueryClient = () => {
  const client = new QueryClient({
    ...queryClient.getDefaultOptions(),
    defaultOptions: {
      ...queryClient.getDefaultOptions(),
      queries: {
        ...queryClient.getDefaultOptions().queries,
        // Production-specific settings
        staleTime: 10 * 60 * 1000, // 10 minutes in production
        gcTime: 30 * 60 * 1000, // 30 minutes in production
        
        // More conservative refetching in production
        refetchOnWindowFocus: false,
        refetchOnMount: false,
      },
    },
  });

  return client;
};

/**
 * Categories-specific Query Client
 * Specialized configuration untuk Categories data patterns
 */
export const createCategoriesQueryClient = () => {
  const client = new QueryClient({
    defaultOptions: {
      queries: {
        // Categories data caching strategy
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 15 * 60 * 1000, // 15 minutes
        
        // Retry strategy untuk Categories API
        retry: (failureCount, error) => {
          // Categories API specific error handling
          if (error && typeof error === 'object') {
            const errorMessage = (error as any).message || '';
            
            // Don't retry on specific API errors
            if (errorMessage.includes('Category not found') || 
                errorMessage.includes('Invalid category')) {
              return false;
            }
          }
          
          return failureCount < 2; // Max 2 retries for categories
        },
        retryDelay: (attemptIndex) => Math.min(500 * 2 ** attemptIndex, 5000),
        
        // Background refetching strategy
        refetchOnWindowFocus: true,
        refetchOnReconnect: true,
        refetchOnMount: true,
        
        // Optimistic updates support
        placeholderData: (previousData, previousQuery) => {
          // Keep previous data while loading new data
          return previousData;
        },
        
        // Error boundary integration
        throwOnError: (error, query) => {
          // Only throw on critical errors
          if (error && typeof error === 'object') {
            const errorMessage = (error as any).message || '';
            return errorMessage.includes('Critical') || errorMessage.includes('Fatal');
          }
          return false;
        },
      },
      mutations: {
        // Categories mutations configuration
        retry: 1,
        retryDelay: 1000,
        
        // Optimistic updates
        onMutate: async () => {
          // Cancel outgoing refetches
          // This will be handled in specific mutation hooks
        },
        
        onError: (error, variables, context) => {
          // Global error handling for categories mutations
          console.error('Categories mutation error:', error);
        },
        
        onSettled: () => {
          // Cleanup after mutation
          // This will be handled in specific mutation hooks
        },
      },
    },
  });

  return client;
};

/**
 * Environment-aware Query Client Factory
 */
export const createQueryClient = (environment: 'development' | 'production' | 'test' = 'development') => {
  switch (environment) {
    case 'production':
      return createProductionQueryClient();
    case 'test':
      return new QueryClient({
        defaultOptions: {
          queries: {
            retry: false,
            gcTime: 0,
            staleTime: 0,
          },
          mutations: {
            retry: false,
          },
        },
      });
    case 'development':
    default:
      return createQueryClientWithDevtools();
  }
};

/**
 * Default export - Categories-optimized client
 */
export default createCategoriesQueryClient();
