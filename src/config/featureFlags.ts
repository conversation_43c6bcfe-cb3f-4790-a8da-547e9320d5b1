/**
 * Feature Flags Configuration
 * 
 * This file controls the rollout of new component implementations.
 * All flags default to false to ensure existing functionality continues working.
 * 
 * Safety Guidelines:
 * - Always set new flags to false initially
 * - Test thoroughly before enabling any flag
 * - Flags can be toggled instantly for immediate rollback
 * - Each flag controls a specific component or feature
 */

export interface FeatureFlags {
  // Phase 2: Component Splitting Flags
  USE_NEW_CATEGORY_TABS: boolean;
  USE_NEW_CATEGORY_TABLE: boolean;
  USE_NEW_TOP_AUTHORS_PANEL: boolean;
  USE_NEW_CATEGORY_STATS: boolean;
  USE_NEW_PAGINATION_CONTROLS: boolean;
  
  // Phase 3: Custom Hooks Extraction
  USE_CATEGORY_STATS_HOOK: boolean;
  USE_CATEGORY_DATA_HOOK: boolean;
  USE_CATEGORY_NAVIGATION_HOOK: boolean;
  USE_TOP_AUTHORS_HOOK: boolean;
  USE_PAGINATION_HOOK: boolean;

  // Phase 4: Advanced Component Splitting
  USE_NEW_PAGINATION_CONTROLS: boolean;
  USE_NEW_CATEGORY_TABLE: boolean;
  USE_NEW_TOP_AUTHORS_PANEL: boolean;
  USE_NEW_AUTHOR_DETAILS_MODAL: boolean;

  // Phase 4: Advanced Features (for future use)
  USE_NEW_CATEGORY_HOOKS: boolean;
  USE_NEW_API_SERVICE: boolean;
  USE_VIRTUAL_SCROLLING: boolean;
  USE_DEBOUNCED_SEARCH: boolean;
  
  // Phase 4: Architecture Changes (for future use)
  USE_NEW_STATE_MANAGEMENT: boolean;
  USE_NEW_ERROR_HANDLING: boolean;
  USE_PERFORMANCE_MONITORING: boolean;
}

/**
 * Feature Flags Configuration
 * 
 * 🚨 SAFETY FIRST: All flags default to false
 * This ensures existing code continues running unchanged
 */
export const FEATURE_FLAGS: FeatureFlags = {
  // Phase 2: Component Splitting - START WITH FALSE
  USE_NEW_CATEGORY_TABS: false,        // CategoryTabs component
  USE_NEW_CATEGORY_TABLE: false,       // CategoryTable component  
  USE_NEW_TOP_AUTHORS_PANEL: false,    // TopAuthorsPanel component
  USE_NEW_CATEGORY_STATS: false,       // CategoryStats component
  USE_NEW_PAGINATION_CONTROLS: false,  // PaginationControls component
  
  // Phase 3: Custom Hooks Extraction - START WITH FALSE
  USE_CATEGORY_STATS_HOOK: false,      // useCategoryStats hook
  USE_CATEGORY_DATA_HOOK: false,       // useCategoryData hook
  USE_CATEGORY_NAVIGATION_HOOK: false, // useCategoryNavigation hook
  USE_TOP_AUTHORS_HOOK: false,         // useTopAuthors hook
  USE_PAGINATION_HOOK: false,          // usePagination hook

  // Phase 4: Advanced Component Splitting - START WITH FALSE
  USE_NEW_PAGINATION_CONTROLS: false,  // PaginationControls component
  USE_NEW_CATEGORY_TABLE: false,       // CategoryTable component
  USE_NEW_TOP_AUTHORS_PANEL: false,    // TopAuthorsPanel component
  USE_NEW_AUTHOR_DETAILS_MODAL: false, // AuthorDetailsModal component

  // Phase 4: Advanced Features - DISABLED
  USE_NEW_CATEGORY_HOOKS: false,       // Custom hooks extraction
  USE_NEW_API_SERVICE: false,          // Service layer refactoring
  USE_VIRTUAL_SCROLLING: false,        // Virtual scrolling for large datasets
  USE_DEBOUNCED_SEARCH: false,         // Debounced search functionality
  
  // Phase 4: Architecture Changes - DISABLED  
  USE_NEW_STATE_MANAGEMENT: false,     // State management refactoring
  USE_NEW_ERROR_HANDLING: false,       // Enhanced error handling
  USE_PERFORMANCE_MONITORING: false,   // Performance monitoring tools
};

/**
 * Feature Flag Utilities
 */
export class FeatureFlagManager {
  private static flags = { ...FEATURE_FLAGS };

  /**
   * Get current value of a feature flag
   */
  static isEnabled(flag: keyof FeatureFlags): boolean {
    return this.flags[flag];
  }

  /**
   * Enable a feature flag (for testing/debugging)
   * ⚠️ Use with caution in production
   */
  static enable(flag: keyof FeatureFlags): void {
    this.flags[flag] = true;
    console.log(`🚩 Feature flag enabled: ${flag}`);
  }

  /**
   * Disable a feature flag (for immediate rollback)
   */
  static disable(flag: keyof FeatureFlags): void {
    this.flags[flag] = false;
    console.log(`🚩 Feature flag disabled: ${flag}`);
  }

  /**
   * Emergency rollback - disable all new features
   * 🚨 Use this for immediate rollback to stable state
   */
  static emergencyRollback(): void {
    Object.keys(this.flags).forEach(key => {
      this.flags[key as keyof FeatureFlags] = false;
    });
    console.warn('🚨 EMERGENCY ROLLBACK: All feature flags disabled');
    
    // Force page reload to apply changes immediately
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  }

  /**
   * Get all current flag states (for debugging)
   */
  static getAllFlags(): FeatureFlags {
    return { ...this.flags };
  }

  /**
   * Reset flags to default configuration
   */
  static reset(): void {
    this.flags = { ...FEATURE_FLAGS };
    console.log('🔄 Feature flags reset to default configuration');
  }
}

/**
 * Development utilities
 * These are available in the browser console for testing
 */
if (typeof window !== 'undefined') {
  // Make feature flag manager available globally for debugging
  (window as any).FeatureFlags = FeatureFlagManager;
  
  // Quick access functions for common operations
  (window as any).enableCategoryTabs = () => FeatureFlagManager.enable('USE_NEW_CATEGORY_TABS');
  (window as any).disableCategoryTabs = () => FeatureFlagManager.disable('USE_NEW_CATEGORY_TABS');
  (window as any).emergencyRollback = () => FeatureFlagManager.emergencyRollback();
  
  console.log('🚩 Feature flags available in console:');
  console.log('  - FeatureFlags.enable("USE_NEW_CATEGORY_TABS")');
  console.log('  - FeatureFlags.disable("USE_NEW_CATEGORY_TABS")');
  console.log('  - FeatureFlags.emergencyRollback()');
  console.log('  - enableCategoryTabs() / disableCategoryTabs()');
}

export default FEATURE_FLAGS;
