import React, { useState, useCallback } from 'react';
import { Routes, Route, Navigate, useParams, useLocation } from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import AnalysisLayout from './components/AnalysisLayout';
import { NotificationProvider } from './contexts/NotificationContext';
import { AuthProvider } from './contexts/AuthContext';
import {
  FileManagement,
  SystemSettings,
  preloadFileManagement,
  preloadSystemSettings,
  usePreloadOnHover
} from './components/LazyComponents';
import { useRenderTime, performanceUtils } from './hooks/usePerformance';
// Import devtools utilities for development
import './utils/devtools';
// Import Backend Dashboard Routes
import BackendRoutes from './components/backend/BackendRoutes';
// Import Authentication Pages
import LoginPage from './pages/LoginPage';
import { ProtectedRoute } from './components/auth';

// Admin SystemSettings wrapper component for dynamic routing
const AdminSystemSettings: React.FC = () => {
  const { tab } = useParams<{ tab?: string }>();
  return (
    <SystemSettings
      adminMode={true}
      userRole="admin"
      initialTab={tab || 'overview'}
    />
  );
};

function App() {
  // 🔥 Hot reload AKTIF - perubahan ini akan langsung terlihat di browser!
  // ✨ Test edit: Aplikasi terus berjalan saat diedit!

  // Performance monitoring
  const { averageRenderTime } = useRenderTime('App');



  // State management
  const [activeAnalysisTab, setActiveAnalysisTab] = useState<'categories' | 'competitors'>('categories');
  const [showSystemSettings, setShowSystemSettings] = useState(false);
  const [systemSettingsTab, setSystemSettingsTab] = useState('overview');
  const [showFileManagement, setShowFileManagement] = useState(false);

  // Preload hooks for better UX
  const fileManagementPreload = usePreloadOnHover(preloadFileManagement);
  const systemSettingsPreload = usePreloadOnHover(preloadSystemSettings);

  const handleSystemSettingsClick = useCallback((tab?: string) => {
    // If a specific tab is provided, we want to show System Settings
    if (tab) {
      setSystemSettingsTab(tab);
      setShowSystemSettings(true);
      setShowFileManagement(false);
    } else {
      // Only toggle if no specific tab is provided (direct Settings button click)
      setShowSystemSettings(!showSystemSettings);
      setShowFileManagement(false);
    }
  }, [showSystemSettings]);

  const handleFileManagementClick = useCallback(() => {
    setShowFileManagement(!showFileManagement);
    setShowSystemSettings(false);
  }, [showFileManagement]);

  const handleAnalysisTabChange = useCallback((tab: 'categories' | 'competitors') => {
    setActiveAnalysisTab(tab);
    // Reset other states when switching to analysis
    setShowSystemSettings(false);
    setShowFileManagement(false);
  }, []);

  // App Layout Component (inside Router context)
  const AppLayout: React.FC = () => {
    const location = useLocation();
    const isBackendDashboard = location.pathname.startsWith('/dashboard');
    const isLoginPage = location.pathname === '/login';
    const shouldShowHeaderFooter = !isBackendDashboard && !isLoginPage;

    return (
      <div className="min-h-screen bg-gradient-to-br from-theme-seashell via-white to-theme-seashell/50">
        <div className="flex flex-col min-h-screen">
          {/* Only show Header for non-backend dashboard and non-login routes */}
          {shouldShowHeaderFooter && <Header />}
          <main className={`flex-1 ${shouldShowHeaderFooter ? 'pt-14' : ''}`}>
            <Routes>
              {/* Authentication routes */}
              <Route path="/login" element={<LoginPage />} />

              {/* Backend Dashboard routes - Require authentication */}
              <Route path="/dashboard/*" element={
                <ProtectedRoute requireAuth={true} redirectTo="/login">
                  <BackendRoutes />
                </ProtectedRoute>
              } />

              {/* Conditional routes based on state */}
              <Route path="*" element={
                showSystemSettings ? (
                  <ProtectedRoute requireAuth={true} redirectTo="/login">
                    <SystemSettings initialTab={systemSettingsTab} />
                  </ProtectedRoute>
                ) : showFileManagement ? (
                  <ProtectedRoute requireAuth={true} redirectTo="/login">
                    <FileManagement />
                  </ProtectedRoute>
                ) : (
                  <Routes>
                    <Route path="/" element={<Navigate to="/analysis" replace />} />

                    {/* Protected routes - require authentication */}
                    <Route path="/analysis" element={
                      <ProtectedRoute requireAuth={true} redirectTo="/login">
                        <AnalysisLayout
                          title="Analysis Dashboard"
                          subtitle="Analyze categories and competitors data with advanced insights"
                          activeTab={activeAnalysisTab}
                        />
                      </ProtectedRoute>
                    } />

                    <Route path="/files" element={
                      <ProtectedRoute requireAuth={true} redirectTo="/login">
                        <FileManagement />
                      </ProtectedRoute>
                    } />

                    {/* Admin routes - require authentication */}
                    <Route path="/admin" element={
                      <ProtectedRoute requireAuth={true} redirectTo="/login">
                        <AdminSystemSettings />
                      </ProtectedRoute>
                    } />
                    <Route path="/admin/:tab" element={
                      <ProtectedRoute requireAuth={true} redirectTo="/login">
                        <AdminSystemSettings />
                      </ProtectedRoute>
                    } />
                  </Routes>
                )
              } />
            </Routes>
          </main>
          {/* Only show Footer for non-backend dashboard and non-login routes */}
          {shouldShowHeaderFooter && <Footer />}
        </div>
      </div>
    );
  };

  return (
    <AuthProvider>
      <NotificationProvider>
        <AppLayout />
      </NotificationProvider>
    </AuthProvider>
  );
}

export default App;