#!/usr/bin/env python3
"""
Configuration Optimizer
Automatically optimizes configuration settings based on system performance and usage patterns
"""

import os
import time
import psutil
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime
import logging

# Import modules
from config import config
from performance_validator import PerformanceValidator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConfigurationOptimizer:
    """Optimizes configuration settings for best performance"""
    
    def __init__(self):
        self.validator = PerformanceValidator()
        self.optimization_history = []
        self.current_config = {}
        self.baseline_performance = {}
    
    def analyze_system_resources(self) -> Dict[str, Any]:
        """Analyze available system resources"""
        logger.info("🔍 Analyzing system resources...")
        
        try:
            # CPU information
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory information
            memory = psutil.virtual_memory()
            
            # Disk information
            disk = psutil.disk_usage('/')
            
            # Network information (if available)
            network = psutil.net_io_counters()
            
            analysis = {
                'cpu': {
                    'cores': cpu_count,
                    'frequency_mhz': cpu_freq.current if cpu_freq else None,
                    'current_usage_percent': cpu_percent,
                    'classification': self._classify_cpu_power(cpu_count, cpu_freq)
                },
                'memory': {
                    'total_gb': round(memory.total / (1024**3), 2),
                    'available_gb': round(memory.available / (1024**3), 2),
                    'usage_percent': memory.percent,
                    'classification': self._classify_memory_capacity(memory.total)
                },
                'disk': {
                    'total_gb': round(disk.total / (1024**3), 2),
                    'free_gb': round(disk.free / (1024**3), 2),
                    'usage_percent': round((disk.used / disk.total) * 100, 1),
                    'classification': self._classify_disk_capacity(disk.total)
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                } if network else None,
                'overall_classification': 'unknown'
            }
            
            # Overall system classification
            analysis['overall_classification'] = self._classify_overall_system(analysis)
            
            logger.info(f"💻 System: {analysis['overall_classification']} "
                       f"({analysis['cpu']['cores']} cores, "
                       f"{analysis['memory']['total_gb']}GB RAM)")
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Error analyzing system resources: {e}")
            return {'error': str(e)}
    
    def _classify_cpu_power(self, cores: int, freq_info) -> str:
        """Classify CPU power level"""
        if cores >= 8:
            return 'high_performance'
        elif cores >= 4:
            return 'medium_performance'
        else:
            return 'low_performance'
    
    def _classify_memory_capacity(self, total_bytes: int) -> str:
        """Classify memory capacity"""
        total_gb = total_bytes / (1024**3)
        if total_gb >= 16:
            return 'high_capacity'
        elif total_gb >= 8:
            return 'medium_capacity'
        else:
            return 'low_capacity'
    
    def _classify_disk_capacity(self, total_bytes: int) -> str:
        """Classify disk capacity"""
        total_gb = total_bytes / (1024**3)
        if total_gb >= 500:
            return 'high_capacity'
        elif total_gb >= 100:
            return 'medium_capacity'
        else:
            return 'low_capacity'
    
    def _classify_overall_system(self, analysis: Dict[str, Any]) -> str:
        """Classify overall system performance level"""
        cpu_class = analysis['cpu']['classification']
        memory_class = analysis['memory']['classification']
        
        if cpu_class == 'high_performance' and memory_class == 'high_capacity':
            return 'high_performance'
        elif cpu_class in ['medium_performance', 'high_performance'] and memory_class in ['medium_capacity', 'high_capacity']:
            return 'medium_performance'
        else:
            return 'low_performance'
    
    def generate_optimal_config(self, 
                              system_analysis: Dict[str, Any],
                              usage_pattern: str = 'balanced') -> Dict[str, Any]:
        """
        Generate optimal configuration based on system analysis
        
        Args:
            system_analysis: System resource analysis
            usage_pattern: Expected usage pattern ('light', 'balanced', 'heavy')
            
        Returns:
            Optimal configuration settings
        """
        logger.info(f"⚙️ Generating optimal config for {system_analysis.get('overall_classification', 'unknown')} system")
        
        optimal_config = {}
        
        # Base configuration templates
        config_templates = {
            'high_performance': {
                'CACHE_DEFAULT_TIMEOUT': 3600,  # 1 hour
                'MAX_CACHE_SIZE': 10 * 1024 * 1024,  # 10MB
                'CIRCUIT_BREAKER_FAILURE_THRESHOLD': 10,
                'CIRCUIT_BREAKER_RECOVERY_TIMEOUT': 30,
                'FILE_MONITOR_QUEUE_SIZE': 200,
                'ENABLE_PERFORMANCE_MONITORING': True,
                'SLOW_REQUEST_THRESHOLD': 2.0
            },
            'medium_performance': {
                'CACHE_DEFAULT_TIMEOUT': 1800,  # 30 minutes
                'MAX_CACHE_SIZE': 5 * 1024 * 1024,  # 5MB
                'CIRCUIT_BREAKER_FAILURE_THRESHOLD': 5,
                'CIRCUIT_BREAKER_RECOVERY_TIMEOUT': 60,
                'FILE_MONITOR_QUEUE_SIZE': 100,
                'ENABLE_PERFORMANCE_MONITORING': True,
                'SLOW_REQUEST_THRESHOLD': 1.0
            },
            'low_performance': {
                'CACHE_DEFAULT_TIMEOUT': 900,  # 15 minutes
                'MAX_CACHE_SIZE': 2 * 1024 * 1024,  # 2MB
                'CIRCUIT_BREAKER_FAILURE_THRESHOLD': 3,
                'CIRCUIT_BREAKER_RECOVERY_TIMEOUT': 120,
                'FILE_MONITOR_QUEUE_SIZE': 50,
                'ENABLE_PERFORMANCE_MONITORING': False,
                'SLOW_REQUEST_THRESHOLD': 0.5
            }
        }
        
        # Get base template
        system_class = system_analysis.get('overall_classification', 'medium_performance')
        base_config = config_templates.get(system_class, config_templates['medium_performance'])
        optimal_config.update(base_config)
        
        # Adjust based on usage pattern
        if usage_pattern == 'heavy':
            optimal_config['CACHE_DEFAULT_TIMEOUT'] *= 2
            optimal_config['MAX_CACHE_SIZE'] = int(optimal_config['MAX_CACHE_SIZE'] * 1.5)
            optimal_config['CIRCUIT_BREAKER_FAILURE_THRESHOLD'] += 2
        elif usage_pattern == 'light':
            optimal_config['CACHE_DEFAULT_TIMEOUT'] = int(optimal_config['CACHE_DEFAULT_TIMEOUT'] * 0.5)
            optimal_config['MAX_CACHE_SIZE'] = int(optimal_config['MAX_CACHE_SIZE'] * 0.7)
            optimal_config['CIRCUIT_BREAKER_FAILURE_THRESHOLD'] = max(2, optimal_config['CIRCUIT_BREAKER_FAILURE_THRESHOLD'] - 1)
        
        # Memory-based adjustments
        memory_gb = system_analysis.get('memory', {}).get('total_gb', 8)
        if memory_gb < 4:
            optimal_config['MAX_CACHE_SIZE'] = min(optimal_config['MAX_CACHE_SIZE'], 1024 * 1024)  # 1MB max
            optimal_config['ENABLE_FILE_MONITORING'] = False
        elif memory_gb > 16:
            optimal_config['MAX_CACHE_SIZE'] = int(optimal_config['MAX_CACHE_SIZE'] * 2)
        
        # CPU-based adjustments
        cpu_cores = system_analysis.get('cpu', {}).get('cores', 4)
        if cpu_cores < 2:
            optimal_config['FILE_MONITOR_QUEUE_SIZE'] = min(optimal_config['FILE_MONITOR_QUEUE_SIZE'], 25)
        elif cpu_cores > 8:
            optimal_config['FILE_MONITOR_QUEUE_SIZE'] = int(optimal_config['FILE_MONITOR_QUEUE_SIZE'] * 1.5)
        
        # Feature enablement recommendations
        optimal_config['ENABLE_OPTIMIZATIONS'] = True
        optimal_config['ENABLE_COMPRESSION'] = True
        
        if system_class in ['medium_performance', 'high_performance']:
            optimal_config['ENABLE_CACHING'] = True
            optimal_config['ENABLE_FILE_MONITORING'] = True
        else:
            optimal_config['ENABLE_CACHING'] = memory_gb >= 2
            optimal_config['ENABLE_FILE_MONITORING'] = cpu_cores >= 2
        
        logger.info(f"⚙️ Generated config: Cache={optimal_config['MAX_CACHE_SIZE']//1024//1024}MB, "
                   f"Timeout={optimal_config['CACHE_DEFAULT_TIMEOUT']}s")
        
        return optimal_config
    
    def apply_configuration(self, new_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply new configuration settings
        
        Args:
            new_config: New configuration to apply
            
        Returns:
            Application result
        """
        logger.info("🔧 Applying optimized configuration...")
        
        result = {
            'timestamp': datetime.now().isoformat(),
            'applied_settings': {},
            'skipped_settings': {},
            'errors': []
        }
        
        for key, value in new_config.items():
            try:
                # Update environment variable
                os.environ[key] = str(value)
                
                # Update config object if it exists
                if hasattr(config, key):
                    setattr(config, key, value)
                    result['applied_settings'][key] = value
                    logger.debug(f"  ✅ {key} = {value}")
                else:
                    result['skipped_settings'][key] = f"Config attribute {key} not found"
                    logger.warning(f"  ⚠️ Skipped {key}: attribute not found")
                    
            except Exception as e:
                result['errors'].append(f"Error setting {key}: {e}")
                logger.error(f"  ❌ Error setting {key}: {e}")
        
        logger.info(f"🔧 Applied {len(result['applied_settings'])} settings, "
                   f"skipped {len(result['skipped_settings'])}, "
                   f"{len(result['errors'])} errors")
        
        return result
    
    def optimize_configuration(self, 
                             csv_data_dir: str,
                             usage_pattern: str = 'balanced',
                             validate_performance: bool = True) -> Dict[str, Any]:
        """
        Complete configuration optimization process
        
        Args:
            csv_data_dir: CSV data directory
            usage_pattern: Expected usage pattern
            validate_performance: Whether to validate performance after optimization
            
        Returns:
            Optimization results
        """
        logger.info("🚀 Starting configuration optimization...")
        
        optimization_result = {
            'timestamp': datetime.now().isoformat(),
            'usage_pattern': usage_pattern,
            'system_analysis': {},
            'optimal_config': {},
            'application_result': {},
            'performance_validation': {},
            'recommendation': ''
        }
        
        try:
            # Step 1: Analyze system
            system_analysis = self.analyze_system_resources()
            optimization_result['system_analysis'] = system_analysis
            
            if 'error' in system_analysis:
                optimization_result['recommendation'] = 'System analysis failed, using default configuration'
                return optimization_result
            
            # Step 2: Generate optimal configuration
            optimal_config = self.generate_optimal_config(system_analysis, usage_pattern)
            optimization_result['optimal_config'] = optimal_config
            
            # Step 3: Apply configuration
            application_result = self.apply_configuration(optimal_config)
            optimization_result['application_result'] = application_result
            
            # Step 4: Validate performance (if requested)
            if validate_performance:
                logger.info("📊 Validating performance with new configuration...")
                performance_validation = self.validator.validate_optimization_impact(csv_data_dir)
                optimization_result['performance_validation'] = performance_validation
                
                # Generate recommendation based on validation
                if performance_validation.get('summary', {}).get('optimization_available'):
                    improvement = performance_validation['summary'].get('performance_improvement', 0)
                    if improvement > 10:
                        optimization_result['recommendation'] = f"✅ Configuration optimized successfully! {improvement:+.1f}% performance improvement"
                    elif improvement > 0:
                        optimization_result['recommendation'] = f"✅ Configuration applied with {improvement:+.1f}% improvement"
                    else:
                        optimization_result['recommendation'] = "⚠️ Configuration applied but no significant improvement detected"
                else:
                    optimization_result['recommendation'] = "✅ Configuration applied (performance validation unavailable)"
            else:
                optimization_result['recommendation'] = "✅ Configuration optimized based on system analysis"
            
            # Record optimization
            self.optimization_history.append(optimization_result)
            
            logger.info("🎉 Configuration optimization completed successfully")
            
        except Exception as e:
            logger.error(f"❌ Configuration optimization failed: {e}")
            optimization_result['error'] = str(e)
            optimization_result['recommendation'] = f"❌ Optimization failed: {e}"
        
        return optimization_result
    
    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """Get history of configuration optimizations"""
        return self.optimization_history.copy()
    
    def reset_to_defaults(self) -> Dict[str, Any]:
        """Reset configuration to default values"""
        logger.info("🔄 Resetting configuration to defaults...")
        
        default_config = {
            'ENABLE_OPTIMIZATIONS': False,
            'ENABLE_CACHING': False,
            'ENABLE_FILE_MONITORING': False,
            'ENABLE_COMPRESSION': True,
            'USE_OPTIMIZED_ENDPOINTS': False,
            'CACHE_DEFAULT_TIMEOUT': 1800,
            'MAX_CACHE_SIZE': 1024 * 1024,
            'CIRCUIT_BREAKER_FAILURE_THRESHOLD': 5,
            'CIRCUIT_BREAKER_RECOVERY_TIMEOUT': 60,
            'FILE_MONITOR_QUEUE_SIZE': 100,
            'ENABLE_PERFORMANCE_MONITORING': True,
            'SLOW_REQUEST_THRESHOLD': 1.0
        }
        
        return self.apply_configuration(default_config)

if __name__ == "__main__":
    # Test configuration optimizer
    print("🧪 Testing Configuration Optimizer...")
    
    optimizer = ConfigurationOptimizer()
    
    # Test system analysis
    analysis = optimizer.analyze_system_resources()
    if 'error' not in analysis:
        print(f"💻 System: {analysis['overall_classification']}")
        print(f"   CPU: {analysis['cpu']['cores']} cores")
        print(f"   RAM: {analysis['memory']['total_gb']}GB")
        
        # Test config generation
        optimal_config = optimizer.generate_optimal_config(analysis, 'balanced')
        print(f"⚙️ Optimal cache size: {optimal_config['MAX_CACHE_SIZE']//1024//1024}MB")
        print(f"⚙️ Cache timeout: {optimal_config['CACHE_DEFAULT_TIMEOUT']}s")
    else:
        print(f"❌ System analysis failed: {analysis['error']}")
    
    print("✅ Configuration optimizer test completed")
