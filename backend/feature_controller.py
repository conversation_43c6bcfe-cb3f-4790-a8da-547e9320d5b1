#!/usr/bin/env python3
"""
Feature Enablement Controller
Provides gradual feature rollout with safety controls and rollback mechanisms
"""

import os
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from flask import Blueprint, jsonify, request
import logging

# Import optimization modules
from config import config
from optimization_service import get_optimization_service, shutdown_optimization_service
from redis_client import get_redis_client, shutdown_redis_client
from file_monitor import get_file_monitor, shutdown_file_monitor

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

# Create blueprint for feature control endpoints
feature_bp = Blueprint('features', __name__)

class FeatureController:
    """Controls gradual feature enablement and rollback"""
    
    def __init__(self):
        self.feature_states = {
            'optimizations': config.ENABLE_OPTIMIZATIONS,
            'caching': config.ENABLE_CACHING,
            'file_monitoring': config.ENABLE_FILE_MONITORING,
            'compression': config.ENABLE_COMPRESSION,
            'optimized_endpoints': config.USE_OPTIMIZED_ENDPOINTS
        }
        
        self.rollout_history = []
        self.safety_checks = []
        self.rollback_triggers = {
            'error_rate_threshold': 0.1,  # 10% error rate
            'response_time_threshold': 5.0,  # 5 seconds
            'memory_usage_threshold': 90,  # 90% memory usage
            'cpu_usage_threshold': 95  # 95% CPU usage
        }
    
    def enable_feature(self, feature_name: str, csv_data_dir: str) -> Dict[str, Any]:
        """
        Safely enable a feature with health checks
        
        Args:
            feature_name: Name of the feature to enable
            csv_data_dir: CSV data directory path
            
        Returns:
            Result of feature enablement
        """
        result = {
            'feature': feature_name,
            'action': 'enable',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'message': '',
            'health_check': {}
        }
        
        if feature_name not in self.feature_states:
            result['message'] = f'Unknown feature: {feature_name}'
            return result
        
        if self.feature_states[feature_name]:
            result['message'] = f'Feature {feature_name} is already enabled'
            result['success'] = True
            return result
        
        logger.info(f"🚀 Enabling feature: {feature_name}")
        
        try:
            # Perform pre-enablement health check
            pre_health = self._perform_health_check(csv_data_dir)
            
            # Enable the feature
            self._set_feature_state(feature_name, True)
            
            # Wait for feature to initialize
            time.sleep(2)
            
            # Perform post-enablement health check
            post_health = self._perform_health_check(csv_data_dir)
            
            # Check if enablement was successful
            if self._is_health_acceptable(post_health):
                result['success'] = True
                result['message'] = f'Feature {feature_name} enabled successfully'
                result['health_check'] = {
                    'pre_enablement': pre_health,
                    'post_enablement': post_health
                }
                
                # Record successful enablement
                self.rollout_history.append({
                    'feature': feature_name,
                    'action': 'enable',
                    'timestamp': datetime.now().isoformat(),
                    'success': True,
                    'health_before': pre_health,
                    'health_after': post_health
                })
                
                logger.info(f"✅ Feature {feature_name} enabled successfully")
                
            else:
                # Health check failed, rollback
                logger.warning(f"⚠️ Health check failed after enabling {feature_name}, rolling back...")
                rollback_result = self.disable_feature(feature_name, csv_data_dir)
                
                result['message'] = f'Feature {feature_name} enabled but health check failed, rolled back'
                result['health_check'] = post_health
                result['rollback'] = rollback_result
                
        except Exception as e:
            logger.error(f"❌ Error enabling feature {feature_name}: {e}")
            result['message'] = f'Error enabling feature: {e}'
            
            # Attempt rollback on error
            try:
                self.disable_feature(feature_name, csv_data_dir)
            except Exception as rollback_error:
                logger.error(f"❌ Rollback failed: {rollback_error}")
        
        return result
    
    def disable_feature(self, feature_name: str, csv_data_dir: str) -> Dict[str, Any]:
        """
        Disable a feature safely
        
        Args:
            feature_name: Name of the feature to disable
            csv_data_dir: CSV data directory path
            
        Returns:
            Result of feature disablement
        """
        result = {
            'feature': feature_name,
            'action': 'disable',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'message': ''
        }
        
        if feature_name not in self.feature_states:
            result['message'] = f'Unknown feature: {feature_name}'
            return result
        
        if not self.feature_states[feature_name]:
            result['message'] = f'Feature {feature_name} is already disabled'
            result['success'] = True
            return result
        
        logger.info(f"🛑 Disabling feature: {feature_name}")
        
        try:
            # Disable the feature
            self._set_feature_state(feature_name, False)
            
            # Perform cleanup if needed
            self._cleanup_feature(feature_name, csv_data_dir)
            
            result['success'] = True
            result['message'] = f'Feature {feature_name} disabled successfully'
            
            # Record disablement
            self.rollout_history.append({
                'feature': feature_name,
                'action': 'disable',
                'timestamp': datetime.now().isoformat(),
                'success': True
            })
            
            logger.info(f"✅ Feature {feature_name} disabled successfully")
            
        except Exception as e:
            logger.error(f"❌ Error disabling feature {feature_name}: {e}")
            result['message'] = f'Error disabling feature: {e}'
        
        return result
    
    def _set_feature_state(self, feature_name: str, enabled: bool):
        """Set feature state and update configuration"""
        self.feature_states[feature_name] = enabled
        
        # Update environment variables (for current session)
        env_mapping = {
            'optimizations': 'ENABLE_OPTIMIZATIONS',
            'caching': 'ENABLE_CACHING',
            'file_monitoring': 'ENABLE_FILE_MONITORING',
            'compression': 'ENABLE_COMPRESSION',
            'optimized_endpoints': 'USE_OPTIMIZED_ENDPOINTS'
        }
        
        if feature_name in env_mapping:
            os.environ[env_mapping[feature_name]] = 'true' if enabled else 'false'
            
            # Update config object
            setattr(config, env_mapping[feature_name], enabled)
    
    def _cleanup_feature(self, feature_name: str, csv_data_dir: str):
        """Cleanup resources when disabling a feature"""
        if feature_name == 'caching':
            # Shutdown Redis client
            shutdown_redis_client()
        
        elif feature_name == 'file_monitoring':
            # Shutdown file monitor
            shutdown_file_monitor()
        
        elif feature_name == 'optimizations':
            # Shutdown entire optimization service
            shutdown_optimization_service()
    
    def _perform_health_check(self, csv_data_dir: str) -> Dict[str, Any]:
        """Perform health check for feature enablement"""
        from monitoring import SystemMonitor
        
        monitor = SystemMonitor()
        return monitor.perform_health_check(csv_data_dir)
    
    def _is_health_acceptable(self, health: Dict[str, Any]) -> bool:
        """Check if health status is acceptable for feature enablement"""
        if health['status'] == 'unhealthy':
            return False
        
        # Check specific metrics
        if 'components' in health and 'system' in health['components']:
            system = health['components']['system']
            if system['status'] == 'healthy':
                # Check resource usage
                if (system.get('cpu_percent', 0) > self.rollback_triggers['cpu_usage_threshold'] or
                    system.get('memory_percent', 0) > self.rollback_triggers['memory_usage_threshold']):
                    return False
        
        return True
    
    def get_feature_status(self) -> Dict[str, Any]:
        """Get current status of all features"""
        return {
            'timestamp': datetime.now().isoformat(),
            'features': self.feature_states.copy(),
            'rollout_history': self.rollout_history[-10:],  # Last 10 changes
            'safety_settings': self.rollback_triggers
        }
    
    def gradual_rollout(self, csv_data_dir: str, rollout_plan: List[str]) -> Dict[str, Any]:
        """
        Perform gradual rollout of multiple features
        
        Args:
            csv_data_dir: CSV data directory path
            rollout_plan: List of features to enable in order
            
        Returns:
            Results of gradual rollout
        """
        results = {
            'timestamp': datetime.now().isoformat(),
            'rollout_plan': rollout_plan,
            'results': [],
            'overall_success': True,
            'stopped_at': None
        }
        
        logger.info(f"🚀 Starting gradual rollout: {rollout_plan}")
        
        for i, feature in enumerate(rollout_plan):
            logger.info(f"📈 Rollout step {i+1}/{len(rollout_plan)}: {feature}")
            
            # Enable feature
            result = self.enable_feature(feature, csv_data_dir)
            results['results'].append(result)
            
            if not result['success']:
                results['overall_success'] = False
                results['stopped_at'] = feature
                logger.error(f"❌ Rollout stopped at {feature} due to failure")
                break
            
            # Wait between features for stability
            if i < len(rollout_plan) - 1:
                logger.info("⏳ Waiting for system stability...")
                time.sleep(5)
        
        if results['overall_success']:
            logger.info("🎉 Gradual rollout completed successfully")
        else:
            logger.warning("⚠️ Gradual rollout incomplete due to failures")
        
        return results

# Global feature controller instance
feature_controller = FeatureController()

# Feature control endpoints
@feature_bp.route('/api/features/status', methods=['GET'])
def get_feature_status():
    """Get current feature status"""
    return jsonify(feature_controller.get_feature_status())

@feature_bp.route('/api/features/<feature_name>/enable', methods=['POST'])
def enable_feature(feature_name):
    """Enable a specific feature"""
    csv_data_dir = request.json.get('csv_data_dir', '/tmp') if request.json else '/tmp'
    result = feature_controller.enable_feature(feature_name, csv_data_dir)
    return jsonify(result)

@feature_bp.route('/api/features/<feature_name>/disable', methods=['POST'])
def disable_feature(feature_name):
    """Disable a specific feature"""
    csv_data_dir = request.json.get('csv_data_dir', '/tmp') if request.json else '/tmp'
    result = feature_controller.disable_feature(feature_name, csv_data_dir)
    return jsonify(result)

@feature_bp.route('/api/features/rollout', methods=['POST'])
def gradual_rollout():
    """Perform gradual feature rollout"""
    data = request.json or {}
    csv_data_dir = data.get('csv_data_dir', '/tmp')
    rollout_plan = data.get('rollout_plan', ['compression', 'caching', 'file_monitoring', 'optimizations'])
    
    result = feature_controller.gradual_rollout(csv_data_dir, rollout_plan)
    return jsonify(result)

@feature_bp.route('/api/features/emergency-disable', methods=['POST'])
def emergency_disable_all():
    """Emergency disable all features"""
    csv_data_dir = request.json.get('csv_data_dir', '/tmp') if request.json else '/tmp'
    
    results = []
    for feature in feature_controller.feature_states.keys():
        if feature_controller.feature_states[feature]:
            result = feature_controller.disable_feature(feature, csv_data_dir)
            results.append(result)
    
    return jsonify({
        'timestamp': datetime.now().isoformat(),
        'action': 'emergency_disable_all',
        'results': results
    })

def register_feature_controller(app, csv_data_dir: str):
    """Register feature controller with Flask app"""
    app.register_blueprint(feature_bp)
    
    # Set default csv_data_dir for feature endpoints
    @app.before_request
    def set_csv_data_dir():
        if request.endpoint and request.endpoint.startswith('features.'):
            if request.json and 'csv_data_dir' not in request.json:
                request.json = request.json or {}
                request.json['csv_data_dir'] = csv_data_dir
    
    logger.info("✅ Feature controller endpoints registered")

if __name__ == "__main__":
    # Test feature controller
    print("🧪 Testing feature controller...")
    
    controller = FeatureController()
    
    # Test feature status
    status = controller.get_feature_status()
    print(f"📊 Feature status: {len(status['features'])} features")
    
    # Test feature enablement (dry run)
    print("🔧 Testing feature enablement (compression)...")
    result = controller.enable_feature('compression', '/tmp')
    print(f"Result: {result['success']} - {result['message']}")
    
    print("✅ Feature controller test completed")
