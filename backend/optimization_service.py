#!/usr/bin/env python3
"""
Main Optimization Service
Coordinates caching, file monitoring, and performance optimizations
"""

import os
import time
import hashlib
from functools import wraps
from typing import Optional, Any, Callable, Dict
import logging
from datetime import datetime

# Import optimization modules
from config import config
from redis_client import get_redis_client, shutdown_redis_client
from file_monitor import get_file_monitor, shutdown_file_monitor
from smart_serializer import SmartSerializer
from circuit_breaker import CircuitBreaker, CircuitBreakerOpenException

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class OptimizationService:
    """Main service that coordinates all optimizations"""
    
    def __init__(self, csv_data_dir: str):
        """
        Initialize optimization service
        
        Args:
            csv_data_dir: Directory containing CSV files
        """
        self.csv_data_dir = csv_data_dir
        self.initialized = False
        
        # Components
        self.redis_client = None
        self.file_monitor = None
        
        # Statistics
        self.cache_hits = 0
        self.cache_misses = 0
        self.cache_errors = 0
        self.total_requests = 0
        self.start_time = datetime.now()
        
        # Circuit breakers
        self.cache_breaker = CircuitBreaker(
            failure_threshold=config.CIRCUIT_BREAKER_FAILURE_THRESHOLD,
            recovery_timeout=config.CIRCUIT_BREAKER_RECOVERY_TIMEOUT,
            name="cache_operations"
        )
        
        self.file_monitor_breaker = CircuitBreaker(
            failure_threshold=2,
            recovery_timeout=30,
            name="file_monitor"
        )
        
        # Initialize if optimizations are enabled
        if config.is_optimization_enabled():
            self.initialize()
    
    def initialize(self) -> bool:
        """Initialize optimization components"""
        if self.initialized:
            return True
        
        logger.info("🚀 Initializing optimization service...")
        
        success = True
        
        # Initialize Redis client
        if config.ENABLE_CACHING:
            try:
                self.redis_client = self.cache_breaker.call(get_redis_client)
                if self.redis_client and self.redis_client.connected:
                    logger.info("✅ Redis client initialized")
                else:
                    logger.warning("⚠️ Redis client not connected")
                    success = False
            except Exception as e:
                logger.error(f"❌ Redis initialization failed: {e}")
                success = False
        
        # Initialize file monitor
        if config.ENABLE_FILE_MONITORING:
            try:
                self.file_monitor = self.file_monitor_breaker.call(
                    get_file_monitor, self.csv_data_dir, self.redis_client
                )
                if self.file_monitor:
                    if self.file_monitor.start():
                        logger.info("✅ File monitor initialized")
                    else:
                        logger.warning("⚠️ File monitor failed to start")
                        success = False
                else:
                    logger.warning("⚠️ File monitor not created")
                    success = False
            except Exception as e:
                logger.error(f"❌ File monitor initialization failed: {e}")
                success = False
        
        self.initialized = success
        
        if success:
            logger.info("🎉 Optimization service initialized successfully")
        else:
            logger.warning("⚠️ Optimization service initialized with warnings")
        
        return success
    
    def smart_cache(self, 
                   key_prefix: str = "",
                   expiration: int = None,
                   dependencies: list = None):
        """
        Smart caching decorator with fallback mechanisms
        
        Args:
            key_prefix: Prefix for cache keys
            expiration: Cache expiration time (seconds)
            dependencies: List of file dependencies
        """
        if expiration is None:
            expiration = config.CACHE_DEFAULT_TIMEOUT
        
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                self.total_requests += 1
                
                # Skip caching if not enabled or not initialized
                if not config.ENABLE_CACHING or not self.initialized or not self.redis_client:
                    return func(*args, **kwargs)
                
                # Generate cache key
                cache_key = self._generate_cache_key(func.__name__, key_prefix, args, kwargs)
                
                # Try to get from cache
                try:
                    cached_result = self.cache_breaker.call(self._get_from_cache, cache_key)
                    if cached_result is not None:
                        self.cache_hits += 1
                        if config.LOG_CACHE_OPERATIONS:
                            logger.debug(f"🎯 Cache HIT: {func.__name__}")
                        return cached_result
                    
                except CircuitBreakerOpenException:
                    logger.warning(f"⚡ Cache circuit breaker open for {func.__name__}")
                except Exception as e:
                    logger.warning(f"⚠️ Cache get error for {func.__name__}: {e}")
                    self.cache_errors += 1
                
                # Cache miss - execute function
                self.cache_misses += 1
                if config.LOG_CACHE_OPERATIONS:
                    logger.debug(f"💾 Cache MISS: {func.__name__}")
                
                result = func(*args, **kwargs)
                
                # Try to cache result
                try:
                    self.cache_breaker.call(self._set_to_cache, cache_key, result, expiration)
                except CircuitBreakerOpenException:
                    logger.warning(f"⚡ Cache circuit breaker open, not caching {func.__name__}")
                except Exception as e:
                    logger.warning(f"⚠️ Cache set error for {func.__name__}: {e}")
                    self.cache_errors += 1
                
                return result
            
            return wrapper
        return decorator
    
    def _generate_cache_key(self, func_name: str, prefix: str, args: tuple, kwargs: dict) -> str:
        """Generate cache key from function parameters"""
        key_data = f"{func_name}:{prefix}:{args}:{sorted(kwargs.items())}"
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"{config.CACHE_KEY_PREFIX}:{key_hash}"
    
    def _get_from_cache(self, cache_key: str) -> Any:
        """Get value from cache with deserialization"""
        if not self.redis_client:
            return None
        
        cached_data = self.redis_client.get(cache_key)
        if cached_data:
            return SmartSerializer.safe_deserialize(cached_data)
        return None
    
    def _set_to_cache(self, cache_key: str, value: Any, expiration: int):
        """Set value to cache with serialization"""
        if not self.redis_client:
            return
        
        # Serialize data
        serialized_data = SmartSerializer.safe_serialize(value)
        if not serialized_data:
            logger.warning(f"⚠️ Failed to serialize data for cache key: {cache_key}")
            return
        
        # Check size limit
        data_size = len(serialized_data.encode('utf-8'))
        if data_size > config.MAX_CACHE_SIZE:
            logger.warning(f"⚠️ Data too large for cache: {data_size} bytes (limit: {config.MAX_CACHE_SIZE})")
            return
        
        # Store in cache
        self.redis_client.setex(cache_key, expiration, serialized_data)
        
        if config.LOG_CACHE_OPERATIONS:
            logger.debug(f"💾 Cached data: {data_size} bytes, expires in {expiration}s")
    
    def invalidate_cache_pattern(self, pattern: str) -> int:
        """Invalidate cache entries matching pattern"""
        if not self.redis_client:
            return 0
        
        try:
            deleted_count = 0
            for key in self.redis_client.scan_iter(match=pattern):
                self.redis_client.delete(key)
                deleted_count += 1
            
            if deleted_count > 0:
                logger.info(f"🗑️ Invalidated {deleted_count} cache entries matching: {pattern}")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"❌ Error invalidating cache pattern {pattern}: {e}")
            return 0
    
    def clear_all_cache(self) -> bool:
        """Clear all cache entries"""
        if not self.redis_client:
            return False
        
        try:
            self.redis_client.flushdb()
            logger.info("🗑️ All cache cleared")
            return True
        except Exception as e:
            logger.error(f"❌ Error clearing cache: {e}")
            return False
    
    def get_stats(self) -> dict:
        """Get optimization service statistics"""
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        stats = {
            'initialized': self.initialized,
            'uptime_seconds': uptime,
            'total_requests': self.total_requests,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'cache_errors': self.cache_errors,
            'cache_hit_rate': (self.cache_hits / max(self.total_requests, 1)) * 100,
            'components': {
                'redis_connected': self.redis_client.connected if self.redis_client else False,
                'file_monitor_running': (self.file_monitor.get_stats()['is_running'] 
                                       if self.file_monitor else False)
            }
        }
        
        # Add Redis stats
        if self.redis_client:
            stats['redis_stats'] = self.redis_client.get_stats()
        
        # Add file monitor stats
        if self.file_monitor:
            stats['file_monitor_stats'] = self.file_monitor.get_stats()
        
        # Add circuit breaker stats
        stats['circuit_breakers'] = {
            'cache': self.cache_breaker.get_stats(),
            'file_monitor': self.file_monitor_breaker.get_stats()
        }
        
        return stats
    
    def health_check(self) -> dict:
        """Perform health check on all components"""
        health = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'components': {}
        }
        
        # Check Redis
        if config.ENABLE_CACHING:
            if self.redis_client and self.redis_client.connected:
                try:
                    self.redis_client.ping()
                    health['components']['redis'] = {'status': 'healthy', 'connected': True}
                except Exception as e:
                    health['components']['redis'] = {'status': 'unhealthy', 'error': str(e)}
                    health['status'] = 'degraded'
            else:
                health['components']['redis'] = {'status': 'disconnected'}
                health['status'] = 'degraded'
        else:
            health['components']['redis'] = {'status': 'disabled'}
        
        # Check file monitor
        if config.ENABLE_FILE_MONITORING:
            if self.file_monitor:
                monitor_stats = self.file_monitor.get_stats()
                if monitor_stats['is_running']:
                    health['components']['file_monitor'] = {'status': 'healthy', 'running': True}
                else:
                    health['components']['file_monitor'] = {'status': 'unhealthy', 'running': False}
                    health['status'] = 'degraded'
            else:
                health['components']['file_monitor'] = {'status': 'not_initialized'}
                health['status'] = 'degraded'
        else:
            health['components']['file_monitor'] = {'status': 'disabled'}
        
        # Check CSV directory
        if os.path.exists(self.csv_data_dir) and os.access(self.csv_data_dir, os.R_OK):
            csv_files = len([f for f in os.listdir(self.csv_data_dir) if f.endswith('.csv')])
            health['components']['csv_directory'] = {
                'status': 'healthy',
                'path': self.csv_data_dir,
                'csv_files_count': csv_files
            }
        else:
            health['components']['csv_directory'] = {
                'status': 'unhealthy',
                'path': self.csv_data_dir,
                'error': 'Directory not accessible'
            }
            health['status'] = 'unhealthy'
        
        return health
    
    def shutdown(self):
        """Shutdown optimization service"""
        logger.info("🛑 Shutting down optimization service...")
        
        # Shutdown file monitor
        if self.file_monitor:
            shutdown_file_monitor()
            self.file_monitor = None
        
        # Shutdown Redis client
        if self.redis_client:
            shutdown_redis_client()
            self.redis_client = None
        
        self.initialized = False
        logger.info("✅ Optimization service shutdown complete")

# Global optimization service instance
optimization_service: Optional[OptimizationService] = None

def get_optimization_service(csv_data_dir: str) -> Optional[OptimizationService]:
    """Get global optimization service instance"""
    global optimization_service
    
    if not config.is_optimization_enabled():
        return None
    
    if optimization_service is None:
        try:
            optimization_service = OptimizationService(csv_data_dir)
        except Exception as e:
            logger.error(f"❌ Failed to initialize optimization service: {e}")
            optimization_service = None
    
    return optimization_service

def shutdown_optimization_service():
    """Shutdown global optimization service"""
    global optimization_service
    if optimization_service:
        optimization_service.shutdown()
        optimization_service = None

if __name__ == "__main__":
    # Test optimization service
    import tempfile
    
    print("🧪 Testing Optimization Service...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        service = OptimizationService(temp_dir)
        
        print(f"📊 Stats: {service.get_stats()}")
        print(f"🏥 Health: {service.health_check()}")
        
        # Test caching decorator
        @service.smart_cache(key_prefix="test", expiration=60)
        def test_function(x, y):
            return x + y
        
        # Test function calls
        result1 = test_function(1, 2)
        result2 = test_function(1, 2)  # Should be cached
        
        print(f"Results: {result1}, {result2}")
        print(f"📊 Final stats: {service.get_stats()}")
        
        service.shutdown()
