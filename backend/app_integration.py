#!/usr/bin/env python3
"""
App Integration Module
Safely integrates optimizations with existing Flask app without modifying original code
"""

import os
import atexit
from flask import Flask
from flask_compress import Compress
import logging

# Import optimization modules
from config import config, print_config_status
from optimized_endpoints import initialize_optimizations, shutdown_optimizations
from monitoring import register_monitoring
from feature_controller import register_feature_controller

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

def integrate_optimizations(app: Flask, csv_data_dir: str):
    """
    Integrate optimizations with existing Flask app
    
    Args:
        app: Flask application instance
        csv_data_dir: Directory containing CSV files
    """
    logger.info("🔧 Integrating optimizations with Flask app...")
    
    # Print configuration status
    print_config_status()
    
    # Add compression if enabled
    if config.ENABLE_COMPRESSION:
        try:
            Compress(app)
            logger.info("✅ Response compression enabled")
        except Exception as e:
            logger.error(f"❌ Failed to enable compression: {e}")
    
    # Initialize optimizations
    try:
        initialize_optimizations(app, csv_data_dir)
        logger.info("✅ Optimizations integrated successfully")
    except Exception as e:
        logger.error(f"❌ Failed to integrate optimizations: {e}")

    # Register monitoring endpoints
    try:
        register_monitoring(app, csv_data_dir)
        logger.info("✅ Monitoring endpoints registered")
    except Exception as e:
        logger.error(f"❌ Failed to register monitoring: {e}")

    # Register feature controller endpoints
    try:
        register_feature_controller(app, csv_data_dir)
        logger.info("✅ Feature controller registered")
    except Exception as e:
        logger.error(f"❌ Failed to register feature controller: {e}")
    
    # Register shutdown handler
    @atexit.register
    def cleanup_on_exit():
        """Cleanup optimizations on app exit"""
        logger.info("🛑 Cleaning up optimizations...")
        shutdown_optimizations()
    
    # Add error handlers for optimization-related errors
    @app.errorhandler(500)
    def handle_internal_error(error):
        """Handle internal server errors gracefully"""
        logger.error(f"❌ Internal server error: {error}")
        
        # If it's an optimization-related error, try to disable optimizations
        if "optimization" in str(error).lower() or "cache" in str(error).lower():
            logger.warning("⚠️ Optimization-related error detected, consider disabling optimizations")
        
        # Return original error response
        return error
    
    logger.info("🎉 App integration completed")

def create_optimized_app(original_app_module):
    """
    Create an optimized version of the app by importing and enhancing the original
    
    Args:
        original_app_module: The original app module (e.g., 'app')
        
    Returns:
        Enhanced Flask app with optimizations
    """
    # Import the original app
    import importlib
    app_module = importlib.import_module(original_app_module)
    app = app_module.app
    
    # Get CSV data directory from original app
    csv_data_dir = getattr(app_module, 'CSV_DATA_DIR', 
                          os.path.join(os.path.dirname(__file__), 'data'))
    
    # Integrate optimizations
    integrate_optimizations(app, csv_data_dir)
    
    return app

def run_with_optimizations():
    """
    Run the Flask app with optimizations enabled
    """
    logger.info("🚀 Starting Flask app with optimizations...")
    
    try:
        # Create optimized app
        app = create_optimized_app('app')
        
        # Run the app
        app.run(
            host='0.0.0.0',
            port=5001,
            debug=False,  # Disable debug mode for optimizations
            threaded=True
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 Shutting down Flask app...")
    except Exception as e:
        logger.error(f"❌ Failed to start Flask app: {e}")
        raise

def validate_integration():
    """
    Validate that the integration is working correctly
    """
    logger.info("🔍 Validating optimization integration...")
    
    validation_results = {
        'config_valid': False,
        'modules_importable': False,
        'optimization_service_available': False,
        'issues': []
    }
    
    # Validate configuration
    try:
        is_valid, issues = config.validate_config()
        validation_results['config_valid'] = is_valid
        if issues:
            validation_results['issues'].extend([f"Config: {issue}" for issue in issues])
    except Exception as e:
        validation_results['issues'].append(f"Config validation error: {e}")
    
    # Test module imports
    try:
        from optimization_service import get_optimization_service
        from redis_client import get_redis_client
        from file_monitor import get_file_monitor
        validation_results['modules_importable'] = True
        logger.info("✅ All optimization modules importable")
    except Exception as e:
        validation_results['issues'].append(f"Module import error: {e}")
        logger.error(f"❌ Module import failed: {e}")
    
    # Test optimization service
    try:
        from optimization_service import get_optimization_service
        service = get_optimization_service('/tmp')  # Use temp dir for testing
        if service:
            validation_results['optimization_service_available'] = True
            logger.info("✅ Optimization service available")
        else:
            validation_results['issues'].append("Optimization service not available")
    except Exception as e:
        validation_results['issues'].append(f"Optimization service error: {e}")
    
    # Print validation results
    print("\n" + "="*50)
    print("🔍 INTEGRATION VALIDATION RESULTS")
    print("="*50)
    print(f"Config Valid: {'✅' if validation_results['config_valid'] else '❌'}")
    print(f"Modules Importable: {'✅' if validation_results['modules_importable'] else '❌'}")
    print(f"Optimization Service: {'✅' if validation_results['optimization_service_available'] else '❌'}")
    
    if validation_results['issues']:
        print(f"\n⚠️ Issues Found ({len(validation_results['issues'])}):")
        for issue in validation_results['issues']:
            print(f"  - {issue}")
    else:
        print("\n🎉 All validations passed!")
    
    print("="*50)
    
    return validation_results

def create_startup_script():
    """
    Create a startup script for running the optimized app
    """
    script_content = '''#!/bin/bash

# Flask Optimization Startup Script
# This script starts the Flask app with optimizations enabled

echo "🚀 Starting Flask app with optimizations..."

# Set environment variables
export FLASK_ENV=production
export ENABLE_OPTIMIZATIONS=true
export ENABLE_CACHING=true
export ENABLE_FILE_MONITORING=true
export ENABLE_COMPRESSION=true

# Activate virtual environment
source venv/bin/activate

# Start the optimized app
python -c "
from app_integration import run_with_optimizations
run_with_optimizations()
"
'''
    
    script_path = os.path.join(os.path.dirname(__file__), 'start_optimized.sh')
    
    try:
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # Make script executable
        os.chmod(script_path, 0o755)
        
        logger.info(f"✅ Startup script created: {script_path}")
        print(f"💡 To start the optimized app, run: ./start_optimized.sh")
        
    except Exception as e:
        logger.error(f"❌ Failed to create startup script: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "validate":
            # Run validation
            validate_integration()
        
        elif command == "create-script":
            # Create startup script
            create_startup_script()
        
        elif command == "run":
            # Run with optimizations
            run_with_optimizations()
        
        else:
            print("Usage: python app_integration.py [validate|create-script|run]")
    
    else:
        # Default: run validation
        validate_integration()
