# Authentication Configuration
# Copy this file to .env and update the values

# Enable/Disable Authentication
ENABLE_AUTHENTICATION=true

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-please
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRES=3600
JWT_REFRESH_TOKEN_EXPIRES=2592000

# Supabase Configuration (from frontend .env)
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_JWT_SECRET=your-supabase-jwt-secret

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=true

# CORS Configuration
CORS_ORIGINS=http://localhost:5173,http://localhost:5174,http://localhost:3000
