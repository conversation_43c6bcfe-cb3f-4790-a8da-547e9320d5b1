# CSV Data Processing API

This Python Flask API processes CSV files containing competitor data and provides endpoints for the React dashboard to consume.

## Features

- **CSV File Processing**: Automatically processes competitor CSV files
- **Data Categorization**: Organizes data into Popular, New, All, and Old categories
- **RESTful API**: Clean API endpoints for data retrieval
- **CORS Support**: Configured for React frontend integration
- **Error Handling**: Comprehensive error handling and validation

## Prerequisites

- Python 3.8 or higher
- pip3 (Python package installer)

## Quick Start

### 1. Start the API Server

```bash
cd backend
./start.sh
```

The startup script will:
- Create a virtual environment
- Install dependencies
- Create necessary directories
- Start the Flask server on http://localhost:5000

### 2. Manual Setup (Alternative)

If you prefer manual setup:

```bash
cd backend

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Start the server
python app.py
```

## API Endpoints

### Health Check
```
GET /api/health
```

### Get Competitors List
```
GET /api/competitors
```

### Get Competitor Data
```
GET /api/competitors/{competitor_name}/data?type={popular|new|all|old}
```

### Get All Competitor Data
```
GET /api/competitors/{competitor_name}/all-data
```

### Get Files List
```
GET /api/files
```

## CSV File Format

Competitor CSV files should follow this naming convention:
```
EE_{competitor_name}_{date}.csv
```

Example: `EE_artchiles_design_12_06_2025.csv`

### Required CSV Columns

- `Title`: Product/item title
- `Author`: Creator/author name
- `Page Old`: Previous page ranking
- `Page New`: Current page ranking
- `Order Old`: Previous order position
- `Order New`: Current order position
- `new_item`: Boolean flag for new items (optional)

## Data Directory Structure

```
backend/
├── data/                 # CSV files storage
├── uploads/             # File upload directory
├── app.py              # Main Flask application
├── requirements.txt    # Python dependencies
├── start.sh           # Startup script
└── README.md          # This file
```

## Sample Data

The API includes sample competitor data for testing:
- artchiles_design
- creative_studio
- design_masters
- pixel_perfect

## Integration with React Frontend

The React frontend automatically connects to this API at `http://localhost:5000`. Ensure the API is running before starting the React development server.

## Troubleshooting

### Port Already in Use
If port 5000 is already in use, modify the port in `app.py`:
```python
app.run(debug=True, host='0.0.0.0', port=5001)  # Change port here
```

### CORS Issues
CORS is already configured. If you encounter issues, ensure the React app is running on the expected port.

### Missing Dependencies
Run the installation command again:
```bash
pip install -r requirements.txt
```

## Development

To add new competitors or modify data processing logic, edit the relevant functions in `app.py`. The API will automatically reload in debug mode.
