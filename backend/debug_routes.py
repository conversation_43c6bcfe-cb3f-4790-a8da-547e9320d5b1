#!/usr/bin/env python3
"""
Debug Route Protection
Check if routes are properly protected and why protection might not be working
"""

import os
import sys
import requests
from flask import Flask

# Add backend directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_route_protection():
    """Debug route protection implementation"""
    print("\n" + "="*60)
    print("🔍 DEBUGGING ROUTE PROTECTION")
    print("="*60)
    
    # Import app to check routes
    try:
        from app import app
        from protected_endpoints import get_protection_level, validate_endpoint_protection
        
        print("\n1. 📋 CHECKING REGISTERED ROUTES:")
        print("-" * 40)
        
        api_routes = []
        for rule in app.url_map.iter_rules():
            rule_str = str(rule)
            if rule_str.startswith('/api'):
                endpoint = rule.endpoint
                methods = list(rule.methods - {'HEAD', 'OPTIONS'})
                protection = get_protection_level(rule_str)
                
                api_routes.append({
                    'rule': rule_str,
                    'endpoint': endpoint,
                    'methods': methods,
                    'protection': protection
                })
                
                print(f"  {rule_str:<30} | {endpoint:<20} | {str(methods):<15} | {protection or 'public'}")
        
        print(f"\nTotal API routes found: {len(api_routes)}")
        
        print("\n2. 🛡️  CHECKING PROTECTION APPLICATION:")
        print("-" * 40)
        
        # Check if view functions are protected
        protected_count = 0
        for route in api_routes:
            endpoint = route['endpoint']
            view_func = app.view_functions.get(endpoint)
            
            if view_func:
                is_protected = hasattr(view_func, '_auth_protected')
                if is_protected:
                    protected_count += 1
                    print(f"  ✅ {route['rule']:<30} | PROTECTED")
                else:
                    expected_protection = route['protection']
                    if expected_protection:
                        print(f"  ❌ {route['rule']:<30} | NOT PROTECTED (should be {expected_protection})")
                    else:
                        print(f"  ⚪ {route['rule']:<30} | PUBLIC (correct)")
            else:
                print(f"  ⚠️  {route['rule']:<30} | NO VIEW FUNCTION")
        
        print(f"\nProtected routes: {protected_count}/{len(api_routes)}")
        
        print("\n3. 🧪 TESTING SPECIFIC ENDPOINTS:")
        print("-" * 40)
        
        # Test specific endpoints
        test_endpoints = [
            ('/api/categories', 'Should be protected'),
            ('/api/auth/health', 'Should be public'),
            ('/api/auth/info', 'Should be public'),
        ]
        
        for endpoint, description in test_endpoints:
            try:
                response = requests.get(f"http://localhost:5001{endpoint}", timeout=5)
                print(f"  {endpoint:<25} | Status: {response.status_code} | {description}")
            except Exception as e:
                print(f"  {endpoint:<25} | ERROR: {str(e)}")
        
        print("\n4. 🔧 CHECKING PROTECTION CONFIGURATION:")
        print("-" * 40)
        
        validation = validate_endpoint_protection(app)
        print(f"  Total routes: {validation['total_routes']}")
        print(f"  Protected routes: {validation['protected_routes']}")
        print(f"  Public routes: {validation['public_routes']}")
        print(f"  Unprotected routes: {len(validation['unprotected_routes'])}")
        
        if validation['unprotected_routes']:
            print("\n  Unprotected routes that should be protected:")
            for route in validation['unprotected_routes'][:5]:  # Show first 5
                print(f"    - {route['endpoint']} (needs {route['required_protection']})")
        
        print("\n5. 🔑 CHECKING JWT CONFIGURATION:")
        print("-" * 40)
        
        from auth_middleware import jwt_auth
        config = jwt_auth.config
        
        print(f"  JWT Secret configured: {'✅' if config.JWT_SECRET_KEY else '❌'}")
        print(f"  Supabase URL configured: {'✅' if config.SUPABASE_URL else '❌'}")
        print(f"  Supabase Key configured: {'✅' if config.SUPABASE_ANON_KEY else '❌'}")
        print(f"  Supabase JWT Secret: {'✅' if config.SUPABASE_JWT_SECRET and config.SUPABASE_JWT_SECRET != 'your-supabase-jwt-secret' else '❌'}")
        
        return {
            'total_routes': len(api_routes),
            'protected_routes': protected_count,
            'validation': validation
        }
        
    except Exception as e:
        print(f"❌ Debug failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_manual_protection():
    """Test manual route protection"""
    print("\n6. 🧪 TESTING MANUAL PROTECTION:")
    print("-" * 40)
    
    try:
        from app import app
        from auth_middleware import token_required
        
        # Create a test route with manual protection
        @app.route('/api/test/protected')
        @token_required
        def test_protected():
            return {'message': 'This is protected', 'success': True}
        
        @app.route('/api/test/public')
        def test_public():
            return {'message': 'This is public', 'success': True}
        
        print("  Created test routes:")
        print("    - /api/test/protected (with @token_required)")
        print("    - /api/test/public (no protection)")
        
        # Test the routes
        try:
            # Test public route
            response = requests.get("http://localhost:5001/api/test/public", timeout=5)
            print(f"  Public route: Status {response.status_code} (expected 200)")
            
            # Test protected route without token
            response = requests.get("http://localhost:5001/api/test/protected", timeout=5)
            print(f"  Protected route (no token): Status {response.status_code} (expected 401)")
            
        except Exception as e:
            print(f"  Test request failed: {str(e)}")
            
    except Exception as e:
        print(f"  Manual protection test failed: {str(e)}")

if __name__ == "__main__":
    result = debug_route_protection()
    test_manual_protection()
    
    print("\n" + "="*60)
    print("🎯 DEBUG SUMMARY")
    print("="*60)
    
    if result:
        print(f"Total API routes: {result['total_routes']}")
        print(f"Protected routes: {result['protected_routes']}")
        print(f"Protection rate: {(result['protected_routes']/result['total_routes']*100):.1f}%")
        
        if result['protected_routes'] == 0:
            print("\n❌ ISSUE: No routes are protected!")
            print("   Possible causes:")
            print("   - Protection decorators not applied")
            print("   - Routes registered after protection applied")
            print("   - Import/circular dependency issues")
        else:
            print(f"\n✅ Some routes are protected ({result['protected_routes']} routes)")
    
    print("\n💡 RECOMMENDATIONS:")
    print("   1. Check if routes are registered before protection is applied")
    print("   2. Verify decorator application in protected_endpoints.py")
    print("   3. Test manual protection with @token_required decorator")
    print("   4. Check for circular import issues")
    print("="*60)
