#!/usr/bin/env python3
"""
Optimized Endpoints Module
Provides optimized versions of existing endpoints without modifying original code
"""

import os
import time
from functools import wraps
from typing import Callable, Any, Dict
from flask import jsonify, request, Blueprint, current_app, g
import logging

# Import optimization modules
from config import config
from optimization_service import get_optimization_service, shutdown_optimization_service

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

# Create blueprint for optimized endpoints
optimized_bp = Blueprint('optimized', __name__)

# Initialize optimization service
CSV_DATA_DIR = None  # Will be set during initialization

def initialize_optimizations(app, csv_data_dir):
    """Initialize optimizations and register blueprint"""
    global CSV_DATA_DIR
    CSV_DATA_DIR = csv_data_dir
    
    # Register blueprint with optional prefix
    if config.USE_OPTIMIZED_ENDPOINTS:
        # Replace original endpoints
        app.register_blueprint(optimized_bp)
        logger.info("✅ Optimized endpoints registered (replacing originals)")
    else:
        # Add as separate endpoints with /v2 prefix
        app.register_blueprint(optimized_bp, url_prefix='/api/v2')
        logger.info("✅ Optimized endpoints registered with /api/v2 prefix")
    
    # Initialize optimization service
    if config.is_optimization_enabled():
        optimization_service = get_optimization_service(csv_data_dir)
        if optimization_service and optimization_service.initialized:
            logger.info("✅ Optimization service initialized")
        else:
            logger.warning("⚠️ Optimization service initialization failed")
    
    # Add request timing middleware if enabled
    if config.ENABLE_PERFORMANCE_MONITORING:
        @app.before_request
        def before_request():
            g.start_time = time.time()

        @app.after_request
        def after_request(response):
            if hasattr(g, 'start_time'):
                duration = time.time() - g.start_time
                
                # Add timing header
                response.headers['X-Response-Time'] = f"{duration:.3f}s"
                
                # Log slow requests
                if duration > config.SLOW_REQUEST_THRESHOLD:
                    logger.warning(f"⚠️ Slow request: {request.method} {request.path} took {duration:.3f}s")
            
            return response
    
    # Add health check endpoint
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """Health check endpoint"""
        health = {
            'status': 'healthy',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'components': {}
        }
        
        # Check optimization service
        optimization_service = get_optimization_service(csv_data_dir)
        if optimization_service:
            service_health = optimization_service.health_check()
            health['components']['optimizations'] = service_health
            
            # Update overall status based on service health
            if service_health['status'] != 'healthy':
                health['status'] = service_health['status']
        else:
            health['components']['optimizations'] = {'status': 'disabled'}
        
        # Check CSV directory
        if os.path.exists(csv_data_dir) and os.access(csv_data_dir, os.R_OK):
            csv_files = len([f for f in os.listdir(csv_data_dir) if f.endswith('.csv')])
            health['components']['csv_directory'] = {
                'status': 'healthy',
                'path': csv_data_dir,
                'csv_files_count': csv_files
            }
        else:
            health['components']['csv_directory'] = {
                'status': 'unhealthy',
                'path': csv_data_dir,
                'error': 'Directory not accessible'
            }
            health['status'] = 'unhealthy'
        
        # Check configuration
        health['components']['config'] = {
            'status': 'healthy',
            'optimizations_enabled': config.ENABLE_OPTIMIZATIONS,
            'caching_enabled': config.ENABLE_CACHING,
            'file_monitoring_enabled': config.ENABLE_FILE_MONITORING,
            'compression_enabled': config.ENABLE_COMPRESSION
        }
        
        return jsonify(health)
    
    # Add cache management endpoints
    @app.route('/api/cache/status', methods=['GET'])
    def cache_status():
        """Get cache status"""
        optimization_service = get_optimization_service(csv_data_dir)
        if not optimization_service:
            return jsonify({
                'cache_enabled': False,
                'message': 'Caching is disabled'
            })
        
        return jsonify(optimization_service.get_stats())
    
    @app.route('/api/cache/clear/<category_name>', methods=['POST'])
    def clear_category_cache(category_name):
        """Clear cache for specific category"""
        optimization_service = get_optimization_service(csv_data_dir)
        if not optimization_service:
            return jsonify({'error': 'Caching is disabled'}), 400
        
        pattern = f"*{category_name}*"
        deleted_count = optimization_service.invalidate_cache_pattern(pattern)
        
        return jsonify({
            'success': True,
            'message': f'Cache cleared for category: {category_name}',
            'deleted_count': deleted_count
        })
    
    @app.route('/api/cache/clear-all', methods=['POST'])
    def clear_all_cache():
        """Clear all cache entries"""
        optimization_service = get_optimization_service(csv_data_dir)
        if not optimization_service:
            return jsonify({'error': 'Caching is disabled'}), 400
        
        success = optimization_service.clear_all_cache()
        
        return jsonify({
            'success': success,
            'message': 'All cache cleared' if success else 'Failed to clear cache'
        })
    
    logger.info("✅ Health check and cache management endpoints registered")

def shutdown_optimizations():
    """Shutdown optimizations"""
    shutdown_optimization_service()
    logger.info("✅ Optimizations shutdown complete")

def with_optimization(func):
    """Decorator to add optimization to existing functions"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Call original function
        return func(*args, **kwargs)
    return wrapper

def call_original_function(func_name, *args, **kwargs):
    """Call original function from app.py"""
    # Import original function
    from app import app as original_app
    
    # Get function from app
    func = getattr(original_app.view_functions, func_name)
    
    # Call function with args and kwargs
    return func(*args, **kwargs)

# Optimized endpoints
@optimized_bp.route('/api/categories', methods=['GET'])
def get_categories_optimized():
    """
    Optimized version of get_categories
    """
    # Get optimization service
    optimization_service = get_optimization_service(CSV_DATA_DIR)
    
    if not optimization_service:
        # Fall back to original function
        return call_original_function('get_categories')
    
    # Use smart caching
    @optimization_service.smart_cache(key_prefix="categories_list", expiration=3600)
    def get_categories_cached():
        # Call original function
        return call_original_function('get_categories')
    
    # Call cached function
    return get_categories_cached()

@optimized_bp.route('/api/categories/<category_name>/data', methods=['GET'])
def get_category_data_optimized(category_name):
    """
    Optimized version of get_category_data
    """
    # Get optimization service
    optimization_service = get_optimization_service(CSV_DATA_DIR)
    
    if not optimization_service:
        # Fall back to original function
        return call_original_function('get_category_data', category_name)
    
    # Use smart caching
    @optimization_service.smart_cache(
        key_prefix=f"category_data_{category_name}",
        expiration=1800
    )
    def get_category_data_cached():
        # Call original function
        return call_original_function('get_category_data', category_name)
    
    # Call cached function
    return get_category_data_cached()

@optimized_bp.route('/api/categories/<category_name>/comparison', methods=['GET'])
def get_category_comparison_optimized(category_name):
    """
    Optimized version of get_category_comparison
    """
    # Get optimization service
    optimization_service = get_optimization_service(CSV_DATA_DIR)
    
    if not optimization_service:
        # Fall back to original function
        return call_original_function('get_category_comparison', category_name)
    
    # Use smart caching
    @optimization_service.smart_cache(
        key_prefix=f"category_comparison_{category_name}",
        expiration=1800
    )
    def get_category_comparison_cached():
        # Call original function
        return call_original_function('get_category_comparison', category_name)
    
    # Call cached function
    return get_category_comparison_cached()

@optimized_bp.route('/api/categories/<category_name>/history', methods=['GET'])
def get_category_history_optimized(category_name):
    """
    Optimized version of get_category_history
    """
    # Get optimization service
    optimization_service = get_optimization_service(CSV_DATA_DIR)
    
    if not optimization_service:
        # Fall back to original function
        return call_original_function('get_category_history', category_name)
    
    # Use smart caching
    @optimization_service.smart_cache(
        key_prefix=f"category_history_{category_name}",
        expiration=3600  # Longer cache for historical data
    )
    def get_category_history_cached():
        # Call original function
        return call_original_function('get_category_history', category_name)
    
    # Call cached function
    return get_category_history_cached()
