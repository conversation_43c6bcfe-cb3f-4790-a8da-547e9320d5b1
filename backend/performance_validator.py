#!/usr/bin/env python3
"""
Performance Validation Module
Validates and benchmarks optimization performance improvements
"""

import time
import statistics
import concurrent.futures
from typing import Dict, Any, List, Callable, Optional
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceValidator:
    """Validates performance improvements from optimizations"""
    
    def __init__(self):
        self.benchmark_results = []
        self.baseline_metrics = {}
        self.optimization_metrics = {}
    
    def benchmark_function(self, 
                          func: Callable,
                          iterations: int = 10,
                          warmup_iterations: int = 3,
                          *args, **kwargs) -> Dict[str, Any]:
        """
        Benchmark a function's performance
        
        Args:
            func: Function to benchmark
            iterations: Number of test iterations
            warmup_iterations: Number of warmup iterations
            *args, **kwargs: Function arguments
            
        Returns:
            Performance metrics
        """
        logger.info(f"🏃 Benchmarking {func.__name__} ({iterations} iterations)")
        
        # Warmup runs
        for _ in range(warmup_iterations):
            try:
                func(*args, **kwargs)
            except Exception as e:
                logger.warning(f"⚠️ Warmup iteration failed: {e}")
        
        # Actual benchmark runs
        execution_times = []
        successful_runs = 0
        errors = []
        
        for i in range(iterations):
            try:
                start_time = time.perf_counter()
                result = func(*args, **kwargs)
                end_time = time.perf_counter()
                
                execution_time = end_time - start_time
                execution_times.append(execution_time)
                successful_runs += 1
                
                logger.debug(f"  Run {i+1}: {execution_time:.4f}s")
                
            except Exception as e:
                errors.append(str(e))
                logger.warning(f"⚠️ Benchmark iteration {i+1} failed: {e}")
        
        if not execution_times:
            return {
                'function': func.__name__,
                'success': False,
                'error': 'All benchmark iterations failed',
                'errors': errors
            }
        
        # Calculate statistics
        metrics = {
            'function': func.__name__,
            'success': True,
            'iterations': iterations,
            'successful_runs': successful_runs,
            'error_rate': (iterations - successful_runs) / iterations * 100,
            'execution_times': execution_times,
            'min_time': min(execution_times),
            'max_time': max(execution_times),
            'mean_time': statistics.mean(execution_times),
            'median_time': statistics.median(execution_times),
            'std_dev': statistics.stdev(execution_times) if len(execution_times) > 1 else 0,
            'p95_time': self._percentile(execution_times, 95),
            'p99_time': self._percentile(execution_times, 99),
            'errors': errors,
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"✅ {func.__name__}: {metrics['mean_time']:.4f}s avg, {metrics['error_rate']:.1f}% errors")
        
        return metrics
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile of data"""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def compare_performance(self, 
                          baseline_func: Callable,
                          optimized_func: Callable,
                          test_cases: List[tuple],
                          iterations: int = 10) -> Dict[str, Any]:
        """
        Compare performance between baseline and optimized functions
        
        Args:
            baseline_func: Original function
            optimized_func: Optimized function
            test_cases: List of (args, kwargs) tuples for testing
            iterations: Number of iterations per test case
            
        Returns:
            Comparison results
        """
        logger.info(f"⚖️ Comparing {baseline_func.__name__} vs {optimized_func.__name__}")
        
        comparison_results = {
            'baseline_function': baseline_func.__name__,
            'optimized_function': optimized_func.__name__,
            'test_cases': len(test_cases),
            'iterations_per_case': iterations,
            'results': [],
            'summary': {}
        }
        
        all_baseline_times = []
        all_optimized_times = []
        improvements = []
        
        for i, (args, kwargs) in enumerate(test_cases):
            logger.info(f"📊 Test case {i+1}/{len(test_cases)}")
            
            # Benchmark baseline
            baseline_metrics = self.benchmark_function(
                baseline_func, iterations, 2, *args, **kwargs
            )
            
            # Benchmark optimized
            optimized_metrics = self.benchmark_function(
                optimized_func, iterations, 2, *args, **kwargs
            )
            
            # Calculate improvement
            if baseline_metrics['success'] and optimized_metrics['success']:
                baseline_time = baseline_metrics['mean_time']
                optimized_time = optimized_metrics['mean_time']
                
                improvement = ((baseline_time - optimized_time) / baseline_time) * 100
                improvements.append(improvement)
                
                all_baseline_times.extend(baseline_metrics['execution_times'])
                all_optimized_times.extend(optimized_metrics['execution_times'])
                
                case_result = {
                    'test_case': i + 1,
                    'baseline': baseline_metrics,
                    'optimized': optimized_metrics,
                    'improvement_percent': improvement,
                    'speedup_factor': baseline_time / optimized_time if optimized_time > 0 else 0
                }
                
                logger.info(f"  📈 Improvement: {improvement:+.1f}% ({baseline_time:.4f}s → {optimized_time:.4f}s)")
                
            else:
                case_result = {
                    'test_case': i + 1,
                    'baseline': baseline_metrics,
                    'optimized': optimized_metrics,
                    'improvement_percent': None,
                    'error': 'One or both functions failed'
                }
                
                logger.warning(f"  ⚠️ Test case {i+1} failed")
            
            comparison_results['results'].append(case_result)
        
        # Calculate overall summary
        if improvements:
            comparison_results['summary'] = {
                'average_improvement': statistics.mean(improvements),
                'median_improvement': statistics.median(improvements),
                'best_improvement': max(improvements),
                'worst_improvement': min(improvements),
                'consistent_improvement': all(imp > 0 for imp in improvements),
                'baseline_avg_time': statistics.mean(all_baseline_times),
                'optimized_avg_time': statistics.mean(all_optimized_times),
                'overall_speedup': statistics.mean(all_baseline_times) / statistics.mean(all_optimized_times)
            }
            
            logger.info(f"🎯 Overall improvement: {comparison_results['summary']['average_improvement']:+.1f}%")
        else:
            comparison_results['summary'] = {'error': 'No successful comparisons'}
        
        return comparison_results
    
    def stress_test(self, 
                   func: Callable,
                   concurrent_users: int = 10,
                   duration_seconds: int = 30,
                   *args, **kwargs) -> Dict[str, Any]:
        """
        Perform stress test on a function
        
        Args:
            func: Function to stress test
            concurrent_users: Number of concurrent users
            duration_seconds: Test duration in seconds
            *args, **kwargs: Function arguments
            
        Returns:
            Stress test results
        """
        logger.info(f"💪 Stress testing {func.__name__} ({concurrent_users} users, {duration_seconds}s)")
        
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        results = {
            'function': func.__name__,
            'concurrent_users': concurrent_users,
            'duration_seconds': duration_seconds,
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'execution_times': [],
            'errors': [],
            'requests_per_second': 0,
            'average_response_time': 0,
            'error_rate': 0
        }
        
        def worker():
            """Worker function for stress testing"""
            worker_results = {
                'requests': 0,
                'successes': 0,
                'failures': 0,
                'times': [],
                'errors': []
            }
            
            while time.time() < end_time:
                try:
                    request_start = time.perf_counter()
                    func(*args, **kwargs)
                    request_end = time.perf_counter()
                    
                    worker_results['requests'] += 1
                    worker_results['successes'] += 1
                    worker_results['times'].append(request_end - request_start)
                    
                except Exception as e:
                    worker_results['requests'] += 1
                    worker_results['failures'] += 1
                    worker_results['errors'].append(str(e))
                
                # Small delay to prevent overwhelming
                time.sleep(0.01)
            
            return worker_results
        
        # Run concurrent workers
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(worker) for _ in range(concurrent_users)]
            worker_results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Aggregate results
        for worker_result in worker_results:
            results['total_requests'] += worker_result['requests']
            results['successful_requests'] += worker_result['successes']
            results['failed_requests'] += worker_result['failures']
            results['execution_times'].extend(worker_result['times'])
            results['errors'].extend(worker_result['errors'])
        
        # Calculate metrics
        if results['total_requests'] > 0:
            results['requests_per_second'] = results['total_requests'] / duration_seconds
            results['error_rate'] = (results['failed_requests'] / results['total_requests']) * 100
        
        if results['execution_times']:
            results['average_response_time'] = statistics.mean(results['execution_times'])
            results['min_response_time'] = min(results['execution_times'])
            results['max_response_time'] = max(results['execution_times'])
            results['p95_response_time'] = self._percentile(results['execution_times'], 95)
        
        logger.info(f"💪 Stress test complete: {results['requests_per_second']:.1f} RPS, {results['error_rate']:.1f}% errors")
        
        return results
    
    def validate_optimization_impact(self, 
                                   csv_data_dir: str,
                                   test_categories: List[str] = None) -> Dict[str, Any]:
        """
        Validate the overall impact of optimizations
        
        Args:
            csv_data_dir: CSV data directory
            test_categories: Categories to test (uses defaults if None)
            
        Returns:
            Validation results
        """
        logger.info("🔍 Validating optimization impact...")
        
        if test_categories is None:
            test_categories = ['Graphic_Design_Templates']  # Default test category
        
        validation_results = {
            'timestamp': datetime.now().isoformat(),
            'test_categories': test_categories,
            'csv_data_dir': csv_data_dir,
            'performance_tests': [],
            'stress_tests': [],
            'summary': {}
        }
        
        # Import functions for testing
        try:
            from app import get_category_data as original_get_category_data
            from optimization_service import get_optimization_service
            
            # Get optimization service
            optimization_service = get_optimization_service(csv_data_dir)
            
            if optimization_service:
                # Create optimized version
                @optimization_service.smart_cache(key_prefix="test", expiration=300)
                def optimized_get_category_data(category):
                    return original_get_category_data(category)
                
                # Performance comparison
                test_cases = [(category,) for category in test_categories]
                
                comparison = self.compare_performance(
                    original_get_category_data,
                    optimized_get_category_data,
                    [(args, {}) for args in test_cases],
                    iterations=5
                )
                
                validation_results['performance_tests'].append(comparison)
                
                # Stress test optimized version
                if test_categories:
                    stress_result = self.stress_test(
                        optimized_get_category_data,
                        5,  # concurrent_users
                        10,  # duration_seconds
                        test_categories[0]
                    )
                    validation_results['stress_tests'].append(stress_result)
                
                # Generate summary
                if comparison['summary'] and 'average_improvement' in comparison['summary']:
                    validation_results['summary'] = {
                        'optimization_available': True,
                        'performance_improvement': comparison['summary']['average_improvement'],
                        'speedup_factor': comparison['summary'].get('overall_speedup', 1.0),
                        'stress_test_rps': stress_result.get('requests_per_second', 0),
                        'stress_test_error_rate': stress_result.get('error_rate', 0),
                        'recommendation': self._generate_recommendation(comparison, stress_result)
                    }
                else:
                    validation_results['summary'] = {
                        'optimization_available': True,
                        'error': 'Performance comparison failed'
                    }
            else:
                validation_results['summary'] = {
                    'optimization_available': False,
                    'message': 'Optimization service not available'
                }
                
        except Exception as e:
            logger.error(f"❌ Validation error: {e}")
            validation_results['summary'] = {
                'optimization_available': False,
                'error': str(e)
            }
        
        return validation_results
    
    def _generate_recommendation(self, 
                               performance_comparison: Dict[str, Any],
                               stress_test: Dict[str, Any]) -> str:
        """Generate recommendation based on test results"""
        
        avg_improvement = performance_comparison['summary'].get('average_improvement', 0)
        error_rate = stress_test.get('error_rate', 100)
        rps = stress_test.get('requests_per_second', 0)
        
        if avg_improvement > 20 and error_rate < 5:
            return "✅ RECOMMENDED: Significant performance improvement with low error rate"
        elif avg_improvement > 10 and error_rate < 10:
            return "✅ RECOMMENDED: Good performance improvement with acceptable error rate"
        elif avg_improvement > 0 and error_rate < 15:
            return "⚠️ CONDITIONAL: Moderate improvement, monitor error rates"
        elif error_rate > 20:
            return "❌ NOT RECOMMENDED: High error rate detected"
        else:
            return "⚠️ REVIEW NEEDED: Mixed results, manual review required"

if __name__ == "__main__":
    # Test performance validator
    print("🧪 Testing Performance Validator...")
    
    validator = PerformanceValidator()
    
    # Test benchmark function
    def test_function(n):
        return sum(range(n))
    
    metrics = validator.benchmark_function(test_function, iterations=5, n=1000)
    print(f"📊 Benchmark result: {metrics['mean_time']:.4f}s average")
    
    # Test comparison
    def slow_function(n):
        time.sleep(0.01)  # Simulate slow operation
        return sum(range(n))
    
    def fast_function(n):
        return sum(range(n))
    
    comparison = validator.compare_performance(
        slow_function, fast_function, 
        [((100,), {})], 
        iterations=3
    )
    
    if comparison['summary'] and 'average_improvement' in comparison['summary']:
        print(f"⚖️ Comparison: {comparison['summary']['average_improvement']:+.1f}% improvement")
    
    print("✅ Performance validator test completed")
