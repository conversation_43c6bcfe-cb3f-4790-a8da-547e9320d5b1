#!/usr/bin/env python3
"""
Thread-Safe File Monitoring Module
Monitors CSV files for changes and provides cache invalidation
"""

import os
import threading
import queue
import time
import hashlib
from typing import Optional, Callable, Dict, Set
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from concurrent.futures import ThreadPoolExecutor
import logging
from config import config

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ThreadSafeFileMonitor:
    """Thread-safe file monitoring with proper synchronization"""
    
    def __init__(self, 
                 watch_directory: str,
                 redis_client=None,
                 max_workers: int = 2):
        """
        Initialize file monitor
        
        Args:
            watch_directory: Directory to monitor
            redis_client: Redis client for cache invalidation
            max_workers: Maximum worker threads for processing
        """
        self.watch_directory = watch_directory
        self.redis_client = redis_client
        self.max_workers = max_workers
        
        # Thread-safe components
        self.file_change_queue = queue.Queue(maxsize=config.FILE_MONITOR_QUEUE_SIZE)
        self.processing_lock = threading.RLock()
        self.shutdown_event = threading.Event()
        
        # File tracking
        self.file_hashes: Dict[str, str] = {}
        self.cache_dependencies: Dict[str, Set[str]] = {}
        
        # Thread pool for processing file changes
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # Observer and handler
        self.observer: Optional[Observer] = None
        self.event_handler: Optional[FileSystemEventHandler] = None
        
        # Statistics
        self.files_processed = 0
        self.cache_invalidations = 0
        self.errors_count = 0
        
        # Background processor thread
        self.processor_thread = threading.Thread(
            target=self._process_file_changes,
            daemon=True,
            name="FileMonitorProcessor"
        )
        
        # Initialize file hashes
        self._initialize_file_hashes()
        
        logger.info(f"🔧 File monitor initialized for: {watch_directory}")
    
    def _initialize_file_hashes(self):
        """Initialize file hashes for existing files"""
        if not os.path.exists(self.watch_directory):
            logger.warning(f"⚠️ Watch directory does not exist: {self.watch_directory}")
            return
        
        try:
            for filename in os.listdir(self.watch_directory):
                if filename.endswith('.csv'):
                    filepath = os.path.join(self.watch_directory, filename)
                    if os.path.isfile(filepath):
                        file_hash = self._get_file_hash(filepath)
                        if file_hash:
                            self.file_hashes[filepath] = file_hash
            
            logger.info(f"📁 Initialized hashes for {len(self.file_hashes)} CSV files")
            
        except Exception as e:
            logger.error(f"❌ Error initializing file hashes: {e}")
    
    def _get_file_hash(self, filepath: str) -> Optional[str]:
        """Get MD5 hash of file content"""
        try:
            with open(filepath, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()
        except Exception as e:
            logger.warning(f"⚠️ Error hashing file {filepath}: {e}")
            return None
    
    def _process_file_changes(self):
        """Background thread to process file changes"""
        logger.info("👁️ File change processor started")
        
        while not self.shutdown_event.is_set():
            try:
                # Get file change from queue (with timeout)
                filepath = self.file_change_queue.get(timeout=1.0)
                
                # Submit to thread pool for processing
                future = self.executor.submit(self._handle_file_change_safe, filepath)
                
                # Mark task as done
                self.file_change_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"❌ Error in file change processor: {e}")
                self.errors_count += 1
        
        logger.info("🛑 File change processor stopped")
    
    def _handle_file_change_safe(self, filepath: str):
        """Thread-safe file change handler"""
        with self.processing_lock:
            try:
                filename = os.path.basename(filepath)
                
                if config.LOG_FILE_OPERATIONS:
                    logger.info(f"📁 Processing file change: {filename}")
                
                # Check if file actually changed
                if self._has_file_changed(filepath):
                    # Extract category from filename
                    category_name = self._extract_category_from_filename(filename)
                    if category_name:
                        self._invalidate_category_cache(category_name)
                        logger.info(f"🗑️ Cache invalidated for category: {category_name}")
                    
                    # Update file hash
                    new_hash = self._get_file_hash(filepath)
                    if new_hash:
                        self.file_hashes[filepath] = new_hash
                    
                    self.files_processed += 1
                else:
                    logger.debug(f"📁 File {filename} unchanged (hash match)")
                
            except Exception as e:
                logger.error(f"❌ Error processing file change {filepath}: {e}")
                self.errors_count += 1
    
    def _has_file_changed(self, filepath: str) -> bool:
        """Check if file has actually changed by comparing hashes"""
        if not os.path.exists(filepath):
            # File was deleted
            if filepath in self.file_hashes:
                del self.file_hashes[filepath]
            return True
        
        current_hash = self._get_file_hash(filepath)
        if not current_hash:
            return False
        
        old_hash = self.file_hashes.get(filepath)
        return current_hash != old_hash
    
    def _extract_category_from_filename(self, filename: str) -> Optional[str]:
        """Extract category name from CSV filename"""
        try:
            # Handle different filename patterns
            # Pattern 1: Category_Name_DD_MM_YYYY.csv
            # Pattern 2: EE_category_name_DD_MM_YYYY.csv
            
            if filename.startswith('EE_'):
                # Remove EE_ prefix and extract category
                parts = filename[3:].split('_')
                if len(parts) >= 4:  # category_name_DD_MM_YYYY.csv
                    # Join all parts except last 3 (DD_MM_YYYY.csv)
                    category = '_'.join(parts[:-3])
                    return category
            else:
                # Standard pattern
                parts = filename.split('_')
                if len(parts) >= 4:  # Category_Name_DD_MM_YYYY.csv
                    # Join all parts except last 3 (DD_MM_YYYY.csv)
                    category = '_'.join(parts[:-3])
                    return category
            
            logger.warning(f"⚠️ Could not extract category from filename: {filename}")
            return None
            
        except Exception as e:
            logger.error(f"❌ Error extracting category from {filename}: {e}")
            return None
    
    def _invalidate_category_cache(self, category_name: str):
        """Invalidate all cache entries for a category"""
        if not self.redis_client:
            logger.debug("⚠️ No Redis client, skipping cache invalidation")
            return
        
        try:
            # Find all cache keys for this category
            patterns = [
                f"*{category_name}*",
                f"category_data:*{category_name}*",
                f"categories_list:*"  # Also invalidate categories list
            ]
            
            deleted_count = 0
            for pattern in patterns:
                try:
                    for key in self.redis_client.scan_iter(match=pattern):
                        self.redis_client.delete(key)
                        deleted_count += 1
                except Exception as e:
                    logger.warning(f"⚠️ Error scanning pattern {pattern}: {e}")
            
            if deleted_count > 0:
                logger.info(f"🗑️ Deleted {deleted_count} cache entries for {category_name}")
                self.cache_invalidations += deleted_count
            
        except Exception as e:
            logger.error(f"❌ Error invalidating cache for {category_name}: {e}")
            self.errors_count += 1
    
    def queue_file_change(self, filepath: str):
        """Queue a file change for processing"""
        try:
            self.file_change_queue.put_nowait(filepath)
            logger.debug(f"📥 Queued file change: {os.path.basename(filepath)}")
        except queue.Full:
            logger.warning(f"⚠️ File change queue full, dropping: {filepath}")
            self.errors_count += 1
    
    def start(self) -> bool:
        """Start file monitoring"""
        if self.observer and self.observer.is_alive():
            logger.warning("⚠️ File monitor already running")
            return True
        
        try:
            # Start processor thread
            if not self.processor_thread.is_alive():
                self.processor_thread.start()
            
            # Create event handler
            self.event_handler = ThreadSafeFileHandler(self)
            
            # Create and start observer
            self.observer = Observer()
            self.observer.schedule(
                self.event_handler,
                self.watch_directory,
                recursive=config.FILE_MONITOR_RECURSIVE
            )
            self.observer.start()
            
            logger.info(f"👁️ File monitoring started for: {self.watch_directory}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start file monitoring: {e}")
            return False
    
    def stop(self):
        """Stop file monitoring"""
        logger.info("🛑 Stopping file monitor...")
        
        # Signal shutdown
        self.shutdown_event.set()
        
        # Stop observer
        if self.observer:
            self.observer.stop()
            self.observer.join(timeout=5.0)
            self.observer = None
        
        # Wait for queue to empty
        try:
            self.file_change_queue.join()
        except Exception:
            pass
        
        # Shutdown thread pool
        self.executor.shutdown(wait=True)
        
        # Wait for processor thread
        if self.processor_thread.is_alive():
            self.processor_thread.join(timeout=5.0)
        
        logger.info("✅ File monitor stopped")
    
    def get_stats(self) -> dict:
        """Get file monitoring statistics"""
        return {
            'watch_directory': self.watch_directory,
            'files_tracked': len(self.file_hashes),
            'files_processed': self.files_processed,
            'cache_invalidations': self.cache_invalidations,
            'errors_count': self.errors_count,
            'queue_size': self.file_change_queue.qsize(),
            'is_running': self.observer.is_alive() if self.observer else False,
            'processor_alive': self.processor_thread.is_alive()
        }

class ThreadSafeFileHandler(FileSystemEventHandler):
    """Thread-safe file system event handler"""
    
    def __init__(self, file_monitor: ThreadSafeFileMonitor):
        self.file_monitor = file_monitor
        super().__init__()
    
    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith('.csv'):
            self.file_monitor.queue_file_change(event.src_path)
    
    def on_created(self, event):
        if not event.is_directory and event.src_path.endswith('.csv'):
            self.file_monitor.queue_file_change(event.src_path)
    
    def on_deleted(self, event):
        if not event.is_directory and event.src_path.endswith('.csv'):
            self.file_monitor.queue_file_change(event.src_path)
    
    def on_moved(self, event):
        if not event.is_directory and event.dest_path.endswith('.csv'):
            self.file_monitor.queue_file_change(event.dest_path)

# Global file monitor instance
file_monitor: Optional[ThreadSafeFileMonitor] = None

def get_file_monitor(watch_directory: str, redis_client=None) -> Optional[ThreadSafeFileMonitor]:
    """Get global file monitor instance"""
    global file_monitor
    
    if not config.ENABLE_FILE_MONITORING:
        return None
    
    if file_monitor is None:
        try:
            file_monitor = ThreadSafeFileMonitor(watch_directory, redis_client)
        except Exception as e:
            logger.error(f"❌ Failed to initialize file monitor: {e}")
            file_monitor = None
    
    return file_monitor

def shutdown_file_monitor():
    """Shutdown global file monitor"""
    global file_monitor
    if file_monitor:
        file_monitor.stop()
        file_monitor = None

if __name__ == "__main__":
    # Test file monitor
    import tempfile
    
    print("🧪 Testing File Monitor...")
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Using temp directory: {temp_dir}")
        
        # Create test CSV file
        test_file = os.path.join(temp_dir, "Test_Category_17_07_2025.csv")
        with open(test_file, 'w') as f:
            f.write("Title,Author,Rank\nTest,TestAuthor,1\n")
        
        # Initialize monitor
        monitor = ThreadSafeFileMonitor(temp_dir)
        monitor.start()
        
        print(f"📊 Initial stats: {monitor.get_stats()}")
        
        # Modify file
        time.sleep(1)
        with open(test_file, 'a') as f:
            f.write("Test2,TestAuthor2,2\n")
        
        # Wait for processing
        time.sleep(2)
        
        print(f"📊 Final stats: {monitor.get_stats()}")
        
        monitor.stop()
