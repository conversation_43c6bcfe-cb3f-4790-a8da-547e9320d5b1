#!/usr/bin/env python3
"""
Authentication Middleware for OTP System
Provides decorators for protecting endpoints with session validation
"""

from functools import wraps
from flask import request, jsonify, g
from typing import Optional, Dict, Callable
import logging

# Setup logging
logger = logging.getLogger(__name__)

# Global session manager instance (will be set from app.py)
session_manager = None

def init_auth_middleware(session_mgr):
    """Initialize middleware with session manager"""
    global session_manager
    session_manager = session_mgr
    logger.info("🔐 Auth middleware initialized")

def get_session_token() -> Optional[str]:
    """Extract session token from Authorization header"""
    auth_header = request.headers.get('Authorization', '')
    
    if auth_header.startswith('Bearer '):
        return auth_header[7:]  # Remove 'Bearer ' prefix
    
    return None

def validate_session_token(session_token: str) -> Dict[str, any]:
    """Validate session token and return user data"""
    if not session_manager:
        return {
            'success': False,
            'error': 'Session manager not initialized',
            'code': 'SERVICE_UNAVAILABLE'
        }
    
    return session_manager.validate_session(session_token)

def require_auth(f: Callable) -> Callable:
    """
    Decorator to require valid authentication for endpoint
    
    Usage:
        @app.route('/protected')
        @require_auth
        def protected_endpoint():
            user = g.current_user
            return jsonify({'user': user})
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get session token
        session_token = get_session_token()
        
        if not session_token:
            return jsonify({
                'success': False,
                'error': 'Authorization header required',
                'code': 'AUTHORIZATION_REQUIRED'
            }), 401
        
        # Validate session
        result = validate_session_token(session_token)
        
        if not result.get('success'):
            return jsonify({
                'success': False,
                'error': result.get('error', 'Invalid session'),
                'code': result.get('code', 'INVALID_SESSION')
            }), 401
        
        # Store user info in Flask's g object
        g.current_user = result
        g.session_token = session_token
        
        return f(*args, **kwargs)
    
    return decorated_function

def optional_auth(f: Callable) -> Callable:
    """
    Decorator for optional authentication
    Endpoint works with or without authentication, but provides user info if authenticated
    
    Usage:
        @app.route('/optional-protected')
        @optional_auth
        def optional_endpoint():
            user = g.get('current_user')  # None if not authenticated
            if user:
                return jsonify({'message': f'Hello {user["email"]}'})
            else:
                return jsonify({'message': 'Hello anonymous user'})
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get session token
        session_token = get_session_token()
        
        if session_token:
            # Validate session
            result = validate_session_token(session_token)
            
            if result.get('success'):
                # Store user info in Flask's g object
                g.current_user = result
                g.session_token = session_token
            else:
                # Invalid session, but continue without auth
                g.current_user = None
                g.session_token = None
        else:
            # No session token, continue without auth
            g.current_user = None
            g.session_token = None
        
        return f(*args, **kwargs)
    
    return decorated_function

def require_email(allowed_emails: list) -> Callable:
    """
    Decorator to restrict access to specific email addresses
    
    Usage:
        @app.route('/admin-only')
        @require_auth
        @require_email(['<EMAIL>', '<EMAIL>'])
        def admin_endpoint():
            return jsonify({'message': 'Admin access granted'})
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # This decorator should be used after @require_auth
            if not hasattr(g, 'current_user') or not g.current_user:
                return jsonify({
                    'success': False,
                    'error': 'Authentication required',
                    'code': 'AUTHENTICATION_REQUIRED'
                }), 401
            
            user_email = g.current_user.get('email', '')
            
            if user_email not in allowed_emails:
                return jsonify({
                    'success': False,
                    'error': 'Access denied for this email address',
                    'code': 'EMAIL_ACCESS_DENIED'
                }), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def rate_limit_by_user(max_requests: int = 100, window_seconds: int = 3600) -> Callable:
    """
    Decorator for rate limiting by authenticated user
    
    Usage:
        @app.route('/limited')
        @require_auth
        @rate_limit_by_user(max_requests=10, window_seconds=60)
        def limited_endpoint():
            return jsonify({'message': 'Rate limited endpoint'})
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # This decorator should be used after @require_auth
            if not hasattr(g, 'current_user') or not g.current_user:
                return jsonify({
                    'success': False,
                    'error': 'Authentication required',
                    'code': 'AUTHENTICATION_REQUIRED'
                }), 401
            
            user_email = g.current_user.get('email', '')
            
            # Check rate limit using Redis (if available)
            if session_manager and hasattr(session_manager, 'redis_client') and session_manager.redis_client:
                try:
                    rate_key = f"rate_limit:user:{user_email}:{f.__name__}"
                    current_count = session_manager.redis_client.get(rate_key)
                    
                    if current_count is None:
                        # First request in window
                        session_manager.redis_client.setex(rate_key, window_seconds, 1)
                    else:
                        current_count = int(current_count)
                        if current_count >= max_requests:
                            return jsonify({
                                'success': False,
                                'error': f'Rate limit exceeded. Max {max_requests} requests per {window_seconds} seconds',
                                'code': 'RATE_LIMIT_EXCEEDED'
                            }), 429
                        
                        # Increment counter
                        session_manager.redis_client.incr(rate_key)
                
                except Exception as e:
                    logger.warning(f"Rate limiting error: {e}")
                    # Continue without rate limiting on error
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def get_current_user() -> Optional[Dict[str, any]]:
    """
    Helper function to get current authenticated user
    
    Returns:
        User data dict if authenticated, None otherwise
    """
    return getattr(g, 'current_user', None)

def get_current_session_token() -> Optional[str]:
    """
    Helper function to get current session token
    
    Returns:
        Session token if authenticated, None otherwise
    """
    return getattr(g, 'session_token', None)

def is_authenticated() -> bool:
    """
    Helper function to check if current request is authenticated
    
    Returns:
        True if authenticated, False otherwise
    """
    return get_current_user() is not None

def get_user_email() -> Optional[str]:
    """
    Helper function to get current user's email
    
    Returns:
        User email if authenticated, None otherwise
    """
    user = get_current_user()
    return user.get('email') if user else None

# Convenience decorators for common use cases
def admin_required(f: Callable) -> Callable:
    """Decorator for admin-only endpoints (customize email list as needed)"""
    admin_emails = [
        '<EMAIL>',
        '<EMAIL>'  # Add your admin emails here
    ]
    return require_email(admin_emails)(require_auth(f))

def authenticated_required(f: Callable) -> Callable:
    """Alias for require_auth for backward compatibility"""
    return require_auth(f)

# Export commonly used decorators and functions
__all__ = [
    'init_auth_middleware',
    'require_auth',
    'optional_auth',
    'require_email',
    'rate_limit_by_user',
    'admin_required',
    'authenticated_required',
    'get_current_user',
    'get_current_session_token',
    'is_authenticated',
    'get_user_email'
]
