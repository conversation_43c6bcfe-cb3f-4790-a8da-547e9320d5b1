#!/usr/bin/env python3
"""
Final Optimization Integration
Complete optimization setup and validation for Flask Categories Analysis
"""

import os
import sys
import time
from datetime import datetime
from typing import Dict, Any, Optional
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

# Import all optimization modules
from config import config, print_config_status
from config_optimizer import ConfigurationOptimizer
from performance_validator import PerformanceValidator
from monitoring import create_monitoring_dashboard
from feature_controller import FeatureController

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalOptimizer:
    """Complete optimization setup and management"""
    
    def __init__(self, csv_data_dir: str):
        self.csv_data_dir = csv_data_dir
        self.config_optimizer = ConfigurationOptimizer()
        self.performance_validator = PerformanceValidator()
        self.feature_controller = FeatureController()
        self.optimization_results = {}
    
    def run_complete_optimization(self, 
                                usage_pattern: str = 'balanced',
                                enable_features: bool = True,
                                validate_performance: bool = True) -> Dict[str, Any]:
        """
        Run complete optimization process
        
        Args:
            usage_pattern: Expected usage pattern ('light', 'balanced', 'heavy')
            enable_features: Whether to enable optimized features
            validate_performance: Whether to validate performance improvements
            
        Returns:
            Complete optimization results
        """
        logger.info("🚀 Starting complete Flask optimization process...")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'csv_data_dir': self.csv_data_dir,
            'usage_pattern': usage_pattern,
            'phases': {},
            'overall_success': False,
            'recommendations': []
        }
        
        try:
            # Phase 1: System Analysis & Configuration Optimization
            logger.info("📊 Phase 1: System Analysis & Configuration Optimization")
            config_result = self.config_optimizer.optimize_configuration(
                self.csv_data_dir, usage_pattern, validate_performance=False
            )
            results['phases']['configuration'] = config_result
            
            if 'error' in config_result:
                results['recommendations'].append("⚠️ Configuration optimization failed, using defaults")
            else:
                results['recommendations'].append("✅ Configuration optimized for system resources")
            
            # Phase 2: Feature Enablement (if requested)
            if enable_features:
                logger.info("🔧 Phase 2: Gradual Feature Enablement")
                
                # Define rollout plan based on system classification
                system_class = config_result.get('system_analysis', {}).get('overall_classification', 'medium_performance')
                
                if system_class == 'high_performance':
                    rollout_plan = ['compression', 'caching', 'file_monitoring', 'optimizations']
                elif system_class == 'medium_performance':
                    rollout_plan = ['compression', 'caching', 'optimizations']
                else:
                    rollout_plan = ['compression']
                
                feature_result = self.feature_controller.gradual_rollout(self.csv_data_dir, rollout_plan)
                results['phases']['feature_enablement'] = feature_result
                
                if feature_result['overall_success']:
                    results['recommendations'].append(f"✅ Features enabled: {', '.join(rollout_plan)}")
                else:
                    stopped_at = feature_result.get('stopped_at', 'unknown')
                    results['recommendations'].append(f"⚠️ Feature rollout stopped at: {stopped_at}")
            
            # Phase 3: Performance Validation (if requested)
            if validate_performance:
                logger.info("📈 Phase 3: Performance Validation")
                
                # Wait for features to stabilize
                time.sleep(3)
                
                validation_result = self.performance_validator.validate_optimization_impact(self.csv_data_dir)
                results['phases']['performance_validation'] = validation_result
                
                if validation_result.get('summary', {}).get('optimization_available'):
                    improvement = validation_result['summary'].get('performance_improvement', 0)
                    if improvement > 0:
                        results['recommendations'].append(f"📈 Performance improved by {improvement:+.1f}%")
                    else:
                        results['recommendations'].append("📊 Performance validation completed (no significant change)")
                else:
                    results['recommendations'].append("⚠️ Performance validation unavailable")
            
            # Phase 4: Create Monitoring Dashboard
            logger.info("📊 Phase 4: Setting up Monitoring")
            try:
                create_monitoring_dashboard()
                results['phases']['monitoring'] = {'success': True, 'dashboard_created': True}
                results['recommendations'].append("📊 Monitoring dashboard created")
            except Exception as e:
                results['phases']['monitoring'] = {'success': False, 'error': str(e)}
                results['recommendations'].append("⚠️ Monitoring dashboard creation failed")
            
            # Determine overall success
            results['overall_success'] = self._determine_overall_success(results)
            
            # Generate final summary
            results['summary'] = self._generate_final_summary(results)
            
            logger.info("🎉 Complete optimization process finished")
            
        except Exception as e:
            logger.error(f"❌ Optimization process failed: {e}")
            results['error'] = str(e)
            results['recommendations'].append(f"❌ Optimization failed: {e}")
        
        self.optimization_results = results
        return results
    
    def _determine_overall_success(self, results: Dict[str, Any]) -> bool:
        """Determine if optimization was overall successful"""
        phases = results.get('phases', {})
        
        # Configuration phase should succeed
        config_success = 'error' not in phases.get('configuration', {})
        
        # Feature enablement should succeed if attempted
        feature_success = True
        if 'feature_enablement' in phases:
            feature_success = phases['feature_enablement'].get('overall_success', False)
        
        # Performance validation is optional
        perf_success = True
        if 'performance_validation' in phases:
            perf_validation = phases['performance_validation']
            perf_success = perf_validation.get('summary', {}).get('optimization_available', True)
        
        # Monitoring is optional
        monitoring_success = phases.get('monitoring', {}).get('success', True)
        
        return config_success and feature_success and perf_success
    
    def _generate_final_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final optimization summary"""
        phases = results.get('phases', {})
        
        summary = {
            'optimization_completed': results['overall_success'],
            'phases_completed': len(phases),
            'features_enabled': [],
            'performance_improvement': 0,
            'system_classification': 'unknown',
            'recommendations_count': len(results.get('recommendations', []))
        }
        
        # Extract system classification
        if 'configuration' in phases:
            summary['system_classification'] = phases['configuration'].get('system_analysis', {}).get('overall_classification', 'unknown')
        
        # Extract enabled features
        if 'feature_enablement' in phases:
            feature_results = phases['feature_enablement'].get('results', [])
            summary['features_enabled'] = [
                result['feature'] for result in feature_results 
                if result.get('success', False)
            ]
        
        # Extract performance improvement
        if 'performance_validation' in phases:
            perf_summary = phases['performance_validation'].get('summary', {})
            summary['performance_improvement'] = perf_summary.get('performance_improvement', 0)
        
        return summary
    
    def print_optimization_report(self, results: Optional[Dict[str, Any]] = None):
        """Print detailed optimization report"""
        if results is None:
            results = self.optimization_results
        
        if not results:
            print("❌ No optimization results available")
            return
        
        print("\n" + "="*80)
        print("🎯 FLASK CATEGORIES ANALYSIS - OPTIMIZATION REPORT")
        print("="*80)
        
        # Header information
        print(f"📅 Timestamp: {results['timestamp']}")
        print(f"📁 CSV Directory: {results['csv_data_dir']}")
        print(f"⚙️ Usage Pattern: {results['usage_pattern']}")
        print(f"✅ Overall Success: {'YES' if results['overall_success'] else 'NO'}")
        
        # Summary
        if 'summary' in results:
            summary = results['summary']
            print(f"\n📊 SUMMARY:")
            print(f"   System Classification: {summary['system_classification']}")
            print(f"   Phases Completed: {summary['phases_completed']}")
            print(f"   Features Enabled: {', '.join(summary['features_enabled']) if summary['features_enabled'] else 'None'}")
            if summary['performance_improvement'] != 0:
                print(f"   Performance Improvement: {summary['performance_improvement']:+.1f}%")
        
        # Phase details
        phases = results.get('phases', {})
        
        if 'configuration' in phases:
            config_phase = phases['configuration']
            print(f"\n🔧 CONFIGURATION OPTIMIZATION:")
            if 'system_analysis' in config_phase:
                sys_analysis = config_phase['system_analysis']
                print(f"   CPU: {sys_analysis.get('cpu', {}).get('cores', 'Unknown')} cores")
                print(f"   Memory: {sys_analysis.get('memory', {}).get('total_gb', 'Unknown')}GB")
                print(f"   Classification: {sys_analysis.get('overall_classification', 'Unknown')}")
            
            if 'optimal_config' in config_phase:
                opt_config = config_phase['optimal_config']
                print(f"   Cache Size: {opt_config.get('MAX_CACHE_SIZE', 0)//1024//1024}MB")
                print(f"   Cache Timeout: {opt_config.get('CACHE_DEFAULT_TIMEOUT', 0)}s")
        
        if 'feature_enablement' in phases:
            feature_phase = phases['feature_enablement']
            print(f"\n🚀 FEATURE ENABLEMENT:")
            print(f"   Rollout Plan: {', '.join(feature_phase.get('rollout_plan', []))}")
            print(f"   Success: {'YES' if feature_phase.get('overall_success') else 'NO'}")
            if feature_phase.get('stopped_at'):
                print(f"   Stopped At: {feature_phase['stopped_at']}")
        
        if 'performance_validation' in phases:
            perf_phase = phases['performance_validation']
            print(f"\n📈 PERFORMANCE VALIDATION:")
            perf_summary = perf_phase.get('summary', {})
            if perf_summary.get('optimization_available'):
                print(f"   Performance Improvement: {perf_summary.get('performance_improvement', 0):+.1f}%")
                print(f"   Speedup Factor: {perf_summary.get('speedup_factor', 1.0):.2f}x")
                print(f"   Recommendation: {perf_summary.get('recommendation', 'N/A')}")
            else:
                print(f"   Status: Not available")
        
        # Recommendations
        recommendations = results.get('recommendations', [])
        if recommendations:
            print(f"\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        
        # Next steps
        print(f"\n🎯 NEXT STEPS:")
        if results['overall_success']:
            print("   1. ✅ Optimization completed successfully")
            print("   2. 📊 Monitor performance using the monitoring dashboard")
            print("   3. 🔧 Fine-tune settings based on actual usage patterns")
            print("   4. 📈 Consider enabling additional features if performance is good")
        else:
            print("   1. ⚠️ Review failed phases and error messages")
            print("   2. 🔧 Check system resources and requirements")
            print("   3. 🔄 Try optimization with different usage pattern")
            print("   4. 📞 Consider manual configuration if automatic optimization fails")
        
        print("\n" + "="*80)
        print("🎉 Optimization report complete!")
        print("="*80)

def main():
    """Main optimization function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Flask Categories Analysis Optimizer')
    parser.add_argument('--csv-dir', default='data', help='CSV data directory')
    parser.add_argument('--usage-pattern', choices=['light', 'balanced', 'heavy'], 
                       default='balanced', help='Expected usage pattern')
    parser.add_argument('--no-features', action='store_true', help='Skip feature enablement')
    parser.add_argument('--no-validation', action='store_true', help='Skip performance validation')
    parser.add_argument('--config-only', action='store_true', help='Only show current configuration')
    
    args = parser.parse_args()
    
    # Resolve CSV directory path
    csv_data_dir = os.path.abspath(args.csv_dir)
    
    if args.config_only:
        print("🔧 Current Configuration:")
        print_config_status()
        return
    
    # Run optimization
    optimizer = FinalOptimizer(csv_data_dir)
    
    results = optimizer.run_complete_optimization(
        usage_pattern=args.usage_pattern,
        enable_features=not args.no_features,
        validate_performance=not args.no_validation
    )
    
    # Print report
    optimizer.print_optimization_report(results)
    
    # Exit with appropriate code
    sys.exit(0 if results['overall_success'] else 1)

if __name__ == "__main__":
    main()
