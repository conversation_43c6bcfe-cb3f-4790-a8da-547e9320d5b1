#!/bin/bash

# Redis Setup Script for macOS
# This script provides multiple ways to install and run Redis

set -e

echo "🚀 Redis Setup for Flask Optimizations"
echo "======================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if Redis is running
redis_running() {
    redis-cli ping >/dev/null 2>&1
}

# Function to find available port
find_available_port() {
    local port=6379
    while lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; do
        port=$((port + 1))
    done
    echo $port
}

echo "🔍 Checking current Redis status..."

# Check if Redis is already running
if redis_running; then
    echo "✅ Redis is already running!"
    redis-cli info server | grep redis_version
    exit 0
fi

# Check if Redis is installed but not running
if command_exists redis-server; then
    echo "📦 Redis is installed but not running"
    echo "🚀 Starting Redis server..."
    
    # Find available port
    REDIS_PORT=$(find_available_port)
    echo "🔌 Using port: $REDIS_PORT"
    
    # Start Redis with custom port if needed
    if [ "$REDIS_PORT" != "6379" ]; then
        echo "⚠️ Port 6379 is in use, starting Redis on port $REDIS_PORT"
        redis-server --port $REDIS_PORT --daemonize yes
        echo "REDIS_PORT=$REDIS_PORT" >> .env
    else
        redis-server --daemonize yes
    fi
    
    echo "✅ Redis started successfully!"
    exit 0
fi

echo "❌ Redis is not installed. Installing Redis..."

# Try different installation methods
if command_exists brew; then
    echo "🍺 Installing Redis using Homebrew..."
    brew install redis
    brew services start redis
    echo "✅ Redis installed and started via Homebrew"
    
elif command_exists port; then
    echo "🚢 Installing Redis using MacPorts..."
    sudo port install redis
    sudo port load redis
    echo "✅ Redis installed and started via MacPorts"
    
elif command_exists docker; then
    echo "🐳 Installing Redis using Docker..."
    
    # Check if Redis container already exists
    if docker ps -a | grep -q redis-optimization; then
        echo "📦 Redis container exists, starting it..."
        docker start redis-optimization
    else
        echo "🚀 Creating new Redis container..."
        REDIS_PORT=$(find_available_port)
        docker run -d \
            --name redis-optimization \
            -p $REDIS_PORT:6379 \
            redis:7-alpine \
            redis-server --appendonly yes
        
        if [ "$REDIS_PORT" != "6379" ]; then
            echo "REDIS_PORT=$REDIS_PORT" >> .env
        fi
    fi
    
    echo "✅ Redis container started successfully!"
    
else
    echo "🔧 Installing Redis from source..."
    
    # Create temporary directory
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # Download and compile Redis
    echo "📥 Downloading Redis..."
    curl -O http://download.redis.io/redis-stable.tar.gz
    tar xzf redis-stable.tar.gz
    cd redis-stable
    
    echo "🔨 Compiling Redis..."
    make
    
    # Install to local directory
    REDIS_DIR="$HOME/.local/redis"
    mkdir -p "$REDIS_DIR/bin"
    cp src/redis-server src/redis-cli "$REDIS_DIR/bin/"
    
    # Add to PATH
    echo "export PATH=\"$REDIS_DIR/bin:\$PATH\"" >> ~/.bashrc
    echo "export PATH=\"$REDIS_DIR/bin:\$PATH\"" >> ~/.zshrc
    
    # Start Redis
    REDIS_PORT=$(find_available_port)
    "$REDIS_DIR/bin/redis-server" --port $REDIS_PORT --daemonize yes
    
    if [ "$REDIS_PORT" != "6379" ]; then
        echo "REDIS_PORT=$REDIS_PORT" >> .env
    fi
    
    # Cleanup
    cd /
    rm -rf "$TEMP_DIR"
    
    echo "✅ Redis compiled and started from source!"
fi

# Verify Redis is running
echo "🔍 Verifying Redis installation..."
sleep 2

if redis_running; then
    echo "✅ Redis is running successfully!"
    redis-cli info server | grep redis_version
    
    # Test basic operations
    echo "🧪 Testing Redis operations..."
    redis-cli set test_key "Hello Redis" > /dev/null
    TEST_VALUE=$(redis-cli get test_key)
    redis-cli del test_key > /dev/null
    
    if [ "$TEST_VALUE" = "Hello Redis" ]; then
        echo "✅ Redis operations test passed!"
    else
        echo "⚠️ Redis operations test failed"
    fi
    
else
    echo "❌ Redis installation failed or not running"
    echo "💡 Manual installation options:"
    echo "   1. Install Homebrew: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    echo "   2. Install Docker: https://docs.docker.com/docker-for-mac/install/"
    echo "   3. Use Redis Cloud: https://redis.com/try-free/"
    exit 1
fi

echo ""
echo "🎉 Redis setup complete!"
echo "📝 Configuration:"
echo "   Host: localhost"
echo "   Port: $(redis-cli config get port | tail -1)"
echo "   Status: Running"
echo ""
echo "🔧 To stop Redis:"
echo "   redis-cli shutdown"
echo ""
echo "🔧 To start Redis manually:"
echo "   redis-server --daemonize yes"
