#!/bin/bash

# Flask Optimization Startup Script
# This script starts the Flask app with optimizations enabled

echo "🚀 Starting Flask app with optimizations..."

# Set environment variables
export FLASK_ENV=production
export ENABLE_OPTIMIZATIONS=true
export ENABLE_CACHING=true
export ENABLE_FILE_MONITORING=true
export ENABLE_COMPRESSION=true

# Activate virtual environment
source venv/bin/activate

# Start the optimized app
python -c "
from app_integration import run_with_optimizations
run_with_optimizations()
"
