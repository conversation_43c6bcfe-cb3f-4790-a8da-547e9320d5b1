#!/usr/bin/env python3
"""
Resilient Redis Client Implementation
Provides Redis connectivity with automatic reconnection and fallback mechanisms
"""

import redis
import time
import threading
import json
import hashlib
from typing import Optional, Any, Union
import logging
from config import config
from circuit_breaker import CircuitBreaker, CircuitBreakerOpenException

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResilientRedisClient:
    """Redis client with automatic reconnection and fallback"""
    
    def __init__(self, 
                 host: str = None,
                 port: int = None,
                 db: int = None,
                 password: str = None,
                 max_retries: int = 3,
                 retry_delay: int = 1):
        """
        Initialize resilient Redis client
        
        Args:
            host: Redis host (defaults to config)
            port: Redis port (defaults to config)
            db: Redis database (defaults to config)
            password: Redis password (defaults to config)
            max_retries: Maximum connection retries
            retry_delay: Delay between retries (seconds)
        """
        self.host = host or config.REDIS_HOST
        self.port = port or config.REDIS_PORT
        self.db = db or config.REDIS_DB
        self.password = password or config.REDIS_PASSWORD
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # Connection state
        self.client: Optional[redis.Redis] = None
        self.connected = False
        self.last_connection_attempt = 0
        self.connection_retry_interval = 30  # seconds
        
        # Circuit breaker for Redis operations
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=30,
            expected_exception=(redis.ConnectionError, redis.TimeoutError),
            name="redis_client"
        )
        
        # Connection monitoring
        self.monitor_thread = None
        self.shutdown_event = threading.Event()
        
        # Statistics
        self.connection_attempts = 0
        self.successful_operations = 0
        self.failed_operations = 0
        
        # Initialize connection
        self.connect()
        self.start_connection_monitor()
    
    def connect(self) -> bool:
        """Establish Redis connection with retry logic"""
        self.connection_attempts += 1
        
        for attempt in range(self.max_retries):
            try:
                logger.info(f"🔄 Attempting Redis connection (attempt {attempt + 1}/{self.max_retries})")
                
                self.client = redis.Redis(
                    host=self.host,
                    port=self.port,
                    db=self.db,
                    password=self.password,
                    decode_responses=True,
                    socket_connect_timeout=config.REDIS_SOCKET_CONNECT_TIMEOUT,
                    socket_timeout=config.REDIS_SOCKET_TIMEOUT,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
                
                # Test connection
                self.client.ping()
                self.connected = True
                logger.info(f"✅ Redis connected successfully to {self.host}:{self.port}")
                return True
                
            except (redis.ConnectionError, redis.TimeoutError) as e:
                logger.warning(f"⚠️ Redis connection attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
            except Exception as e:
                logger.error(f"❌ Unexpected Redis connection error: {e}")
                break
        
        self.connected = False
        self.client = None
        logger.error(f"❌ Redis connection failed after {self.max_retries} attempts")
        return False
    
    def start_connection_monitor(self):
        """Start background thread to monitor connection"""
        if self.monitor_thread and self.monitor_thread.is_alive():
            return
        
        def monitor():
            while not self.shutdown_event.is_set():
                if not self.connected:
                    current_time = time.time()
                    if current_time - self.last_connection_attempt > self.connection_retry_interval:
                        logger.info("🔄 Attempting to reconnect to Redis...")
                        self.last_connection_attempt = current_time
                        self.connect()
                
                time.sleep(10)  # Check every 10 seconds
        
        self.monitor_thread = threading.Thread(target=monitor, daemon=True)
        self.monitor_thread.start()
        logger.info("👁️ Redis connection monitor started")
    
    def execute_with_fallback(self, operation_name: str, operation, *args, **kwargs) -> Optional[Any]:
        """Execute Redis operation with circuit breaker and fallback"""
        if not self.connected or not self.client:
            logger.debug(f"⚠️ Redis not connected, skipping {operation_name}")
            return None
        
        try:
            result = self.circuit_breaker.call(operation, *args, **kwargs)
            self.successful_operations += 1
            if config.LOG_CACHE_OPERATIONS:
                logger.debug(f"✅ Redis {operation_name} successful")
            return result
            
        except CircuitBreakerOpenException:
            logger.warning(f"⚡ Redis circuit breaker open, skipping {operation_name}")
            return None
        except (redis.ConnectionError, redis.TimeoutError) as e:
            logger.warning(f"⚠️ Redis connection error during {operation_name}: {e}")
            self.connected = False
            self.failed_operations += 1
            return None
        except Exception as e:
            logger.error(f"❌ Redis operation error during {operation_name}: {e}")
            self.failed_operations += 1
            return None
    
    def get(self, key: str) -> Optional[str]:
        """Get value from Redis"""
        return self.execute_with_fallback("GET", self.client.get, key)
    
    def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """Set value in Redis"""
        result = self.execute_with_fallback("SET", self.client.set, key, value, ex=ex)
        return result is not None
    
    def setex(self, key: str, time: int, value: str) -> bool:
        """Set value with expiration in Redis"""
        result = self.execute_with_fallback("SETEX", self.client.setex, key, time, value)
        return result is not None
    
    def delete(self, *keys: str) -> int:
        """Delete keys from Redis"""
        result = self.execute_with_fallback("DELETE", self.client.delete, *keys)
        return result or 0
    
    def exists(self, key: str) -> bool:
        """Check if key exists in Redis"""
        result = self.execute_with_fallback("EXISTS", self.client.exists, key)
        return bool(result)
    
    def ping(self) -> bool:
        """Ping Redis server"""
        result = self.execute_with_fallback("PING", self.client.ping)
        return result is not None
    
    def flushdb(self) -> bool:
        """Flush current database"""
        result = self.execute_with_fallback("FLUSHDB", self.client.flushdb)
        return result is not None
    
    def dbsize(self) -> int:
        """Get database size"""
        result = self.execute_with_fallback("DBSIZE", self.client.dbsize)
        return result or 0
    
    def info(self, section: str = None) -> dict:
        """Get Redis info"""
        result = self.execute_with_fallback("INFO", self.client.info, section)
        return result or {}
    
    def scan_iter(self, match: str = None, count: int = None):
        """Scan keys with pattern"""
        if not self.connected or not self.client:
            return []
        
        try:
            return self.client.scan_iter(match=match, count=count)
        except Exception as e:
            logger.warning(f"⚠️ Redis SCAN error: {e}")
            return []
    
    def get_stats(self) -> dict:
        """Get client statistics"""
        return {
            'connected': self.connected,
            'host': self.host,
            'port': self.port,
            'db': self.db,
            'connection_attempts': self.connection_attempts,
            'successful_operations': self.successful_operations,
            'failed_operations': self.failed_operations,
            'circuit_breaker_stats': self.circuit_breaker.get_stats()
        }
    
    def shutdown(self):
        """Shutdown Redis client"""
        logger.info("🛑 Shutting down Redis client...")
        
        # Stop monitoring
        self.shutdown_event.set()
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        # Close connection
        if self.client:
            try:
                self.client.close()
            except Exception as e:
                logger.warning(f"⚠️ Error closing Redis connection: {e}")
        
        self.connected = False
        self.client = None
        logger.info("✅ Redis client shutdown complete")

# Global Redis client instance
redis_client: Optional[ResilientRedisClient] = None

def get_redis_client() -> Optional[ResilientRedisClient]:
    """Get global Redis client instance"""
    global redis_client
    
    if not config.ENABLE_CACHING:
        return None
    
    if redis_client is None:
        try:
            redis_client = ResilientRedisClient()
        except Exception as e:
            logger.error(f"❌ Failed to initialize Redis client: {e}")
            redis_client = None
    
    return redis_client

def shutdown_redis_client():
    """Shutdown global Redis client"""
    global redis_client
    if redis_client:
        redis_client.shutdown()
        redis_client = None

if __name__ == "__main__":
    # Test Redis client
    print("Testing Redis client...")
    
    client = ResilientRedisClient()
    
    # Test operations
    print(f"Connected: {client.connected}")
    print(f"Stats: {client.get_stats()}")
    
    if client.connected:
        # Test basic operations
        client.set("test_key", "test_value", ex=60)
        value = client.get("test_key")
        print(f"Test value: {value}")
        
        # Test delete
        deleted = client.delete("test_key")
        print(f"Deleted: {deleted}")
    
    client.shutdown()
