#!/usr/bin/env python3
"""
Configuration management for Flask optimizations
Handles feature flags, Redis settings, and environment variables
"""

import os
from typing import Optional

class OptimizationConfig:
    """Configuration class for optimization features"""
    
    # Feature flags - can be controlled via environment variables
    ENABLE_OPTIMIZATIONS = os.getenv('ENABLE_OPTIMIZATIONS', 'false').lower() == 'true'
    ENABLE_CACHING = os.getenv('ENABLE_CACHING', 'true').lower() == 'true'
    ENABLE_FILE_MONITORING = os.getenv('ENABLE_FILE_MONITORING', 'true').lower() == 'true'
    ENABLE_COMPRESSION = os.getenv('ENABLE_COMPRESSION', 'true').lower() == 'true'
    USE_OPTIMIZED_ENDPOINTS = os.getenv('USE_OPTIMIZED_ENDPOINTS', 'false').lower() == 'true'
    
    # Redis configuration
    REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
    REDIS_PORT = int(os.getenv('REDIS_PORT', 6379))
    REDIS_DB = int(os.getenv('REDIS_DB', 0))
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', None)
    REDIS_SOCKET_TIMEOUT = int(os.getenv('REDIS_SOCKET_TIMEOUT', 5))
    REDIS_SOCKET_CONNECT_TIMEOUT = int(os.getenv('REDIS_SOCKET_CONNECT_TIMEOUT', 5))
    
    # Cache configuration
    CACHE_DEFAULT_TIMEOUT = int(os.getenv('CACHE_TIMEOUT', 1800))  # 30 minutes
    CACHE_KEY_PREFIX = os.getenv('CACHE_KEY_PREFIX', 'progress_dashboard')
    MAX_CACHE_SIZE = int(os.getenv('MAX_CACHE_SIZE', 1024 * 1024))  # 1MB per cache entry
    
    # File monitoring configuration
    FILE_MONITOR_RECURSIVE = os.getenv('FILE_MONITOR_RECURSIVE', 'false').lower() == 'true'
    FILE_MONITOR_QUEUE_SIZE = int(os.getenv('FILE_MONITOR_QUEUE_SIZE', 100))
    
    # Circuit breaker configuration
    CIRCUIT_BREAKER_FAILURE_THRESHOLD = int(os.getenv('CIRCUIT_BREAKER_FAILURE_THRESHOLD', 5))
    CIRCUIT_BREAKER_RECOVERY_TIMEOUT = int(os.getenv('CIRCUIT_BREAKER_RECOVERY_TIMEOUT', 60))
    
    # Performance monitoring
    ENABLE_PERFORMANCE_MONITORING = os.getenv('ENABLE_PERFORMANCE_MONITORING', 'true').lower() == 'true'
    SLOW_REQUEST_THRESHOLD = float(os.getenv('SLOW_REQUEST_THRESHOLD', 1.0))  # seconds
    
    # Logging configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_CACHE_OPERATIONS = os.getenv('LOG_CACHE_OPERATIONS', 'true').lower() == 'true'
    LOG_FILE_OPERATIONS = os.getenv('LOG_FILE_OPERATIONS', 'true').lower() == 'true'
    
    @classmethod
    def get_redis_url(cls) -> str:
        """Get Redis connection URL"""
        if cls.REDIS_PASSWORD:
            return f"redis://:{cls.REDIS_PASSWORD}@{cls.REDIS_HOST}:{cls.REDIS_PORT}/{cls.REDIS_DB}"
        else:
            return f"redis://{cls.REDIS_HOST}:{cls.REDIS_PORT}/{cls.REDIS_DB}"
    
    @classmethod
    def is_optimization_enabled(cls) -> bool:
        """Check if any optimization is enabled"""
        return (cls.ENABLE_OPTIMIZATIONS and 
                (cls.ENABLE_CACHING or cls.ENABLE_FILE_MONITORING or cls.ENABLE_COMPRESSION))
    
    @classmethod
    def get_config_summary(cls) -> dict:
        """Get configuration summary for debugging"""
        return {
            'optimizations_enabled': cls.ENABLE_OPTIMIZATIONS,
            'caching_enabled': cls.ENABLE_CACHING,
            'file_monitoring_enabled': cls.ENABLE_FILE_MONITORING,
            'compression_enabled': cls.ENABLE_COMPRESSION,
            'optimized_endpoints_enabled': cls.USE_OPTIMIZED_ENDPOINTS,
            'redis_host': cls.REDIS_HOST,
            'redis_port': cls.REDIS_PORT,
            'cache_timeout': cls.CACHE_DEFAULT_TIMEOUT,
            'performance_monitoring': cls.ENABLE_PERFORMANCE_MONITORING
        }
    
    @classmethod
    def validate_config(cls) -> tuple[bool, list[str]]:
        """Validate configuration and return any issues"""
        issues = []
        
        # Check Redis configuration
        if cls.ENABLE_CACHING:
            if not cls.REDIS_HOST:
                issues.append("Redis host not configured")
            if cls.REDIS_PORT < 1 or cls.REDIS_PORT > 65535:
                issues.append(f"Invalid Redis port: {cls.REDIS_PORT}")
        
        # Check cache configuration
        if cls.CACHE_DEFAULT_TIMEOUT < 1:
            issues.append(f"Invalid cache timeout: {cls.CACHE_DEFAULT_TIMEOUT}")
        
        if cls.MAX_CACHE_SIZE < 1024:  # Minimum 1KB
            issues.append(f"Cache size too small: {cls.MAX_CACHE_SIZE}")
        
        # Check circuit breaker configuration
        if cls.CIRCUIT_BREAKER_FAILURE_THRESHOLD < 1:
            issues.append(f"Invalid circuit breaker threshold: {cls.CIRCUIT_BREAKER_FAILURE_THRESHOLD}")
        
        if cls.CIRCUIT_BREAKER_RECOVERY_TIMEOUT < 1:
            issues.append(f"Invalid circuit breaker recovery timeout: {cls.CIRCUIT_BREAKER_RECOVERY_TIMEOUT}")
        
        return len(issues) == 0, issues

# Create global config instance
config = OptimizationConfig()

def print_config_status():
    """Print current configuration status"""
    print("🔧 Optimization Configuration:")
    print(f"  Optimizations: {'✅ Enabled' if config.ENABLE_OPTIMIZATIONS else '❌ Disabled'}")
    print(f"  Caching: {'✅ Enabled' if config.ENABLE_CACHING else '❌ Disabled'}")
    print(f"  File Monitoring: {'✅ Enabled' if config.ENABLE_FILE_MONITORING else '❌ Disabled'}")
    print(f"  Compression: {'✅ Enabled' if config.ENABLE_COMPRESSION else '❌ Disabled'}")
    print(f"  Optimized Endpoints: {'✅ Enabled' if config.USE_OPTIMIZED_ENDPOINTS else '❌ Disabled'}")
    print(f"  Redis: {config.REDIS_HOST}:{config.REDIS_PORT}")
    print(f"  Cache Timeout: {config.CACHE_DEFAULT_TIMEOUT}s")
    
    # Validate configuration
    is_valid, issues = config.validate_config()
    if not is_valid:
        print("⚠️ Configuration Issues:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ Configuration is valid")

if __name__ == "__main__":
    print_config_status()
