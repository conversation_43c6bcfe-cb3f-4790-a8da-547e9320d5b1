#!/usr/bin/env python3
"""
Circuit Breaker Pattern Implementation
Provides safe feature rollout with automatic fallback mechanisms
"""

import time
import threading
from enum import Enum
from typing import Callable, Any, Optional
from functools import wraps
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "CLOSED"      # Normal operation
    OPEN = "OPEN"          # Circuit is open, calls fail fast
    HALF_OPEN = "HALF_OPEN"  # Testing if service is back

class CircuitBreaker:
    """
    Circuit breaker implementation for safe feature rollout
    """
    
    def __init__(self, 
                 failure_threshold: int = 5,
                 recovery_timeout: int = 60,
                 expected_exception: type = Exception,
                 name: str = "CircuitBreaker"):
        """
        Initialize circuit breaker
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Time to wait before trying again (seconds)
            expected_exception: Exception type to catch
            name: Name for logging purposes
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.name = name
        
        # State management
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
        self.lock = threading.RLock()
        
        # Statistics
        self.total_calls = 0
        self.successful_calls = 0
        self.failed_calls = 0
        self.circuit_open_count = 0
        
        logger.info(f"🔧 Circuit breaker '{self.name}' initialized")
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Call function with circuit breaker protection
        
        Args:
            func: Function to call
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            CircuitBreakerOpenException: When circuit is open
            Original exception: When function fails
        """
        with self.lock:
            self.total_calls += 1
            
            # Check circuit state
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                    logger.info(f"🔄 Circuit breaker '{self.name}' moved to HALF_OPEN")
                else:
                    logger.warning(f"⚡ Circuit breaker '{self.name}' is OPEN - failing fast")
                    raise CircuitBreakerOpenException(f"Circuit breaker '{self.name}' is OPEN")
            
            try:
                # Call the function
                result = func(*args, **kwargs)
                
                # Success - reset failure count
                self._on_success()
                return result
                
            except self.expected_exception as e:
                # Handle expected failures
                self._on_failure()
                raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        if self.last_failure_time is None:
            return True
        return time.time() - self.last_failure_time >= self.recovery_timeout
    
    def _on_success(self):
        """Handle successful call"""
        self.successful_calls += 1
        
        if self.state == CircuitState.HALF_OPEN:
            # Reset circuit breaker
            self.state = CircuitState.CLOSED
            self.failure_count = 0
            logger.info(f"✅ Circuit breaker '{self.name}' reset to CLOSED")
        
        # Reset failure count on success
        self.failure_count = 0
    
    def _on_failure(self):
        """Handle failed call"""
        self.failed_calls += 1
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
            self.circuit_open_count += 1
            logger.error(f"🚨 Circuit breaker '{self.name}' OPENED after {self.failure_count} failures")
    
    def get_stats(self) -> dict:
        """Get circuit breaker statistics"""
        with self.lock:
            success_rate = (self.successful_calls / self.total_calls * 100) if self.total_calls > 0 else 0
            
            return {
                'name': self.name,
                'state': self.state.value,
                'total_calls': self.total_calls,
                'successful_calls': self.successful_calls,
                'failed_calls': self.failed_calls,
                'success_rate': round(success_rate, 2),
                'failure_count': self.failure_count,
                'failure_threshold': self.failure_threshold,
                'circuit_open_count': self.circuit_open_count,
                'last_failure_time': self.last_failure_time
            }
    
    def reset(self):
        """Manually reset circuit breaker"""
        with self.lock:
            self.state = CircuitState.CLOSED
            self.failure_count = 0
            self.last_failure_time = None
            logger.info(f"🔄 Circuit breaker '{self.name}' manually reset")
    
    def force_open(self):
        """Manually open circuit breaker"""
        with self.lock:
            self.state = CircuitState.OPEN
            self.last_failure_time = time.time()
            logger.warning(f"⚡ Circuit breaker '{self.name}' manually opened")

class CircuitBreakerOpenException(Exception):
    """Exception raised when circuit breaker is open"""
    pass

def circuit_breaker(failure_threshold: int = 5, 
                   recovery_timeout: int = 60,
                   expected_exception: type = Exception,
                   name: Optional[str] = None):
    """
    Decorator for circuit breaker pattern
    
    Args:
        failure_threshold: Number of failures before opening circuit
        recovery_timeout: Time to wait before trying again (seconds)
        expected_exception: Exception type to catch
        name: Name for the circuit breaker
    """
    def decorator(func: Callable):
        breaker_name = name or f"{func.__module__}.{func.__name__}"
        breaker = CircuitBreaker(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            expected_exception=expected_exception,
            name=breaker_name
        )
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            return breaker.call(func, *args, **kwargs)
        
        # Attach circuit breaker to function for external access
        wrapper.circuit_breaker = breaker
        return wrapper
    
    return decorator

# Global circuit breaker registry
_circuit_breakers = {}

def get_circuit_breaker(name: str) -> Optional[CircuitBreaker]:
    """Get circuit breaker by name"""
    return _circuit_breakers.get(name)

def register_circuit_breaker(name: str, breaker: CircuitBreaker):
    """Register circuit breaker globally"""
    _circuit_breakers[name] = breaker
    logger.info(f"📝 Registered circuit breaker: {name}")

def get_all_circuit_breakers() -> dict:
    """Get all registered circuit breakers"""
    return _circuit_breakers.copy()

def get_circuit_breaker_stats() -> dict:
    """Get statistics for all circuit breakers"""
    stats = {}
    for name, breaker in _circuit_breakers.items():
        stats[name] = breaker.get_stats()
    return stats

def reset_all_circuit_breakers():
    """Reset all circuit breakers"""
    for name, breaker in _circuit_breakers.items():
        breaker.reset()
    logger.info("🔄 All circuit breakers reset")

if __name__ == "__main__":
    # Test circuit breaker
    @circuit_breaker(failure_threshold=3, recovery_timeout=5, name="test_breaker")
    def test_function(should_fail=False):
        if should_fail:
            raise Exception("Test failure")
        return "Success"
    
    # Test successful calls
    print("Testing successful calls:")
    for i in range(3):
        try:
            result = test_function(False)
            print(f"  Call {i+1}: {result}")
        except Exception as e:
            print(f"  Call {i+1}: Failed - {e}")
    
    # Test failing calls
    print("\nTesting failing calls:")
    for i in range(5):
        try:
            result = test_function(True)
            print(f"  Call {i+1}: {result}")
        except Exception as e:
            print(f"  Call {i+1}: Failed - {e}")
    
    # Print stats
    print(f"\nCircuit breaker stats: {test_function.circuit_breaker.get_stats()}")
