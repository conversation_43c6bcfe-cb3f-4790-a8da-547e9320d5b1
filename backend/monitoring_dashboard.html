
<!DOCTYPE html>
<html>
<head>
    <title>Flask Optimization Monitoring</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-healthy { color: #28a745; }
        .status-degraded { color: #ffc107; }
        .status-unhealthy { color: #dc3545; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        .alert { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 5px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .refresh-info { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Flask Optimization Monitoring</h1>
        
        <div class="card">
            <h2>System Status</h2>
            <div id="status">Loading...</div>
            <button onclick="refreshData()">Refresh</button>
            <span class="refresh-info">Last updated: <span id="lastUpdate">Never</span></span>
        </div>
        
        <div class="card">
            <h2>Performance Metrics</h2>
            <div id="performance">Loading...</div>
        </div>
        
        <div class="card">
            <h2>Alerts</h2>
            <div id="alerts">Loading...</div>
        </div>
        
        <div class="card">
            <h2>Configuration</h2>
            <div id="config">Loading...</div>
        </div>
    </div>
    
    <script>
        async function fetchData(endpoint) {
            try {
                const response = await fetch(endpoint);
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }
        
        async function refreshData() {
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            
            // Fetch health data
            const health = await fetchData('/api/monitoring/health');
            const statusDiv = document.getElementById('status');
            
            if (health.error) {
                statusDiv.innerHTML = `<div class="alert">Error: ${health.error}</div>`;
            } else {
                let statusClass = `status-${health.status}`;
                let statusHtml = `<h3 class="${statusClass}">Status: ${health.status.toUpperCase()}</h3>`;
                statusHtml += `<p>Uptime: ${Math.round(health.uptime_seconds / 60)} minutes</p>`;
                
                for (const [component, info] of Object.entries(health.components)) {
                    statusHtml += `<div class="metric">
                        <strong>${component}:</strong> 
                        <span class="status-${info.status}">${info.status}</span>
                    </div>`;
                }
                
                statusDiv.innerHTML = statusHtml;
            }
            
            // Fetch performance data
            const performance = await fetchData('/api/monitoring/performance');
            const perfDiv = document.getElementById('performance');
            
            if (performance.error) {
                perfDiv.innerHTML = `<div class="alert">Error: ${performance.error}</div>`;
            } else {
                let perfHtml = '';
                if (performance.cache_performance) {
                    perfHtml += `<div class="metric">
                        <strong>Cache Hit Rate:</strong> ${performance.cache_performance.hit_rate.toFixed(1)}%
                    </div>`;
                    perfHtml += `<div class="metric">
                        <strong>Total Requests:</strong> ${performance.cache_performance.total_requests}
                    </div>`;
                }
                
                if (performance.system_performance && !performance.system_performance.error) {
                    perfHtml += `<div class="metric">
                        <strong>CPU:</strong> ${performance.system_performance.cpu.percent.toFixed(1)}%
                    </div>`;
                    perfHtml += `<div class="metric">
                        <strong>Memory:</strong> ${performance.system_performance.memory.percent.toFixed(1)}%
                    </div>`;
                }
                
                perfDiv.innerHTML = perfHtml || 'No performance data available';
            }
            
            // Fetch alerts
            const alerts = await fetchData('/api/monitoring/alerts');
            const alertsDiv = document.getElementById('alerts');
            
            if (alerts.error) {
                alertsDiv.innerHTML = `<div class="alert">Error: ${alerts.error}</div>`;
            } else if (alerts.alerts && alerts.alerts.length > 0) {
                let alertsHtml = '';
                alerts.alerts.forEach(alert => {
                    alertsHtml += `<div class="alert">${alert}</div>`;
                });
                alertsDiv.innerHTML = alertsHtml;
            } else {
                alertsDiv.innerHTML = '<p style="color: #28a745;">No alerts</p>';
            }
            
            // Show configuration
            const configDiv = document.getElementById('config');
            configDiv.innerHTML = `
                <div class="metric"><strong>Optimizations:</strong> ${health.components?.redis?.status !== 'disabled' ? 'Enabled' : 'Disabled'}</div>
                <div class="metric"><strong>Caching:</strong> ${health.components?.redis?.status || 'Unknown'}</div>
                <div class="metric"><strong>File Monitoring:</strong> ${health.components?.file_monitor?.status || 'Unknown'}</div>
            `;
        }
        
        // Auto-refresh every 30 seconds
        setInterval(refreshData, 30000);
        
        // Initial load
        refreshData();
    </script>
</body>
</html>
    