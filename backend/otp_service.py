#!/usr/bin/env python3
"""
OTP Service for Chrome Extension Authentication
Handles OTP generation, validation, and management
"""

import pyotp
import secrets
import time
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Tuple
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

class OTPService:
    """Service for managing OTP generation and validation"""
    
    def __init__(self, redis_client=None):
        """Initialize OTP service with Redis client for storage"""
        self.redis_client = redis_client
        self.otp_validity_seconds = 300  # 5 minutes
        self.max_attempts = 3
        self.rate_limit_window = 60  # 1 minute
        self.max_requests_per_window = 5

        # In-memory storage fallback when Redis is not available
        self.memory_storage = {}
        self.rate_limit_storage = {}

        # Initialize encryption key
        self._init_encryption()
    
    def _init_encryption(self):
        """Initialize encryption for OTP data"""
        # Use environment variable or generate a key
        encryption_key = os.getenv('OTP_ENCRYPTION_KEY')
        if not encryption_key:
            # Generate a key for development (in production, use a fixed key)
            password = b"otp_service_key_2025"
            salt = b"progress_dashboard_salt"
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            encryption_key = key.decode()
        
        self.cipher_suite = Fernet(encryption_key.encode() if isinstance(encryption_key, str) else encryption_key)
    
    def generate_otp(self, email: str) -> Dict[str, any]:
        """
        Generate OTP for user email
        
        Args:
            email: User email address
            
        Returns:
            Dict containing OTP data and metadata
        """
        try:
            # Check rate limiting
            if not self._check_rate_limit(email):
                return {
                    'success': False,
                    'error': 'Rate limit exceeded. Please wait before requesting another OTP.',
                    'code': 'RATE_LIMIT_EXCEEDED'
                }
            
            # Generate OTP using TOTP
            secret = pyotp.random_base32()
            totp = pyotp.TOTP(secret, interval=self.otp_validity_seconds)
            otp_code = totp.now()
            
            # Create OTP data
            otp_data = {
                'email': email,
                'otp_code': otp_code,
                'secret': secret,
                'created_at': datetime.now().isoformat(),
                'expires_at': (datetime.now() + timedelta(seconds=self.otp_validity_seconds)).isoformat(),
                'attempts': 0,
                'max_attempts': self.max_attempts,
                'used': False
            }
            
            # Encrypt OTP data
            encrypted_data = self._encrypt_data(otp_data)
            
            # Store in Redis or memory with expiration
            otp_key = f"otp:{email}:{int(time.time())}"
            if self.redis_client:
                self.redis_client.setex(
                    otp_key,
                    self.otp_validity_seconds,
                    encrypted_data
                )
            else:
                # Store in memory with expiration time
                expiry_time = time.time() + self.otp_validity_seconds
                self.memory_storage[otp_key] = {
                    'data': encrypted_data,
                    'expires_at': expiry_time
                }
            
            # Update rate limiting
            self._update_rate_limit(email)
            
            return {
                'success': True,
                'otp_key': otp_key,
                'otp_code': otp_code,  # For Chrome Extension
                'expires_in': self.otp_validity_seconds,
                'email': email,
                'created_at': otp_data['created_at']
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to generate OTP: {str(e)}',
                'code': 'GENERATION_ERROR'
            }
    
    def validate_otp(self, email: str, otp_code: str, otp_key: str = None) -> Dict[str, any]:
        """
        Validate OTP code for user
        
        Args:
            email: User email address
            otp_code: OTP code to validate
            otp_key: Optional OTP key for direct validation
            
        Returns:
            Dict containing validation result
        """
        try:
            # Find OTP data
            otp_data = None
            used_key = None
            
            if otp_key:
                # Direct validation with key
                otp_data = self._get_otp_data(otp_key)
                used_key = otp_key
            else:
                # Search for valid OTP for email
                otp_data, used_key = self._find_valid_otp(email)
            
            if not otp_data:
                return {
                    'success': False,
                    'error': 'OTP not found or expired',
                    'code': 'OTP_NOT_FOUND'
                }
            
            # Check if OTP is already used
            if otp_data.get('used', False):
                return {
                    'success': False,
                    'error': 'OTP has already been used',
                    'code': 'OTP_ALREADY_USED'
                }
            
            # Check attempts
            if otp_data.get('attempts', 0) >= self.max_attempts:
                return {
                    'success': False,
                    'error': 'Maximum validation attempts exceeded',
                    'code': 'MAX_ATTEMPTS_EXCEEDED'
                }
            
            # Validate OTP code
            secret = otp_data.get('secret')
            totp = pyotp.TOTP(secret, interval=self.otp_validity_seconds)
            
            if totp.verify(otp_code, valid_window=1):  # Allow 1 window tolerance
                # Mark as used
                otp_data['used'] = True
                otp_data['validated_at'] = datetime.now().isoformat()

                # Update in Redis or memory
                if used_key:
                    self._update_otp_data(used_key, otp_data)
                
                return {
                    'success': True,
                    'email': email,
                    'validated_at': otp_data['validated_at'],
                    'otp_key': used_key
                }
            else:
                # Increment attempts
                otp_data['attempts'] = otp_data.get('attempts', 0) + 1
                
                # Update in Redis
                if self.redis_client and used_key:
                    encrypted_data = self._encrypt_data(otp_data)
                    remaining_ttl = self.redis_client.ttl(used_key)
                    if remaining_ttl > 0:
                        self.redis_client.setex(used_key, remaining_ttl, encrypted_data)
                
                return {
                    'success': False,
                    'error': 'Invalid OTP code',
                    'code': 'INVALID_OTP',
                    'attempts_remaining': self.max_attempts - otp_data['attempts']
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to validate OTP: {str(e)}',
                'code': 'VALIDATION_ERROR'
            }
    
    def _encrypt_data(self, data: Dict) -> str:
        """Encrypt OTP data"""
        json_data = json.dumps(data)
        encrypted = self.cipher_suite.encrypt(json_data.encode())
        return base64.urlsafe_b64encode(encrypted).decode()
    
    def _decrypt_data(self, encrypted_data: str) -> Dict:
        """Decrypt OTP data"""
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted = self.cipher_suite.decrypt(encrypted_bytes)
        return json.loads(decrypted.decode())
    
    def _get_otp_data(self, otp_key: str) -> Optional[Dict]:
        """Get OTP data from Redis or memory"""
        try:
            if self.redis_client:
                encrypted_data = self.redis_client.get(otp_key)
                if encrypted_data:
                    return self._decrypt_data(encrypted_data.decode())
            else:
                # Check memory storage
                if otp_key in self.memory_storage:
                    stored_item = self.memory_storage[otp_key]
                    # Check if expired
                    if time.time() < stored_item['expires_at']:
                        return self._decrypt_data(stored_item['data'])
                    else:
                        # Remove expired item
                        del self.memory_storage[otp_key]
        except Exception:
            pass

        return None
    
    def _find_valid_otp(self, email: str) -> Tuple[Optional[Dict], Optional[str]]:
        """Find valid OTP for email"""
        if self.redis_client:
            # Search in Redis
            try:
                pattern = f"otp:{email}:*"
                keys = self.redis_client.keys(pattern)

                for key in keys:
                    otp_data = self._get_otp_data(key)
                    if otp_data and not otp_data.get('used', False):
                        return otp_data, key
            except Exception:
                pass
        else:
            # Search in memory storage
            try:
                current_time = time.time()
                for key, stored_item in list(self.memory_storage.items()):
                    if key.startswith(f"otp:{email}:"):
                        # Check if expired
                        if current_time < stored_item['expires_at']:
                            otp_data = self._decrypt_data(stored_item['data'])
                            if otp_data and not otp_data.get('used', False):
                                return otp_data, key
                        else:
                            # Remove expired item
                            del self.memory_storage[key]
            except Exception:
                pass

        return None, None
    
    def _update_otp_data(self, otp_key: str, otp_data: Dict) -> bool:
        """Update OTP data in Redis or memory"""
        try:
            encrypted_data = self._encrypt_data(otp_data)

            if self.redis_client:
                # Update in Redis
                ttl = self.redis_client.ttl(otp_key)
                if ttl > 0:
                    self.redis_client.setex(otp_key, ttl, encrypted_data)
                    return True
            else:
                # Update in memory storage
                if otp_key in self.memory_storage:
                    self.memory_storage[otp_key]['data'] = encrypted_data
                    return True
        except Exception:
            pass

        return False

    def _check_rate_limit(self, email: str) -> bool:
        """Check if email is within rate limit"""
        current_time = time.time()
        rate_key = f"rate:{email}"

        if self.redis_client:
            # Use Redis for rate limiting
            try:
                count = self.redis_client.get(rate_key)
                if count is None:
                    self.redis_client.setex(rate_key, self.rate_limit_window, 1)
                    return True
                elif int(count) < self.max_requests_per_window:
                    self.redis_client.incr(rate_key)
                    return True
                else:
                    return False
            except Exception:
                return True  # Allow if Redis error
        else:
            # Use memory for rate limiting
            if rate_key not in self.rate_limit_storage:
                self.rate_limit_storage[rate_key] = {
                    'count': 1,
                    'window_start': current_time
                }
                return True

            rate_data = self.rate_limit_storage[rate_key]

            # Check if window has expired
            if current_time - rate_data['window_start'] > self.rate_limit_window:
                # Reset window
                self.rate_limit_storage[rate_key] = {
                    'count': 1,
                    'window_start': current_time
                }
                return True
            elif rate_data['count'] < self.max_requests_per_window:
                # Increment count
                rate_data['count'] += 1
                return True
            else:
                return False
        
        try:
            rate_key = f"rate_limit:{email}"
            current_count = self.redis_client.get(rate_key)
            
            if current_count is None:
                return True
            
            return int(current_count) < self.max_requests_per_window
        except Exception:
            return True  # Allow on error
    
    def _update_rate_limit(self, email: str):
        """Update rate limiting counter"""
        if not self.redis_client:
            return
        
        try:
            rate_key = f"rate_limit:{email}"
            current_count = self.redis_client.get(rate_key)
            
            if current_count is None:
                self.redis_client.setex(rate_key, self.rate_limit_window, 1)
            else:
                self.redis_client.incr(rate_key)
        except Exception:
            pass  # Ignore rate limiting errors
    
    def cleanup_expired_otps(self):
        """Cleanup expired OTP entries (maintenance function)"""
        if not self.redis_client:
            return
        
        try:
            # Redis TTL handles expiration automatically
            # This is for additional cleanup if needed
            pattern = "otp:*"
            keys = self.redis_client.keys(pattern)
            
            cleaned_count = 0
            for key in keys:
                ttl = self.redis_client.ttl(key)
                if ttl == -1:  # No expiration set
                    self.redis_client.delete(key)
                    cleaned_count += 1
            
            return cleaned_count
        except Exception:
            return 0
    
    def get_otp_stats(self) -> Dict[str, any]:
        """Get OTP service statistics"""
        if not self.redis_client:
            return {'error': 'Redis not available'}
        
        try:
            otp_keys = self.redis_client.keys("otp:*")
            rate_limit_keys = self.redis_client.keys("rate_limit:*")
            
            return {
                'active_otps': len(otp_keys),
                'rate_limited_users': len(rate_limit_keys),
                'otp_validity_seconds': self.otp_validity_seconds,
                'max_attempts': self.max_attempts,
                'rate_limit_window': self.rate_limit_window,
                'max_requests_per_window': self.max_requests_per_window
            }
        except Exception as e:
            return {'error': str(e)}
