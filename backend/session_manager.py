#!/usr/bin/env python3
"""
Session Manager for OTP Authentication
Handles user sessions after successful OTP validation
"""

import secrets
import json
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

class SessionManager:
    """Manages user sessions after OTP authentication"""
    
    def __init__(self, redis_client=None):
        """Initialize session manager with Redis client"""
        self.redis_client = redis_client
        self.session_duration = 3600 * 24  # 24 hours
        self.refresh_token_duration = 3600 * 24 * 7  # 7 days
        self.max_sessions_per_user = 5  # Maximum concurrent sessions
        
        # Initialize encryption
        self._init_encryption()
    
    def _init_encryption(self):
        """Initialize encryption for session data"""
        encryption_key = os.getenv('SESSION_ENCRYPTION_KEY')
        if not encryption_key:
            # Generate a key for development
            password = b"session_manager_key_2025"
            salt = b"progress_dashboard_session_salt"
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            encryption_key = key.decode()
        
        self.cipher_suite = Fernet(encryption_key.encode() if isinstance(encryption_key, str) else encryption_key)
    
    def create_session(self, email: str, otp_key: str = None) -> Dict[str, any]:
        """
        Create new session after successful OTP validation
        
        Args:
            email: User email address
            otp_key: OTP key used for validation (for audit)
            
        Returns:
            Dict containing session data
        """
        try:
            # Generate session tokens
            session_token = self._generate_token()
            refresh_token = self._generate_token()
            
            # Create session data
            session_data = {
                'email': email,
                'session_token': session_token,
                'refresh_token': refresh_token,
                'created_at': datetime.now().isoformat(),
                'expires_at': (datetime.now() + timedelta(seconds=self.session_duration)).isoformat(),
                'refresh_expires_at': (datetime.now() + timedelta(seconds=self.refresh_token_duration)).isoformat(),
                'last_activity': datetime.now().isoformat(),
                'otp_key': otp_key,  # For audit trail
                'active': True,
                'user_agent': None,  # Can be set from request
                'ip_address': None   # Can be set from request
            }
            
            # Clean up old sessions for user
            self._cleanup_user_sessions(email)
            
            # Store session
            session_key = f"session:{session_token}"
            user_session_key = f"user_sessions:{email}"
            
            if self.redis_client:
                # Store session data
                encrypted_data = self._encrypt_data(session_data)
                self.redis_client.setex(session_key, self.session_duration, encrypted_data)
                
                # Add to user's session list
                self.redis_client.sadd(user_session_key, session_token)
                self.redis_client.expire(user_session_key, self.refresh_token_duration)
                
                # Store refresh token mapping
                refresh_key = f"refresh:{refresh_token}"
                refresh_data = {
                    'session_token': session_token,
                    'email': email,
                    'created_at': session_data['created_at']
                }
                encrypted_refresh = self._encrypt_data(refresh_data)
                self.redis_client.setex(refresh_key, self.refresh_token_duration, encrypted_refresh)
            
            return {
                'success': True,
                'session_token': session_token,
                'refresh_token': refresh_token,
                'expires_in': self.session_duration,
                'refresh_expires_in': self.refresh_token_duration,
                'email': email,
                'created_at': session_data['created_at']
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to create session: {str(e)}',
                'code': 'SESSION_CREATION_ERROR'
            }
    
    def validate_session(self, session_token: str) -> Dict[str, any]:
        """
        Validate session token and return user data
        
        Args:
            session_token: Session token to validate
            
        Returns:
            Dict containing validation result and user data
        """
        try:
            session_key = f"session:{session_token}"
            session_data = self._get_session_data(session_key)
            
            if not session_data:
                return {
                    'success': False,
                    'error': 'Session not found or expired',
                    'code': 'SESSION_NOT_FOUND'
                }
            
            if not session_data.get('active', False):
                return {
                    'success': False,
                    'error': 'Session is inactive',
                    'code': 'SESSION_INACTIVE'
                }
            
            # Update last activity
            session_data['last_activity'] = datetime.now().isoformat()
            
            # Update in Redis
            if self.redis_client:
                encrypted_data = self._encrypt_data(session_data)
                remaining_ttl = self.redis_client.ttl(session_key)
                if remaining_ttl > 0:
                    self.redis_client.setex(session_key, remaining_ttl, encrypted_data)
            
            return {
                'success': True,
                'email': session_data['email'],
                'session_token': session_token,
                'created_at': session_data['created_at'],
                'last_activity': session_data['last_activity'],
                'expires_at': session_data['expires_at']
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to validate session: {str(e)}',
                'code': 'SESSION_VALIDATION_ERROR'
            }
    
    def refresh_session(self, refresh_token: str) -> Dict[str, any]:
        """
        Refresh session using refresh token
        
        Args:
            refresh_token: Refresh token
            
        Returns:
            Dict containing new session data
        """
        try:
            refresh_key = f"refresh:{refresh_token}"
            refresh_data = self._get_session_data(refresh_key)
            
            if not refresh_data:
                return {
                    'success': False,
                    'error': 'Refresh token not found or expired',
                    'code': 'REFRESH_TOKEN_NOT_FOUND'
                }
            
            # Get current session
            old_session_token = refresh_data['session_token']
            email = refresh_data['email']
            
            # Invalidate old session
            self.invalidate_session(old_session_token)
            
            # Create new session
            return self.create_session(email)
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to refresh session: {str(e)}',
                'code': 'SESSION_REFRESH_ERROR'
            }
    
    def invalidate_session(self, session_token: str) -> Dict[str, any]:
        """
        Invalidate session
        
        Args:
            session_token: Session token to invalidate
            
        Returns:
            Dict containing result
        """
        try:
            session_key = f"session:{session_token}"
            session_data = self._get_session_data(session_key)
            
            if session_data:
                email = session_data['email']
                
                # Remove from Redis
                if self.redis_client:
                    self.redis_client.delete(session_key)
                    
                    # Remove from user's session list
                    user_session_key = f"user_sessions:{email}"
                    self.redis_client.srem(user_session_key, session_token)
                    
                    # Remove refresh token if exists
                    refresh_token = session_data.get('refresh_token')
                    if refresh_token:
                        refresh_key = f"refresh:{refresh_token}"
                        self.redis_client.delete(refresh_key)
            
            return {
                'success': True,
                'message': 'Session invalidated successfully'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to invalidate session: {str(e)}',
                'code': 'SESSION_INVALIDATION_ERROR'
            }
    
    def invalidate_all_user_sessions(self, email: str) -> Dict[str, any]:
        """
        Invalidate all sessions for a user
        
        Args:
            email: User email
            
        Returns:
            Dict containing result
        """
        try:
            if not self.redis_client:
                return {'success': False, 'error': 'Redis not available'}
            
            user_session_key = f"user_sessions:{email}"
            session_tokens = self.redis_client.smembers(user_session_key)
            
            invalidated_count = 0
            for token in session_tokens:
                token_str = token.decode() if isinstance(token, bytes) else token
                result = self.invalidate_session(token_str)
                if result.get('success'):
                    invalidated_count += 1
            
            # Clean up user session list
            self.redis_client.delete(user_session_key)
            
            return {
                'success': True,
                'invalidated_sessions': invalidated_count,
                'message': f'Invalidated {invalidated_count} sessions for {email}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Failed to invalidate user sessions: {str(e)}',
                'code': 'USER_SESSION_INVALIDATION_ERROR'
            }
    
    def _generate_token(self) -> str:
        """Generate secure random token"""
        return secrets.token_urlsafe(32)
    
    def _encrypt_data(self, data: Dict) -> str:
        """Encrypt session data"""
        json_data = json.dumps(data)
        encrypted = self.cipher_suite.encrypt(json_data.encode())
        return base64.urlsafe_b64encode(encrypted).decode()
    
    def _decrypt_data(self, encrypted_data: str) -> Dict:
        """Decrypt session data"""
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted = self.cipher_suite.decrypt(encrypted_bytes)
        return json.loads(decrypted.decode())
    
    def _get_session_data(self, session_key: str) -> Optional[Dict]:
        """Get session data from Redis"""
        if not self.redis_client:
            return None
        
        try:
            encrypted_data = self.redis_client.get(session_key)
            if encrypted_data:
                return self._decrypt_data(encrypted_data.decode())
        except Exception:
            pass
        
        return None
    
    def _cleanup_user_sessions(self, email: str):
        """Clean up old sessions for user if exceeding limit"""
        if not self.redis_client:
            return
        
        try:
            user_session_key = f"user_sessions:{email}"
            session_tokens = list(self.redis_client.smembers(user_session_key))
            
            if len(session_tokens) >= self.max_sessions_per_user:
                # Remove oldest sessions
                sessions_to_remove = len(session_tokens) - self.max_sessions_per_user + 1
                
                # Get session creation times
                session_times = []
                for token in session_tokens:
                    token_str = token.decode() if isinstance(token, bytes) else token
                    session_key = f"session:{token_str}"
                    session_data = self._get_session_data(session_key)
                    if session_data:
                        session_times.append((token_str, session_data.get('created_at', '')))
                
                # Sort by creation time and remove oldest
                session_times.sort(key=lambda x: x[1])
                for i in range(sessions_to_remove):
                    if i < len(session_times):
                        self.invalidate_session(session_times[i][0])
        except Exception:
            pass  # Ignore cleanup errors
    
    def get_session_stats(self) -> Dict[str, any]:
        """Get session statistics"""
        if not self.redis_client:
            return {'error': 'Redis not available'}
        
        try:
            session_keys = self.redis_client.keys("session:*")
            refresh_keys = self.redis_client.keys("refresh:*")
            user_session_keys = self.redis_client.keys("user_sessions:*")
            
            return {
                'active_sessions': len(session_keys),
                'active_refresh_tokens': len(refresh_keys),
                'users_with_sessions': len(user_session_keys),
                'session_duration': self.session_duration,
                'refresh_token_duration': self.refresh_token_duration,
                'max_sessions_per_user': self.max_sessions_per_user
            }
        except Exception as e:
            return {'error': str(e)}
