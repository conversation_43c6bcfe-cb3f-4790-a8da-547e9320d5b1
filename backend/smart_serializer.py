#!/usr/bin/env python3
"""
Smart Serialization Module
Handles complex object serialization with multiple fallback mechanisms
"""

import json
import pickle
import base64
from datetime import datetime, date
from decimal import Decimal
import logging
from typing import Any, Optional, Union

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmartSerializer:
    """Intelligent serialization with fallback mechanisms"""
    
    @staticmethod
    def custom_json_encoder(obj: Any) -> Any:
        """Custom JSON encoder for complex objects"""
        try:
            # Handle datetime objects
            if isinstance(obj, (datetime, date)):
                return obj.isoformat()
            
            # Handle decimal objects
            elif isinstance(obj, Decimal):
                return float(obj)
            
            # Handle numpy types (if available)
            elif hasattr(obj, 'dtype'):  # numpy array/scalar
                if hasattr(obj, 'tolist'):
                    return obj.tolist()
                else:
                    return obj.item()
            
            # Handle pandas objects (if available)
            elif hasattr(obj, 'to_dict'):  # DataFrame/Series
                if hasattr(obj, 'index'):  # Series
                    return obj.to_dict()
                else:  # DataFrame
                    return obj.to_dict('records')
            
            # Handle objects with __dict__
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
            
            # Handle iterables (but not strings)
            elif hasattr(obj, '__iter__') and not isinstance(obj, (str, bytes)):
                return list(obj)
            
            # Last resort - convert to string
            else:
                return str(obj)
                
        except Exception as e:
            logger.warning(f"⚠️ Custom encoder failed for {type(obj)}: {e}")
            return str(obj)
    
    @staticmethod
    def safe_serialize(data: Any, method: str = 'json') -> Optional[str]:
        """
        Safely serialize data with multiple fallback methods
        
        Args:
            data: Data to serialize
            method: Serialization method ('json', 'pickle', 'string')
            
        Returns:
            Serialized string or None if all methods fail
        """
        
        # Method 1: JSON serialization (fastest and most compatible)
        if method == 'json':
            try:
                result = json.dumps(
                    data,
                    default=SmartSerializer.custom_json_encoder,
                    ensure_ascii=False,
                    separators=(',', ':'),
                    sort_keys=True
                )
                logger.debug(f"✅ JSON serialization successful ({len(result)} chars)")
                return result
                
            except (TypeError, ValueError, OverflowError) as e:
                logger.debug(f"⚠️ JSON serialization failed: {e}, trying pickle...")
                method = 'pickle'
        
        # Method 2: Pickle + Base64 (more compatible but larger)
        if method == 'pickle':
            try:
                pickled = pickle.dumps(data, protocol=pickle.HIGHEST_PROTOCOL)
                encoded = base64.b64encode(pickled).decode('utf-8')
                logger.debug(f"✅ Pickle serialization successful ({len(encoded)} chars)")
                return f"PICKLE:{encoded}"
                
            except Exception as e:
                logger.debug(f"⚠️ Pickle serialization failed: {e}, trying string...")
                method = 'string'
        
        # Method 3: String representation (last resort)
        if method == 'string':
            try:
                result = str(data)
                logger.debug(f"✅ String serialization successful ({len(result)} chars)")
                return f"STRING:{result}"
                
            except Exception as e:
                logger.error(f"❌ All serialization methods failed: {e}")
                return None
        
        return None
    
    @staticmethod
    def safe_deserialize(data: str, method: str = 'auto') -> Any:
        """
        Safely deserialize data with automatic method detection
        
        Args:
            data: Serialized data string
            method: Deserialization method ('auto', 'json', 'pickle', 'string')
            
        Returns:
            Deserialized object or original string if deserialization fails
        """
        
        if not data or not isinstance(data, str):
            return data
        
        # Auto-detect method from prefix
        if method == 'auto':
            if data.startswith('PICKLE:'):
                method = 'pickle'
                data = data[7:]  # Remove prefix
            elif data.startswith('STRING:'):
                method = 'string'
                data = data[7:]  # Remove prefix
            else:
                method = 'json'
        
        # Method 1: JSON deserialization
        if method == 'json':
            try:
                result = json.loads(data)
                logger.debug("✅ JSON deserialization successful")
                return result
                
            except (TypeError, ValueError, json.JSONDecodeError) as e:
                logger.debug(f"⚠️ JSON deserialization failed: {e}")
                # Try to detect if it's actually a pickle
                if data.startswith('PICKLE:'):
                    return SmartSerializer.safe_deserialize(data, 'pickle')
                method = 'pickle'
        
        # Method 2: Pickle + Base64 deserialization
        if method == 'pickle':
            try:
                if data.startswith('PICKLE:'):
                    data = data[7:]
                
                decoded = base64.b64decode(data.encode('utf-8'))
                result = pickle.loads(decoded)
                logger.debug("✅ Pickle deserialization successful")
                return result
                
            except Exception as e:
                logger.debug(f"⚠️ Pickle deserialization failed: {e}")
                method = 'string'
        
        # Method 3: Return as string
        if method == 'string':
            if data.startswith('STRING:'):
                data = data[7:]
            logger.debug("✅ String deserialization (passthrough)")
            return data
        
        # If all else fails, return original data
        logger.warning(f"⚠️ Deserialization failed, returning original data")
        return data
    
    @staticmethod
    def get_serialized_size(data: Any) -> int:
        """Get the size of serialized data in bytes"""
        serialized = SmartSerializer.safe_serialize(data)
        if serialized:
            return len(serialized.encode('utf-8'))
        return 0
    
    @staticmethod
    def is_serializable(data: Any) -> bool:
        """Check if data can be serialized"""
        return SmartSerializer.safe_serialize(data) is not None
    
    @staticmethod
    def compress_if_large(data: str, threshold: int = 1024) -> str:
        """Compress data if it's larger than threshold"""
        if len(data) > threshold:
            try:
                import gzip
                compressed = gzip.compress(data.encode('utf-8'))
                encoded = base64.b64encode(compressed).decode('utf-8')
                return f"GZIP:{encoded}"
            except Exception as e:
                logger.warning(f"⚠️ Compression failed: {e}")
        return data
    
    @staticmethod
    def decompress_if_compressed(data: str) -> str:
        """Decompress data if it's compressed"""
        if data.startswith('GZIP:'):
            try:
                import gzip
                encoded_data = data[5:]  # Remove prefix
                compressed = base64.b64decode(encoded_data.encode('utf-8'))
                decompressed = gzip.decompress(compressed).decode('utf-8')
                return decompressed
            except Exception as e:
                logger.warning(f"⚠️ Decompression failed: {e}")
                return data[5:]  # Return without prefix
        return data

class SerializationStats:
    """Track serialization statistics"""
    
    def __init__(self):
        self.json_success = 0
        self.json_failures = 0
        self.pickle_success = 0
        self.pickle_failures = 0
        self.string_fallbacks = 0
        self.total_bytes_serialized = 0
        self.total_bytes_deserialized = 0
    
    def record_serialization(self, method: str, success: bool, size: int = 0):
        """Record serialization attempt"""
        if method == 'json':
            if success:
                self.json_success += 1
            else:
                self.json_failures += 1
        elif method == 'pickle':
            if success:
                self.pickle_success += 1
            else:
                self.pickle_failures += 1
        elif method == 'string':
            self.string_fallbacks += 1
        
        if success:
            self.total_bytes_serialized += size
    
    def get_stats(self) -> dict:
        """Get serialization statistics"""
        total_attempts = (self.json_success + self.json_failures + 
                         self.pickle_success + self.pickle_failures + 
                         self.string_fallbacks)
        
        return {
            'total_attempts': total_attempts,
            'json_success_rate': (self.json_success / max(self.json_success + self.json_failures, 1)) * 100,
            'pickle_success_rate': (self.pickle_success / max(self.pickle_success + self.pickle_failures, 1)) * 100,
            'string_fallback_rate': (self.string_fallbacks / max(total_attempts, 1)) * 100,
            'total_bytes_serialized': self.total_bytes_serialized,
            'total_bytes_deserialized': self.total_bytes_deserialized,
            'average_size': self.total_bytes_serialized / max(self.json_success + self.pickle_success, 1)
        }

# Global statistics instance
serialization_stats = SerializationStats()

if __name__ == "__main__":
    # Test serialization with various data types
    test_data = [
        {"simple": "string", "number": 42},
        [1, 2, 3, "mixed", {"nested": True}],
        datetime.now(),
        {"decimal": Decimal("123.45")},
        "simple string",
        123,
        None,
        {"complex": {"nested": {"deep": [1, 2, {"very": "deep"}]}}}
    ]
    
    print("🧪 Testing Smart Serializer...")
    
    for i, data in enumerate(test_data):
        print(f"\nTest {i+1}: {type(data).__name__}")
        
        # Serialize
        serialized = SmartSerializer.safe_serialize(data)
        if serialized:
            size = len(serialized)
            print(f"  ✅ Serialized: {size} chars")
            
            # Deserialize
            deserialized = SmartSerializer.safe_deserialize(serialized)
            print(f"  ✅ Deserialized: {type(deserialized).__name__}")
            
            # Check if data is preserved (basic check)
            if str(data) == str(deserialized):
                print("  ✅ Data integrity: OK")
            else:
                print("  ⚠️ Data integrity: Changed")
        else:
            print("  ❌ Serialization failed")
    
    print(f"\n📊 Statistics: {serialization_stats.get_stats()}")
