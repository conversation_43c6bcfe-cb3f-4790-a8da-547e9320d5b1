#!/usr/bin/env python3
"""
CSV Data Processing API for Competitors Analysis
Handles CSV file processing and data analysis for the dashboard
🔥 Auto-restart aktif - Flask akan restart otomatis saat file ini diedit!
"""

import os
import pandas as pd
import json
from datetime import datetime, timedelta
from flask import Flask, jsonify, request, send_from_directory, Response
from flask_cors import CORS
import glob
import re
import shutil
from werkzeug.utils import secure_filename
import uuid
import sqlite3
import threading
import time
from queue import Queue
from collections import defaultdict, Counter

# OTP Authentication System
try:
    from auth_routes import auth_bp, init_auth_services
    from auth_middleware import init_auth_middleware, require_auth, optional_auth, admin_required
    from redis_client import ResilientRedisClient
    AUTH_AVAILABLE = True
    print("🔐 OTP Authentication system imported successfully")
except ImportError as e:
    AUTH_AVAILABLE = False
    print(f"🔓 OTP Authentication system not available: {e}")

app = Flask(__name__)

# Configure CORS with authentication support
CORS(app,
     origins=['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000'],  # Frontend URLs
     allow_headers=['Content-Type', 'Authorization'],  # Include Authorization header
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],  # Allow all methods
     supports_credentials=True  # Support credentials for authentication
)

# Initialize OTP Authentication System
redis_client = None
if AUTH_AVAILABLE:
    try:
        # Try to initialize Redis client
        redis_client = ResilientRedisClient()

        # Test Redis connection
        if redis_client and redis_client.ping():
            print("✅ Redis connection successful")

            # Initialize authentication services with Redis
            init_auth_services(redis_client)

            # Initialize authentication middleware
            from session_manager import SessionManager
            session_mgr = SessionManager(redis_client)
            init_auth_middleware(session_mgr)

            print("🔐 OTP Authentication system initialized with Redis")
        else:
            print("⚠️ Redis connection failed - Using in-memory storage")

            # Initialize authentication services with None (in-memory fallback)
            init_auth_services(None)

            # Initialize authentication middleware with None
            from session_manager import SessionManager
            session_mgr = SessionManager(None)
            init_auth_middleware(session_mgr)

            print("🔐 OTP Authentication system initialized with in-memory storage")

        # Register authentication routes
        app.register_blueprint(auth_bp)
        print("📍 Authentication endpoints available at /api/auth/*")

    except Exception as e:
        print(f"⚠️ Failed to initialize authentication: {e}")
        print("🔄 Trying fallback initialization...")

        try:
            # Fallback: Initialize with None (in-memory)
            init_auth_services(None)
            from session_manager import SessionManager
            session_mgr = SessionManager(None)
            init_auth_middleware(session_mgr)
            app.register_blueprint(auth_bp)
            print("🔐 OTP Authentication system initialized with fallback in-memory storage")
            print("📍 Authentication endpoints available at /api/auth/*")
        except Exception as fallback_error:
            print(f"❌ Fallback initialization also failed: {fallback_error}")
            AUTH_AVAILABLE = False
else:
    print("🔓 OTP Authentication system disabled")

# Configuration
CSV_DATA_DIR = os.path.join(os.path.dirname(__file__), 'data')
UPLOAD_DIR = os.path.join(os.path.dirname(__file__), 'uploads')

# File upload configuration
ALLOWED_EXTENSIONS = {'csv'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

# Ensure directories exist
os.makedirs(CSV_DATA_DIR, exist_ok=True)
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Notification system configuration
NOTIFICATION_DB = os.path.join(os.path.dirname(__file__), 'notifications.db')
notification_queue = Queue()
notification_clients = []

# Initialize notification database
def init_notification_db():
    """Initialize SQLite database for notifications"""
    conn = sqlite3.connect(NOTIFICATION_DB)
    cursor = conn.cursor()

    # Create notifications table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS notifications (
            id TEXT PRIMARY KEY,
            type TEXT NOT NULL,
            priority TEXT DEFAULT 'medium',
            status TEXT DEFAULT 'unread',
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            category TEXT,
            source TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP,
            expires_at TIMESTAMP,
            persistent BOOLEAN DEFAULT 0,
            dismissible BOOLEAN DEFAULT 1
        )
    ''')

    # Create notification settings table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS notification_settings (
            id INTEGER PRIMARY KEY,
            enabled BOOLEAN DEFAULT 1,
            email BOOLEAN DEFAULT 1,
            browser BOOLEAN DEFAULT 1,
            sound BOOLEAN DEFAULT 1,
            desktop BOOLEAN DEFAULT 0,
            data_updates BOOLEAN DEFAULT 1,
            system_alerts BOOLEAN DEFAULT 1,
            analysis_complete BOOLEAN DEFAULT 1,
            file_operations BOOLEAN DEFAULT 1,
            priority_low BOOLEAN DEFAULT 1,
            priority_medium BOOLEAN DEFAULT 1,
            priority_high BOOLEAN DEFAULT 1,
            priority_urgent BOOLEAN DEFAULT 1,
            quiet_hours_enabled BOOLEAN DEFAULT 0,
            quiet_hours_start TEXT DEFAULT '22:00',
            quiet_hours_end TEXT DEFAULT '08:00',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Insert default settings if not exists
    cursor.execute('SELECT COUNT(*) FROM notification_settings')
    if cursor.fetchone()[0] == 0:
        cursor.execute('INSERT INTO notification_settings DEFAULT VALUES')

    conn.commit()
    conn.close()

# Initialize database on startup
init_notification_db()

# Notification helper functions
def create_notification(type_name, title, message, priority='medium', category=None, source=None, metadata=None, persistent=False, dismissible=True, expires_at=None):
    """Create a new notification"""
    notification_id = str(uuid.uuid4())

    conn = sqlite3.connect(NOTIFICATION_DB)
    cursor = conn.cursor()

    cursor.execute('''
        INSERT INTO notifications (id, type, priority, title, message, category, source, metadata, persistent, dismissible, expires_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (notification_id, type_name, priority, title, message, category, source,
          json.dumps(metadata) if metadata else None, persistent, dismissible, expires_at))

    conn.commit()

    # Get the created notification
    cursor.execute('SELECT * FROM notifications WHERE id = ?', (notification_id,))
    row = cursor.fetchone()
    conn.close()

    if row:
        notification = dict(zip([col[0] for col in cursor.description], row))
        notification['metadata'] = json.loads(notification['metadata']) if notification['metadata'] else {}

        # Send real-time update
        send_notification_event('notification_created', notification)

        return notification

    return None

def get_notifications(filters=None, page=1, limit=50):
    """Get notifications with optional filtering"""
    conn = sqlite3.connect(NOTIFICATION_DB)
    cursor = conn.cursor()

    query = 'SELECT * FROM notifications WHERE 1=1'
    params = []

    if filters:
        if filters.get('type'):
            types = filters['type'].split(',')
            placeholders = ','.join(['?' for _ in types])
            query += f' AND type IN ({placeholders})'
            params.extend(types)

        if filters.get('priority'):
            priorities = filters['priority'].split(',')
            placeholders = ','.join(['?' for _ in priorities])
            query += f' AND priority IN ({placeholders})'
            params.extend(priorities)

        if filters.get('status'):
            statuses = filters['status'].split(',')
            placeholders = ','.join(['?' for _ in statuses])
            query += f' AND status IN ({placeholders})'
            params.extend(statuses)

        if filters.get('category'):
            categories = filters['category'].split(',')
            placeholders = ','.join(['?' for _ in categories])
            query += f' AND category IN ({placeholders})'
            params.extend(categories)

        if filters.get('search'):
            query += ' AND (title LIKE ? OR message LIKE ?)'
            search_term = f"%{filters['search']}%"
            params.extend([search_term, search_term])

        if filters.get('startDate'):
            query += ' AND created_at >= ?'
            params.append(filters['startDate'])

        if filters.get('endDate'):
            query += ' AND created_at <= ?'
            params.append(filters['endDate'])

    # Add ordering and pagination
    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?'
    params.extend([limit, (page - 1) * limit])

    cursor.execute(query, params)
    rows = cursor.fetchall()

    # Get total count
    count_query = query.replace('SELECT *', 'SELECT COUNT(*)').split('ORDER BY')[0]
    cursor.execute(count_query, params[:-2])  # Remove limit and offset params
    total = cursor.fetchone()[0]

    conn.close()

    notifications = []
    for row in rows:
        notification = dict(zip([
            'id', 'type', 'priority', 'status', 'title', 'message', 'category',
            'source', 'metadata', 'created_at', 'read_at', 'expires_at', 'persistent', 'dismissible'
        ], row))
        notification['metadata'] = json.loads(notification['metadata']) if notification['metadata'] else {}
        notifications.append(notification)

    return {
        'notifications': notifications,
        'total': total,
        'page': page,
        'limit': limit,
        'hasMore': total > page * limit
    }

def send_notification_event(event_type, data):
    """Send real-time notification event to connected clients"""
    event_data = {
        'type': event_type,
        'data': data,
        'timestamp': datetime.now().isoformat()
    }

    # Add to queue for SSE clients
    notification_queue.put(event_data)

def format_sse_data(data):
    """Format data for Server-Sent Events"""
    return f"data: {json.dumps(data)}\n\n"

# Notification integration helpers
def notify_data_processing_complete(competitor, record_count, processing_time):
    """Create notification for completed data processing"""
    create_notification(
        type_name='data_update',
        title='Data Processing Complete',
        message=f'{competitor} data has been processed with {record_count} records',
        priority='medium',
        category='processing',
        source='data_processor',
        metadata={
            'competitor': competitor,
            'recordCount': record_count,
            'processingTime': processing_time
        }
    )

def notify_file_upload_result(filename, success, error=None):
    """Create notification for file upload result"""
    create_notification(
        type_name='success' if success else 'error',
        title='File Upload ' + ('Successful' if success else 'Failed'),
        message=f'{filename} has been {"uploaded successfully" if success else f"failed to upload: {error}"}',
        priority='low' if success else 'high',
        category='upload',
        source='file_manager',
        metadata={
            'filename': filename,
            'success': success,
            'error': error
        }
    )

def notify_analysis_complete(analysis_type, competitor, duration, result_count):
    """Create notification for completed analysis"""
    create_notification(
        type_name='analysis_complete',
        title='Analysis Complete',
        message=f'{analysis_type} analysis for {competitor} completed with {result_count} results',
        priority='medium',
        category='analysis',
        source='analysis_engine',
        metadata={
            'analysisType': analysis_type,
            'competitor': competitor,
            'duration': duration,
            'resultCount': result_count
        }
    )

def notify_system_event(message, category='system', priority='medium'):
    """Create system notification"""
    create_notification(
        type_name='system',
        title='System Notification',
        message=message,
        priority=priority,
        category=category,
        source='system'
    )

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_csv_structure(filepath):
    """Validate CSV file structure and return file type and metadata"""
    try:
        df = pd.read_csv(filepath)

        # Check if file is empty
        if df.empty:
            return False, "CSV file is empty"

        # Determine file type based on filename and columns
        filename = os.path.basename(filepath)

        if filename.startswith('EE_'):
            # Competitor file validation
            required_cols = ['web-scraper-order', 'web-scraper-start-url', 'Author', 'Link']
            title_col = 'Tiltle' if 'Tiltle' in df.columns else 'Title'
            if title_col not in df.columns:
                return False, f"Missing required column: Title/Tiltle"

            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                return False, f"Missing required columns for competitor file: {missing_cols}"

            # Parse competitor name and date from filename
            competitor_name, date_str = parse_competitor_filename(filename)
            if not competitor_name:
                return False, "Invalid competitor filename format. Expected: EE_[competitor_name]_[DD_MM_YYYY].csv"

            return True, {
                'type': 'competitor',
                'name': competitor_name,
                'date': date_str,
                'rows': len(df),
                'columns': list(df.columns)
            }
        else:
            # Category file validation
            required_cols = ['web-scraper-order', 'web-scraper-start-url', 'Title', 'Author', 'Link']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                return False, f"Missing required columns for category file: {missing_cols}"

            # Parse category name and date from filename
            category_name, date_str = parse_category_filename(filename)
            if not category_name:
                return False, "Invalid category filename format. Expected: [category_name]_[DD_MM_YYYY].csv"

            return True, {
                'type': 'category',
                'name': category_name,
                'date': date_str,
                'rows': len(df),
                'columns': list(df.columns)
            }

    except Exception as e:
        return False, f"Error reading CSV file: {str(e)}"

def parse_competitor_filename(filename):
    """
    Parse competitor CSV filename to extract competitor name and date
    Expected format: EE_[competitor_name]_[date].csv
    """
    # More flexible pattern to handle multi-word competitor names
    pattern = r'EE_(.+)_(\d{2}_\d{2}_\d{4})\.csv'
    match = re.match(pattern, filename)
    if match:
        competitor_name = match.group(1)
        date_str = match.group(2)
        print(f"Parsed filename {filename}: competitor={competitor_name}, date={date_str}")
        return competitor_name, date_str
    else:
        print(f"Failed to parse filename: {filename} with pattern {pattern}")
    return None, None

def get_latest_and_previous_files(competitor_name):
    """
    Get the latest and previous CSV files for a competitor
    """
    pattern = os.path.join(CSV_DATA_DIR, f'EE_{competitor_name}_*.csv')
    csv_files = glob.glob(pattern)

    if not csv_files:
        raise FileNotFoundError(f"No CSV files found for competitor: {competitor_name}")

    # Sort files by filename date (prioritize filename date over modification time)
    file_dates = []
    for filepath in csv_files:
        filename = os.path.basename(filepath)
        _, date_str = parse_competitor_filename(filename)
        if date_str:
            try:
                date = datetime.strptime(date_str, '%d_%m_%Y')
                file_dates.append((filepath, date, 'filename'))
                print(f"Parsed date from filename {filename}: {date}")
            except ValueError as e:
                print(f"Date parsing error for {filename}: {e}")
                # Fallback to file modification time
                mod_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                file_dates.append((filepath, mod_time, 'modtime'))
        else:
            # Fallback to file modification time if filename parsing fails
            mod_time = datetime.fromtimestamp(os.path.getmtime(filepath))
            file_dates.append((filepath, mod_time, 'modtime'))

    if not file_dates:
        raise FileNotFoundError(f"No valid files found for competitor: {competitor_name}")

    # Sort by date (most recent first), prioritizing filename dates
    file_dates.sort(key=lambda x: (x[2] == 'filename', x[1]), reverse=True)

    latest_file = file_dates[0][0]
    previous_file = file_dates[1][0] if len(file_dates) >= 2 else None

    print(f"Found files for {competitor_name}:")
    print(f"  Latest: {os.path.basename(latest_file)} ({file_dates[0][1]} - {file_dates[0][2]})")
    print(f"  Previous: {os.path.basename(previous_file) if previous_file else 'None'} ({file_dates[1][1] if len(file_dates) >= 2 else 'N/A'} - {file_dates[1][2] if len(file_dates) >= 2 else 'N/A'})")

    return latest_file, previous_file

def standardize_dataframe(df):
    """
    Standardize DataFrame columns and data types for processing
    """
    # Standardize column names
    df.columns = df.columns.str.strip()

    # Handle column name variations (for compatibility with web-scraper data)
    column_mapping = {
        'web-scraper-start-url': 'Page New',
        'web-scraper-order': 'Order New',
        'Tiltle': 'Title'  # Common typo
    }

    for old_col, new_col in column_mapping.items():
        if old_col in df.columns:
            df = df.rename(columns={old_col: new_col})

    # Ensure required columns exist with default values
    required_columns = {
        'Title': 'Unknown Title',
        'Author': 'Unknown Author',
        'Page Old': 0,
        'Page New': 0,
        'Order Old': 0,
        'Order New': 0,
        'new_item': False
    }

    for col, default_value in required_columns.items():
        if col not in df.columns:
            df[col] = default_value

    # Convert numeric columns
    numeric_columns = ['Page Old', 'Page New', 'Order Old', 'Order New']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)

    # Standardize new_item column
    if 'new_item' in df.columns:
        df['new_item'] = df['new_item'].fillna(False).apply(
            lambda x: True if x in [True, 1, '1', 'new', 'New', 'NEW', 'true', 'True'] else False
        )

    # Remove rows with missing essential data
    essential_columns = ['Title']
    df = df.dropna(subset=essential_columns)

    return df

def compare_envato_data(new_file_path, old_file_path=None):
    """
    Implementasi script analisa kompetitor sesuai dengan script yang diberikan
    """
    try:
        # Load CSV files
        df_new = pd.read_csv(new_file_path)
        df_old = pd.read_csv(old_file_path) if old_file_path and os.path.exists(old_file_path) else pd.DataFrame(columns=df_new.columns)

        # Handle column name variations (Tiltle -> Title)
        if 'Tiltle' in df_new.columns:
            df_new = df_new.rename(columns={'Tiltle': 'Title'})
        if 'Tiltle' in df_old.columns:
            df_old = df_old.rename(columns={'Tiltle': 'Title'})

        # Process both dataframes
        for df in [df_old, df_new]:
            if not df.empty:
                # Convert web-scraper columns to numeric
                if 'web-scraper-start-url' in df.columns:
                    df['web-scraper-start-url'] = pd.to_numeric(df['web-scraper-start-url'], errors='coerce')
                if 'web-scraper-order' in df.columns:
                    df['web-scraper-order'] = pd.to_numeric(df['web-scraper-order'], errors='coerce')

                # Handle new_item column
                if 'new_item' in df.columns:
                    df['new_item'] = df['new_item'].fillna(False).apply(
                        lambda x: True if x in [True, 1, '1', 'new', 'New', 'NEW', 'true', 'True'] else False
                    )
                else:
                    df['new_item'] = False

        # Clean data - remove rows with missing essential data
        required_columns = ['Link', 'web-scraper-start-url', 'web-scraper-order']
        if all(col in df_new.columns for col in required_columns):
            df_new = df_new.dropna(subset=required_columns)
        if not df_old.empty and all(col in df_old.columns for col in required_columns):
            df_old = df_old.dropna(subset=required_columns)

        # 1. NEW ITEMS: Items marked as new_item
        new_items = pd.DataFrame()
        if 'new_item' in df_new.columns and not df_new.empty:
            new_items_data = df_new[df_new['new_item'] == True]
            if not new_items_data.empty:
                new_items = new_items_data[['Title', 'Author', 'Link', 'web-scraper-start-url', 'web-scraper-order', 'new_item']].copy()
                new_items = new_items.rename(columns={
                    'web-scraper-start-url': 'Page',
                    'web-scraper-order': 'Order'
                })
                # Convert to int safely
                new_items['Page'] = pd.to_numeric(new_items['Page'], errors='coerce').fillna(0).astype(int)
                new_items['Order'] = pd.to_numeric(new_items['Order'], errors='coerce').fillna(0).astype(int)

        # 2. OLD ITEMS: Items from old file not in new file
        old_items = pd.DataFrame()
        if not df_old.empty and 'Link' in df_old.columns and 'Link' in df_new.columns:
            old_links = set(df_old['Link']) if not df_old.empty else set()
            new_links = set(df_new['Link']) if not df_new.empty else set()
            removed_links = old_links - new_links

            if removed_links:
                old_items_data = df_old[df_old['Link'].isin(removed_links)]
                if not old_items_data.empty:
                    old_items = old_items_data[['Title', 'Author', 'Link', 'web-scraper-start-url', 'web-scraper-order']].copy()
                    old_items = old_items.rename(columns={
                        'web-scraper-start-url': 'Page Old',
                        'web-scraper-order': 'Order Old'
                    })
                    # Convert to int safely
                    old_items['Page Old'] = pd.to_numeric(old_items['Page Old'], errors='coerce').fillna(0).astype(int)
                    old_items['Order Old'] = pd.to_numeric(old_items['Order Old'], errors='coerce').fillna(0).astype(int)

        # 3. POPULAR ITEMS: Items from start until first new item
        popular_items = pd.DataFrame()
        if not df_new.empty:
            # Sort by web-scraper columns
            sort_columns = []
            if 'web-scraper-start-url' in df_new.columns:
                sort_columns.append('web-scraper-start-url')
            if 'web-scraper-order' in df_new.columns:
                sort_columns.append('web-scraper-order')

            if sort_columns:
                df_new_sorted = df_new.sort_values(by=sort_columns).reset_index(drop=True)
            else:
                df_new_sorted = df_new.copy()

            popular_data = []
            for _, row in df_new_sorted.iterrows():
                if row.get('new_item', False):
                    break
                popular_data.append({
                    'Title': row.get('Title', ''),
                    'Author': row.get('Author', ''),
                    'Link': row.get('Link', ''),
                    'Page': int(row.get('web-scraper-start-url', 0)) if pd.notna(row.get('web-scraper-start-url')) else 0,
                    'Order': int(row.get('web-scraper-order', 0)) if pd.notna(row.get('web-scraper-order')) else 0,
                    'new_item': row.get('new_item', False)
                })

            if popular_data:
                popular_items = pd.DataFrame(popular_data)

        # 4. ALL ITEMS: All items in new file
        all_items = pd.DataFrame()
        if not df_new.empty:
            all_items_data = df_new[['Title', 'Author', 'Link', 'web-scraper-start-url', 'web-scraper-order', 'new_item']].copy()
            all_items = all_items_data.rename(columns={
                'web-scraper-start-url': 'Page',
                'web-scraper-order': 'Order'
            })
            # Convert to int safely
            all_items['Page'] = pd.to_numeric(all_items['Page'], errors='coerce').fillna(0).astype(int)
            all_items['Order'] = pd.to_numeric(all_items['Order'], errors='coerce').fillna(0).astype(int)

            # Sort by Page and Order
            all_items = all_items.sort_values(by=['Page', 'Order']).reset_index(drop=True)

        return {
            'popular': popular_items.to_dict('records') if not popular_items.empty else [],
            'new': new_items.to_dict('records') if not new_items.empty else [],
            'all': all_items.to_dict('records') if not all_items.empty else [],
            'old': old_items.to_dict('records') if not old_items.empty else []
        }

    except Exception as e:
        print(f"Error in compare_envato_data: {e}")
        import traceback
        traceback.print_exc()
        return {
            'popular': [],
            'new': [],
            'all': [],
            'old': []
        }

def process_csv_data(filepath, data_type='all', competitor_name=None):
    """
    Enhanced CSV data processing with advanced analysis capabilities
    """
    try:
        if competitor_name:
            # Use advanced comparison logic for competitor data
            latest_file, previous_file = get_latest_and_previous_files(competitor_name)
            all_data = compare_envato_data(latest_file, previous_file)

            if data_type in all_data:
                return all_data[data_type]
            else:
                return all_data['all']
        else:
            # Fallback to simple processing for single file
            df = pd.read_csv(filepath)
            df = standardize_dataframe(df)

            # Apply filtering based on data_type
            if data_type == 'popular':
                # Get items with best current rankings (lowest Order New)
                if 'Order New' in df.columns:
                    popular_df = df.nsmallest(10, 'Order New')
                    return popular_df.to_dict('records')
                else:
                    return df.head(10).to_dict('records')

            elif data_type == 'new':
                # Filter for new items
                if 'new_item' in df.columns:
                    new_df = df[df['new_item'] == True]
                    return new_df.to_dict('records')
                else:
                    return df.head(5).to_dict('records')

            elif data_type == 'old':
                # Items that moved to worse positions
                if 'Page Old' in df.columns and 'Page New' in df.columns:
                    old_df = df[df['Page Old'] < df['Page New']]  # Worse page position
                    return old_df.to_dict('records')
                else:
                    return df.tail(5).to_dict('records')

            else:  # 'all'
                return df.to_dict('records')

    except Exception as e:
        print(f"Error processing CSV: {e}")
        return []

@app.route('/api/competitors', methods=['GET'])
def get_competitors():
    """
    Get list of available competitors from CSV files
    """
    try:
        competitors = []
        csv_files = glob.glob(os.path.join(CSV_DATA_DIR, 'EE_*.csv'))
        
        for filepath in csv_files:
            filename = os.path.basename(filepath)
            competitor_name, date_str = parse_competitor_filename(filename)
            
            if competitor_name:
                competitors.append({
                    'name': competitor_name,
                    'filename': filename,
                    'date': date_str,
                    'filepath': filepath
                })
        
        # Remove duplicates and sort
        unique_competitors = {}
        for comp in competitors:
            name = comp['name']
            if name not in unique_competitors or comp['date'] > unique_competitors[name]['date']:
                unique_competitors[name] = comp
        
        competitor_list = [comp['name'] for comp in unique_competitors.values()]
        
        return jsonify({
            'success': True,
            'competitors': competitor_list,
            'details': list(unique_competitors.values())
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/competitors/<competitor_name>/data', methods=['GET'])
def get_competitor_data(competitor_name):
    """
    Get data for a specific competitor using enhanced processing
    """
    try:
        data_type = request.args.get('type', 'all')  # popular, new, all, old

        # Use enhanced processing with competitor comparison
        data = process_csv_data(None, data_type, competitor_name)

        # Get file info for response
        try:
            latest_file, _ = get_latest_and_previous_files(competitor_name)
            filename = os.path.basename(latest_file)
        except:
            filename = f"EE_{competitor_name}_latest.csv"

        # Create notification for data processing completion
        notify_data_processing_complete(
            competitor=competitor_name,
            record_count=len(data),
            processing_time=0  # Could be calculated if needed
        )

        return jsonify({
            'success': True,
            'competitor': competitor_name,
            'type': data_type,
            'data': data,
            'file': filename,
            'count': len(data)
        })

    except FileNotFoundError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error processing competitor data: {str(e)}'
        }), 500

@app.route('/api/competitors/<competitor_name>/all-data', methods=['GET'])
def get_all_competitor_data(competitor_name):
    """
    Get all categorized data for a specific competitor using enhanced processing
    """
    try:
        # Use enhanced processing to get all data categories at once
        latest_file, previous_file = get_latest_and_previous_files(competitor_name)
        all_data = compare_envato_data(latest_file, previous_file)

        return jsonify({
            'success': True,
            'competitor': competitor_name,
            'data': all_data,
            'file': os.path.basename(latest_file),
            'previous_file': os.path.basename(previous_file) if previous_file else None,
            'analysis_type': 'enhanced_comparison'
        })

    except FileNotFoundError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error processing competitor data: {str(e)}'
        }), 500

@app.route('/api/files', methods=['GET'])
def get_files():
    """
    Get list of all CSV files
    """
    try:
        files = []
        csv_files = glob.glob(os.path.join(CSV_DATA_DIR, '*.csv'))
        
        for filepath in csv_files:
            filename = os.path.basename(filepath)
            stat = os.stat(filepath)
            
            # Determine file type
            file_type = 'competitor' if filename.startswith('EE_') else 'category'
            
            files.append({
                'id': filename,
                'name': filename,
                'type': file_type,
                'size': f"{stat.st_size / 1024 / 1024:.1f} MB",
                'uploadDate': datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d'),
                'lastModified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d'),
                'author': 'System'
            })
        
        return jsonify({
            'success': True,
            'files': files
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/files/<file_id>/download', methods=['GET'])
def download_file(file_id):
    """
    Download a specific CSV file
    """
    try:
        # Validate file_id (should be a filename)
        if not file_id.endswith('.csv'):
            return jsonify({
                'success': False,
                'error': 'Invalid file ID. Must be a CSV file.'
            }), 400

        # Secure the filename to prevent directory traversal
        filename = secure_filename(file_id)
        filepath = os.path.join(CSV_DATA_DIR, filename)

        # Check if file exists
        if not os.path.exists(filepath):
            return jsonify({
                'success': False,
                'error': 'File not found'
            }), 404

        # Check if it's actually a file (not a directory)
        if not os.path.isfile(filepath):
            return jsonify({
                'success': False,
                'error': 'Invalid file'
            }), 400

        # Send the file
        return send_from_directory(
            CSV_DATA_DIR,
            filename,
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Download failed: {str(e)}'
        }), 500

@app.route('/api/files/<file_id>', methods=['DELETE'])
def delete_file(file_id):
    """
    Delete a specific CSV file
    """
    try:
        # Validate file_id (should be a filename)
        if not file_id.endswith('.csv'):
            return jsonify({
                'success': False,
                'error': 'Invalid file ID. Must be a CSV file.'
            }), 400

        # Secure the filename to prevent directory traversal
        filename = secure_filename(file_id)
        filepath = os.path.join(CSV_DATA_DIR, filename)

        # Check if file exists
        if not os.path.exists(filepath):
            return jsonify({
                'success': False,
                'error': 'File not found'
            }), 404

        # Check if it's actually a file (not a directory)
        if not os.path.isfile(filepath):
            return jsonify({
                'success': False,
                'error': 'Invalid file'
            }), 400

        # Get file size before deletion for notification
        file_size = os.path.getsize(filepath)

        # Delete the file with proper error handling
        try:
            os.remove(filepath)
        except PermissionError:
            raise Exception(f"Permission denied: Cannot delete {filename}. File may be in use.")
        except OSError as e:
            raise Exception(f"Failed to delete file: {str(e)}")

        # Verify file was actually deleted
        if os.path.exists(filepath):
            raise Exception("File deletion failed: File still exists after deletion attempt")

        # Create notification for file deletion
        try:
            create_notification(
                type_name='system',
                title='File Deleted',
                message=f'CSV file {filename} has been deleted successfully. Size freed: {format_file_size(file_size)}',
                priority='low',
                category='file_management',
                source='system',
                metadata={
                    'file_name': filename,
                    'size_freed': file_size
                }
            )
        except Exception as notif_error:
            print(f"Warning: Failed to create deletion notification: {str(notif_error)}")
            # Don't fail the deletion if notification fails

        return jsonify({
            'success': True,
            'message': f'File {filename} deleted successfully',
            'size_freed': file_size,
            'size_freed_formatted': format_file_size(file_size)
        })

    except Exception as e:
        # Create notification for file deletion failure
        try:
            create_notification(
                type_name='error',
                title='File Deletion Failed',
                message=f'Failed to delete file {file_id}: {str(e)}',
                priority='medium',
                category='file_management',
                source='system',
                metadata={'error': str(e), 'file_name': file_id}
            )
        except Exception as notif_error:
            print(f"Warning: Failed to create error notification: {str(notif_error)}")

        return jsonify({
            'success': False,
            'error': f'Delete failed: {str(e)}'
        }), 500

@app.route('/api/files/<file_id>/rename', methods=['PUT'])
def rename_file(file_id):
    """
    Rename a specific CSV file
    """
    try:
        # Get new filename from request
        data = request.get_json()
        if not data or 'new_name' not in data:
            return jsonify({
                'success': False,
                'error': 'New filename is required'
            }), 400

        new_name = data['new_name'].strip()

        # Validate new filename
        if not new_name:
            return jsonify({
                'success': False,
                'error': 'New filename cannot be empty'
            }), 400

        if not new_name.endswith('.csv'):
            return jsonify({
                'success': False,
                'error': 'New filename must end with .csv'
            }), 400

        # Validate file_id (should be a filename)
        if not file_id.endswith('.csv'):
            return jsonify({
                'success': False,
                'error': 'Invalid file ID. Must be a CSV file.'
            }), 400

        # Secure the filenames to prevent directory traversal
        old_filename = secure_filename(file_id)
        new_filename = secure_filename(new_name)

        old_filepath = os.path.join(CSV_DATA_DIR, old_filename)
        new_filepath = os.path.join(CSV_DATA_DIR, new_filename)

        # Check if old file exists
        if not os.path.exists(old_filepath):
            return jsonify({
                'success': False,
                'error': 'File not found'
            }), 404

        # Check if new filename already exists
        if os.path.exists(new_filepath):
            return jsonify({
                'success': False,
                'error': 'A file with the new name already exists'
            }), 409

        # Check if it's actually a file (not a directory)
        if not os.path.isfile(old_filepath):
            return jsonify({
                'success': False,
                'error': 'Invalid file'
            }), 400

        # Rename the file
        try:
            os.rename(old_filepath, new_filepath)
        except PermissionError:
            raise Exception(f"Permission denied: Cannot rename {old_filename}. File may be in use.")
        except OSError as e:
            raise Exception(f"Failed to rename file: {str(e)}")

        # Verify file was actually renamed
        if not os.path.exists(new_filepath) or os.path.exists(old_filepath):
            raise Exception("File rename failed: Operation did not complete successfully")

        # Create notification for file rename
        try:
            create_notification(
                type_name='system',
                title='File Renamed',
                message=f'CSV file renamed from {old_filename} to {new_filename}',
                priority='low',
                category='file_management',
                source='system',
                metadata={
                    'old_name': old_filename,
                    'new_name': new_filename
                }
            )
        except Exception as notif_error:
            print(f"Warning: Failed to create rename notification: {str(notif_error)}")
            # Don't fail the rename if notification fails

        return jsonify({
            'success': True,
            'message': f'File renamed from {old_filename} to {new_filename}',
            'old_name': old_filename,
            'new_name': new_filename
        })

    except Exception as e:
        # Create notification for file rename failure
        try:
            create_notification(
                type_name='error',
                title='File Rename Failed',
                message=f'Failed to rename file {file_id}: {str(e)}',
                priority='medium',
                category='file_management',
                source='system',
                metadata={'error': str(e), 'file_name': file_id}
            )
        except Exception as notif_error:
            print(f"Warning: Failed to create error notification: {str(notif_error)}")

        return jsonify({
            'success': False,
            'error': f'Rename failed: {str(e)}'
        }), 500

def parse_category_filename(filename):
    """
    Parse category CSV filename to extract category name and date
    Expected format: [category_name]_[date].csv
    """
    # Pattern for category files like "Graphic_Design_Templates_16_07_2025.csv"
    pattern = r'(.+)_(\d{2}_\d{2}_\d{4})\.csv'
    match = re.match(pattern, filename)
    if match:
        category_name = match.group(1)
        date_str = match.group(2)
        print(f"Parsed category filename {filename}: category={category_name}, date={date_str}")
        return category_name, date_str
    else:
        print(f"Failed to parse category filename: {filename} with pattern {pattern}")
    return None, None

def get_category_files_by_date(category_name):
    """
    Get all CSV files for a category sorted by date (newest first)
    Returns list of tuples: (filepath, date_object, date_string)
    """
    try:
        csv_files = glob.glob(os.path.join(CSV_DATA_DIR, f'{category_name}_*.csv'))
        file_dates = []

        for filepath in csv_files:
            filename = os.path.basename(filepath)
            _, date_str = parse_category_filename(filename)
            if date_str:
                try:
                    date_obj = datetime.strptime(date_str, '%d_%m_%Y')
                    file_dates.append((filepath, date_obj, date_str))
                    print(f"Found category file: {filename} with date {date_obj}")
                except ValueError as e:
                    print(f"Date parsing error for {filename}: {e}")
                    continue

        # Sort by date (newest first)
        file_dates.sort(key=lambda x: x[1], reverse=True)
        return file_dates

    except Exception as e:
        print(f"Error getting category files: {e}")
        return []

def get_latest_and_previous_category_files(category_name):
    """
    Get the latest and previous CSV files for a category
    Returns tuple: (latest_file_path, previous_file_path)
    """
    try:
        file_dates = get_category_files_by_date(category_name)

        if len(file_dates) == 0:
            print(f"No files found for category: {category_name}")
            return None, None
        elif len(file_dates) == 1:
            print(f"Only one file found for category: {category_name}")
            return file_dates[0][0], None
        else:
            latest_file = file_dates[0][0]
            previous_file = file_dates[1][0]
            print(f"Found latest: {os.path.basename(latest_file)}, previous: {os.path.basename(previous_file)}")
            return latest_file, previous_file

    except Exception as e:
        print(f"Error getting latest and previous files: {e}")
        return None, None

def standardize_category_dataframe(df):
    """
    Standardize category CSV dataframe columns and clean data
    """
    # Standardize column names
    if 'web-scraper-order' in df.columns:
        df = df.rename(columns={'web-scraper-order': 'Order'})
    if 'web-scraper-start-url' in df.columns:
        df = df.rename(columns={'web-scraper-start-url': 'Page'})

    # Ensure required columns exist
    required_columns = ['Title', 'Author', 'Link']
    for col in required_columns:
        if col not in df.columns:
            df[col] = 'Unknown'

    # Clean data and handle NaN values
    df = df.dropna(subset=['Title'])
    df['Author'] = df['Author'].fillna('Unknown Author')
    df['Link'] = df['Link'].fillna('')

    # Ensure Order and Page columns exist with proper values
    if 'Order' not in df.columns:
        df['Order'] = range(1, len(df) + 1)
    if 'Page' not in df.columns:
        df['Page'] = 1

    # Fill NaN values in Order and Page
    # Create a series for Order values where NaN
    order_mask = df['Order'].isna()
    if order_mask.any():
        # Get indices where Order is NaN and assign sequential values
        nan_indices = df[order_mask].index
        df.loc[nan_indices, 'Order'] = range(1, len(nan_indices) + 1)

    df['Page'] = df['Page'].fillna(1)

    # Handle is_new column
    if 'is_new' in df.columns:
        df['is_new'] = df['is_new'].fillna(False)
        df['is_new'] = df['is_new'].astype(bool)
    else:
        df['is_new'] = False

    return df

def compare_category_data(latest_file, previous_file):
    """
    Compare two category CSV files and calculate changes
    Returns processed data with comparison metrics
    """
    try:
        print(f"Comparing files: {os.path.basename(latest_file)} vs {os.path.basename(previous_file)}")

        # Read and standardize both files
        df_new = pd.read_csv(latest_file)
        df_old = pd.read_csv(previous_file)

        df_new = standardize_category_dataframe(df_new)
        df_old = standardize_category_dataframe(df_old)

        print(f"New file: {len(df_new)} records, Old file: {len(df_old)} records")

        # Create unique identifiers for matching
        # Use Link as primary identifier since it's most unique
        # Fallback to Title+Author for items without links
        df_new['item_id'] = df_new.apply(lambda row:
            row['Link'] if pd.notna(row['Link']) and row['Link'].strip() != ''
            else f"{row['Title']}|{row['Author']}", axis=1)
        df_old['item_id'] = df_old.apply(lambda row:
            row['Link'] if pd.notna(row['Link']) and row['Link'].strip() != ''
            else f"{row['Title']}|{row['Author']}", axis=1)

        # Remove duplicates within each file (keep first occurrence)
        # This handles cases where same item appears multiple times in one file
        df_new_dedup = df_new.drop_duplicates(subset=['item_id'], keep='first')
        df_old_dedup = df_old.drop_duplicates(subset=['item_id'], keep='first')

        if len(df_new) != len(df_new_dedup):
            print(f"Removed {len(df_new) - len(df_new_dedup)} duplicates from new file")
        if len(df_old) != len(df_old_dedup):
            print(f"Removed {len(df_old) - len(df_old_dedup)} duplicates from old file")

        df_new = df_new_dedup
        df_old = df_old_dedup

        # Merge data to compare
        merged = df_new.merge(df_old, on='item_id', how='outer', suffixes=('_new', '_old'))

        # Calculate changes and status
        result_data = []

        for _, row in merged.iterrows():
            # Determine if item is new, removed, or existing
            is_new_item = pd.isna(row.get('Order_old'))
            is_removed_item = pd.isna(row.get('Order_new'))

            if is_removed_item:
                # Skip removed items for now (could be included in future)
                continue

            # Build result record
            record = {
                'Title': row.get('Title_new', row.get('Title_old', 'Unknown')),
                'Author': row.get('Author_new', row.get('Author_old', 'Unknown Author')),
                'Link': row.get('Link_new', row.get('Link_old', '')),
                'Page New': int(row.get('Page_new', 1)) if not pd.isna(row.get('Page_new')) else 1,
                'Order New': int(row.get('Order_new', 0)) if not pd.isna(row.get('Order_new')) else 0,
                'is_new': bool(row.get('is_new_new', False)) if not pd.isna(row.get('is_new_new')) else False
            }

            if is_new_item:
                # New item
                record['Page Old'] = record['Page New']
                record['Order Old'] = record['Order New']
                record['Change'] = 0
                record['Status'] = 'New'
            else:
                # Existing item - calculate changes
                record['Page Old'] = int(row.get('Page_old', 1)) if not pd.isna(row.get('Page_old')) else 1
                record['Order Old'] = int(row.get('Order_old', 0)) if not pd.isna(row.get('Order_old')) else 0

                # Calculate position change (improvement = positive change)
                order_change = record['Order Old'] - record['Order New']
                page_change = record['Page Old'] - record['Page New']

                # Primary metric is order change, secondary is page change
                if order_change != 0:
                    record['Change'] = order_change
                elif page_change != 0:
                    record['Change'] = page_change * 100  # Weight page changes more
                else:
                    record['Change'] = 0

                # Determine status
                if record['Change'] > 0:
                    record['Status'] = 'Up'
                elif record['Change'] < 0:
                    record['Status'] = 'Down'
                else:
                    record['Status'] = 'Same'

            result_data.append(record)

        # Sort by current position (Page New, then Order New)
        result_data.sort(key=lambda x: (x['Page New'], x['Order New']))

        print(f"Comparison complete: {len(result_data)} items processed")
        return result_data

    except Exception as e:
        print(f"Error in compare_category_data: {e}")
        import traceback
        traceback.print_exc()
        return []

def process_category_data(filepath):
    """
    Process single category CSV data (fallback when no comparison available)
    """
    try:
        df = pd.read_csv(filepath)
        df = standardize_category_dataframe(df)

        # Add calculated fields for compatibility with frontend
        df['Page Old'] = df['Page']
        df['Page New'] = df['Page']
        df['Order Old'] = df['Order']
        df['Order New'] = df['Order']
        df['Change'] = 0  # No change for single file
        df['Status'] = 'Same'  # Default status

        # Convert to records and ensure JSON serializable
        records = df.to_dict('records')

        # Final cleanup to ensure no NaN values in the output
        for record in records:
            for key, value in record.items():
                if pd.isna(value):
                    if key in ['Order', 'Page', 'Page Old', 'Page New', 'Order Old', 'Order New', 'Change']:
                        record[key] = 0
                    elif key == 'is_new':
                        record[key] = False
                    else:
                        record[key] = ''

        return records

    except Exception as e:
        print(f"Error processing category data: {e}")
        import traceback
        traceback.print_exc()
        return []

# Test endpoints for OTP authentication
@app.route('/api/test-no-auth', methods=['GET'])
def test_no_auth():
    """Test endpoint without authentication"""
    return jsonify({
        'success': True,
        'message': 'No auth endpoint working!',
        'timestamp': datetime.now().isoformat(),
        'auth_available': AUTH_AVAILABLE
    })

@app.route('/api/test-auth', methods=['GET'])
@require_auth if AUTH_AVAILABLE else lambda f: f
def test_auth():
    """Test endpoint with authentication required"""
    if AUTH_AVAILABLE:
        from flask import g
        user = getattr(g, 'current_user', None)
        return jsonify({
            'success': True,
            'message': 'Authentication successful!',
            'user': user,
            'timestamp': datetime.now().isoformat()
        })
    else:
        return jsonify({
            'success': True,
            'message': 'Auth not available - endpoint accessible',
            'timestamp': datetime.now().isoformat()
        })

@app.route('/api/test-optional-auth', methods=['GET'])
@optional_auth if AUTH_AVAILABLE else lambda f: f
def test_optional_auth():
    """Test endpoint with optional authentication"""
    if AUTH_AVAILABLE:
        from flask import g
        user = getattr(g, 'current_user', None)
        if user:
            return jsonify({
                'success': True,
                'message': f'Hello authenticated user: {user.get("email", "unknown")}',
                'user': user,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': True,
                'message': 'Hello anonymous user',
                'timestamp': datetime.now().isoformat()
            })
    else:
        return jsonify({
            'success': True,
            'message': 'Auth not available - anonymous access',
            'timestamp': datetime.now().isoformat()
        })

@app.route('/api/categories', methods=['GET'])
def get_categories():
    """
    Get list of available categories from CSV files
    """
    try:
        categories = []
        csv_files = glob.glob(os.path.join(CSV_DATA_DIR, '*.csv'))

        # Filter out competitor files (those starting with EE_)
        category_files = [f for f in csv_files if not os.path.basename(f).startswith('EE_')]

        for filepath in category_files:
            filename = os.path.basename(filepath)
            category_name, date_str = parse_category_filename(filename)

            if category_name:
                categories.append({
                    'name': category_name,
                    'filename': filename,
                    'date': date_str,
                    'filepath': filepath
                })

        # Remove duplicates and sort by date (latest first)
        unique_categories = {}
        for cat in categories:
            name = cat['name']
            if name not in unique_categories or cat['date'] > unique_categories[name]['date']:
                unique_categories[name] = cat

        sorted_categories = sorted(unique_categories.values(), key=lambda x: x['date'], reverse=True)

        return jsonify({
            'success': True,
            'categories': [cat['name'] for cat in sorted_categories],
            'details': sorted_categories
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/categories/<category_name>/data', methods=['GET'])
def get_category_data(category_name):
    """
    Get data for a specific category with comparison if available
    """
    try:
        # Get latest and previous files
        latest_file, previous_file = get_latest_and_previous_category_files(category_name)

        if not latest_file:
            return jsonify({
                'success': False,
                'error': f'No data found for category: {category_name}'
            }), 404

        # Use comparison logic if previous file exists
        if previous_file:
            print(f"Using comparison mode for {category_name}")
            data = compare_category_data(latest_file, previous_file)
            analysis_type = 'comparison'
            previous_filename = os.path.basename(previous_file)
        else:
            print(f"Using single file mode for {category_name}")
            data = process_category_data(latest_file)
            analysis_type = 'single_file'
            previous_filename = None

        # Calculate statistics
        stats = {
            'total_items': len(data),
            'improved': len([item for item in data if item.get('Status') == 'Up']),
            'declined': len([item for item in data if item.get('Status') == 'Down']),
            'unchanged': len([item for item in data if item.get('Status') == 'Same']),
            'new_items': len([item for item in data if item.get('Status') == 'New'])
        }

        return jsonify({
            'success': True,
            'category': category_name,
            'data': data,
            'file': os.path.basename(latest_file),
            'previous_file': previous_filename,
            'count': len(data),
            'analysis_type': analysis_type,
            'statistics': stats
        })

    except Exception as e:
        print(f"Error in get_category_data: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/categories/<category_name>/comparison', methods=['GET'])
def get_category_comparison(category_name):
    """
    Get detailed comparison data for a specific category
    """
    try:
        # Get latest and previous files
        latest_file, previous_file = get_latest_and_previous_category_files(category_name)

        if not latest_file:
            return jsonify({
                'success': False,
                'error': f'No data found for category: {category_name}'
            }), 404

        if not previous_file:
            return jsonify({
                'success': False,
                'error': f'No previous data found for comparison in category: {category_name}'
            }), 404

        # Perform comparison
        comparison_data = compare_category_data(latest_file, previous_file)

        # Calculate detailed statistics
        stats = {
            'total_items': len(comparison_data),
            'improved': len([item for item in comparison_data if item.get('Status') == 'Up']),
            'declined': len([item for item in comparison_data if item.get('Status') == 'Down']),
            'unchanged': len([item for item in comparison_data if item.get('Status') == 'Same']),
            'new_items': len([item for item in comparison_data if item.get('Status') == 'New']),
            'avg_change': sum([item.get('Change', 0) for item in comparison_data]) / len(comparison_data) if comparison_data else 0,
            'max_improvement': max([item.get('Change', 0) for item in comparison_data if item.get('Change', 0) > 0], default=0),
            'max_decline': min([item.get('Change', 0) for item in comparison_data if item.get('Change', 0) < 0], default=0)
        }

        # Get top movers
        top_improvements = sorted([item for item in comparison_data if item.get('Change', 0) > 0],
                                key=lambda x: x.get('Change', 0), reverse=True)[:10]
        top_declines = sorted([item for item in comparison_data if item.get('Change', 0) < 0],
                            key=lambda x: x.get('Change', 0))[:10]
        new_items = [item for item in comparison_data if item.get('Status') == 'New']

        return jsonify({
            'success': True,
            'category': category_name,
            'data': comparison_data,
            'latest_file': os.path.basename(latest_file),
            'previous_file': os.path.basename(previous_file),
            'count': len(comparison_data),
            'statistics': stats,
            'top_improvements': top_improvements,
            'top_declines': top_declines,
            'new_items': new_items,
            'analysis_type': 'detailed_comparison'
        })

    except Exception as e:
        print(f"Error in get_category_comparison: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/categories/<category_name>/history', methods=['GET'])
def get_category_history(category_name):
    """
    Get historical data for a category (all available dates)
    """
    try:
        file_dates = get_category_files_by_date(category_name)

        if not file_dates:
            return jsonify({
                'success': False,
                'error': f'No data found for category: {category_name}'
            }), 404

        history = []
        for filepath, date_obj, date_str in file_dates:
            try:
                # Get basic stats for each file
                df = pd.read_csv(filepath)
                df = standardize_category_dataframe(df)

                history.append({
                    'date': date_str,
                    'date_formatted': date_obj.strftime('%Y-%m-%d'),
                    'filename': os.path.basename(filepath),
                    'total_items': len(df),
                    'unique_authors': df['Author'].nunique(),
                    'file_size': os.path.getsize(filepath)
                })
            except Exception as e:
                print(f"Error processing historical file {filepath}: {e}")
                continue

        return jsonify({
            'success': True,
            'category': category_name,
            'history': history,
            'total_files': len(history)
        })

    except Exception as e:
        print(f"Error in get_category_history: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """
    Health check endpoint
    """
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'data_dir': CSV_DATA_DIR,
        'upload_dir': UPLOAD_DIR
    })

def format_uptime(seconds):
    """Format uptime seconds to human readable string"""
    days = int(seconds // 86400)
    hours = int((seconds % 86400) // 3600)
    minutes = int((seconds % 3600) // 60)

    if days > 0:
        return f"{days} days, {hours} hours"
    elif hours > 0:
        return f"{hours} hours, {minutes} minutes"
    else:
        return f"{minutes} minutes"

def format_file_size(bytes_size):
    """Format file size to human readable string"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_size < 1024.0:
            return f"{bytes_size:.1f} {unit}"
        bytes_size /= 1024.0
    return f"{bytes_size:.1f} TB"

def get_last_backup_time():
    """Get last backup timestamp"""
    backup_dir = os.path.join(os.path.dirname(__file__), 'backups')
    if not os.path.exists(backup_dir):
        return None

    backup_files = glob.glob(os.path.join(backup_dir, 'backup_*.zip'))
    if not backup_files:
        return None

    latest_backup = max(backup_files, key=os.path.getctime)
    backup_time = datetime.fromtimestamp(os.path.getctime(latest_backup))
    return backup_time.strftime('%Y-%m-%d %H:%M')

def get_system_status(cpu_percent, memory_percent):
    """Determine system status based on resource usage"""
    if cpu_percent > 90 or memory_percent > 90:
        return 'error'
    elif cpu_percent > 70 or memory_percent > 70:
        return 'warning'
    else:
        return 'healthy'

@app.route('/api/system/metrics', methods=['GET'])
@require_auth if AUTH_AVAILABLE else lambda f: f
def get_system_metrics():
    """Get comprehensive system metrics"""
    try:
        import time

        # Calculate real metrics
        csv_files = glob.glob(os.path.join(CSV_DATA_DIR, '*.csv'))
        total_size = sum(os.path.getsize(f) for f in csv_files if os.path.exists(f))

        # Get system uptime (process start time) - fallback without psutil
        try:
            import psutil
            process = psutil.Process()
            uptime_seconds = time.time() - process.create_time()
        except ImportError:
            # Fallback: use a simple uptime calculation
            uptime_seconds = time.time() - **********  # Approximate start time
        except:
            uptime_seconds = 0

        # Memory and CPU usage - with psutil fallback
        try:
            import psutil
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=0.01)
        except ImportError:
            # Fallback without psutil
            memory = type('obj', (object,), {
                'used': 1024 * 1024 * 1024,  # 1GB mock
                'total': 8 * 1024 * 1024 * 1024,  # 8GB mock
                'percent': 12.5  # Mock percentage
            })()
            cpu_percent = 15.0  # Mock CPU usage
        except:
            memory = type('obj', (object,), {'used': 0, 'total': 0, 'percent': 0})()
            cpu_percent = 0

        # Database stats
        notification_count = 0
        try:
            conn = sqlite3.connect(NOTIFICATION_DB)
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM notifications')
            notification_count = cursor.fetchone()[0]
            conn.close()
        except:
            pass

        # Determine system status
        status = get_system_status(cpu_percent, memory.percent)

        return jsonify({
            'status': status,
            'version': '1.0.0',
            'uptime': format_uptime(uptime_seconds),
            'dataFiles': len(csv_files),
            'totalSize': format_file_size(total_size),
            'lastBackup': get_last_backup_time(),
            'memory': {
                'used': memory.used,
                'total': memory.total,
                'percent': round(memory.percent, 1)
            },
            'cpu': {
                'percent': round(cpu_percent, 1)
            },
            'notifications': {
                'total': notification_count
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'version': '1.0.0',
            'uptime': 'Unknown',
            'dataFiles': 0,
            'totalSize': '0 B',
            'lastBackup': None,
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/system/backup', methods=['POST'])
@admin_required if AUTH_AVAILABLE else lambda f: f
def create_backup():
    """Create system backup"""
    backup_file = None
    try:
        import zipfile
        import gc  # Garbage collection

        # Force garbage collection before starting
        gc.collect()

        backup_dir = os.path.join(os.path.dirname(__file__), 'backups')
        os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = os.path.join(backup_dir, f'backup_{timestamp}.zip')

        # Get CSV files list first
        csv_files = glob.glob(os.path.join(CSV_DATA_DIR, '*.csv'))
        csv_files = [f for f in csv_files if os.path.exists(f)]  # Filter existing files

        # Check if we have files to backup
        if not csv_files and not os.path.exists(NOTIFICATION_DB):
            return jsonify({
                'success': False,
                'error': 'No files found to backup'
            }), 400

        # Create backup with proper resource management
        try:
            with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
                # Backup CSV files with error handling
                for csv_file in csv_files:
                    try:
                        arcname = f"data/{os.path.basename(csv_file)}"
                        zipf.write(csv_file, arcname)
                    except Exception as e:
                        print(f"Warning: Failed to backup {csv_file}: {str(e)}")
                        continue

                # Backup database with error handling
                if os.path.exists(NOTIFICATION_DB):
                    try:
                        zipf.write(NOTIFICATION_DB, 'notifications.db')
                    except Exception as e:
                        print(f"Warning: Failed to backup database: {str(e)}")

                # Create backup manifest
                manifest = {
                    'timestamp': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'files_count': len(csv_files),
                    'includes': ['csv_data', 'notifications_db']
                }

                import json
                zipf.writestr('manifest.json', json.dumps(manifest, indent=2))

        except Exception as zip_error:
            # Clean up partial backup file
            if backup_file and os.path.exists(backup_file):
                try:
                    os.remove(backup_file)
                except:
                    pass
            raise zip_error

        # Verify backup file was created successfully
        if not os.path.exists(backup_file):
            raise Exception("Backup file was not created successfully")

        backup_size = os.path.getsize(backup_file)

        # Create notification for backup completion (with error handling)
        try:
            create_notification(
                type_name='system',
                title='Backup Created',
                message=f'System backup completed successfully. Size: {format_file_size(backup_size)}',
                priority='medium',
                category='backup',
                source='system',
                metadata={
                    'backup_file': os.path.basename(backup_file),
                    'size': backup_size,
                    'files_count': len(csv_files)
                }
            )
        except Exception as notif_error:
            print(f"Warning: Failed to create notification: {str(notif_error)}")
            # Don't fail the backup if notification fails

        # Force garbage collection after backup
        gc.collect()

        return jsonify({
            'success': True,
            'backup_file': os.path.basename(backup_file),
            'timestamp': timestamp,
            'size': backup_size,
            'size_formatted': format_file_size(backup_size),
            'files_count': len(csv_files)
        })
    except Exception as e:
        # Create notification for backup failure
        create_notification(
            type_name='error',
            title='Backup Failed',
            message=f'System backup failed: {str(e)}',
            priority='high',
            category='backup',
            source='system',
            metadata={'error': str(e)}
        )

        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/system/backups', methods=['GET'])
def list_backups():
    """Get list of all available backups"""
    try:
        backup_dir = os.path.join(os.path.dirname(__file__), 'backups')
        if not os.path.exists(backup_dir):
            return jsonify({
                'success': True,
                'backups': []
            })

        backup_files = glob.glob(os.path.join(backup_dir, 'backup_*.zip'))
        backups = []

        for backup_file in backup_files:
            filename = os.path.basename(backup_file)
            file_stat = os.stat(backup_file)

            # Extract timestamp from filename
            timestamp_str = filename.replace('backup_', '').replace('.zip', '')
            try:
                backup_time = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                formatted_time = backup_time.strftime('%Y-%m-%d %H:%M:%S')
            except:
                formatted_time = 'Unknown'

            # Try to read manifest from backup
            manifest_info = {}
            try:
                import zipfile
                with zipfile.ZipFile(backup_file, 'r') as zipf:
                    if 'manifest.json' in zipf.namelist():
                        manifest_data = zipf.read('manifest.json')
                        manifest_info = json.loads(manifest_data.decode('utf-8'))
            except:
                pass

            backups.append({
                'filename': filename,
                'size': file_stat.st_size,
                'size_formatted': format_file_size(file_stat.st_size),
                'created_at': formatted_time,
                'timestamp': timestamp_str,
                'files_count': manifest_info.get('files_count', 0),
                'version': manifest_info.get('version', 'Unknown'),
                'includes': manifest_info.get('includes', [])
            })

        # Sort by creation time (newest first)
        backups.sort(key=lambda x: x['timestamp'], reverse=True)

        return jsonify({
            'success': True,
            'backups': backups,
            'total_count': len(backups),
            'total_size': sum(b['size'] for b in backups),
            'total_size_formatted': format_file_size(sum(b['size'] for b in backups))
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/system/backups/<backup_filename>/download', methods=['GET'])
def download_backup(backup_filename):
    """Download a specific backup file"""
    try:
        # Validate filename to prevent directory traversal
        if not backup_filename.startswith('backup_') or not backup_filename.endswith('.zip'):
            return jsonify({
                'success': False,
                'error': 'Invalid backup filename'
            }), 400

        backup_dir = os.path.join(os.path.dirname(__file__), 'backups')
        backup_file = os.path.join(backup_dir, backup_filename)

        if not os.path.exists(backup_file):
            return jsonify({
                'success': False,
                'error': 'Backup file not found'
            }), 404

        return send_from_directory(
            backup_dir,
            backup_filename,
            as_attachment=True,
            download_name=backup_filename
        )

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/system/backups/<backup_filename>', methods=['DELETE'])
def delete_backup(backup_filename):
    """Delete a specific backup file"""
    try:
        # Validate filename to prevent directory traversal
        if not backup_filename.startswith('backup_') or not backup_filename.endswith('.zip'):
            return jsonify({
                'success': False,
                'error': 'Invalid backup filename'
            }), 400

        backup_dir = os.path.join(os.path.dirname(__file__), 'backups')
        backup_file = os.path.join(backup_dir, backup_filename)

        if not os.path.exists(backup_file):
            return jsonify({
                'success': False,
                'error': 'Backup file not found'
            }), 404

        # Get file size before deletion for notification
        file_size = os.path.getsize(backup_file)

        # Delete the backup file with proper error handling
        try:
            os.remove(backup_file)
        except PermissionError:
            raise Exception(f"Permission denied: Cannot delete {backup_filename}. File may be in use.")
        except OSError as e:
            raise Exception(f"Failed to delete file: {str(e)}")

        # Verify file was actually deleted
        if os.path.exists(backup_file):
            raise Exception("File deletion failed: File still exists after deletion attempt")

        # Create notification for backup deletion (with error handling)
        try:
            create_notification(
                type_name='system',
                title='Backup Deleted',
                message=f'Backup file {backup_filename} has been deleted. Size freed: {format_file_size(file_size)}',
                priority='low',
                category='backup',
                source='system',
                metadata={
                    'backup_file': backup_filename,
                    'size_freed': file_size
                }
            )
        except Exception as notif_error:
            print(f"Warning: Failed to create deletion notification: {str(notif_error)}")
            # Don't fail the deletion if notification fails

        return jsonify({
            'success': True,
            'message': f'Backup {backup_filename} deleted successfully',
            'size_freed': file_size,
            'size_freed_formatted': format_file_size(file_size)
        })

    except Exception as e:
        # Create notification for backup deletion failure
        create_notification(
            type_name='error',
            title='Backup Deletion Failed',
            message=f'Failed to delete backup {backup_filename}: {str(e)}',
            priority='medium',
            category='backup',
            source='system',
            metadata={'error': str(e), 'backup_file': backup_filename}
        )

        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/system/backups/<backup_filename>/validate', methods=['GET'])
def validate_backup(backup_filename):
    """Validate backup file integrity"""
    try:
        # Validate filename to prevent directory traversal
        if not backup_filename.startswith('backup_') or not backup_filename.endswith('.zip'):
            return jsonify({
                'success': False,
                'error': 'Invalid backup filename'
            }), 400

        backup_dir = os.path.join(os.path.dirname(__file__), 'backups')
        backup_file = os.path.join(backup_dir, backup_filename)

        if not os.path.exists(backup_file):
            return jsonify({
                'success': False,
                'error': 'Backup file not found'
            }), 404

        import zipfile
        validation_results = {
            'is_valid': False,
            'has_manifest': False,
            'has_data_files': False,
            'has_database': False,
            'file_count': 0,
            'manifest_info': {},
            'errors': []
        }

        try:
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                # Test if ZIP file is valid
                zipf.testzip()
                validation_results['is_valid'] = True

                file_list = zipf.namelist()
                validation_results['file_count'] = len(file_list)

                # Check for manifest
                if 'manifest.json' in file_list:
                    validation_results['has_manifest'] = True
                    try:
                        manifest_data = zipf.read('manifest.json')
                        validation_results['manifest_info'] = json.loads(manifest_data.decode('utf-8'))
                    except:
                        validation_results['errors'].append('Manifest file is corrupted')

                # Check for data files
                data_files = [f for f in file_list if f.startswith('data/') and f.endswith('.csv')]
                if data_files:
                    validation_results['has_data_files'] = True

                # Check for database
                if 'notifications.db' in file_list:
                    validation_results['has_database'] = True

        except zipfile.BadZipFile:
            validation_results['errors'].append('Invalid ZIP file format')
        except Exception as e:
            validation_results['errors'].append(f'Validation error: {str(e)}')

        # Overall validation status
        validation_results['overall_status'] = (
            validation_results['is_valid'] and
            validation_results['has_manifest'] and
            len(validation_results['errors']) == 0
        )

        return jsonify({
            'success': True,
            'validation': validation_results
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/system/cache/clear', methods=['DELETE'])
def clear_cache():
    """Clear system cache and temporary files"""
    try:
        cleared_items = []
        total_size_cleared = 0

        # Clear temporary files
        temp_dir = os.path.join(os.path.dirname(__file__), 'temp')
        if os.path.exists(temp_dir):
            for file in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, file)
                if os.path.isfile(file_path):
                    file_size = os.path.getsize(file_path)
                    os.remove(file_path)
                    cleared_items.append(f'temp/{file}')
                    total_size_cleared += file_size

        # Clear old backup files (keep last 5)
        backup_dir = os.path.join(os.path.dirname(__file__), 'backups')
        if os.path.exists(backup_dir):
            backups = sorted(glob.glob(os.path.join(backup_dir, 'backup_*.zip')))
            for old_backup in backups[:-5]:  # Keep last 5
                if os.path.exists(old_backup):
                    file_size = os.path.getsize(old_backup)
                    os.remove(old_backup)
                    cleared_items.append(f'backup/{os.path.basename(old_backup)}')
                    total_size_cleared += file_size

        # Clear old log files if any
        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
        if os.path.exists(log_dir):
            log_files = glob.glob(os.path.join(log_dir, '*.log'))
            for log_file in log_files:
                # Keep logs from last 7 days
                if os.path.getctime(log_file) < time.time() - (7 * 24 * 3600):
                    file_size = os.path.getsize(log_file)
                    os.remove(log_file)
                    cleared_items.append(f'logs/{os.path.basename(log_file)}')
                    total_size_cleared += file_size

        # Create notification for cache clear completion
        create_notification(
            type_name='system',
            title='Cache Cleared',
            message=f'System cache cleared successfully. {len(cleared_items)} items removed, {format_file_size(total_size_cleared)} freed.',
            priority='low',
            category='maintenance',
            source='system',
            metadata={
                'items_count': len(cleared_items),
                'size_cleared': total_size_cleared
            }
        )

        return jsonify({
            'success': True,
            'cleared_items': cleared_items,
            'count': len(cleared_items),
            'size_cleared': total_size_cleared,
            'size_cleared_formatted': format_file_size(total_size_cleared)
        })
    except Exception as e:
        # Create notification for cache clear failure
        create_notification(
            type_name='error',
            title='Cache Clear Failed',
            message=f'Failed to clear system cache: {str(e)}',
            priority='medium',
            category='maintenance',
            source='system',
            metadata={'error': str(e)}
        )

        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """
    Upload CSV file endpoint with validation and automatic indexing
    """
    try:
        # Check if file is present in request
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400

        file = request.files['file']

        # Check if file is selected
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400

        # Check file extension
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': 'Only CSV files are allowed'
            }), 400

        # Check file size
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > MAX_FILE_SIZE:
            return jsonify({
                'success': False,
                'error': f'File size exceeds maximum limit of {MAX_FILE_SIZE // (1024*1024)}MB'
            }), 400

        # Secure filename
        filename = secure_filename(file.filename)

        # Save to upload directory first for validation
        temp_filepath = os.path.join(UPLOAD_DIR, filename)
        file.save(temp_filepath)

        # Validate CSV structure
        is_valid, validation_result = validate_csv_structure(temp_filepath)

        if not is_valid:
            # Remove invalid file
            os.remove(temp_filepath)
            return jsonify({
                'success': False,
                'error': f'Invalid CSV structure: {validation_result}'
            }), 400

        # Move validated file to data directory
        final_filepath = os.path.join(CSV_DATA_DIR, filename)

        # Check if file already exists and handle accordingly
        if os.path.exists(final_filepath):
            # Create backup of existing file
            backup_filepath = final_filepath + f'.backup_{int(datetime.now().timestamp())}'
            shutil.move(final_filepath, backup_filepath)
            print(f"Existing file backed up to: {backup_filepath}")

        # Move new file to data directory
        shutil.move(temp_filepath, final_filepath)

        # Get file stats
        file_stat = os.stat(final_filepath)

        # Create notification for successful file upload
        notify_file_upload_result(
            filename=filename,
            success=True
        )

        return jsonify({
            'success': True,
            'message': 'File uploaded and indexed successfully',
            'file': {
                'name': filename,
                'type': validation_result['type'],
                'size': f"{file_stat.st_size / 1024 / 1024:.1f} MB",
                'uploadDate': datetime.fromtimestamp(file_stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                'metadata': validation_result
            }
        })

    except Exception as e:
        # Clean up temp file if it exists
        temp_filepath = os.path.join(UPLOAD_DIR, secure_filename(request.files['file'].filename))
        if os.path.exists(temp_filepath):
            os.remove(temp_filepath)

        # Create notification for failed file upload
        filename = request.files['file'].filename if 'file' in request.files else 'Unknown file'
        notify_file_upload_result(
            filename=filename,
            success=False,
            error=str(e)
        )

        return jsonify({
            'success': False,
            'error': f'Upload failed: {str(e)}'
        }), 500

@app.route('/api/upload/validate', methods=['POST'])
def validate_upload():
    """
    Validate CSV file without saving it
    """
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400

        file = request.files['file']

        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400

        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': 'Only CSV files are allowed'
            }), 400

        # Save temporarily for validation
        filename = secure_filename(file.filename)
        temp_filepath = os.path.join(UPLOAD_DIR, f'temp_{filename}')
        file.save(temp_filepath)

        # Validate structure
        is_valid, validation_result = validate_csv_structure(temp_filepath)

        # Clean up temp file
        os.remove(temp_filepath)

        if is_valid:
            return jsonify({
                'success': True,
                'valid': True,
                'metadata': validation_result
            })
        else:
            return jsonify({
                'success': True,
                'valid': False,
                'error': validation_result
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Validation failed: {str(e)}'
        }), 500

@app.route('/api/authors/top', methods=['GET'])
def get_top_authors():
    """
    Get top authors by frequency across all CSV files
    Adapted from your Python script for frequency calculation
    """
    try:
        limit = int(request.args.get('limit', 20))  # Default top 20
        analysis_type = request.args.get('type', 'frequency')  # 'frequency' or 'points'

        # Process all CSV files in the data directory
        csv_files = glob.glob(os.path.join(CSV_DATA_DIR, '*.csv'))

        if not csv_files:
            return jsonify({
                'success': False,
                'error': 'No CSV files found'
            }), 404

        # Combine all CSV files into one DataFrame (like your script)
        all_dataframes = []

        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path)
                df = standardize_dataframe(df)
                all_dataframes.append(df)
            except Exception as e:
                print(f"Error processing file {file_path}: {str(e)}")
                continue

        if not all_dataframes:
            return jsonify({
                'success': False,
                'error': 'No valid CSV data found'
            }), 404

        # Combine all dataframes (like your cleaned_envato_data.csv)
        combined_df = pd.concat(all_dataframes, ignore_index=True)

        if analysis_type == 'frequency':
            # Step 3 from your script: Calculate frequency of each author
            author_counts = combined_df['Author'].value_counts().reset_index()
            author_counts.columns = ['Author', 'Count']

            # Select top N authors
            top_authors_df = author_counts.head(limit)

            # Format response
            authors_data = [
                {
                    'author': row['Author'],
                    'count': int(row['Count']),
                    'rank': idx + 1
                }
                for idx, (_, row) in enumerate(top_authors_df.iterrows())
                if pd.notna(row['Author']) and row['Author'] != 'Unknown Author'
            ]

        elif analysis_type == 'points':
            # Step 4 from your script: Calculate points for each author
            # After standardization, web-scraper-start-url becomes 'Page New'
            page_column = 'Page New'

            # Check if required columns exist
            if page_column not in combined_df.columns:
                return jsonify({
                    'success': False,
                    'error': f'Column {page_column} not found in CSV data'
                }), 400

            # Convert Page New to numeric for calculations (already done in standardize_dataframe)
            # Remove rows where Page New is 0 or NaN
            combined_df = combined_df[combined_df[page_column] > 0]

            if combined_df.empty:
                return jsonify({
                    'success': False,
                    'error': 'No valid page data found for points calculation'
                }), 400

            # Find the maximum page number
            max_page = combined_df[page_column].max()

            # Calculate points for each item: points = max_page - page_number + 1
            combined_df['points'] = max_page - combined_df[page_column] + 1

            # Calculate total points per author
            author_points = combined_df.groupby('Author')['points'].sum().reset_index()
            author_points.columns = ['Author', 'Total_Points']

            # Sort by total points in descending order
            author_points = author_points.sort_values(by='Total_Points', ascending=False)

            # Select top N authors
            top_authors_df = author_points.head(limit)

            # Format response
            authors_data = [
                {
                    'author': row['Author'],
                    'count': int(row['Total_Points']),
                    'rank': idx + 1,
                    'points': int(row['Total_Points'])
                }
                for idx, (_, row) in enumerate(top_authors_df.iterrows())
                if pd.notna(row['Author']) and row['Author'] != 'Unknown Author'
            ]

        # Create notification for analysis completion
        notify_analysis_complete(
            analysis_type=f'Top Authors ({analysis_type})',
            competitor='All Competitors',
            duration=0,  # Could be calculated if needed
            result_count=len(authors_data)
        )

        return jsonify({
            'success': True,
            'data': authors_data,
            'total_authors': len(combined_df['Author'].unique()),
            'total_files_processed': len(csv_files),
            'total_items': len(combined_df),
            'analysis_type': analysis_type,
            'limit': limit
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error processing author data: {str(e)}'
        }), 500

@app.route('/api/authors/detailed', methods=['GET'])
def get_authors_detailed():
    """
    Get detailed author analysis with page distribution
    Adapted from your Python script for pivot table analysis
    """
    try:
        limit = int(request.args.get('limit', 20))  # Default top 20

        # Process all CSV files in the data directory
        csv_files = glob.glob(os.path.join(CSV_DATA_DIR, '*.csv'))

        if not csv_files:
            return jsonify({
                'success': False,
                'error': 'No CSV files found'
            }), 404

        # Combine all CSV files into one DataFrame
        all_dataframes = []

        for file_path in csv_files:
            try:
                df = pd.read_csv(file_path)
                df = standardize_dataframe(df)
                all_dataframes.append(df)
            except Exception as e:
                print(f"Error processing file {file_path}: {str(e)}")
                continue

        if not all_dataframes:
            return jsonify({
                'success': False,
                'error': 'No valid CSV data found'
            }), 404

        # Combine all dataframes
        combined_df = pd.concat(all_dataframes, ignore_index=True)

        # After standardization, web-scraper-start-url becomes 'Page New'
        page_column = 'Page New'

        # Check if required columns exist
        if page_column not in combined_df.columns:
            return jsonify({
                'success': False,
                'error': f'Column {page_column} not found in CSV data'
            }), 400

        # Remove rows where Page New is 0 or NaN
        combined_df = combined_df[combined_df[page_column] > 0]

        if combined_df.empty:
            return jsonify({
                'success': False,
                'error': 'No valid page data found for detailed analysis'
            }), 400

        # Find the maximum page number
        max_page = combined_df[page_column].max()

        # Calculate points for each item: points = max_page - page_number + 1
        combined_df['points'] = max_page - combined_df[page_column] + 1

        # Create a pivot table to count items per author per page
        pivot_table = combined_df.pivot_table(
            index='Author',
            columns=page_column,
            aggfunc='size',
            fill_value=0
        )

        # Calculate total points per author
        author_points = combined_df.groupby('Author')['points'].sum().reset_index()
        author_points.columns = ['Author', 'Total_Points']

        # Merge pivot table with total points
        pivot_reset = pivot_table.reset_index()
        author_points_table = pivot_reset.merge(author_points, on='Author')

        # Sort by total points in descending order
        author_points_table = author_points_table.sort_values(by='Total_Points', ascending=False)

        # Select top N authors
        top_authors_detailed = author_points_table.head(limit)

        # Format response with page distribution
        authors_data = []
        for idx, (_, row) in enumerate(top_authors_detailed.iterrows()):
            if pd.notna(row['Author']) and row['Author'] != 'Unknown Author':
                author_data = {
                    'author': row['Author'],
                    'total_points': int(row['Total_Points']),
                    'rank': idx + 1,
                    'page_distribution': {}
                }

                # Add page distribution data
                for col in pivot_table.columns:
                    if col != 'Author' and col != 'Total_Points':
                        page_num = int(col)
                        count = int(row[col]) if col in row and pd.notna(row[col]) else 0
                        if count > 0:  # Only include pages with items
                            author_data['page_distribution'][f'page_{page_num}'] = count

                authors_data.append(author_data)

        return jsonify({
            'success': True,
            'data': authors_data,
            'total_authors': len(combined_df['Author'].unique()),
            'total_files_processed': len(csv_files),
            'total_items': len(combined_df),
            'max_page': int(max_page) if pd.notna(max_page) else 0,
            'limit': limit
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error processing detailed author data: {str(e)}'
        }), 500

# ============================================================================
# NOTIFICATION API ENDPOINTS
# ============================================================================

@app.route('/api/notifications', methods=['GET'])
def get_notifications_api():
    """Get notifications with optional filtering and pagination"""
    try:
        filters = {
            'type': request.args.get('type'),
            'priority': request.args.get('priority'),
            'status': request.args.get('status'),
            'category': request.args.get('category'),
            'search': request.args.get('search'),
            'startDate': request.args.get('startDate'),
            'endDate': request.args.get('endDate')
        }

        # Remove None values
        filters = {k: v for k, v in filters.items() if v is not None}

        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))

        result = get_notifications(filters, page, limit)

        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications', methods=['POST'])
def create_notification_api():
    """Create a new notification"""
    try:
        data = request.get_json()

        if not data or not data.get('title') or not data.get('message'):
            return jsonify({
                'success': False,
                'error': 'Title and message are required'
            }), 400

        notification = create_notification(
            type_name=data.get('type', 'info'),
            title=data['title'],
            message=data['message'],
            priority=data.get('priority', 'medium'),
            category=data.get('category'),
            source=data.get('source'),
            metadata=data.get('metadata'),
            persistent=data.get('persistent', False),
            dismissible=data.get('dismissible', True),
            expires_at=data.get('expiresAt')
        )

        if notification:
            return jsonify({
                'success': True,
                'data': notification
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to create notification'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/<notification_id>/read', methods=['PATCH'])
def mark_notification_read(notification_id):
    """Mark a notification as read"""
    try:
        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE notifications
            SET status = 'read', read_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (notification_id,))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': 'Notification not found'
            }), 404

        conn.commit()
        conn.close()

        # Send real-time update
        send_notification_event('notification_updated', {
            'id': notification_id,
            'status': 'read',
            'readAt': datetime.now().isoformat()
        })

        return jsonify({
            'success': True,
            'message': 'Notification marked as read'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/read-all', methods=['PATCH'])
def mark_all_notifications_read():
    """Mark all notifications as read"""
    try:
        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE notifications
            SET status = 'read', read_at = CURRENT_TIMESTAMP
            WHERE status = 'unread'
        ''')

        updated_count = cursor.rowcount
        conn.commit()
        conn.close()

        # Send real-time update
        send_notification_event('notifications_bulk_updated', {
            'action': 'mark_all_read',
            'count': updated_count
        })

        return jsonify({
            'success': True,
            'message': f'{updated_count} notifications marked as read'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/<notification_id>', methods=['DELETE'])
def delete_notification(notification_id):
    """Delete a notification"""
    try:
        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        cursor.execute('DELETE FROM notifications WHERE id = ?', (notification_id,))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': 'Notification not found'
            }), 404

        conn.commit()
        conn.close()

        # Send real-time update
        send_notification_event('notification_deleted', {'id': notification_id})

        return jsonify({
            'success': True,
            'message': 'Notification deleted'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/<notification_id>/archive', methods=['PATCH'])
def archive_notification(notification_id):
    """Archive a notification"""
    try:
        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE notifications
            SET status = 'archived'
            WHERE id = ?
        ''', (notification_id,))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': 'Notification not found'
            }), 404

        conn.commit()
        conn.close()

        # Send real-time update
        send_notification_event('notification_updated', {
            'id': notification_id,
            'status': 'archived'
        })

        return jsonify({
            'success': True,
            'message': 'Notification archived'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/stats', methods=['GET'])
def get_notification_stats():
    """Get notification statistics"""
    try:
        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        # Get total and unread counts
        cursor.execute('SELECT COUNT(*) FROM notifications')
        total = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM notifications WHERE status = "unread"')
        unread = cursor.fetchone()[0]

        # Get counts by type
        cursor.execute('SELECT type, COUNT(*) FROM notifications GROUP BY type')
        by_type = dict(cursor.fetchall())

        # Get counts by priority
        cursor.execute('SELECT priority, COUNT(*) FROM notifications GROUP BY priority')
        by_priority = dict(cursor.fetchall())

        # Get today's count
        cursor.execute('''
            SELECT COUNT(*) FROM notifications
            WHERE DATE(created_at) = DATE('now')
        ''')
        today_count = cursor.fetchone()[0]

        # Get this week's count
        cursor.execute('''
            SELECT COUNT(*) FROM notifications
            WHERE created_at >= DATE('now', '-7 days')
        ''')
        week_count = cursor.fetchone()[0]

        conn.close()

        # Fill in missing types and priorities with 0
        all_types = ['success', 'error', 'warning', 'info', 'data_update', 'system', 'file_upload', 'analysis_complete']
        all_priorities = ['low', 'medium', 'high', 'urgent']

        for type_name in all_types:
            if type_name not in by_type:
                by_type[type_name] = 0

        for priority in all_priorities:
            if priority not in by_priority:
                by_priority[priority] = 0

        stats = {
            'total': total,
            'unread': unread,
            'byType': by_type,
            'byPriority': by_priority,
            'todayCount': today_count,
            'weekCount': week_count
        }

        return jsonify({
            'success': True,
            'data': stats
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/settings', methods=['GET'])
def get_notification_settings():
    """Get notification settings"""
    try:
        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM notification_settings ORDER BY id DESC LIMIT 1')
        row = cursor.fetchone()
        conn.close()

        if not row:
            return jsonify({
                'success': False,
                'error': 'Settings not found'
            }), 404

        settings = {
            'enabled': bool(row[1]),
            'email': bool(row[2]),
            'browser': bool(row[3]),
            'sound': bool(row[4]),
            'desktop': bool(row[5]),
            'dataUpdates': bool(row[6]),
            'systemAlerts': bool(row[7]),
            'analysisComplete': bool(row[8]),
            'fileOperations': bool(row[9]),
            'priorities': {
                'low': bool(row[10]),
                'medium': bool(row[11]),
                'high': bool(row[12]),
                'urgent': bool(row[13])
            },
            'quietHours': {
                'enabled': bool(row[14]),
                'start': row[15],
                'end': row[16]
            }
        }

        return jsonify({
            'success': True,
            'data': settings
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/settings', methods=['PATCH'])
def update_notification_settings():
    """Update notification settings"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400

        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        # Build update query dynamically
        updates = []
        params = []

        if 'enabled' in data:
            updates.append('enabled = ?')
            params.append(data['enabled'])

        if 'email' in data:
            updates.append('email = ?')
            params.append(data['email'])

        if 'browser' in data:
            updates.append('browser = ?')
            params.append(data['browser'])

        if 'sound' in data:
            updates.append('sound = ?')
            params.append(data['sound'])

        if 'desktop' in data:
            updates.append('desktop = ?')
            params.append(data['desktop'])

        if 'dataUpdates' in data:
            updates.append('data_updates = ?')
            params.append(data['dataUpdates'])

        if 'systemAlerts' in data:
            updates.append('system_alerts = ?')
            params.append(data['systemAlerts'])

        if 'analysisComplete' in data:
            updates.append('analysis_complete = ?')
            params.append(data['analysisComplete'])

        if 'fileOperations' in data:
            updates.append('file_operations = ?')
            params.append(data['fileOperations'])

        if 'priorities' in data:
            priorities = data['priorities']
            if 'low' in priorities:
                updates.append('priority_low = ?')
                params.append(priorities['low'])
            if 'medium' in priorities:
                updates.append('priority_medium = ?')
                params.append(priorities['medium'])
            if 'high' in priorities:
                updates.append('priority_high = ?')
                params.append(priorities['high'])
            if 'urgent' in priorities:
                updates.append('priority_urgent = ?')
                params.append(priorities['urgent'])

        if 'quietHours' in data:
            quiet_hours = data['quietHours']
            if 'enabled' in quiet_hours:
                updates.append('quiet_hours_enabled = ?')
                params.append(quiet_hours['enabled'])
            if 'start' in quiet_hours:
                updates.append('quiet_hours_start = ?')
                params.append(quiet_hours['start'])
            if 'end' in quiet_hours:
                updates.append('quiet_hours_end = ?')
                params.append(quiet_hours['end'])

        if updates:
            updates.append('updated_at = CURRENT_TIMESTAMP')
            query = f"UPDATE notification_settings SET {', '.join(updates)}"
            cursor.execute(query, params)
            conn.commit()

        conn.close()

        # Send real-time update
        send_notification_event('settings_updated', data)

        return jsonify({
            'success': True,
            'message': 'Settings updated successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/bulk/read', methods=['PATCH'])
def bulk_mark_as_read():
    """Mark multiple notifications as read"""
    try:
        data = request.get_json()
        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'error': 'IDs array is required'
            }), 400

        ids = data['ids']
        if not ids:
            return jsonify({
                'success': False,
                'error': 'At least one ID is required'
            }), 400

        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        placeholders = ','.join(['?' for _ in ids])
        cursor.execute(f'''
            UPDATE notifications
            SET status = 'read', read_at = CURRENT_TIMESTAMP
            WHERE id IN ({placeholders})
        ''', ids)

        updated_count = cursor.rowcount
        conn.commit()
        conn.close()

        # Send real-time update
        send_notification_event('notifications_bulk_updated', {
            'action': 'bulk_read',
            'ids': ids,
            'count': updated_count
        })

        return jsonify({
            'success': True,
            'message': f'{updated_count} notifications marked as read'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/bulk/delete', methods=['DELETE'])
def bulk_delete_notifications():
    """Delete multiple notifications"""
    try:
        data = request.get_json()
        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'error': 'IDs array is required'
            }), 400

        ids = data['ids']
        if not ids:
            return jsonify({
                'success': False,
                'error': 'At least one ID is required'
            }), 400

        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        placeholders = ','.join(['?' for _ in ids])
        cursor.execute(f'DELETE FROM notifications WHERE id IN ({placeholders})', ids)

        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()

        # Send real-time update
        send_notification_event('notifications_bulk_updated', {
            'action': 'bulk_delete',
            'ids': ids,
            'count': deleted_count
        })

        return jsonify({
            'success': True,
            'message': f'{deleted_count} notifications deleted'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/bulk/archive', methods=['PATCH'])
def bulk_archive_notifications():
    """Archive multiple notifications"""
    try:
        data = request.get_json()
        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'error': 'IDs array is required'
            }), 400

        ids = data['ids']
        if not ids:
            return jsonify({
                'success': False,
                'error': 'At least one ID is required'
            }), 400

        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        placeholders = ','.join(['?' for _ in ids])
        cursor.execute(f'''
            UPDATE notifications
            SET status = 'archived'
            WHERE id IN ({placeholders})
        ''', ids)

        archived_count = cursor.rowcount
        conn.commit()
        conn.close()

        # Send real-time update
        send_notification_event('notifications_bulk_updated', {
            'action': 'bulk_archive',
            'ids': ids,
            'count': archived_count
        })

        return jsonify({
            'success': True,
            'message': f'{archived_count} notifications archived'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/events')
def notification_events():
    """Server-Sent Events endpoint for real-time notifications"""
    def event_stream():
        while True:
            try:
                # Get event from queue (blocking with timeout)
                event_data = notification_queue.get(timeout=30)
                yield format_sse_data(event_data)
            except:
                # Send heartbeat to keep connection alive
                yield format_sse_data({'type': 'heartbeat', 'timestamp': datetime.now().isoformat()})

    return Response(
        event_stream(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )

@app.route('/api/notifications/sample', methods=['POST'])
def create_sample_notifications():
    """Create sample notifications for testing"""
    try:
        sample_notifications = [
            {
                'type': 'data_update',
                'title': 'Data Processing Complete',
                'message': 'GraphicRiver data has been processed with 1,247 new records',
                'priority': 'medium',
                'category': 'processing',
                'source': 'data_processor',
                'metadata': {'competitor': 'GraphicRiver', 'recordCount': 1247, 'processingTime': 45}
            },
            {
                'type': 'analysis_complete',
                'title': 'Analysis Complete',
                'message': 'Competitor analysis for ThemeForest completed with 892 results',
                'priority': 'medium',
                'category': 'analysis',
                'source': 'analysis_engine',
                'metadata': {'analysisType': 'competitor', 'competitor': 'ThemeForest', 'resultCount': 892}
            },
            {
                'type': 'success',
                'title': 'File Upload Successful',
                'message': 'EE_GraphicRiver_2025-01-16.csv has been uploaded successfully',
                'priority': 'low',
                'category': 'upload',
                'source': 'file_manager',
                'metadata': {'filename': 'EE_GraphicRiver_2025-01-16.csv', 'fileSize': 2048576}
            },
            {
                'type': 'warning',
                'title': 'Data Quality Warning',
                'message': 'Some records in CodeCanyon data may have incomplete information',
                'priority': 'high',
                'category': 'validation',
                'source': 'data_validator',
                'metadata': {'competitor': 'CodeCanyon', 'issueCount': 23}
            },
            {
                'type': 'system',
                'title': 'System Maintenance',
                'message': 'Scheduled maintenance will begin at 2:00 AM UTC',
                'priority': 'medium',
                'category': 'maintenance',
                'source': 'system',
                'metadata': {'scheduledTime': '2025-01-17T02:00:00Z'}
            },
            {
                'type': 'error',
                'title': 'Processing Error',
                'message': 'Failed to process AudioJungle data due to format issues',
                'priority': 'urgent',
                'category': 'error',
                'source': 'data_processor',
                'metadata': {'competitor': 'AudioJungle', 'error': 'Invalid CSV format'}
            }
        ]

        created_notifications = []
        for notification_data in sample_notifications:
            notification = create_notification(**notification_data)
            if notification:
                created_notifications.append(notification)

        return jsonify({
            'success': True,
            'message': f'{len(created_notifications)} sample notifications created',
            'data': created_notifications
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/notifications/clear-all', methods=['DELETE'])
def clear_all_notifications():
    """Clear all notifications (for testing purposes)"""
    try:
        conn = sqlite3.connect(NOTIFICATION_DB)
        cursor = conn.cursor()

        cursor.execute('DELETE FROM notifications')
        deleted_count = cursor.rowcount

        conn.commit()
        conn.close()

        # Send real-time update
        send_notification_event('notifications_cleared', {
            'count': deleted_count
        })

        return jsonify({
            'success': True,
            'message': f'{deleted_count} notifications cleared'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Authentication system removed - no auth status endpoint needed

# Test routes - no authentication required

@app.route('/api/test/public', methods=['GET'])
def test_public_route():
    """Test route without protection"""
    return jsonify({
        'success': True,
        'message': 'This is a public route',
        'protection': 'none'
    })

if __name__ == '__main__':
    print(f"Starting CSV Data Processing API...")
    print(f"Data directory: {CSV_DATA_DIR}")
    print(f"Upload directory: {UPLOAD_DIR}")

    # No authentication system
    
    # Create sample data if none exists
    sample_file = os.path.join(CSV_DATA_DIR, 'EE_artchiles_design_12_06_2025.csv')
    if not os.path.exists(sample_file):
        print("Creating sample competitor data...")
        sample_data = pd.DataFrame({
            'Title': [
                'Modern Business Card Template',
                'Creative Logo Pack', 
                'Website UI Kit',
                'Brand Identity Package',
                'Social Media Templates'
            ],
            'Author': [
                'DesignStudio',
                'GraphicsPro',
                'WebDesigner', 
                'BrandMaster',
                'SocialDesign'
            ],
            'Page Old': [2, 1, 3, 4, 5],
            'Page New': [1, 2, 3, 2, 4],
            'Order Old': [15, 3, 20, 25, 30],
            'Order New': [8, 12, 20, 18, 22],
            'new_item': [False, False, False, True, True]
        })
        sample_data.to_csv(sample_file, index=False)
        print(f"Sample data created: {sample_file}")
    
    app.run(debug=True, host='0.0.0.0', port=5001)
