#!/bin/bash

# CSV Data Processing API Startup Script

echo "🚀 Starting CSV Data Processing API..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3."
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# Create data directory if it doesn't exist
mkdir -p data
mkdir -p uploads

echo "✅ Setup complete!"
echo ""
echo "🌐 Starting Flask API server..."
echo "📊 API will be available at: http://localhost:5000"
echo "📁 Data directory: $(pwd)/data"
echo "📤 Upload directory: $(pwd)/uploads"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the Flask application
python app.py
