#!/usr/bin/env python3
"""
Authentication Routes for OTP System
Handles API endpoints for OTP generation, validation, and session management
"""

from flask import Blueprint, request, jsonify
from functools import wraps
import re
from typing import Optional, Dict
from datetime import datetime

# Import our services
from otp_service import OTPService
from session_manager import SessionManager

# Create blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/api/auth')

# Global service instances (will be initialized in app.py)
otp_service: Optional[OTPService] = None
session_manager: Optional[SessionManager] = None

def init_auth_services(redis_client):
    """Initialize authentication services with Redis client"""
    global otp_service, session_manager
    otp_service = OTPService(redis_client)
    session_manager = SessionManager(redis_client)

def validate_email(email: str) -> bool:
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def get_client_info(request) -> Dict[str, str]:
    """Extract client information from request"""
    return {
        'ip_address': request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown')),
        'user_agent': request.headers.get('User-Agent', 'unknown')
    }

def require_session(f):
    """Decorator to require valid session"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session_manager:
            return jsonify({
                'success': False,
                'error': 'Session manager not initialized',
                'code': 'SERVICE_UNAVAILABLE'
            }), 500
        
        # Get session token from Authorization header
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'error': 'Authorization header required',
                'code': 'AUTHORIZATION_REQUIRED'
            }), 401
        
        session_token = auth_header[7:]  # Remove 'Bearer ' prefix
        
        # Validate session
        result = session_manager.validate_session(session_token)
        if not result.get('success'):
            return jsonify({
                'success': False,
                'error': result.get('error', 'Invalid session'),
                'code': result.get('code', 'INVALID_SESSION')
            }), 401
        
        # Add user info to request
        request.current_user = result
        return f(*args, **kwargs)
    
    return decorated_function

@auth_bp.route('/generate-otp', methods=['POST'])
def generate_otp():
    """Generate OTP for email address"""
    try:
        if not otp_service:
            return jsonify({
                'success': False,
                'error': 'OTP service not initialized',
                'code': 'SERVICE_UNAVAILABLE'
            }), 500
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'Request body required',
                'code': 'INVALID_REQUEST'
            }), 400
        
        email = data.get('email', '').strip().lower()
        if not email:
            return jsonify({
                'success': False,
                'error': 'Email is required',
                'code': 'EMAIL_REQUIRED'
            }), 400
        
        # Validate email format
        if not validate_email(email):
            return jsonify({
                'success': False,
                'error': 'Invalid email format',
                'code': 'INVALID_EMAIL'
            }), 400
        
        # Generate OTP
        result = otp_service.generate_otp(email)
        
        if result.get('success'):
            # Don't return the actual OTP code in production
            # It should be sent to Chrome Extension via postMessage
            response_data = {
                'success': True,
                'message': 'OTP generated successfully',
                'email': email,
                'otp_key': result.get('otp_key'),
                'expires_in': result.get('expires_in'),
                'created_at': result.get('created_at'),
                # Include OTP for Chrome Extension communication
                'otp_code': result.get('otp_code')  # This will be sent to extension
            }
            return jsonify(response_data), 200
        else:
            status_code = 429 if result.get('code') == 'RATE_LIMIT_EXCEEDED' else 400
            return jsonify(result), status_code
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}',
            'code': 'INTERNAL_ERROR'
        }), 500

@auth_bp.route('/validate-otp', methods=['POST'])
def validate_otp():
    """Validate OTP and create session"""
    try:
        if not otp_service or not session_manager:
            return jsonify({
                'success': False,
                'error': 'Authentication services not initialized',
                'code': 'SERVICE_UNAVAILABLE'
            }), 500
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'Request body required',
                'code': 'INVALID_REQUEST'
            }), 400
        
        email = data.get('email', '').strip().lower()
        otp_code = data.get('otp_code', '').strip()
        otp_key = data.get('otp_key', '').strip()
        
        if not email or not otp_code:
            return jsonify({
                'success': False,
                'error': 'Email and OTP code are required',
                'code': 'MISSING_FIELDS'
            }), 400
        
        # Validate email format
        if not validate_email(email):
            return jsonify({
                'success': False,
                'error': 'Invalid email format',
                'code': 'INVALID_EMAIL'
            }), 400
        
        # Validate OTP
        otp_result = otp_service.validate_otp(email, otp_code, otp_key)
        
        if otp_result.get('success'):
            # Create session
            session_result = session_manager.create_session(
                email, 
                otp_result.get('otp_key')
            )
            
            if session_result.get('success'):
                # Get client info
                client_info = get_client_info(request)
                
                response_data = {
                    'success': True,
                    'message': 'Authentication successful',
                    'session_token': session_result.get('session_token'),
                    'refresh_token': session_result.get('refresh_token'),
                    'expires_in': session_result.get('expires_in'),
                    'refresh_expires_in': session_result.get('refresh_expires_in'),
                    'user': {
                        'email': email,
                        'authenticated_at': session_result.get('created_at')
                    }
                }
                return jsonify(response_data), 200
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to create session',
                    'code': 'SESSION_CREATION_FAILED'
                }), 500
        else:
            status_code = 400
            if otp_result.get('code') in ['OTP_NOT_FOUND', 'OTP_ALREADY_USED']:
                status_code = 404
            elif otp_result.get('code') == 'MAX_ATTEMPTS_EXCEEDED':
                status_code = 429
            
            return jsonify(otp_result), status_code
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}',
            'code': 'INTERNAL_ERROR'
        }), 500

@auth_bp.route('/refresh-session', methods=['POST'])
def refresh_session():
    """Refresh session using refresh token"""
    try:
        if not session_manager:
            return jsonify({
                'success': False,
                'error': 'Session manager not initialized',
                'code': 'SERVICE_UNAVAILABLE'
            }), 500
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'Request body required',
                'code': 'INVALID_REQUEST'
            }), 400
        
        refresh_token = data.get('refresh_token', '').strip()
        if not refresh_token:
            return jsonify({
                'success': False,
                'error': 'Refresh token is required',
                'code': 'REFRESH_TOKEN_REQUIRED'
            }), 400
        
        # Refresh session
        result = session_manager.refresh_session(refresh_token)
        
        if result.get('success'):
            return jsonify(result), 200
        else:
            status_code = 401 if result.get('code') == 'REFRESH_TOKEN_NOT_FOUND' else 400
            return jsonify(result), status_code
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}',
            'code': 'INTERNAL_ERROR'
        }), 500

@auth_bp.route('/logout', methods=['POST'])
@require_session
def logout():
    """Logout and invalidate session"""
    try:
        if not session_manager:
            return jsonify({
                'success': False,
                'error': 'Session manager not initialized',
                'code': 'SERVICE_UNAVAILABLE'
            }), 500
        
        # Get session token from current user
        session_token = request.current_user.get('session_token')
        
        # Invalidate session
        result = session_manager.invalidate_session(session_token)
        
        return jsonify(result), 200
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}',
            'code': 'INTERNAL_ERROR'
        }), 500

@auth_bp.route('/logout-all', methods=['POST'])
@require_session
def logout_all():
    """Logout from all sessions"""
    try:
        if not session_manager:
            return jsonify({
                'success': False,
                'error': 'Session manager not initialized',
                'code': 'SERVICE_UNAVAILABLE'
            }), 500
        
        # Get email from current user
        email = request.current_user.get('email')
        
        # Invalidate all user sessions
        result = session_manager.invalidate_all_user_sessions(email)
        
        return jsonify(result), 200
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}',
            'code': 'INTERNAL_ERROR'
        }), 500

@auth_bp.route('/session', methods=['GET'])
@require_session
def get_session():
    """Get current session information"""
    try:
        user_info = request.current_user
        
        response_data = {
            'success': True,
            'user': {
                'email': user_info.get('email'),
                'session_token': user_info.get('session_token'),
                'created_at': user_info.get('created_at'),
                'last_activity': user_info.get('last_activity'),
                'expires_at': user_info.get('expires_at')
            }
        }
        
        return jsonify(response_data), 200
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Internal server error: {str(e)}',
            'code': 'INTERNAL_ERROR'
        }), 500

@auth_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        health_data = {
            'success': True,
            'service': 'OTP Authentication',
            'timestamp': datetime.now().isoformat(),
            'services': {
                'otp_service': otp_service is not None,
                'session_manager': session_manager is not None
            }
        }
        
        # Add service stats if available
        if otp_service:
            health_data['otp_stats'] = otp_service.get_otp_stats()
        
        if session_manager:
            health_data['session_stats'] = session_manager.get_session_stats()
        
        return jsonify(health_data), 200
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Health check failed: {str(e)}',
            'code': 'HEALTH_CHECK_ERROR'
        }), 500
