#!/usr/bin/env python3
"""
Monitoring and Health Check Module
Provides comprehensive monitoring for optimization features
"""

import os
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from flask import Blueprint, jsonify, request
import logging

# Import optimization modules
from config import config
from optimization_service import get_optimization_service
from redis_client import get_redis_client
from file_monitor import get_file_monitor

# Setup logging
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

# Create blueprint for monitoring endpoints
monitoring_bp = Blueprint('monitoring', __name__)

class SystemMonitor:
    """System monitoring and health checks"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.health_checks = []
        self.performance_metrics = []
        self.alerts = []
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system resource statistics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Memory usage
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Process info
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return {
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count,
                    'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used,
                    'free': memory.free
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100
                },
                'process': {
                    'memory_rss': process_memory.rss,
                    'memory_vms': process_memory.vms,
                    'memory_percent': process.memory_percent(),
                    'cpu_percent': process.cpu_percent(),
                    'num_threads': process.num_threads(),
                    'create_time': process.create_time()
                }
            }
        except Exception as e:
            logger.error(f"❌ Error getting system stats: {e}")
            return {'error': str(e)}
    
    def get_optimization_stats(self, csv_data_dir: str) -> Dict[str, Any]:
        """Get optimization service statistics"""
        stats = {
            'service_available': False,
            'redis_available': False,
            'file_monitor_available': False,
            'service_stats': {},
            'redis_stats': {},
            'file_monitor_stats': {}
        }
        
        # Check optimization service
        try:
            optimization_service = get_optimization_service(csv_data_dir)
            if optimization_service:
                stats['service_available'] = True
                stats['service_stats'] = optimization_service.get_stats()
        except Exception as e:
            logger.error(f"❌ Error getting optimization stats: {e}")
            stats['service_error'] = str(e)
        
        # Check Redis
        try:
            redis_client = get_redis_client()
            if redis_client:
                stats['redis_available'] = redis_client.connected
                stats['redis_stats'] = redis_client.get_stats()
        except Exception as e:
            logger.error(f"❌ Error getting Redis stats: {e}")
            stats['redis_error'] = str(e)
        
        # Check file monitor
        try:
            file_monitor = get_file_monitor(csv_data_dir)
            if file_monitor:
                stats['file_monitor_available'] = True
                stats['file_monitor_stats'] = file_monitor.get_stats()
        except Exception as e:
            logger.error(f"❌ Error getting file monitor stats: {e}")
            stats['file_monitor_error'] = str(e)
        
        return stats
    
    def perform_health_check(self, csv_data_dir: str) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        health = {
            'timestamp': datetime.now().isoformat(),
            'uptime_seconds': (datetime.now() - self.start_time).total_seconds(),
            'status': 'healthy',
            'components': {},
            'alerts': []
        }
        
        # System health
        system_stats = self.get_system_stats()
        if 'error' not in system_stats:
            health['components']['system'] = {
                'status': 'healthy',
                'cpu_percent': system_stats['cpu']['percent'],
                'memory_percent': system_stats['memory']['percent'],
                'disk_percent': system_stats['disk']['percent']
            }
            
            # Check for resource alerts
            if system_stats['cpu']['percent'] > 80:
                health['alerts'].append('High CPU usage detected')
                health['status'] = 'degraded'
            
            if system_stats['memory']['percent'] > 85:
                health['alerts'].append('High memory usage detected')
                health['status'] = 'degraded'
            
            if system_stats['disk']['percent'] > 90:
                health['alerts'].append('Low disk space detected')
                health['status'] = 'degraded'
        else:
            health['components']['system'] = {'status': 'unhealthy', 'error': system_stats['error']}
            health['status'] = 'unhealthy'
        
        # Optimization components health
        opt_stats = self.get_optimization_stats(csv_data_dir)
        
        # Redis health
        if config.ENABLE_CACHING:
            if opt_stats['redis_available']:
                health['components']['redis'] = {'status': 'healthy'}
            else:
                health['components']['redis'] = {'status': 'unhealthy'}
                health['alerts'].append('Redis is not available')
                health['status'] = 'degraded'
        else:
            health['components']['redis'] = {'status': 'disabled'}
        
        # File monitor health
        if config.ENABLE_FILE_MONITORING:
            if opt_stats['file_monitor_available']:
                health['components']['file_monitor'] = {'status': 'healthy'}
            else:
                health['components']['file_monitor'] = {'status': 'unhealthy'}
                health['alerts'].append('File monitor is not available')
                health['status'] = 'degraded'
        else:
            health['components']['file_monitor'] = {'status': 'disabled'}
        
        # CSV directory health
        if os.path.exists(csv_data_dir) and os.access(csv_data_dir, os.R_OK):
            csv_files = len([f for f in os.listdir(csv_data_dir) if f.endswith('.csv')])
            health['components']['csv_directory'] = {
                'status': 'healthy',
                'csv_files_count': csv_files
            }
            
            if csv_files == 0:
                health['alerts'].append('No CSV files found in data directory')
                health['status'] = 'degraded'
        else:
            health['components']['csv_directory'] = {'status': 'unhealthy'}
            health['alerts'].append('CSV data directory is not accessible')
            health['status'] = 'unhealthy'
        
        # Store health check result
        self.health_checks.append(health)
        
        # Keep only last 100 health checks
        if len(self.health_checks) > 100:
            self.health_checks = self.health_checks[-100:]
        
        return health
    
    def get_performance_trends(self) -> Dict[str, Any]:
        """Get performance trends from recent health checks"""
        if len(self.health_checks) < 2:
            return {'message': 'Insufficient data for trends'}
        
        recent_checks = self.health_checks[-10:]  # Last 10 checks
        
        trends = {
            'cpu_trend': [],
            'memory_trend': [],
            'disk_trend': [],
            'alert_frequency': 0,
            'status_distribution': {'healthy': 0, 'degraded': 0, 'unhealthy': 0}
        }
        
        for check in recent_checks:
            if 'system' in check['components'] and check['components']['system']['status'] == 'healthy':
                trends['cpu_trend'].append(check['components']['system']['cpu_percent'])
                trends['memory_trend'].append(check['components']['system']['memory_percent'])
                trends['disk_trend'].append(check['components']['system']['disk_percent'])
            
            trends['alert_frequency'] += len(check.get('alerts', []))
            trends['status_distribution'][check['status']] += 1
        
        return trends

# Global monitor instance
system_monitor = SystemMonitor()

# Monitoring endpoints
@monitoring_bp.route('/api/monitoring/health', methods=['GET'])
def detailed_health_check():
    """Detailed health check endpoint"""
    csv_data_dir = request.args.get('csv_data_dir', '/tmp')
    health = system_monitor.perform_health_check(csv_data_dir)
    return jsonify(health)

@monitoring_bp.route('/api/monitoring/stats', methods=['GET'])
def get_monitoring_stats():
    """Get comprehensive monitoring statistics"""
    csv_data_dir = request.args.get('csv_data_dir', '/tmp')
    
    stats = {
        'timestamp': datetime.now().isoformat(),
        'system': system_monitor.get_system_stats(),
        'optimizations': system_monitor.get_optimization_stats(csv_data_dir),
        'configuration': config.get_config_summary(),
        'trends': system_monitor.get_performance_trends()
    }
    
    return jsonify(stats)

@monitoring_bp.route('/api/monitoring/alerts', methods=['GET'])
def get_alerts():
    """Get current alerts"""
    csv_data_dir = request.args.get('csv_data_dir', '/tmp')
    health = system_monitor.perform_health_check(csv_data_dir)
    
    return jsonify({
        'timestamp': datetime.now().isoformat(),
        'alerts': health.get('alerts', []),
        'status': health['status']
    })

@monitoring_bp.route('/api/monitoring/performance', methods=['GET'])
def get_performance_metrics():
    """Get performance metrics"""
    csv_data_dir = request.args.get('csv_data_dir', '/tmp')
    
    # Get optimization service stats
    optimization_service = get_optimization_service(csv_data_dir)
    performance = {
        'timestamp': datetime.now().isoformat(),
        'cache_performance': {},
        'system_performance': system_monitor.get_system_stats(),
        'trends': system_monitor.get_performance_trends()
    }
    
    if optimization_service:
        service_stats = optimization_service.get_stats()
        performance['cache_performance'] = {
            'hit_rate': service_stats.get('cache_hit_rate', 0),
            'total_requests': service_stats.get('total_requests', 0),
            'cache_hits': service_stats.get('cache_hits', 0),
            'cache_misses': service_stats.get('cache_misses', 0),
            'cache_errors': service_stats.get('cache_errors', 0)
        }
    
    return jsonify(performance)

def register_monitoring(app, csv_data_dir: str):
    """Register monitoring endpoints with Flask app"""
    app.register_blueprint(monitoring_bp)
    
    # Set default csv_data_dir for monitoring endpoints
    @app.before_request
    def set_csv_data_dir():
        if request.endpoint and request.endpoint.startswith('monitoring.'):
            if 'csv_data_dir' not in request.args:
                request.args = request.args.copy()
                request.args['csv_data_dir'] = csv_data_dir
    
    logger.info("✅ Monitoring endpoints registered")

def create_monitoring_dashboard():
    """Create a simple HTML monitoring dashboard"""
    dashboard_html = '''
<!DOCTYPE html>
<html>
<head>
    <title>Flask Optimization Monitoring</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-healthy { color: #28a745; }
        .status-degraded { color: #ffc107; }
        .status-unhealthy { color: #dc3545; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        .alert { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 5px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .refresh-info { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Flask Optimization Monitoring</h1>
        
        <div class="card">
            <h2>System Status</h2>
            <div id="status">Loading...</div>
            <button onclick="refreshData()">Refresh</button>
            <span class="refresh-info">Last updated: <span id="lastUpdate">Never</span></span>
        </div>
        
        <div class="card">
            <h2>Performance Metrics</h2>
            <div id="performance">Loading...</div>
        </div>
        
        <div class="card">
            <h2>Alerts</h2>
            <div id="alerts">Loading...</div>
        </div>
        
        <div class="card">
            <h2>Configuration</h2>
            <div id="config">Loading...</div>
        </div>
    </div>
    
    <script>
        async function fetchData(endpoint) {
            try {
                const response = await fetch(endpoint);
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }
        
        async function refreshData() {
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            
            // Fetch health data
            const health = await fetchData('/api/monitoring/health');
            const statusDiv = document.getElementById('status');
            
            if (health.error) {
                statusDiv.innerHTML = `<div class="alert">Error: ${health.error}</div>`;
            } else {
                let statusClass = `status-${health.status}`;
                let statusHtml = `<h3 class="${statusClass}">Status: ${health.status.toUpperCase()}</h3>`;
                statusHtml += `<p>Uptime: ${Math.round(health.uptime_seconds / 60)} minutes</p>`;
                
                for (const [component, info] of Object.entries(health.components)) {
                    statusHtml += `<div class="metric">
                        <strong>${component}:</strong> 
                        <span class="status-${info.status}">${info.status}</span>
                    </div>`;
                }
                
                statusDiv.innerHTML = statusHtml;
            }
            
            // Fetch performance data
            const performance = await fetchData('/api/monitoring/performance');
            const perfDiv = document.getElementById('performance');
            
            if (performance.error) {
                perfDiv.innerHTML = `<div class="alert">Error: ${performance.error}</div>`;
            } else {
                let perfHtml = '';
                if (performance.cache_performance) {
                    perfHtml += `<div class="metric">
                        <strong>Cache Hit Rate:</strong> ${performance.cache_performance.hit_rate.toFixed(1)}%
                    </div>`;
                    perfHtml += `<div class="metric">
                        <strong>Total Requests:</strong> ${performance.cache_performance.total_requests}
                    </div>`;
                }
                
                if (performance.system_performance && !performance.system_performance.error) {
                    perfHtml += `<div class="metric">
                        <strong>CPU:</strong> ${performance.system_performance.cpu.percent.toFixed(1)}%
                    </div>`;
                    perfHtml += `<div class="metric">
                        <strong>Memory:</strong> ${performance.system_performance.memory.percent.toFixed(1)}%
                    </div>`;
                }
                
                perfDiv.innerHTML = perfHtml || 'No performance data available';
            }
            
            // Fetch alerts
            const alerts = await fetchData('/api/monitoring/alerts');
            const alertsDiv = document.getElementById('alerts');
            
            if (alerts.error) {
                alertsDiv.innerHTML = `<div class="alert">Error: ${alerts.error}</div>`;
            } else if (alerts.alerts && alerts.alerts.length > 0) {
                let alertsHtml = '';
                alerts.alerts.forEach(alert => {
                    alertsHtml += `<div class="alert">${alert}</div>`;
                });
                alertsDiv.innerHTML = alertsHtml;
            } else {
                alertsDiv.innerHTML = '<p style="color: #28a745;">No alerts</p>';
            }
            
            // Show configuration
            const configDiv = document.getElementById('config');
            configDiv.innerHTML = `
                <div class="metric"><strong>Optimizations:</strong> ${health.components?.redis?.status !== 'disabled' ? 'Enabled' : 'Disabled'}</div>
                <div class="metric"><strong>Caching:</strong> ${health.components?.redis?.status || 'Unknown'}</div>
                <div class="metric"><strong>File Monitoring:</strong> ${health.components?.file_monitor?.status || 'Unknown'}</div>
            `;
        }
        
        // Auto-refresh every 30 seconds
        setInterval(refreshData, 30000);
        
        // Initial load
        refreshData();
    </script>
</body>
</html>
    '''
    
    dashboard_path = os.path.join(os.path.dirname(__file__), 'monitoring_dashboard.html')
    
    try:
        with open(dashboard_path, 'w') as f:
            f.write(dashboard_html)
        
        logger.info(f"✅ Monitoring dashboard created: {dashboard_path}")
        print(f"💡 Open monitoring dashboard: file://{dashboard_path}")
        
    except Exception as e:
        logger.error(f"❌ Failed to create monitoring dashboard: {e}")

if __name__ == "__main__":
    # Test monitoring
    print("🧪 Testing monitoring system...")

    monitor = SystemMonitor()

    # Test system stats
    system_stats = monitor.get_system_stats()
    print(f"📊 System stats: CPU={system_stats.get('cpu', {}).get('percent', 'N/A')}%")

    # Test health check
    health = monitor.perform_health_check('/tmp')
    print(f"🏥 Health status: {health['status']}")

    # Create monitoring dashboard
    create_monitoring_dashboard()

    print("✅ Monitoring test completed")
