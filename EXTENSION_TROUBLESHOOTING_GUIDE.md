# 🔧 Chrome Extension Troubleshooting Guide

## 🚨 **MASALAH TERIDENTIFIKASI**

### ✅ **YANG SUDAH BEKERJA**
- **Backend API**: ✅ Running di port 5001
- **Frontend**: ✅ Configured dengan benar
- **Network**: ✅ Frontend ↔ Backend communication working
- **CORS**: ✅ Properly configured

### ❌ **MASALAH UTAMA**
**Chrome Extension TIDAK TER-LOAD di Chrome**

## 🔍 **DIAGNOSIS RESULTS**

### **Test Results:**
```
Extension Status:
❌ Available: No
❌ Connected: No  
❌ Version: Unknown

Backend API:
✅ Health Check: 200 OK
✅ OTP Generation: 200 OK
✅ Port: 5001 (correct)

Frontend:
✅ API Configuration: http://localhost:5001/api
✅ Extension Detection: Working (shows "not available")
✅ Error Handling: Proper error messages
```

## 🛠️ **SOLUSI STEP-BY-STEP**

### **STEP 1: Load Extension di Chrome**

1. **Buka Chrome Extensions Page**:
   ```
   chrome://extensions/
   ```

2. **Enable Developer Mode**:
   - Toggle "Developer mode" di kanan atas

3. **Load Extension**:
   ```
   Klik "Load unpacked"
   Navigate ke folder: chrome-extension
   (BUKAN build-production)
   Klik "Select Folder"
   ```

4. **Verifikasi Extension Loaded**:
   - Extension "Progress Dashboard OTP Authenticator" muncul
   - Toggle dalam posisi ON (enabled)
   - Tidak ada error messages

### **STEP 2: Verifikasi Extension Injection**

1. **Refresh Login Page**:
   ```
   http://localhost:5173/login
   ```

2. **Check Extension Status**:
   - Klik "Extension Info" button
   - Harus menunjukkan: Available: Yes, Connected: Yes

3. **Check Browser Console** (F12):
   ```
   ✅ [Extension] Content script loaded and ready
   ✅ [Extension] Initializing content script...
   ✅ [Extension] Content script initialized successfully
   ```

### **STEP 3: Test OTP Flow**

1. **Enter Email**: <EMAIL>
2. **Click "Send OTP"**
3. **Expected Result**: Extension notification muncul dengan OTP

## 🐛 **COMMON ISSUES & SOLUTIONS**

### **Issue 1: Extension Tidak Muncul Setelah Load**
**Cause**: Wrong folder selected
**Solution**: 
- Pastikan pilih folder `chrome-extension` 
- BUKAN `build-production`
- BUKAN subfolder lain

### **Issue 2: Extension Muncul tapi Disabled**
**Cause**: Extension error atau permission issue
**Solution**:
- Check error messages di extension card
- Klik reload button pada extension
- Pastikan manifest.json valid

### **Issue 3: Extension Enabled tapi Content Script Tidak Inject**
**Cause**: URL pattern tidak match atau script error
**Solution**:
- Hard refresh halaman (Ctrl+F5)
- Check console untuk error messages
- Pastikan URL adalah `http://localhost:5173`

### **Issue 4: "Permission Denied" Error**
**Cause**: Chrome security restrictions
**Solution**:
- Restart Chrome
- Clear extension data
- Re-load extension

## 📁 **FILE STRUCTURE VERIFICATION**

Pastikan struktur file benar:
```
chrome-extension/
├── manifest.json          ✅ Required
├── background.js          ✅ Required  
├── content-production.js  ✅ Required
├── popup/                 ✅ Required
├── assets/               ✅ Required
└── utils/                ✅ Required
```

## 🔧 **DEBUGGING TOOLS**

### **Extension Debug Tool**
File sudah tersedia: `chrome-extension/debug-extension.html`
- Buka file ini di browser
- Klik "Check Extension" untuk diagnosis

### **Content Script Test**
File tersedia: `test-content-injection.html`
- Test injection pada localhost
- Verifikasi manifest pattern matching

## 🎯 **EXPECTED RESULTS SETELAH FIX**

### **Extension Info Page**:
```
✅ Available: Yes
✅ Connected: Yes
✅ Version: 1.0.0
```

### **OTP Flow**:
```
1. Enter email → Click "Send OTP"
2. Extension notification muncul
3. User approve/reject
4. Login berhasil/gagal
```

### **Console Messages**:
```
✅ [Extension] Content script loaded and ready
✅ Backend API: OTP generated successfully
✅ Extension communication: Success
```

## 🚀 **NEXT STEPS**

1. **Load extension** menggunakan STEP 1
2. **Verify injection** menggunakan STEP 2  
3. **Test OTP flow** menggunakan STEP 3
4. **Report results** setelah mengikuti langkah-langkah

## 📞 **SUPPORT**

Jika masih ada masalah setelah mengikuti guide ini:
1. Screenshot extension page di chrome://extensions/
2. Screenshot extension info di login page
3. Copy console error messages
4. Report hasil troubleshooting

**🔧 Extension sudah siap, tinggal di-load ke Chrome!**
