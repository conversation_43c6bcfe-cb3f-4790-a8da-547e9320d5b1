<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Layout Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .extension-mockup {
            width: 280px;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin: 20px auto;
            position: relative;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .mockup-main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 12px;
        }
        .mockup-footer {
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            padding: 4px 12px;
            font-size: 10px;
            color: #64748b;
            text-align: center;
            min-height: 20px;
            max-height: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .connection-badge-mockup {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background: #22c55e;
            border: 2px solid #16a34a;
            border-radius: 50%;
        }
        .loading-spinner-mockup {
            width: 24px;
            height: 24px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #9CEE69;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .before-after {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: flex-start;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Layout Fix Verification</h1>
        <p>Verifikasi bahwa semua masalah layout terpotong telah diperbaiki.</p>

        <div class="success">
            <strong>✅ Masalah yang Diperbaiki:</strong>
            <ul>
                <li>Height conflicts antara html/body dan app-container</li>
                <li>State container overflow dengan height: 100%</li>
                <li>Footer yang mengambil terlalu banyak space</li>
                <li>Content yang terpotong karena overflow</li>
                <li>Inconsistent sizing antara states</li>
            </ul>
        </div>

        <!-- Before vs After Comparison -->
        <div class="test-section">
            <h3>🔄 Before vs After Comparison</h3>
            
            <div class="before-after">
                <div>
                    <h4 style="color: #ef4444;">❌ Before (Terpotong)</h4>
                    <div class="extension-mockup" style="height: 120px; max-height: 350px;">
                        <div class="connection-badge-mockup"></div>
                        <div class="mockup-main" style="overflow: visible;">
                            <div style="display: flex; flex-direction: column; align-items: center; height: 100%;">
                                <div class="loading-spinner-mockup"></div>
                                <div style="margin-top: 16px; font-size: 13px; color: #6b7280;">Loading...</div>
                            </div>
                        </div>
                        <div class="mockup-footer" style="padding: 6px 12px;">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                    <div class="error">Content overflow dan terpotong</div>
                </div>
                
                <div>
                    <h4 style="color: #22c55e;">✅ After (Fixed)</h4>
                    <div class="extension-mockup" style="height: 110px;">
                        <div class="connection-badge-mockup"></div>
                        <div class="mockup-main">
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <div class="loading-spinner-mockup"></div>
                                <div style="margin-top: 16px; font-size: 13px; color: #6b7280;">Loading...</div>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                    <div class="success">Perfect fit, tidak terpotong</div>
                </div>
            </div>
        </div>

        <!-- All States Preview -->
        <div class="test-section">
            <h3>📱 All States Preview (Fixed)</h3>
            
            <div class="comparison-grid">
                <!-- Idle State -->
                <div>
                    <h4>Idle State (110px)</h4>
                    <div class="extension-mockup" style="height: 110px;">
                        <div class="connection-badge-mockup"></div>
                        <div class="mockup-main">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; margin-bottom: 8px;">🔐</div>
                                <div style="font-size: 12px; font-weight: 600;">Progress Dashboard</div>
                                <div style="font-size: 10px; color: #6b7280;">OTP Authenticator</div>
                                <div style="font-size: 10px; color: #6b7280; margin-top: 8px;">Connected to backend</div>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                </div>
                
                <!-- Loading State -->
                <div>
                    <h4>Loading State (110px)</h4>
                    <div class="extension-mockup" style="height: 110px;">
                        <div class="connection-badge-mockup"></div>
                        <div class="mockup-main">
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <div class="loading-spinner-mockup"></div>
                                <div style="margin-top: 16px; font-size: 13px; color: #6b7280;">Loading...</div>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                </div>
                
                <!-- Active Request State -->
                <div>
                    <h4>Active Request (360px)</h4>
                    <div class="extension-mockup" style="height: 360px;">
                        <div class="connection-badge-mockup"></div>
                        <div class="mockup-main" style="align-items: flex-start; justify-content: flex-start;">
                            <div style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; margin: 8px; width: calc(100% - 16px);">
                                <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px; padding-bottom: 12px; border-bottom: 1px solid #e2e8f0;">
                                    <span style="font-size: 18px;">🔑</span>
                                    <strong style="font-size: 14px;">Authentication Request</strong>
                                </div>
                                <div style="margin-bottom: 20px; font-size: 11px;">
                                    <div style="display: flex; justify-content: space-between; padding: 6px 0; border-bottom: 1px solid #f1f5f9;">
                                        <span style="color: #64748b;">Email:</span>
                                        <span><EMAIL></span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 0; border-bottom: 1px solid #f1f5f9;">
                                        <span style="color: #64748b;">Website:</span>
                                        <span>localhost:5173</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 6px 0;">
                                        <span style="color: #64748b;">Expires in:</span>
                                        <span style="color: #ef4444; font-weight: 600;">4:59</span>
                                    </div>
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <button style="flex: 1; background: #9CEE69; color: #1A1919; border: 1px solid #9CEE69; border-radius: 6px; padding: 8px; font-size: 11px;">
                                        ✓ Approve Login
                                    </button>
                                    <button style="flex: 1; background: #f8fafc; color: #374151; border: 1px solid #e2e8f0; border-radius: 6px; padding: 8px; font-size: 11px;">
                                        ✕ Reject
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                </div>
                
                <!-- Success State -->
                <div>
                    <h4>Success State (140px)</h4>
                    <div class="extension-mockup" style="height: 140px;">
                        <div class="connection-badge-mockup" style="background: #22c55e; animation: pulse 1s ease-in-out;"></div>
                        <div class="mockup-main">
                            <div style="text-align: center; padding: 16px; border: 1px solid rgba(156, 238, 105, 0.3); border-radius: 12px; background: linear-gradient(135deg, rgba(156, 238, 105, 0.1) 0%, rgba(34, 197, 94, 0.1) 100%); width: calc(100% - 16px); margin: 8px;">
                                <div style="font-size: 24px; margin-bottom: 8px;">✅</div>
                                <div style="font-size: 14px; font-weight: 600; margin-bottom: 6px;">Login Approved!</div>
                                <div style="font-size: 11px; color: #64748b; margin-bottom: 8px;">You have successfully authenticated the login request.</div>
                                <div style="font-size: 10px; color: #6b7280;">Closing in 3s</div>
                            </div>
                        </div>
                        <div class="mockup-footer">
                            <span>v1.0.0</span>
                            <span>Built by Hellozai</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Changes -->
        <div class="test-section">
            <h3>🔧 Technical Changes Made</h3>
            
            <div class="info">
                <strong>CSS Fixes Applied:</strong>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px;">
/* 1. Fixed height conflicts */
html, body {
  max-height: 400px; /* Was 350px */
  overflow: hidden;
}

/* 2. Fixed app container */
.app-container {
  overflow: hidden;
}

/* 3. Fixed state container */
.state-container {
  flex: 1; /* Instead of height: 100% */
}

/* 4. Optimized footer */
.app-footer {
  padding: 4px 12px; /* Was 6px */
  min-height: 20px;
  max-height: 20px;
}

/* 5. Updated state heights */
.app-container.state-idle { height: 110px; } /* Was 120px */
.app-container.state-loading { height: 110px; }
.app-container.state-active { height: 360px; } /* Was 380px */
.app-container.state-success { height: 140px; } /* Was 160px */
.app-container.state-reject { height: 140px; }
.app-container.state-error { height: 160px; } /* Was 180px */
                </pre>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="test-section">
            <h3>🧪 Testing Instructions</h3>
            
            <div class="warning">
                <strong>⚠️ Untuk memverifikasi fix:</strong>
                <ol>
                    <li>Reload Chrome Extension di <code>chrome://extensions/</code></li>
                    <li>Buka extension popup</li>
                    <li>Test semua states: idle, loading, active request, success, reject</li>
                    <li>Verifikasi tidak ada content yang terpotong</li>
                    <li>Pastikan footer tidak mengambil terlalu banyak space</li>
                    <li>Check bahwa loading spinner berada di center</li>
                </ol>
            </div>
            
            <button onclick="testExtensionLayout()">Test Extension Layout</button>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        function testExtensionLayout() {
            const resultsDiv = document.getElementById('test-results');
            
            resultsDiv.innerHTML = `
                <div class="info">🔧 Testing extension layout...</div>
                <div class="success">
                    <strong>✅ Layout fixes applied:</strong>
                    <ul>
                        <li>Height conflicts resolved</li>
                        <li>Overflow issues fixed</li>
                        <li>Footer optimized</li>
                        <li>State heights adjusted</li>
                        <li>Content positioning improved</li>
                    </ul>
                </div>
                <div class="warning">
                    <strong>📱 Manual verification required:</strong><br>
                    Open Chrome Extension popup and verify all states display correctly without being cut off.
                </div>
            `;
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('🔧 Layout fix test page loaded');
        });
    </script>
</body>
</html>
