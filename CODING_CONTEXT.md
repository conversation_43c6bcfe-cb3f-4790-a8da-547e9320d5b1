# Progress Dashboard - Coding Context Guide

## 🎯 Application Overview

**Progress Dashboard** adalah aplikasi full-stack untuk analisis data kompetitor dan manajemen CSV dengan arsitektur modern React + Flask.

### Tech Stack
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Python Flask + SQLite + Pandas + Redis (optional)
- **Build**: Vite untuk frontend, Python virtual environment untuk backend

## 🏗️ Architecture Pattern

### Frontend Architecture
```
src/
├── App.tsx                 # Main app component dengan routing
├── main.tsx               # Entry point dengan React Router
├── components/            # React components
│   ├── Header.tsx         # Navigation header dengan burger menu
│   ├── AnalysisLayout.tsx # Layout untuk analysis pages
│   ├── Categories.tsx     # Categories analysis component
│   ├── Competitors.tsx    # Competitors analysis component
│   ├── FileManagement.tsx # File upload/management
│   ├── SystemSettings.tsx # System configuration
│   └── Notification*.tsx  # Notification system components
├── contexts/              # React contexts
│   └── NotificationContext.tsx # Global notification state
├── services/              # API services
│   ├── api.ts            # Main API service class
│   └── notificationService.ts # Notification API calls
├── types/                 # TypeScript type definitions
└── utils/                 # Utility functions
```

### Backend Architecture
```
backend/
├── app.py                 # Main Flask application
├── config.py             # Configuration management
├── optimization_service.py # Caching & performance
├── monitoring.py         # System monitoring
├── redis_client.py       # Redis connection management
├── notifications.db      # SQLite database
└── data/                 # CSV data storage
```

## 🔄 Data Flow

### 1. CSV Processing Pipeline
```
CSV Upload → File Validation → Pandas Processing → API Response → Frontend Display
```

### 2. API Communication
```
Frontend (React) ←→ Flask API (Port 5001) ←→ SQLite/CSV Files
```

### 3. Notification System
```
Backend Events → SQLite Storage → API Endpoints → React Context → UI Updates
```

## 🎨 UI/UX Patterns

### Design System
- **Theme**: Modern gradient backgrounds dengan glass morphism
- **Colors**: Seashell (#F7F3F0) primary, dengan accent colors
- **Layout**: Mobile-first responsive design
- **Components**: Reusable modal, table, form components

### Component Patterns
```tsx
// Standard component structure
const ComponentName: React.FC<Props> = ({ prop1, prop2 }) => {
  const [state, setState] = useState(initialValue);
  
  useEffect(() => {
    // Side effects
  }, [dependencies]);
  
  return (
    <div className="tailwind-classes">
      {/* JSX content */}
    </div>
  );
};
```

## 🔧 Development Workflow

### Starting Development
```bash
# Frontend (Terminal 1)
npm run dev                # Starts Vite dev server on port 5173

# Backend (Terminal 2)
cd backend
python app.py            # Starts Flask server on port 5001
# OR with optimizations:
./start_optimized.sh     # Starts with Redis caching
```

### Key Development Commands
```bash
# Frontend
npm run build            # Production build
npm run lint            # ESLint checking

# Backend
pip install -r requirements.txt  # Install dependencies
python test_endpoints.py         # Test API endpoints
```

## 📊 Core Features

### 1. Categories Analysis
- **File**: `src/components/Categories.tsx`
- **Purpose**: Analyze category performance data
- **API**: `/api/categories`, `/api/categories/{name}/data`

### 2. Competitors Analysis  
- **File**: `src/components/Competitors.tsx`
- **Purpose**: Track competitor performance
- **API**: `/api/competitors`, `/api/competitors/{name}/data`

### 3. File Management
- **File**: `src/components/FileManagement.tsx`
- **Purpose**: Upload, rename, delete CSV files
- **API**: `/api/files`, `/api/upload`, `/api/files/{id}/rename`

### 4. Notification System
- **Files**: `src/contexts/NotificationContext.tsx`, `src/components/Notification*.tsx`
- **Purpose**: Real-time notifications dengan history
- **API**: `/api/notifications`

## 🔍 Important Code Patterns

### API Service Pattern
```typescript
// src/services/api.ts
class ApiService {
  private async fetchWithErrorHandling<T>(url: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(url);
      return await response.json();
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}
```

### React Context Pattern
```typescript
// Global state management
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [state, dispatch] = useReducer(notificationReducer, initialState);
  // Context logic
  return <NotificationContext.Provider value={contextValue}>{children}</NotificationContext.Provider>;
};
```

### Flask API Pattern
```python
# backend/app.py
@app.route('/api/endpoint', methods=['GET'])
def endpoint_handler():
    try:
        # Process request
        result = process_data()
        return jsonify({'success': True, 'data': result})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
```

## 🚀 Performance Optimizations

### Frontend Optimizations
- **React.memo** untuk prevent unnecessary re-renders
- **useCallback** untuk memoized functions
- **Lazy loading** untuk large components
- **Vite** untuk fast build dan HMR

### Backend Optimizations
- **Redis caching** dengan circuit breaker pattern
- **Response compression** (gzip)
- **File monitoring** untuk real-time updates
- **Smart serialization** dengan fallback methods

## 🔒 Security Considerations

### Frontend Security
- **Input sanitization** pada form inputs
- **XSS prevention** dengan proper escaping
- **CORS configuration** untuk API access

### Backend Security
- **File upload validation** (CSV only)
- **SQL injection prevention** dengan parameterized queries
- **Error handling** tanpa expose sensitive info

## 🧪 Testing Strategy

### Frontend Testing
- **Component testing** dengan React Testing Library
- **API integration testing**
- **E2E testing** untuk critical paths

### Backend Testing
- **Unit tests** untuk individual functions
- **API endpoint testing** dengan test_endpoints.py
- **Performance testing** dengan performance_validator.py

## 📝 Coding Standards

### TypeScript/React
```typescript
// Use proper typing
interface ComponentProps {
  data: DataType[];
  onAction: (id: string) => void;
}

// Use functional components dengan hooks
const Component: React.FC<ComponentProps> = ({ data, onAction }) => {
  // Component logic
};
```

### Python/Flask
```python
# Use type hints
def process_data(data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Process data with proper documentation"""
    try:
        # Implementation
        return result
    except Exception as e:
        logger.error(f"Error processing data: {e}")
        raise
```

## 🔧 Configuration Management

### Environment Variables
```bash
# Frontend (.env)
VITE_API_BASE_URL=http://localhost:5001

# Backend
FLASK_ENV=development
ENABLE_OPTIMIZATIONS=true
ENABLE_CACHING=true
REDIS_HOST=localhost
REDIS_PORT=6379
```

### Feature Flags
```python
# backend/config.py
class OptimizationConfig:
    ENABLE_CACHING = os.getenv('ENABLE_CACHING', 'true').lower() == 'true'
    ENABLE_FILE_MONITORING = os.getenv('ENABLE_FILE_MONITORING', 'true').lower() == 'true'
```

## 🐛 Common Issues & Solutions

### 1. API Connection Issues
```typescript
// Check API availability
const isApiAvailable = await apiService.isApiAvailable();
if (!isApiAvailable) {
  // Handle offline mode
}
```

### 2. File Upload Issues
```python
# Validate file type
if not filename.endswith('.csv'):
    return jsonify({'error': 'Only CSV files allowed'}), 400
```

### 3. State Management Issues
```typescript
// Use proper dependency arrays
useEffect(() => {
  loadData();
}, [selectedCategory]); // Include dependencies
```

## 📚 Key Files Reference

### Must-Know Files
1. **src/App.tsx** - Main application structure
2. **src/components/Header.tsx** - Navigation dan burger menu
3. **backend/app.py** - Main Flask application
4. **src/services/api.ts** - API communication layer
5. **src/contexts/NotificationContext.tsx** - Global state management

### Configuration Files
1. **package.json** - Frontend dependencies
2. **backend/requirements.txt** - Backend dependencies  
3. **tailwind.config.js** - Styling configuration
4. **vite.config.ts** - Build configuration
5. **backend/config.py** - Backend configuration

---

**💡 Tips untuk Development:**
- Selalu test di browser dan mobile view
- Check console untuk errors
- Use React DevTools untuk debugging
- Monitor Flask logs untuk backend issues
- Test API endpoints dengan Postman/curl
